# 技术栈与依赖

- Java 8+，SpringMVC，MyBatis，Maven
- 依赖aaron-common、aaron-web-quick、aaron-exception、aaron-crypto等自有模块
- 支持Stripe、PayPal、支付宝等支付SDK
- HTTP通信用Apache HttpClient
- 前端为JSP/HTML/JS/CSS，无现代前端框架
- 静态站点为纯HTML+JS+CSS
- 兼容PC和移动端访问

## 技术结构图

```text
[SpringMVC Controller] ⇄ [Service] ⇄ [DAO/Mapper (MyBatis)] ⇄ [数据库]
          ↑
      [前端JSP/HTML/JS/CSS]
      [enyan-static静态HTML]


## 用户认证流程
sequenceDiagram
    participant User
    participant Filter
    participant AuthManager
    participant UserService
    participant DB

    User->>Filter: 提交email/password
    Filter->>AuthManager: 封装Authentication对象
    AuthManager->>UserService: 调用findByUsername(email)
    UserService->>DB: 执行SQL查询
    DB-->>UserService: 返回oc_customer记录
    UserService->>UserService: 构建UserDetails（含salt）
    UserService-->>AuthManager: 返回UserDetails
    AuthManager->>PasswordEncoder: 校验密码（password+salt）
    PasswordEncoder-->>AuthManager: 返回校验结果
    AuthManager-->>Filter: 认证结果



## 项目的安全机制

安全框架：
项目使用了Spring Security框架进行安全控制
主要配置类：SecurityConfiguration，继承自WebSecurityConfigurerAdapter
支持基于角色的访问控制（RBAC）
认证机制：
使用SaltedDaoAuthenticationProvider进行认证
支持多种密码编码器：
SaltPasswordEncoder：自定义的加盐密码编码器
BCryptPasswordEncoder：Spring Security推荐的密码编码器
EnyanSha1PasswordEncoder：自定义的SHA-1密码编码器
授权机制：
基于URL的访问控制，通过IAntMatcher接口定义URL模式
支持多种角色：
ROLE_USER：普通用户
ROLE_ADMIN：管理员
ROLE_VENDOR：供应商
ROLE_OPERATION：运营人员
ROLE_FINANCE：财务人员
ROLE_DBA：数据库管理员
密码策略：
密码至少8位
必须包含大小写字母、数字和特殊字符中的至少三种
支持密码加密存储
会话管理：
支持"记住我"功能
使用数据库存储持久化token
支持CSRF防护
安全特性：
支持跨域请求
支持Swagger API文档安全
自定义登录成功/失败处理器
自定义访问拒绝处理器
密码加密：
使用PasswordUtil进行密码加密和验证
支持MD5、SHA-1等多种加密算法
支持加盐加密
安全配置：
通过SecurityWebApplicationInitializer初始化Spring Security
支持自定义的CsrfTokenRepository
支持自定义的AuthenticationProvider
URL访问控制：
通过IAntMatcher接口定义URL模式
支持基于角色的访问控制
支持忽略特定URL的安全检查
登录处理：
自定义登录页面：/login
登录成功后的跳转逻辑在CustomSuccessHandler中定义
支持登录后跳转到之前访问的页面
登出处理：
登出URL：/logout
登出后清除认证信息和会话
登出后重定向到登录页面
异常处理：
自定义访问拒绝页面：/accessDenied
自定义登录失败处理器：CustomFailureHander
支持自定义错误消息



## 用户会话管理详细说明

1. 会话创建与认证流程
登录请求处理:
用户提交用户名和密码到/login端点
UsernamePasswordMultiDataAuthenticationFilter拦截请求
调用SaltedDaoAuthenticationProvider进行认证
认证过程:
java
CopyInsert
// 在SaltedDaoAuthenticationProvider中
protected void additionalAuthenticationChecks(UserDetails details, 
    UsernamePasswordAuthenticationToken auth) throws AuthenticationException {
    // 获取盐值
    Object salt = this.saltSource.getSalt(details);
    String presentedPassword = auth.getCredentials().toString();
    
    // 验证密码
    if (!((SaltPasswordEncoder)this.getPasswordEncoder())
        .isPasswordValid(details.getPassword(), presentedPassword, salt)) {
        throw new BadCredentialsException("Bad credentials");
    }
}
密码加密验证:
使用EnyanSha1PasswordEncoder进行密码加密和验证
加密过程：
java
CopyInsert
public String encodePassword(String rawPass, Object salt) {
    String value = super.encodePassword(rawPass, "");  // 第一次SHA1
    value = super.encodePassword(salt + value, "");    // 加盐后第二次SHA1
    value = super.encodePassword(salt + value, "");    // 加盐后第三次SHA1
    return value;
}
2. 会话保持（Remember Me）
Remember Me配置:
java
CopyInsert
// 在SecurityConfiguration中
.and().rememberMe()
    .rememberMeParameter("remember-me")
    .tokenRepository(tokenRepository())
Remember Me Token存储:
java
CopyInsert
@Bean
public PersistentTokenRepository tokenRepository() {
    JdbcTokenRepositoryImpl jdbcTokenRepositoryImpl = new JdbcTokenRepositoryImpl();
    jdbcTokenRepositoryImpl.setDataSource(dataSource);
    jdbcTokenRepositoryImpl.setCreateTableOnStartup(antMatcher.createPersistentTokenTableOnStartup());
    return jdbcTokenRepositoryImpl;
}
Token表结构:
persistent_logins表存储Remember Me Token
包含字段：username, series, token, last_used
3. 会话安全控制
CSRF防护:
java
CopyInsert
if (!antMatcher.useSecurity()) {
    http.csrf().disable();
} else {
    http.csrf().ignoringAntMatchers(antMatcher.getCsrfIgnoringURL());
}
会话固定保护:
默认启用sessionFixation().migrateSession()
登录成功后生成新的会话ID
并发控制:
默认配置允许同一用户多次登录
可通过maximumSessions()配置最大会话数
4. 会话超时与失效
会话超时:
在web.xml中配置会话超时时间
默认30分钟无活动会话超时
登出处理:
java
CopyInsert
http.logout()
    .logoutRequestMatcher(new AntPathRequestMatcher("/logout", "GET"))
    .logoutSuccessHandler(((request, response, authentication) -> {
        response.sendRedirect("/login");
    }))
    .clearAuthentication(true)
    .invalidateHttpSession(true);
5. 密码安全策略
密码强度要求:
至少8位
必须包含大小写字母、数字和特殊字符中的至少三种
密码加密:
使用SHA1加盐哈希
三次哈希迭代增加安全性
每个用户使用唯一盐值
6. 自定义用户模型
IUser接口:
java
CopyInsert
public interface IUser extends UserDetails {
    Object getUserId();
    Object getSalt();
    String getEmail();
    // ... 其他方法
}
用户详情加载:
通过UserDetailsService加载用户信息
实现类需要返回IUser实例
7. 安全事件处理
认证成功:
调用CustomSuccessHandler处理
根据用户角色重定向到对应页面
认证失败:
调用CustomFailureHandler处理
重定向到登录页面并显示错误信息
访问拒绝:
配置了accessDeniedPage("/accessDenied")
无权限访问时跳转到指定页面
8. 跨域支持
java
CopyInsert
@Bean
public UrlBasedCorsConfigurationSource corsConfigurationSource() {
    final CorsConfiguration configuration = new CorsConfiguration();
    configuration.setAllowedOrigins(asList("*"));
    configuration.setAllowedMethods(asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
    configuration.setAllowedHeaders(asList("X-XSRF-TOKEN", "Content-Type"));
    configuration.setExposedHeaders(asList("Authorization", "X-XSRF-TOKEN"));
    configuration.setAllowCredentials(true);
    configuration.setMaxAge(HOURS.toSeconds(72));
    
    final UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);
    return source;
}
9. 会话数据存储
HttpSession:
默认使用Tomcat的StandardSession实现
会话数据存储在JVM内存中
分布式会话:
支持通过Spring Session集成Redis实现分布式会话
需要额外配置@EnableRedisHttpSession
10. 安全配置扩展性
自定义URL权限:
实现IAntMatcher接口定义URL模式
支持基于角色的细粒度访问控制
自定义认证逻辑:
继承SaltedDaoAuthenticationProvider
重写additionalAuthenticationChecks方法
自定义密码编码器:
实现SaltPasswordEncoder接口
配置为Spring Bean后自动生效





## 用户注册帐号关键信息
根据代码分析，我找到了用户注册和账号创建的逻辑。以下是关键发现：

注册接口:
路径: /api/v4/user/reg (POST)
控制器: RestUserController.reg()
服务层: AuthUserServiceImpl.reg()
注册流程:
验证必填字段(email, 密码, 用户名, sign)
验证邮箱格式
解密sign参数进行安全验证
检查邮箱是否已存在
调用authUserService.reg()创建用户
用户创建逻辑 (AuthUserServiceImpl.reg()):
将邮箱转为小写
生成随机salt
使用salt对密码进行加密
将用户信息插入到oc_customer表
设置用户状态为未激活(approved=0)
关键点:
用户注册后需要激活(approved=0)
密码使用salt加密存储
用户名存储在firstname字段
邮箱必须唯一
购买流程中的账号检查:
在购买流程中，会检查用户是否登录
如果用户未登录，会重定向到登录/注册页面
注册成功后，用户需要激活账号才能完成购买
账号激活:
注册后会发送激活邮件
用户需要点击邮件中的链接激活账号
激活后，approved字段会更新为1




