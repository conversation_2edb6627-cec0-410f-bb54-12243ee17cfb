create table auth_user
(
    user_id       bigint auto_increment
        primary key,
    user_password varchar(40) default ''                           null comment '密码',
    salt          varchar(9)  default ''                           null comment 'salt',
    role_type     tinyint     default 0                            null comment '角色类型（1 管理员；2 供应商）',
    user_name     varchar(30) default ''                           null comment '用户名',
    nick_name     varchar(30) default ''                           null comment '昵称',
    email         varchar(30) default ''                           null comment 'email',
    is_staff      tinyint     default 0                            null comment '是否是staff',
    is_active     tinyint     default 0                            null comment '是否已经激活',
    date_joined   datetime(6) default '1000-01-01 00:00:00.000000' null comment '加入时间',
    last_login    datetime(6) default '1000-01-01 00:00:00.000000' null comment '最后登录时间'
);

create table content
(
    id             varchar(255)  not null
        primary key,
    encryption_key varbinary(64) not null,
    location       text          not null,
    length         bigint        null,
    sha256         varchar(64)   null
);

create table data_rent_stat
(
    data_id            bigint auto_increment
        primary key,
    rent_sc_all_sum    int         default 0                            null comment '新旧约销量',
    rent_sc_ot_sum     int         default 0                            null comment '旧约销量',
    rent_sc_nt_sum     int         default 0                            null comment '新约销量',
    rent_sc_all_new    int         default 0                            null comment '新旧约新增订阅',
    rent_sc_ot_new     int         default 0                            null comment '旧约新增订阅',
    rent_sc_nt_new     int         default 0                            null comment '新约新增订阅',
    rent_sc_all_leave  int         default 0                            null comment '新旧约退订',
    rent_sc_ot_leave   int         default 0                            null comment '旧约退订',
    rent_sc_nt_leave   int         default 0                            null comment '新约退订',
    rent_sc_all_buy    int         default 0                            null comment '新旧约退订购买',
    rent_sc_ot_buy     int         default 0                            null comment '旧约退订购买',
    rent_sc_nt_buy     int         default 0                            null comment '新约退订购买',
    rent_sc_all_active int         default 0                            null comment '新旧约正在阅读',
    rent_sc_ot_active  int         default 0                            null comment '旧约正在阅读',
    rent_sc_nt_active  int         default 0                            null comment '新约正在阅读',
    rent_sc_all_time   varchar(10) default ''                           null comment '新旧约退订平均阅读时长',
    rent_sc_ot_time    varchar(10) default ''                           null comment '旧约平均阅读时长',
    rent_sc_nt_time    varchar(10) default ''                           null comment '新约平均阅读时长',
    rent_tc_all_sum    int         default 0                            null comment '新旧约销量',
    rent_tc_ot_sum     int         default 0                            null comment '旧约销量',
    rent_tc_nt_sum     int         default 0                            null comment '新约销量',
    rent_tc_all_new    int         default 0                            null comment '新旧约新增订阅',
    rent_tc_ot_new     int         default 0                            null comment '旧约新增订阅',
    rent_tc_nt_new     int         default 0                            null comment '新约新增订阅',
    rent_tc_all_leave  int         default 0                            null comment '新旧约退订',
    rent_tc_ot_leave   int         default 0                            null comment '旧约退订',
    rent_tc_nt_leave   int         default 0                            null comment '新约退订',
    rent_tc_all_buy    int         default 0                            null comment '新旧约退订购买',
    rent_tc_ot_buy     int         default 0                            null comment '旧约退订购买',
    rent_tc_nt_buy     int         default 0                            null comment '新约退订购买',
    rent_tc_all_active int         default 0                            null comment '新旧约正在阅读',
    rent_tc_ot_active  int         default 0                            null comment '旧约正在阅读',
    rent_tc_nt_active  int         default 0                            null comment '新约正在阅读',
    rent_tc_all_time   varchar(10) default ''                           null comment '新旧约退订平均阅读时长',
    rent_tc_ot_time    varchar(10) default ''                           null comment '旧约平均阅读时长',
    rent_tc_nt_time    varchar(10) default ''                           null comment '新约平均阅读时长',
    data_at            datetime(6) default '1000-01-01 00:00:00.000000' null comment '数据时间',
    create_time        datetime(6) default '1000-01-01 00:00:00.000000' null comment '创建时间'
);

create table data_stat
(
    data_id          bigint auto_increment
        primary key,
    sales_volume     int            default 0                            null comment '销量',
    income_total     decimal(20, 2) default 0.00                         null comment '销售额',
    order_count      int            default 0                            null comment '订单数',
    order_fee_count  int            default 0                            null comment '付费订单数',
    order_free_count int            default 0                            null comment '免费订单数',
    user_buy_count   int            default 0                            null comment '购买用户数',
    user_new_count   int            default 0                            null comment '新注册用户数',
    create_time      datetime(6)    default '1000-01-01 00:00:00.000000' null comment '创建时间'
);

create table email_history
(
    email_id       bigint auto_increment
        primary key,
    email          varchar(50) default '' null comment 'email',
    email_type     int         default 0  null comment '1：注册激活；2：密码找回',
    repeat_times   int         default 0  null comment '重试次数',
    send_at        bigint      default 0  null comment 'send时间',
    year_month_day int         default 0  null comment 'yyyyMMdd'
);

create table enyan_acsm
(
    acsm_id        bigint auto_increment
        primary key,
    order_num      varchar(40) default ''                           null comment '订单号',
    book_id        bigint      default 0                            null comment 'book ID',
    user_id        bigint      default 0                            null comment '读者ID',
    transaction_id varchar(20) default ''                           null comment 'transaction_id',
    is_fulfilled   tinyint     default 0                            null comment '是否实现',
    acsm_info      varchar(50) default ''                           null comment 'acsm 文件名称',
    create_at      datetime(6) default '1000-01-01 00:00:00.000000' null comment '创建时间',
    constraint enyan_acsm_order_num_book_id_user_id_uindex
        unique (order_num, book_id, user_id)
);

create table enyan_balance
(
    balance_id      bigint auto_increment
        primary key,
    purchased_month int            default 0    null comment '购买月份',
    publisher_id    bigint         default 0    null comment '版权商ID',
    quantity        int            default 1    null comment '数量',
    price_fixed     decimal(20, 2) default 0.00 null comment '定价',
    price_selling   decimal(20, 2) default 0.00 null comment '售价',
    income_vendor   decimal(20, 2) default 0.00 null comment 'Vendor收益',
    income_plat     decimal(20, 2) default 0.00 null comment '平台收益',
    income_total    decimal(20, 2) default 0.00 null comment '总收益',
    income_real     decimal(20, 2) default 0.00 null comment '真实本币（人民币或美元）总收益',
    order_type      int            default 0    null comment '订单类型，0：电子书',
    is_counted      tinyint        default 0    null comment '-1:未结算；:0：结算中；1：已结算；-2：已取消',
    constraint enyan_balance_month_publisher_id_uindex
        unique (purchased_month, publisher_id)
);

create table enyan_balance_history
(
    balance_history_id  bigint auto_increment
        primary key,
    publisher_id        bigint         default 0                            null comment '版权商ID',
    income_vendor_total decimal(20, 2) default 0.00                         null comment 'Vendor收益',
    balance_detail      longtext                                            null comment 'detail',
    balance_day         int            default 0                            null comment '购买月份',
    balance_at          datetime(6)    default '1000-01-01 00:00:00.000000' null comment '结算时间',
    is_counted          tinyint        default 0                            null comment '-1:未结算；:0：结算中；1：已结算；-2：已取消'
);

create table enyan_banner
(
    data_id          bigint auto_increment
        primary key,
    data_name        varchar(40)  default ''                           null comment 'banner名称',
    data_img_url     varchar(100) default ''                           null comment '图片的url',
    data_to_url      varchar(100) default ''                           null comment '转向的url',
    data_index_show  int          default 0                            null comment '是否网站首页显示：0：不显示；1：显示',
    data_middle_show int          default 0                            null comment '是否网站中间banner显示：0：不显示；1：显示',
    data_read_show   int          default 0                            null comment '是否读书会首页显示：0：不显示；1：显示',
    data_priority    int          default 0                            null comment '推荐次序',
    data_status      int          default 0                            null comment '0：不展示；1：展示',
    is_deleted       int          default 0                            null comment '是否已经删除',
    begin_at         datetime(6)  default '2020-01-01 00:00:00.000000' null comment '开始时间',
    end_at           datetime(6)  default '2020-01-01 00:00:00.000000' null comment '结束时间',
    create_at        datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
);

create table enyan_blog
(
    blog_id             bigint auto_increment
        primary key,
    blog_title          varchar(100) default ''                           null comment '标题',
    author              varchar(40)  default ''                           null comment '作者',
    publisher_id        bigint       default 0                            null comment '出版方ID',
    blog_cover          varchar(100) default ''                           null comment '头图',
    blog_cover_app      varchar(100) default ''                           null comment '头图App封面',
    blog_abstract       varchar(200) default ''                           null comment '摘要',
    recommended_order   int          default 0                            null comment '推荐优先级',
    recommended_caption varchar(100) default ''                           null comment '推荐说明',
    blog_content        longtext                                          null comment '正文',
    category_id         int          default 0                            null comment '类别',
    read_count          int          default 0                            null comment '阅读量',
    like_count          int          default 0                            null comment '点赞量',
    is_deleted          int          default 0                            null comment '是否已经删除',
    create_at           datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
);

create table enyan_book
(
    book_id                     bigint auto_increment
        primary key,
    book_title                  varchar(150)   default ''                           null comment '书名',
    book_pinyin                 varchar(200)                                        null,
    author                      varchar(150)   default ''                           null comment '作者',
    author_bio                  varchar(200)   default ''                           null comment '作者介绍',
    translator                  varchar(50)    default ''                           null comment '译者',
    word_count                  varchar(50)    default ''                           null comment '字数',
    word_count_show             varchar(50)    default ''                           null comment '字数的展示',
    product_web                 varchar(50)    default ''                           null comment '产品网站',
    price                       decimal(10, 2) default 0.00                         null comment '价格',
    sales_volume                bigint         default 0                            null comment '销量',
    price_cny                   decimal(10, 2) default 0.00                         null comment '人民币价格',
    price_usd                   decimal(10, 2) default 0.00                         null comment '美元价格',
    price_hkd                   decimal(10, 2) default 0.00                         null comment 'hk价格',
    book_cost                   int            default 0                            null comment '书籍成本',
    category_id                 bigint         default 0                            null comment '类别ID',
    category_name               varchar(50)    default ''                           null comment '类别名称',
    publisher_id                bigint         default 0                            null comment '出版方ID',
    publisher_name              varchar(50)    default ''                           null comment '出版方名称',
    published_at                varchar(20)    default ''                           null comment '发布日期 精确到年份',
    book_description            longtext                                            null comment '简介',
    book_catalogue              varchar(7000)  default ''                           null comment '图书目录',
    book_abstract               varchar(200)   default ''                           null comment '图书摘要',
    book_cover                  varchar(100)   default ''                           null comment '图书封面',
    book_cover_app              varchar(100)   default ''                           null comment '图书App封面',
    book_sample                 varchar(100)   default ''                           null comment '图书样章',
    book_full                   varchar(100)   default ''                           null comment '图书文件',
    book_hash                   varchar(50)    default ''                           null comment '图书识别编号',
    book_isbn                   varchar(20)    default ''                           null comment '纸书ISBN',
    ebook_isbn                  varchar(20)    default ''                           null comment '电子书ISBN',
    book_esin                   varchar(20)    default ''                           null comment '图书ESIN',
    has_book_pagination         tinyint        default 0                            null comment '是否有纸版书页码',
    book_pub_code               varchar(20)    default ''                           null comment '出版编码',
    book_keywords               varchar(100)   default ''                           null comment 'keywords',
    book_type                   int            default 0                            null comment '书籍类型：0：电子书单本；1：电子书套装；2：纸版书；3：纸版书套装',
    special_offer               int            default 0                            null comment '特价区：0：非特价；1：特价',
    area_discount               int            default 0                            null comment '区域折扣：0：没有;1：大陆免费;2：大陆2折',
    can_tts                     int            default 0                            null comment '可否tts：0：不可以；1：可以',
    book_web                    longtext                                            null comment '纸版书链接',
    size                        varchar(20)    default ''                           null comment '书籍大小',
    book_drm_ref                varchar(50)    default ''                           null comment 'DRM的关联',
    discount_single_id          bigint         default 0                            null comment '折扣ID，专用于单个折扣的设置和查询',
    discount_single_type        tinyint        default 0                            null comment '单独折扣类型(0 打折;1 减去)',
    discount_single_value       int            default 100                          null comment '单独折扣',
    discount_single_start_time  date           default '1000-01-01'                 null comment '单独折扣开始日期',
    discount_single_end_time    date           default '1000-01-01'                 null comment '单独折扣结束日期',
    discount_single_is_valid    tinyint        default 0                            null comment '单独折扣是否有效',
    discount_single_description varchar(40)    default ''                           null comment '单独折扣描述',
    discount_id                 bigint         default 0                            null comment '折扣ID',
    discount_description        varchar(40)    default ''                           null comment '折扣描述',
    discount_is_valid           tinyint        default 0                            null comment '折扣是否有效',
    set_name                    varchar(40)    default ''                           null comment '书系名称',
    set_id                      bigint         default 0                            null comment '书系ID',
    is_in_cn                    tinyint        default 0                            null comment '书籍语言(1 简体;2 繁体;3 英文)',
    bible_version               int            default 0                            null comment '0:简体；1：繁体',
    can_member                  int            default 0                            null comment '可否在会员范畴：0：不可以；1：可以',
    show_publisher              tinyint        default 1                            null comment '是否展示出版社(0 不展示;1 展示)',
    recommended_order           int            default 0                            null comment '推荐顺序',
    recommended_caption         varchar(300)   default ''                           null comment '推荐说明',
    print_permission            tinyint        default 0                            null comment '允许打印到纸张',
    copy_permission             tinyint        default 0                            null comment '允许内容复制',
    ebook_format                tinyint        default 1                            null comment '载体格式 (1 ePUB;2 PDF)',
    shelf_status                tinyint        default 0                            null comment '销售状态 (0 已下架;1 已上架;2 待重审)',
    vendor_percent              int            default 0                            null comment 'Vendor比例（版税税率）',
    sales_model                 int            default 0                            null comment '0:正式销售；1：预售',
    star                        varchar(10)    default ''                           null comment '评价等级: 0:默认；1-5：1至5星',
    star_count                  int            default 0                            null comment '书籍的评分数',
    opensale_at                 datetime(6)    default '2020-01-01 00:00:00.000000' null comment '初次上架时间'
);

create table enyan_book_buy
(
    book_buy_id     bigint auto_increment
        primary key,
    order_num       varchar(40)                  default ''                           null comment '订单号',
    user_id         bigint                       default 0                            null comment '读者ID',
    user_email      varchar(96)                  default ''                           null comment '用户email',
    book_id         bigint                       default 0                            null comment 'book ID',
    book_title      varchar(150)                 default ''                           null comment '书名',
    purchased_at    datetime(6)                  default '1000-01-01 00:00:00.000000' null comment '购买时间',
    purchased_day   int                          default 0                            null comment '购买时间Int',
    drmInfo         varchar(500) charset utf8mb3 default ''                           null comment 'DRM信息',
    read_info       varchar(500) charset utf8mb3 default ''                           null comment '阅读的相关信息',
    group_name      varchar(30) charset utf8mb3  default ''                           null comment '分组信息，app的组名',
    read_time       bigint                       default 0                            null comment '阅读信息提交的时间',
    group_name_time bigint                       default 0                            null comment '分组信息提交的时间',
    is_deleted      int                          default 0                            null comment '是否已经删除',
    constraint enyan_book_buy_user_email_book_id_is_deleted_uindex
        unique (user_email, book_id, is_deleted)
);

create table enyan_book_cost
(
    book_cost_id bigint auto_increment
        primary key,
    book_id      bigint       default 0                            null comment '书籍ID',
    book_title   varchar(150) default ''                           null comment '书名',
    publisher_id bigint       default 0                            null comment '版权商ID',
    book_cost    int          default 0                            null comment '书籍成本',
    purchased_at datetime(6)  default '1000-01-01 00:00:00.000000' null comment '结算时间',
    is_counted   tinyint      default 0                            null comment '-1:未结算；:0：结算中；1：已结算；-2：已取消',
    constraint enyan_book_cost_book_id_uindex
        unique (book_id)
);

create table enyan_book_list
(
    set_id            bigint auto_increment
        primary key,
    set_name          varchar(40)    default ''   null comment '书单名称',
    set_name_tc       varchar(40)    default ''   null comment '书单名称繁体',
    set_name_en       varchar(40)    default ''   null comment '书单名称英文',
    banner_url        varchar(100)   default ''   null comment '图片的url',
    set_abstract      varchar(500)   default ''   null comment '书单介绍',
    book_web          varchar(500)   default ''   null comment '其他版本链接',
    book_id_text      varchar(500)   default ''   null comment '书籍ID列表',
    discount_value    int            default 100  null comment '折扣值',
    is_discount_valid int            default 0    null comment '折扣是否有效',
    price             decimal(10, 2) default 0.00 null comment '价格',
    price_discount    decimal(10, 2) default 0.00 null comment '折扣后价格',
    is_index          int            default 0    null comment '是否首页(0 不首页;1 首页列表；2:首页的套系推荐)',
    show_order        int                         not null comment '排序',
    book_cover        varchar(100)   default ''   null comment '图书封面',
    is_valid          int            default 0    null comment '是否有效',
    is_show           int            default 0    null comment '是否书封展示',
    can_all_buy       int            default 0    null comment '是否支持全套购买'
);

create table enyan_book_set
(
    set_id            bigint auto_increment
        primary key,
    set_name          varchar(40)    default ''   null comment '折扣名称',
    set_name_tc       varchar(40)    default ''   null comment '书系名称繁体',
    set_name_en       varchar(40)    default ''   null comment '书系名称英文',
    banner_url        varchar(100)   default ''   null comment '图片的url',
    set_abstract      varchar(500)   default ''   null comment '书系介绍',
    book_web          varchar(500)   default ''   null comment '其他版本链接',
    discount_value    int            default 100  null comment '折扣值',
    is_discount_valid int            default 0    null comment '折扣是否有效',
    price             decimal(10, 2) default 0.00 null comment '价格',
    price_discount    decimal(10, 2) default 0.00 null comment '折扣后价格',
    is_index          int            default 0    null comment '是否首页(0 不首页;1 首页列表；2:首页的套系推荐)',
    show_order        int                         not null comment '排序',
    book_cover        varchar(100)   default ''   null comment '图书封面',
    is_valid          int            default 0    null comment '是否有效',
    is_show           int            default 0    null comment '是否书封展示',
    can_all_buy       int            default 0    null comment '是否支持全套购买'
);

create table enyan_cart
(
    cart_id    varchar(40)                                      not null
        primary key,
    user_email varchar(96) default ''                           null comment '用户email',
    book_id    bigint      default 0                            null comment 'book ID',
    quantity   int                                              not null,
    add_at     datetime(6) default '1000-01-01 00:00:00.000000' null comment 'add时间',
    constraint enyan_cart_user_email_book_id_uindex
        unique (user_email, book_id)
);

create table enyan_category
(
    category_id      bigint auto_increment
        primary key,
    category_name    varchar(40) default '' null comment '类型名',
    category_name_tc varchar(40) default '' null comment '类型名繁体',
    category_name_en varchar(40) default '' null comment '类型名en',
    is_index         tinyint     default 0  null comment '是否首页(0 不首页;1 首页)',
    is_hidden        int         default 0  null comment '是否隐藏',
    category_order   int                    not null comment '排序',
    book_recommended varchar(50) default '' null comment '推荐的书籍'
);

create table enyan_comment
(
    data_id       bigint auto_increment
        primary key,
    email         varchar(96)   default ''                           null comment '用户email',
    nick_name     varchar(32)   default ''                           null comment '用户email',
    sex           int           default 0                            null comment '性别',
    book_id       bigint        default 0                            null comment '书籍ID',
    title         varchar(50)   default ''                           null comment '标题',
    content       varchar(2010) default ''                           null comment '内容',
    star          varchar(10)   default ''                           null comment '评价等级: 0:默认；1-5：1至5星',
    comment_count int           default 0                            null comment '此回复的回复数',
    like_count    int           default 0                            null comment '点赞量',
    can_show      int           default 0                            null comment '用于标记是否可以显示',
    parent_id     bigint        default 0                            null comment '回复的评论: 0:默认；其他：回复的ID',
    create_at     datetime(6)   default '2022-01-01 00:00:00.000000' null comment '创建时间',
    is_deleted    int           default 0                            null comment '是否已经删除'
);

create table enyan_config
(
    config_id          bigint                  not null
        primary key,
    config_description varchar(40)  default '' null comment '展示值',
    config_name        varchar(40)  default '' null comment '配置名',
    config_value       varchar(100) default '' null comment '配置值',
    is_show            tinyint      default 0  null comment '是否有效'
);

create table enyan_coupon
(
    data_id         bigint auto_increment
        primary key,
    coupon_name     varchar(40) default '0'                          null comment '活动名称',
    coupon_code     varchar(40) default '0'                          null comment '优惠码',
    coupon_value    int         default 0                            null comment '优惠码价格',
    buy_max         int         default 0                            null comment '最大购买次数',
    use_max         int         default 0                            null comment '最大使用次数',
    min_limit_value int         default 0                            null comment '满减最低限额',
    buy_count       int         default 0                            null comment '已经购买次数',
    coupon_type     int         default 0                            null comment '优惠码类别（0：所有；1：指定书籍）',
    coupon_status   int         default 0                            null comment '优惠码状态',
    book_string     longtext                                         null comment '优惠码关联书籍',
    begin_time      datetime(6) default '1000-01-01 00:00:00.000000' null comment '开始时间',
    end_time        datetime(6) default '1000-01-01 00:00:00.000000' null comment '结束时间',
    create_at       datetime(6) default '1000-01-01 00:00:00.000000' null comment '创建时间',
    is_deleted      int         default 0                            null comment '是否删除'
);

create table enyan_daily_words
(
    data_id      bigint auto_increment
        primary key,
    data_content varchar(100) default ''                           null comment '金句内容',
    book_id      bigint       default 0                            null comment '书籍ID',
    book_title   varchar(50)  default ''                           null comment '书籍名称',
    book_author  varchar(50)  default ''                           null comment '书籍作者',
    data_img_url varchar(100) default ''                           null comment '图片的url',
    like_count   int          default 0                            null comment '点赞量',
    data_at      int          default 0                            null comment '数据时间 yyyyMMdd',
    create_at    datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
);

create table enyan_discount
(
    discount_id            bigint auto_increment
        primary key,
    discount_title         varchar(40) default ''           null comment '折扣名称',
    discount_type          tinyint     default 0            null comment '0:累计折扣 n件n折;1:满300减30;2:单独折扣',
    book_count             int         default 0            null comment '累计书籍数',
    cumulate_package       int         default 0            null comment '累计件数',
    cumulate_discount      int         default 0            null comment '折扣满，减',
    cumulate_package_muti  int         default 0            null comment '累计件数2',
    cumulate_discount_muti int         default 0            null comment '折扣满，减2',
    full_base              int         default 0            null comment '折扣满',
    full_minus             int         default 0            null comment '折扣满，减',
    discount_single_value  int         default 100          null comment '单独折扣值',
    is_valid               tinyint     default 0            null comment '是否有效',
    is_show                tinyint     default 0            null comment '是否书封展示',
    start_time             date        default '1000-01-01' null comment '开始日期',
    end_time               date        default '1000-01-01' null comment '结束日期'
);

create table enyan_feedback
(
    data_id        bigint auto_increment
        primary key,
    type           int          default 0                            null comment '数据类型：-1:用户没有选择；0:功能问题；1:内容相关；2:需求建议；3:账户相关；4:其他；',
    feed_email     varchar(96)  default ''                           null comment '用户email',
    device_from    varchar(10)  default ''                           null comment '设备类型',
    device_version varchar(20)  default ''                           null comment '设备版本',
    content        varchar(510) default ''                           null comment '内容',
    done_type      int          default 0                            null comment '操作类型',
    create_at      datetime(6)  default '2022-01-01 00:00:00.000000' null comment '创建时间',
    is_deleted     int          default 0                            null comment '是否已经删除'
);

create table enyan_img
(
    img_id          bigint auto_increment
        primary key,
    img_description varchar(40) default '' null comment '展示值',
    img_name        varchar(40) default '' null comment '图片名',
    image_type      int         default 0  null comment '0：商品图；1：图文详情页;2：开屏广告；3：灵修版块；4：首页banner；5：epub文件',
    book_id         bigint                 null
);

create table enyan_logs
(
    class       varchar(32) charset utf8mb3  null,
    method      varchar(32) charset utf8mb3  null,
    create_time datetime                     null,
    log_level   varchar(11) charset utf8mb3  null,
    log_line    varchar(255) charset utf8mb3 null,
    msg         varchar(255) charset utf8mb3 null
);

create table enyan_order
(
    order_id        bigint auto_increment
        primary key,
    order_num       varchar(40)    default ''                           null comment '订单号',
    order_book_hash varchar(20)    default ''                           null comment '订单bookId hash',
    user_id         bigint         default 0                            null comment '读者ID',
    user_email      varchar(96)    default ''                           null comment '用户email',
    order_title     varchar(2000)  default ''                           null comment '订单title',
    purchased_at    datetime(6)    default '1000-01-01 00:00:00.000000' null comment '购买时间',
    expired_at      datetime(6)    default '1000-01-01 00:00:00.000000' null comment '购买时间',
    order_discount  decimal(20, 2) default 0.00                         null comment '享受优惠',
    order_total     decimal(20, 2) default 0.00                         null comment '订单总价',
    order_currency  tinyint        default 0                            null comment '订单币种，0：人民币；1：美元',
    is_valid        tinyint        default 1                            null comment '是否有效 0：无效；1：有效',
    is_paid         tinyint        default 0                            null comment '是否支付',
    pay_info        varchar(2000)  default ''                           null comment '支付情况',
    order_type      int            default 0                            null comment '订单类型，0：电子书；1：购买兑换码；2：兑换码兑换；3：书籍直接购买；4:电子书套装',
    is_counted      tinyint        default 0                            null comment '是否已统计',
    order_from      int            default 0                            null comment '订单来源，0：网站；1：App',
    order_detail    longtext                                            null comment '订单明细',
    is_deleted      int            default 0                            null comment '是否已经删除'
);

create table enyan_order_detail
(
    order_detail_id bigint auto_increment
        primary key,
    order_num       varchar(40)    default ''                           null comment '订单号',
    user_id         bigint         default 0                            null comment '读者ID',
    user_email      varchar(96)    default ''                           null comment '用户email',
    book_id         bigint         default 0                            null comment 'book ID',
    book_title      varchar(150)   default ''                           null comment '书名',
    book_pub_code   varchar(20)    default ''                           null comment '出版编码',
    book_esin       varchar(20)    default ''                           null comment '图书ESIN',
    publisher_id    bigint         default 0                            null comment '版权商ID',
    rate_value      varchar(20)    default ''                           null comment '汇率值',
    price_fixed     decimal(20, 2) default 0.00                         null comment '定价',
    price_selling   decimal(20, 2) default 0.00                         null comment '售价',
    quantity        int            default 1                            null comment '数量',
    vendor_percent  int            default 0                            null comment 'Vendor比例（版税税率）new',
    income_vendor   decimal(20, 2) default 0.00                         null comment 'Vendor收益（版税金额）',
    income_plat     decimal(20, 2) default 0.00                         null comment '平台收益',
    income_total    decimal(20, 2) default 0.00                         null comment '总收益（销售额）',
    income_real     decimal(20, 2) default 0.00                         null comment '真实本币（人民币或美元）总收益',
    pay_fee         decimal(20, 2) default 0.00                         null comment '支付手续费new',
    net_sales       decimal(20, 2) default 0.00                         null comment '净收益new',
    order_currency  tinyint        default 0                            null comment '订单币种，0：人民币；1：美元;2:港币',
    purchased_at    datetime(6)    default '1000-01-01 00:00:00.000000' null comment '购买时间',
    purchased_day   int            default 0                            null comment '购买时间Int',
    order_type      int            default 0                            null comment '订单类型，0：电子书；1：购买兑换码；2：兑换码兑换；3：书籍直接购买；4:电子书套装',
    pay_type        int            default 0                            null comment '支付类型，1：alipay；2：信用卡',
    pay_country     varchar(10)    default ''                           null comment '付费的国家',
    is_counted      tinyint        default 0                            null comment '-1:未结算；:0：结算中；1：已结算；-2：已取消',
    order_from      int            default 0                            null comment '订单来源，0：网站；1：App',
    sales_model     int            default 0                            null comment '0:正式销售；1：预售',
    drmInfo         varchar(500)   default ''                           null comment 'DRM信息',
    is_deleted      int            default 0                            null comment '是否已经删除'
);

create table enyan_pay_rate
(
    pay_rate_id bigint auto_increment
        primary key,
    rate_date   int         default 0  null comment '汇率时间yyyyMMdd',
    rate_time   int         default 0  null comment 'HHmmss',
    rate_type   int         default 1  null comment 'HKD:0 USD:1',
    rate_value  varchar(20) default '' null comment '汇率值',
    constraint enyan_pay_rate_date_time_type_uindex
        unique (rate_date, rate_time, rate_type)
);

create table enyan_plan
(
    plan_id          bigint auto_increment
        primary key,
    user_email       varchar(50)  default '' null comment '用户email',
    book_id          bigint       default 0  null comment 'book ID',
    name             varchar(150) default '' null comment '灵修书籍名',
    start_from       bigint       default 0  null comment '开始时间',
    has_buy          int          default 0  null comment '是否已购(0 未购;1 已购)',
    finished_bit_set blob                    null comment '完成的计划',
    is_deleted       int          default 0  null comment '是否已经删除',
    update_time      bigint       default 0  null comment '同步时间',
    constraint idx_plan_email_bookId_uindex
        unique (user_email, book_id)
);

create table enyan_plan_note
(
    plan_note_id bigint auto_increment
        primary key,
    user_email   varchar(50)   default '' null comment '用户email',
    book_id      bigint        default 0  null comment 'book ID',
    day          int           default 0  null comment '天数',
    day_name     varchar(150)  default '' null comment '当天章节',
    note         varchar(2100) default '' null comment '灵修笔记内容',
    is_deleted   int           default 0  null comment '是否已经删除',
    create_time  bigint        default 0  null comment '创建时间',
    update_time  bigint        default 0  null comment '同步时间',
    constraint idx_note_email_bookId_day_uindex
        unique (user_email, book_id, day)
);

create table enyan_publisher
(
    publisher_id      bigint auto_increment
        primary key,
    publisher_name    varchar(50)    default ''           null comment '出版商名称',
    publisher_name_tc varchar(50)    default ''           null comment '出版商名称繁体',
    publisher_name_en varchar(50)    default ''           null comment '出版商名称英文',
    publisher_avatar  varchar(200)   default ''           null comment '出版商标识',
    start_time        date           default '1000-01-01' null comment '结算开始日期',
    vendor_percent    int            default 0            null comment 'Vendor比例',
    bank_national     int            default 0            null comment '国家(0 中国；1 台湾;2 香港；3 新加坡;4 美国;5 其他地区)',
    misc_config       longtext                            null comment '其他配置项',
    bank_name         varchar(50)    default ''           null comment '开户行',
    bank_address      varchar(50)    default ''           null comment '开户行地址',
    bank_code         varchar(50)    default ''           null comment '银行代码',
    bank_title        varchar(50)    default ''           null comment '账户名称',
    bank_num          varchar(50)    default ''           null comment '开户行账号',
    fund_total        decimal(20, 2) default 0.00         null comment '总销售收入(CNY)',
    fund_withdrawn    decimal(20, 2) default 0.00         null comment '已清算金额(CNY)'
);

create table enyan_reader_highlights
(
    id             varchar(100)              not null
        primary key,
    highlight_id   varchar(100)  default ''  null comment '划线ID',
    user_id        bigint        default 0   null comment '读者ID',
    user_email     varchar(50)   default ''  null comment '用户email',
    publication_id varchar(50)   default ''  null comment '用户email',
    book_id        bigint        default 0   null comment 'book ID',
    resource_index int           default 0   null comment '内容序号',
    resource_href  varchar(50)   default ''  null comment '文件名称',
    resource_type  varchar(40)   default ''  null comment '类型',
    resource_title varchar(100)  default ''  null comment '章节名称',
    location       varchar(500)  default ''  null comment 'CFI位置',
    locator_text   varchar(250)  default ''  null comment '内容',
    color          int           default 0   null comment '划线颜色',
    annotation     varchar(2000) default '0' null comment '备注',
    creation_date  bigint        default 0   null comment '创建时间',
    is_deleted     int           default 0   null comment '是否已经删除',
    update_time    bigint        default 0   null comment '同步时间'
);

create table enyan_reading
(
    data_id        bigint auto_increment
        primary key,
    data_name      varchar(40)  default ''                           null comment '活动名称',
    data_img_url   varchar(100) default ''                           null comment '图片的url',
    data_to_url    varchar(100) default ''                           null comment '活动转向的url',
    data_buy_url   varchar(100) default ''                           null comment '活动购买转向的url',
    data_read_show int          default 0                            null comment '是否读书会显示：0：不显示；1：显示',
    data_priority  int          default 0                            null comment '推荐次序',
    data_status    int          default 0                            null comment '0：结束；1：已经开始；2：即将开始',
    is_deleted     int          default 0                            null comment '是否已经删除',
    begin_at       datetime(6)  default '2020-01-01 00:00:00.000000' null comment '开始时间',
    end_at         datetime(6)  default '2020-01-01 00:00:00.000000' null comment '结束时间',
    create_at      datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
);

create table enyan_redeem_code
(
    redeem_code_id bigint auto_increment
        primary key,
    user_email     varchar(50) default ''  null comment '用户email',
    code           varchar(60) default '0' null comment '兑换码code',
    type           int         default 0   null comment '类型 (指定兑换，任意兑换)',
    status         int         default 0   null comment '状态（0未使用，1已使用）',
    note           text                    null comment '备注',
    end_at         datetime                null comment '失效时间',
    create_time    varchar(60) default '0' null comment '创建时间20191102175000'
);

create index idx_redeem_code
    on enyan_redeem_code (code);

create table enyan_refund
(
    refund_id             bigint auto_increment
        primary key,
    order_num             varchar(40)                  default ''                           null comment '订单号',
    user_id               bigint                       default 0                            null comment '读者ID',
    user_email            varchar(96)                  default ''                           null comment '用户email',
    book_id               bigint                       default 0                            null comment 'book ID',
    book_title            varchar(150)                 default ''                           null comment '书名',
    publisher_id          bigint                       default 0                            null comment '版权商ID',
    publisher_name        varchar(50)                  default ''                           null comment '出版方名称',
    order_type            int                          default 0                            null comment '订单类型，0：电子书；1：购买兑换码；2：兑换码兑换；3：书籍直接购买订单-无用 ；4:电子书套装；5:先租后买转换',
    purchased_at          datetime(6)                  default '1000-01-01 00:00:00.000000' null comment '购买时间',
    sales_volume_decrease int                          default 0                            null comment '销量变动',
    income_total_decrease decimal(20, 2)               default 0.00                         null comment '总收益（销售额）变动',
    reason_type           int                          default 0                            null comment '退款类型',
    reason_content        varchar(100) charset utf8mb3 default ''                           null comment '退款原因及处理结果',
    create_time           datetime(6)                  default '1000-01-01 00:00:00.000000' null comment '创建时间',
    is_deleted            int                          default 0                            null comment '是否已经删除'
);

create table enyan_rent
(
    rent_id      bigint auto_increment
        primary key,
    order_num    varchar(40)    default ''                           null comment '订单号',
    user_email   varchar(96)    default ''                           null comment '用户email',
    publisher_id bigint         default 0                            null comment '版权商ID',
    rent_type    int            default 0                            null comment '套餐类型，1：新旧约；2：旧约；3：新约；',
    rent_lang    int            default 0                            null comment '语言类型，1：简体；2：繁体；3：英语；',
    is_valid     int            default 1                            null comment '是否有效（只依赖于过期时间+宽限期，用于是否阅读） 0：无效；1：有效',
    rent_status  int            default 0                            null comment '订阅状态，-2：失效；-1：退订；0：未启用（待支付）；1：已经订阅；',
    is_auto      int            default 0                            null comment '续订状态，0：手动订阅；1：自动订阅-仅支持信用卡；',
    is_paid      int            default 0                            null comment '是否支付',
    leave_buy    int            default 0                            null comment '退订时是否购买',
    to_order_num varchar(40)    default ''                           null comment '退订时购买的订单号',
    base_license varchar(40)    default ''                           null comment '基础的license',
    rent_price   decimal(8, 2)  default 0.00                         null comment '价格',
    total_fee    decimal(10, 2) default 0.00                         null comment '积累月费',
    total_months int            default 1                            null comment '已经订阅总月份数',
    order_from   int            default 0                            null comment '订单来源，0：网站；1：App',
    rent_detail  longtext                                            null comment '订单明细，存储每本书的license',
    begin_at     datetime(6)    default '2022-01-01 00:00:00.000000' null comment '开始时间',
    leave_at     datetime(6)    default '2022-01-01 00:00:00.000000' null comment '退订时间',
    leave_buy_at datetime(6)    default '2022-01-01 00:00:00.000000' null comment '退订购买时间',
    expired_at   datetime(6)    default '2022-01-01 00:00:00.000000' null comment '过期时间',
    create_at    datetime(6)    default '2022-01-01 00:00:00.000000' null comment '创建时间',
    is_deleted   int            default 0                            null comment '是否已经删除'
);

create table enyan_rent_detail
(
    detail_id      bigint auto_increment
        primary key,
    order_num      varchar(40)    default ''                           null comment '订单号',
    trade_no       varchar(40)    default ''                           null comment '支付交易号',
    user_email     varchar(96)    default ''                           null comment '用户email',
    rent_type      int            default 0                            null comment '套餐类型，1：新旧约；2：旧约；3：新约；',
    rent_lang      int            default 0                            null comment '语言类型，1：简体；2：繁体；3：英语；',
    is_auto        int            default 0                            null comment '续订状态，0：手动订阅；1：自动订阅-仅支持信用卡；',
    publisher_id   bigint         default 0                            null comment '版权商ID',
    rent_months    int            default 1                            null comment '本次订阅月份数',
    vendor_percent int            default 0                            null comment 'Vendor比例（版税税率）new',
    income_vendor  decimal(10, 2) default 0.00                         null comment 'Vendor收益（版税金额）',
    income_plat    decimal(10, 2) default 0.00                         null comment '平台收益',
    income_total   decimal(10, 2) default 0.00                         null comment '总收益（销售额）',
    income_real    decimal(10, 2) default 0.00                         null comment '真实本币（人民币或美元）总收益',
    pay_fee        decimal(10, 2) default 0.00                         null comment '支付手续费new',
    net_sales      decimal(10, 2) default 0.00                         null comment '净收益new',
    order_currency int            default 0                            null comment '订单币种，0：人民币；1：美元;2:港币',
    pay_type       int            default 0                            null comment '支付类型，1：alipay；2：信用卡',
    pay_country    varchar(10)    default ''                           null comment '付费的国家',
    order_from     int            default 0                            null comment '订单来源，0：网站；1：App',
    purchased_at   datetime(6)    default '2022-01-01 00:00:00.000000' null comment '购买时间',
    from_at        datetime(6)    default '2022-01-01 00:00:00.000000' null comment '上次开始时间',
    expired_at     datetime(6)    default '2022-01-01 00:00:00.000000' null comment '过期时间',
    create_at      datetime(6)    default '2022-01-01 00:00:00.000000' null comment '创建时间',
    is_deleted     int            default 0                            null comment '是否已经删除'
);

create table enyan_spirit
(
    spirit_id          bigint auto_increment
        primary key,
    book_id            bigint       default 0  null comment 'book ID',
    name               varchar(150) default '' null comment '灵修书籍名',
    author             varchar(50)  default '' null comment '作者',
    slogan             varchar(50)  default '' null comment 'slogan',
    book_version       varchar(50)  default '' null comment '书籍版本信息',
    author_description varchar(500) default '' null comment '作者介绍',
    book_description   varchar(500) default '' null comment '书籍介绍',
    language_type      int          default 1  null comment '书籍语言(1 简体;2 繁体)',
    recommended_order  int          default 0  null comment '推荐顺序',
    shelf_status       tinyint      default 0  null comment '销售状态 (0 已下架;1 已上架;2 待重审)',
    days               int          default 0  null comment '天数',
    data_bits_bit_set  blob                    null comment '没有数据的天数',
    book_img_url       varchar(100) default '' null comment '我的灵修-封面图',
    info_img_url       varchar(100) default '' null comment '全部灵修-封面图',
    to_buy_img_url     varchar(100) default '' null comment '去购买的图片',
    file_name          varchar(50)  default '' null comment '文件名称',
    copyright          varchar(500) default '' null comment '版权'
);

create table enyan_subscription
(
    data_id   bigint auto_increment
        primary key,
    email     varchar(100) default ''                           null comment 'email',
    create_at datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间',
    constraint idx_subscription
        unique (email)
);

create table enyan_user_bookmarks
(
    bookmark_id  varchar(100)            not null
        primary key,
    user_id      bigint       default 0  null comment '读者ID',
    user_email   varchar(96)  default '' null comment '用户email',
    book_id      bigint       default 0  null comment 'book ID',
    create_time  bigint       default 0  null comment '创建时间',
    chapter_name varchar(100) default '' null comment '章节标题',
    page_chapter int          default 0  null comment '当前章节',
    current_page int          default 0  null comment '当前所在章节内的页面',
    total_page   int          default 0  null comment '本章节总页面数',
    page_process int          default 0  null comment '当前进度*1000',
    is_deleted   int          default 0  null comment '是否已经删除',
    update_time  bigint       default 0  null comment '同步时间'
);

create table enyan_user_highlights
(
    highlight_id       varchar(255)             not null
        primary key,
    user_id            bigint        default 0  null comment '读者ID',
    user_email         varchar(96)   default '' null comment '用户email',
    book_id            bigint        default 0  null comment 'book ID',
    content            varchar(250)  default '' null comment '划线内容',
    create_time        bigint        default 0  null comment '创建时间',
    rangy              varchar(100)  default '' null comment 'rangy',
    note_for_highlight varchar(2000) default '' null comment '备注',
    chapter_name       varchar(100)  default '' null comment '章节标题',
    page_chapter       int           default 0  null comment '当前章节',
    current_page       int           default 0  null comment '当前所在章节内的页面',
    total_page         int           default 0  null comment '本章节总页面数',
    page_process       int           default 0  null comment '当前进度*1000',
    is_deleted         int           default 0  null comment '是否已经删除',
    update_time        bigint        default 0  null comment '同步时间'
);

create table enyan_wish
(
    wish_id    varchar(40)                                      not null
        primary key,
    user_email varchar(96) default ''                           null comment '用户email',
    book_id    bigint      default 0                            null comment 'book ID',
    wished_at  datetime(6) default '1000-01-01 00:00:00.000000' null comment 'wish时间',
    constraint enyan_wish_user_email_book_id_uindex
        unique (user_email, book_id)
);

create table event
(
    id                int auto_increment
        primary key,
    device_name       varchar(255) null,
    timestamp         datetime     not null,
    type              int          not null,
    device_id         varchar(255) null,
    license_status_fk int          not null
);

create table license
(
    id           varchar(255)  not null
        primary key,
    user_id      varchar(255)  not null,
    provider     varchar(255)  not null,
    issued       datetime      not null,
    updated      datetime      null,
    rights_print int           null,
    rights_copy  int           null,
    rights_start datetime      null,
    rights_end   datetime      null,
    content_fk   varchar(255)  not null,
    lsd_status   int default 0 null
);

create table license_status
(
    id                   int auto_increment
        primary key,
    status               int          not null,
    license_updated      datetime     not null,
    status_updated       datetime     not null,
    device_count         int          null,
    potential_rights_end datetime     null,
    license_ref          varchar(255) not null,
    rights_end           datetime     null
);

create index license_ref_index
    on license_status (license_ref);

create table license_view
(
    id           int auto_increment
        primary key,
    uuid         varchar(255) not null,
    device_count int          not null,
    status       varchar(255) not null,
    message      varchar(255) not null
);

create table oc_customer
(
    customer_id       int auto_increment
        primary key,
    store_id          int         default 0                     not null,
    firstname         varchar(32)                               not null,
    lastname          varchar(32)                               not null,
    sex               int         default 0                     null comment '性别',
    birthday          varchar(10)                               null comment 'yyyy-MM-dd',
    email             varchar(96)                               not null,
    telephone         varchar(32)                               not null,
    fax               varchar(32)                               not null,
    password          varchar(40)                               not null,
    salt              varchar(9)                                not null,
    cart              text                                      null,
    wishlist          text                                      null,
    newsletter        tinyint(1)  default 0                     not null,
    address_id        int         default 0                     not null,
    customer_group_id int                                       not null,
    ip                varchar(40) default '0'                   not null,
    status            tinyint(1)                                not null,
    approved          tinyint(1)                                not null,
    verified          tinyint                                   null,
    token             varchar(255)                              not null,
    date_added        datetime    default '1000-01-01 00:00:00' not null
);

create table oc_user
(
    user_id       int auto_increment
        primary key,
    user_group_id int                                              not null,
    username      varchar(20)                                      not null,
    password      varchar(40)                                      not null,
    salt          varchar(9)                                       not null,
    firstname     varchar(32)                                      not null,
    lastname      varchar(32)                                      not null,
    email         varchar(96)                                      not null,
    code          varchar(40)                                      not null,
    ip            varchar(40)                                      not null,
    status        tinyint(1)                                       not null,
    date_added    datetime(6) default '1000-01-01 00:00:00.000000' null
);

create table persistent_logins
(
    username  varchar(64)                         not null,
    series    varchar(64)                         not null
        primary key,
    token     varchar(64)                         not null,
    last_used timestamp default CURRENT_TIMESTAMP not null
);

create table pod_episode
(
    episode_id                  bigint auto_increment comment '单集ID'
        primary key,
    podcast_id                  bigint       default 0                            null comment '所属栏目ID (定义单集归属)',
    topic_id                    bigint       default 0                            null comment '所属专题ID',
    title                       varchar(255) default ''                           null comment '单集标题',
    description                 longtext                                          null comment '单集描述/文稿',
    audio_file_url              varchar(255) default ''                           null comment '音频文件URL',
    duration_seconds            int          default 0                            null comment '时长 (秒)',
    is_published                tinyint      default 0                            null comment '是否已发布',
    publication_date            timestamp                                         null comment '发布日期，转发布时自动设置，一般以此日期给用户作为识别的最新时间',
    episode_number              int          default 0                            null comment '集数, 相对整个栏目。用来栏目里展示排序顺序',
    episode_count               int          default 0                            null comment '总集数（进行冗余）',
    listen_count                int          default 0                            null comment '播放次数。有效触发播放按钮的次数，即触发播放后持续超过2秒。仅放在单集上累计',
    like_count                  int          default 0                            null comment '点赞次数。匿名点赞，可以不限用户，不限次数点击、累加',
    is_deleted                  tinyint      default 0                            null comment '是否已经删除',
    created_at                  datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间',
    cover_image_url             varchar(255)                                      null comment '封面图片URL，与所属播客的cover_image_url保持一致',
    cumulative_playback_seconds int          default 0                            null comment '累计播放时长 (秒) 。pod_user_episode_interaction表会记录用户播放开始时间，然后前端触发结束播放请求：有三种：暂停，播完，杀掉app后重新启动后。这三种情况要向服务更新播放时长信息。仅放在单集上累计'
)
    comment '栏目单集' collate = utf8mb4_unicode_ci;

create table pod_favorite
(
    favorite_id  bigint auto_increment comment '用对栏目的收藏记录ID'
        primary key,
    user_id      bigint      default 0                 null comment '读者ID',
    user_email   varchar(50) default ''                null comment '用户email',
    podcast_id   bigint      default 0                 null comment '所属栏目ID',
    favorited_at timestamp   default CURRENT_TIMESTAMP null comment '收藏时间'
)
    comment '用户播客（栏目）收藏' collate = utf8mb4_unicode_ci;

create table pod_podcast
(
    podcast_id       bigint auto_increment comment '播客(栏目)ID'
        primary key,
    title            varchar(255) default ''                           null comment '栏目标题',
    author_name      varchar(255)                                      null comment '主持人/作者名',
    description      longtext                                          null comment '栏目描述',
    cover_image_url  varchar(255)                                      null comment '封面图片URL',
    display_order    int          default 0                            null comment '首页展示顺序',
    episode_count    int          default 0                            null comment '总集数',
    is_published     tinyint      default 0                            null comment '是否已发布',
    publication_date timestamp                                         null comment '发布日期',
    is_deleted       tinyint      default 0                            null comment '是否已经删除',
    created_at       datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
)
    comment '播客(栏目)信息' collate = utf8mb4_unicode_ci;

create table pod_topic
(
    topic_id      bigint auto_increment comment '专题ID'
        primary key,
    podcast_id    bigint       default 0                            null comment '所属栏目ID',
    title         varchar(255) default ''                           null comment '专题标题',
    description   longtext                                          null comment '专题描述 (可选)',
    display_order int          default 0                            null comment '显示序号，专题在播客内的展示顺序',
    episode_count int          default 0                            null comment '总集数（进行冗余）',
    is_deleted    tinyint      default 0                            null comment '是否已经删除',
    created_at    datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
)
    comment '栏目内专题' collate = utf8mb4_unicode_ci;

create table pod_user_episode_interaction
(
    interaction_id              bigint auto_increment comment '互动记录ID'
        primary key,
    user_id                     bigint      default 0                            null comment '读者ID',
    user_email                  varchar(50) default ''                           null comment '用户email',
    episode_id                  bigint      default 0                            null comment '单集ID',
    is_liked                    tinyint     default 0                            null comment '是否点赞(当前不用了，需要变为匿名点赞)',
    playback_progress_seconds   int         default 0                            null comment '播放进度 (秒) (当前不用了，只在前端记录播放进度)',
    cumulative_playback_seconds int         default 0                            null comment '累计播放时长 (秒) （这里不用了，数据要累积在单集上面）',
    last_played_at              timestamp                                        null comment '最近播放时间（日期）。用户最后一次有效播放动作的时间戳',
    is_completed                tinyint     default 0                            null comment '是否已播完。如果用户播放了最后一秒，即算是播放完成。播完后再次播放也还算是播完，这个字段再不变。',
    downloaded_at               timestamp                                        null comment '标记为下载完成的时间（当前不使用）',
    created_at                  datetime(6) default '2020-01-01 00:00:00.000000' null comment '创建时间'
)
    comment '用户单集互动记录' collate = utf8mb4_unicode_ci;

create table publication
(
    id     int auto_increment
        primary key,
    uuid   varchar(255) not null,
    title  varchar(255) not null,
    status varchar(255) not null
);

create index uuid_index
    on publication (uuid);

create table purchase
(
    id               int auto_increment
        primary key,
    uuid             varchar(255) not null,
    publication_id   int          not null,
    user_id          int          not null,
    license_uuid     varchar(255) null,
    type             varchar(32)  not null,
    transaction_date datetime     null,
    start_date       datetime     null,
    end_date         datetime     null,
    status           varchar(255) not null
);

create index idx_purchase
    on purchase (license_uuid);

create table stu_enroll
(
    data_id       bigint auto_increment
        primary key,
    stu_id        int unsigned  not null,
    last_name     varchar(20)   not null,
    first_name    varchar(40)   not null,
    email         varchar(254)  not null,
    enroll_at     datetime      null,
    enroll_status int           not null,
    enroll_code   varchar(20)   not null,
    checkin       varchar(1000) not null,
    terms         int unsigned  null
);

create table stu_info
(
    data_id         bigint auto_increment
        primary key,
    stu_id          int unsigned not null,
    last_name       varchar(20)  not null,
    first_name      varchar(40)  not null,
    email           varchar(254) not null,
    password        varchar(40)  not null,
    enroll_code     varchar(20)  not null,
    stu_class       varchar(20)  not null,
    stu_housing     varchar(20)  not null,
    stu_team_name   varchar(20)  not null,
    team_role       int          not null,
    discussion_room varchar(20)  not null,
    terms           int          null
);

create table user
(
    id       int auto_increment
        primary key,
    uuid     varchar(255) not null,
    name     varchar(64)  not null,
    email    varchar(64)  not null,
    password varchar(64)  not null,
    hint     varchar(64)  not null,
    constraint user_email_uindex
        unique (email)
);

create index idx_user_email
    on user (email);

create table user_info
(
    username  varchar(96) not null
        primary key,
    info_text longtext    null comment '用户明细'
);

