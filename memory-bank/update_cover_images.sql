-- 更新pod_podcast表的封面图片
-- 为每个播客随机分配1-4的图片组
UPDATE pod_podcast 
SET 
    cover_image_url = CONCAT('https://dl.edhub.cc/root/images/bk/bk', FLOOR(1 + RAND() * 4), '_l.jpg'),
    cover_image_url2 = CONCAT('https://dl.edhub.cc/root/images/bk/bk', FLOOR(1 + RAND() * 4), '_s.jpg')
WHERE is_deleted = 0;

-- 更新pod_episode表的封面图片
-- 确保每个单集的封面图片与其所属播客的图片组一致
UPDATE pod_episode e
JOIN pod_podcast p ON e.podcast_id = p.podcast_id
SET 
    e.cover_image_url = p.cover_image_url,
    e.cover_image_url2 = p.cover_image_url2
WHERE e.is_deleted = 0 AND p.is_deleted = 0;








-- 更新专题中的单集数量
UPDATE pod_topic t
SET episode_count = (
    SELECT COUNT(*) 
    FROM pod_episode e 
    WHERE e.topic_id = t.topic_id
);


-- 导出书籍清单
SELECT book_id, book_title, author,publisher_name, price,book_abstract,book_description, recommended_caption,is_in_cn,can_tts,book_esin,book_cover FROM enyan_book WHERE shelf_status = 1 LIMIT 20