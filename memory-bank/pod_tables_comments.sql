-- pod表注释迁移脚本
-- 生成日期: 2025-06-06

-- 表注释
ALTER TABLE pod_podcast COMMENT = '播客(栏目)信息';
ALTER TABLE pod_episode COMMENT = '栏目单集';
ALTER TABLE pod_favorite COMMENT = '用户播客（栏目）收藏';
ALTER TABLE pod_topic COMMENT = '栏目内专题';
ALTER TABLE pod_user_episode_interaction COMMENT = '用户单集互动记录';

-- pod_podcast表列注释
ALTER TABLE pod_podcast MODIFY COLUMN podcast_id BIGINT COMMENT '播客(栏目)ID';
ALTER TABLE pod_podcast MODIFY COLUMN title VARCHAR(255) COMMENT '栏目标题';
ALTER TABLE pod_podcast MODIFY COLUMN author_name VARCHAR(255) COMMENT '主持人/作者名';
ALTER TABLE pod_podcast MODIFY COLUMN description LONGTEXT COMMENT '栏目描述';
ALTER TABLE pod_podcast MODIFY COLUMN cover_image_url VARCHAR(255) COMMENT '封面图片URL';
ALTER TABLE pod_podcast MODIFY COLUMN cover_image_url2 VARCHAR(255) COMMENT '详情页面长条图片URL';
ALTER TABLE pod_podcast MODIFY COLUMN display_order INT COMMENT '首页展示顺序';
ALTER TABLE pod_podcast MODIFY COLUMN episode_count INT COMMENT '总集数';
ALTER TABLE pod_podcast MODIFY COLUMN is_published TINYINT COMMENT '是否已发布';
ALTER TABLE pod_podcast MODIFY COLUMN publication_date TIMESTAMP COMMENT '发布日期';
ALTER TABLE pod_podcast MODIFY COLUMN is_deleted TINYINT COMMENT '是否已经删除';
ALTER TABLE pod_podcast MODIFY COLUMN created_at DATETIME(6) COMMENT '创建时间';

-- pod_episode表列注释
ALTER TABLE pod_episode MODIFY COLUMN episode_id BIGINT COMMENT '单集ID';
ALTER TABLE pod_episode MODIFY COLUMN podcast_id BIGINT COMMENT '所属栏目ID (定义单集归属)';
ALTER TABLE pod_episode MODIFY COLUMN topic_id BIGINT COMMENT '所属专题ID';
ALTER TABLE pod_episode MODIFY COLUMN title VARCHAR(255) COMMENT '单集标题';
ALTER TABLE pod_episode MODIFY COLUMN description LONGTEXT COMMENT '单集描述/文稿';
ALTER TABLE pod_episode MODIFY COLUMN audio_file_url VARCHAR(255) COMMENT '音频文件URL';
ALTER TABLE pod_episode MODIFY COLUMN duration_seconds INT COMMENT '时长 (秒)';
ALTER TABLE pod_episode MODIFY COLUMN is_published TINYINT COMMENT '是否已发布';
ALTER TABLE pod_episode MODIFY COLUMN publication_date TIMESTAMP COMMENT '发布日期，转发布时自动设置，一般以此日期给用户作为识别的最新时间';
ALTER TABLE pod_episode MODIFY COLUMN episode_number INT COMMENT '集数, 相对整个栏目。用来栏目里展示排序顺序';
ALTER TABLE pod_episode MODIFY COLUMN episode_count INT COMMENT '总集数（进行冗余）';
ALTER TABLE pod_episode MODIFY COLUMN listen_count INT COMMENT '播放次数。有效触发播放按钮的次数，即触发播放后持续超过2秒。仅放在单集上累计';
ALTER TABLE pod_episode MODIFY COLUMN like_count INT COMMENT '点赞次数。匿名点赞，可以不限用户，不限次数点击、累加';
ALTER TABLE pod_episode MODIFY COLUMN is_deleted TINYINT COMMENT '是否已经删除';
ALTER TABLE pod_episode MODIFY COLUMN created_at DATETIME(6) COMMENT '创建时间';
ALTER TABLE pod_episode MODIFY COLUMN cover_image_url VARCHAR(255) COMMENT '封面图片URL，与所属播客的cover_image_url保持一致';
ALTER TABLE pod_episode MODIFY COLUMN cover_image_url2 VARCHAR(255) COMMENT '详情页面长条图片URL，与所属播客的cover_image_url2保持一致';
ALTER TABLE pod_episode MODIFY COLUMN cumulative_playback_seconds INT COMMENT '累计播放时长 (秒) 。pod_user_episode_interaction表会记录用户播放开始时间，然后前端触发结束播放请求：有三种：暂停，播完，杀掉app后重新启动后。这三种情况要向服务更新播放时长信息。仅放在单集上累计';

-- pod_favorite表列注释
ALTER TABLE pod_favorite MODIFY COLUMN favorite_id BIGINT COMMENT '用对栏目的收藏记录ID';
ALTER TABLE pod_favorite MODIFY COLUMN user_id BIGINT COMMENT '读者ID';
ALTER TABLE pod_favorite MODIFY COLUMN user_email VARCHAR(50) COMMENT '用户email';
ALTER TABLE pod_favorite MODIFY COLUMN podcast_id BIGINT COMMENT '所属栏目ID';
ALTER TABLE pod_favorite MODIFY COLUMN favorited_at TIMESTAMP COMMENT '收藏时间';

-- pod_topic表列注释
ALTER TABLE pod_topic MODIFY COLUMN topic_id BIGINT COMMENT '专题ID';
ALTER TABLE pod_topic MODIFY COLUMN podcast_id BIGINT COMMENT '所属栏目ID';
ALTER TABLE pod_topic MODIFY COLUMN title VARCHAR(255) COMMENT '专题标题';
ALTER TABLE pod_topic MODIFY COLUMN description LONGTEXT COMMENT '专题描述 (可选)';
ALTER TABLE pod_topic MODIFY COLUMN display_order INT COMMENT '显示序号，专题在播客内的展示顺序';
ALTER TABLE pod_topic MODIFY COLUMN episode_count INT COMMENT '总集数（进行冗余）';
ALTER TABLE pod_topic MODIFY COLUMN is_deleted TINYINT COMMENT '是否已经删除';
ALTER TABLE pod_topic MODIFY COLUMN created_at DATETIME(6) COMMENT '创建时间';

-- pod_user_episode_interaction表列注释
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN interaction_id BIGINT COMMENT '互动记录ID';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN user_id BIGINT COMMENT '读者ID';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN user_email VARCHAR(50) COMMENT '用户email';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN episode_id BIGINT COMMENT '单集ID';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN is_liked TINYINT COMMENT '是否点赞(当前不用了，需要变为匿名点赞)';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN playback_progress_seconds INT COMMENT '播放进度 (秒) (当前不用了，只在前端记录播放进度)';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN cumulative_playback_seconds INT COMMENT '累计播放时长 (秒) （这里不用了，数据要累积在单集上面）';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN last_played_at TIMESTAMP COMMENT '最近播放时间（日期）。用户最后一次有效播放动作的时间戳';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN is_completed TINYINT COMMENT '是否已播完。如果用户播放了最后一秒，即算是播放完成。播完后再次播放也还算是播完，这个字段再不变。';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN downloaded_at TIMESTAMP COMMENT '标记为下载完成的时间（当前不使用）';
ALTER TABLE pod_user_episode_interaction MODIFY COLUMN created_at DATETIME(6) COMMENT '创建时间';
