# 项目简介（enyan-web & enyan-static）

本项目为数字内容电商平台，核心后端采用SpringMVC+MyBatis，前端为JSP/HTML/JS/CSS，配合静态资源站点（enyan-static）。支持PC和移动端多场景访问，聚焦数字内容售卖、租赁、支付、用户管理等功能。

## 目录结构（细致版）
```text
enyan-web
│
├── src/main/webapp
│   ├── WEB-INF/         # JSP模板、配置
│   ├── css/ fonts/ img/ # 样式与资源
│   ├── js/              # 脚本
│   ├── *.jsp            # 动态页面
│   └── statics/         # 静态资源
│
enyan-static
│
├── *.html               # 各类静态页面（含移动端、支付、阅读等）
├── css/ fonts/ img/ js/ # 静态资源

ebook-web 的平行目录中有如下依赖项目
../aaron-web-quick  spring和web包
../aaron-common  通用工具包
../aaron-exception  通用异常包
../aaron-crypto  通用加密包
../aaron-parent  maven牵头工程



```

## 主要包结构（业务分层）
- com.aaron.spring.controller：控制器层，负责处理前端请求，核心业务入口。其中v4 \com\aaron\spring\api\v4\controller 是当前主用版本
- com.aaron.spring.service：服务层，封装业务逻辑。
- com.aaron.spring.dao / com.aaron.spring.mapper：数据访问层，MyBatis相关。
- com.aaron.spring.model：数据模型、实体类。
- com.aaron.spring.common：通用工具、常量等。
- com.aaron.spring.interceptor：拦截器，AOP相关。
- com.aaron.spring.security：安全相关。
- com.aaron.spring.tags、com.aaron.spring.task：自定义标签、定时任务等。
- 其他包如 com.aaron.annotation、com.aaron.aspect、com.aaron.data、com.aaron.drm、com.aaron.excel、com.aaron.servlet 等为通用扩展功能。

## 主要控制器（部分示例及功能推测）
- AdminController：后台管理相关接口
- BannerController：Banner管理
- BlogController：博客管理
- BookController、BookListController、BookSetController：图书、书单、书集管理
- CategoryController：分类管理
- CheckinController：签到功能
- CommentController：评论相关
- ConfigController：系统配置
- CouponController、DiscountController、RedeemCodeController：优惠券、折扣、兑换码
- DailyWordsController：每日一句
- DataStatController、DataRentStatController：数据统计
- EditionGuardController、LcpController：数字版权管理相关
- EmailController：邮件服务
- ExceptionHandlerController：全局异常处理
- FeedbackController：用户反馈
- HighlightController：高亮笔记
- HomeController：首页/主入口
- ImgController：图片处理
- OrderController、RefundController、PaymentController：订单、退款、支付
- PersonalController、UserController、MUserController：用户相关
- PublisherController：出版方
- ReadingController：阅读相关
- RentController：租赁相关
- ShopController：商城主控制器（文件最大，业务最复杂）
- SpiritController：精神/灵感相关
- TestController：测试接口
- VendorController：供应商相关

## 包结构图（详细版）
```text
com.aaron.spring
│
├── controller
│   ├── AdminController           # 后台管理，用户设备管理，继承UserController
│   ├── BannerController          # Banner广告管理
│   ├── BaseController            # 控制器基础父类
│   ├── BlogController            # 博客内容管理
│   ├── BookController            # 图书详情、操作
│   ├── BookListController        # 书单管理
│   ├── BookSetController         # 书集管理
│   ├── CategoryController        # 分类管理
│   ├── CheckinController         # 签到功能
│   ├── CommentController         # 评论相关
│   ├── ConfigController          # 系统配置
│   ├── CouponController          # 优惠券
│   ├── DailyWordsController      # 每日一句
│   ├── DataRentStatController    # 租赁统计
│   ├── DataStatController        # 综合数据统计
│   ├── DiscountController        # 折扣相关
│   ├── EditionGuardController    # 数字版权管理
│   ├── EmailController           # 邮件服务
│   ├── ExceptionHandlerController# 全局异常处理
│   ├── FeedbackController        # 用户反馈
│   ├── HighlightController       # 阅读高亮笔记
│   ├── HomeController            # 首页/主入口
│   ├── ImgController             # 图片上传与处理
│   ├── LcpController             # 版权管理
│   ├── MUserController           # 移动端用户接口
│   ├── OrderController           # 订单管理
│   ├── PDFController             # PDF相关
│   ├── PaymentController         # 支付接口
│   ├── PersonalController        # 个人中心
│   ├── PublisherController       # 出版方管理
│   ├── ReadingController         # 阅读相关
│   ├── RedeemCodeController      # 兑换码
│   ├── RefundController          # 退款
│   ├── RentController            # 图书租赁
│   ├── ShopController            # 商城主控制器（最大、最复杂，涵盖购物、支付、活动、统计等）
│   ├── SpiritController          # 精神/灵感相关
│   ├── TestController            # 测试接口
│   ├── UserController            # 用户管理（登录、注册、信息等）
│   └── VendorController          # 供应商管理
│
├── service
│   ├── AuthUserService（认证用户相关服务）
│   ├── BaseDataService（基础数据服务）
│   ├── DataRentStatService（租赁数据统计服务）
│   ├── DataStatService（数据统计服务）
│   ├── EmailHistoryService（邮件历史服务）
│   ├── EnyanAcsmService（数字内容管理服务）
│   ├── EnyanBalanceHistoryService（余额历史服务）
│   ├── EnyanBalanceService（余额管理服务）
│   ├── EnyanBannerService（轮播图管理服务）
│   ├── EnyanBlogService（博客管理服务）
│   ├── EnyanBookBuyService（图书购买服务）
│   ├── EnyanBookListService（图书书单服务）
│   ├── EnyanBookService（图书管理服务）
│   ├── EnyanBookSetService（图书合集服务）
│   ├── EnyanCartService（购物车服务）
│   ├── EnyanCategoryService（分类管理服务）
│   ├── EnyanCommentService（评论管理服务）
│   ├── EnyanConfigService（系统配置服务）
│   ├── EnyanCouponService（优惠券服务）
│   ├── EnyanDailyWordsService（每日一句服务）
│   ├── EnyanDiscountService（折扣管理服务）
│   ├── EnyanFeedbackService（用户反馈服务）
│   ├── EnyanImgService（图片管理服务）
│   ├── EnyanOrderDetailService（订单明细服务）
│   ├── EnyanOrderService（订单管理服务）
│   ├── EnyanPayRateService（支付费率服务）
│   ├── EnyanPlanNoteService（计划笔记服务）
│   ├── EnyanPlanService（计划管理服务）
│   ├── EnyanPublisherService（出版社管理服务）
│   ├── EnyanReaderHighlightService（阅读高亮服务）
│   ├── EnyanReadingService（阅读记录服务）
│   ├── EnyanRedeemCodeService（兑换码服务）
│   ├── EnyanRefundService（退款服务）
│   ├── EnyanRentDetailService（租赁明细服务）
│   ├── EnyanRentService（租赁管理服务）
│   ├── EnyanSpiritService（灵感/语录服务）
│   ├── EnyanSubscriptionService（订阅服务）
│   ├── EnyanUserBookmarkService（用户书签服务）
│   ├── EnyanUserHighlightService（用户高亮服务）
│   ├── EnyanWishService（心愿单服务）
│   ├── GeoIPLocationService（IP地理位置服务）
│   ├── IService（通用服务接口）
│   ├── InitServiceConfig（服务初始化配置/注册服务）
│   ├── InitSystemConfig（系统初始化配置）
│   ├── LogService（日志服务）
│   ├── MUserServiceI（多用户服务接口）
│   ├── PublicationService（出版物服务）
│   ├── PurchaseService（购买服务）
│   ├── SendEmailService（邮件发送服务）
│   ├── StuEnrollService（学生报名服务）
│   ├── StuInfoService（学生信息服务）
│   └── ...（其他业务服务）
│
├── model
│   ├── AuthUser（认证用户实体）
│   ├── BalanceDetail（余额明细实体）
│   ├── BookSetInfo（书单信息实体）
│   ├── BookWebInfo（图书网页信息实体）
│   ├── CartDiscountInfo（购物车折扣信息实体）
│   ├── CartInfo（购物车信息实体）
│   ├── CartInfoGerenal（通用购物车实体）
│   ├── CartProduct（购物车商品实体）
│   ├── CartProductInfo（购物车商品信息实体）
│   ├── Content（内容实体）
│   ├── Coupon（优惠券实体）
│   ├── CurrencyType（币种类型枚举/实体）
│   ├── CustomUserDetail（自定义用户详情实体）
│   ├── DataRentStat（租赁数据统计实体）
│   ├── DataStat（数据统计实体）
│   ├── DeviceLimit（设备限制实体）
│   ├── EmailHistory（邮件历史实体）
│   ├── EmailStatusDTO（邮件状态数据传输对象）
│   ├── EnyanAcsm（数字内容实体）
│   ├── EnyanBalance（余额实体）
│   ├── EnyanBalanceHistory（余额历史实体）
│   ├── EnyanBanner（轮播图实体）
│   ├── EnyanBlog（博客实体）
│   ├── EnyanBook（图书实体）
│   ├── EnyanBookBuy（图书购买实体）
│   ├── EnyanBookCost（图书成本实体）
│   ├── EnyanBookList（图书书单实体）
│   ├── EnyanBookSet（图书合集实体）
│   ├── EnyanCart（购物车实体）
│   ├── EnyanCategory（分类实体）
│   ├── EnyanComment（评论实体）
│   ├── EnyanConfig（系统配置实体）
│   ├── EnyanCoupon（优惠券实体）
│   ├── EnyanDailyWords（每日一句实体）
│   ├── EnyanDiscount（折扣实体）
│   ├── EnyanFeedback（用户反馈实体）
│   ├── EnyanImg（图片实体）
│   ├── EnyanOrder（订单实体）
│   ├── EnyanOrderDetail（订单明细实体）
│   ├── EnyanPayRate（支付费率实体）
│   ├── EnyanPlan（计划实体）
│   ├── EnyanPlanNote（计划笔记实体）
│   ├── EnyanPublisher（出版社实体）
│   ├── EnyanReaderHighlights（阅读高亮实体）
│   ├── EnyanReading（阅读记录实体）
│   ├── EnyanRedeemCode（兑换码实体）
│   ├── EnyanRefund（退款实体）
│   ├── EnyanRent（租赁实体）
│   ├── EnyanRentDetail（租赁明细实体）
│   ├── EnyanSpirit（灵感/语录实体）
│   ├── EnyanSubscription（订阅实体）
│   ├── EnyanUserBookmarks（用户书签实体）
│   ├── EnyanUserHighlights（用户高亮实体）
│   ├── EnyanWish（心愿单实体）
│   ├── Event（事件实体）
│   ├── GeoIP（地理位置实体）
│   ├── GiftSendDTO（礼物发送数据传输对象）
│   ├── LicenseStatus（授权状态实体）
│   ├── LicenseView（授权视图实体）
│   ├── MUser（多用户实体）
│   ├── MutiSelectModel（多选模型实体）
│   ├── OCUser（外部合作用户实体）
│   ├── OrderDetailInfo（订单明细信息实体）
│   ├── OrderPayInfo（订单支付信息实体）
│   ├── OrderTitleInfo（订单标题信息实体）
│   ├── PayDetail（支付明细实体）
│   ├── PayRateDTO（支付费率数据传输对象）
│   ├── ProductInfo（产品信息实体）
│   ├── Publication（出版物实体）
│   ├── PublisherInfo（出版社信息实体）
│   ├── Purchase（购买实体）
│   ├── RedeemCodeNoteInfo（兑换码备注信息实体）
│   ├── RentInfo（租赁信息实体）
│   ├── ShopIndex（店铺首页实体）
│   ├── SignInfo（签到信息实体）
│   ├── SlackMsg（Slack消息实体）
│   ├── StuCheckin（学生签到实体）
│   ├── StuCheckinInfo（学生签到信息实体）
│   ├── StuEnroll（学生报名实体）
│   ├── StuInfo（学生信息实体）
│   ├── User（用户实体）
│   ├── UserInfo（用户信息实体）
│   └── ...（其他数据模型）
│
├── dao/mapper
│   ├── 各种Mapper/DAO接口和实现         # MyBatis数据访问，命名与业务实体对应
│   └── ...
│
├── common                        # 通用工具、常量、辅助类
├── interceptor                   # 拦截器，AOP切面
├── security                      # 安全相关配置与实现
├── tags                          # 自定义JSP标签
└── task                          # 定时任务
