%% DRM服务使用流程---初级方框图
graph TD
%% 1. 用户认证
A[用户请求受保护内容] --> B{是否已认证?}
B -->|是| C[AuthUserService.getCurrentUser]
B -->|否| D[返回401未授权错误]

    %% 2. DRM信息检查
    C --> E{是否已有有效License?}
    E -->|是| F[LicenseService.validateLicense]
    E -->|否| G[LicenseService.createLicense]
    
    %% 3. License验证
    F --> H{License是否有效?}
    H -->|是| I[KeyManagementService.getContentKey]
    H -->|否| G
    
    %% 4. 创建新License
    G --> J[DRMService.createUserIfNotExists]
    J --> K[UserDeviceService.checkDeviceLimit]
    K --> L[LicenseGenerator.generate]
    
    %% 5. 存储License
    L --> M[LicenseRepository.save]
    M --> I
    
    %% 6. 返回内容
    I --> N[ContentService.getEncryptedContent]
    N --> O[客户端DRMClient.decrypt]
    
    %% 7. 定时任务
    P[LicenseExpiryTask] --> Q[检查过期License]
    Q --> R{是否即将过期?}
    R -->|是| S[发送续期提醒]
    R -->|否| T[继续监控]
    
    %% 8. 设备管理
    U[用户设备访问] --> V{设备限制检查?}
    V -->|是| W[抛出异常]
    V -->|否| X[记录设备访问]
    X --> O



%% %% 下单、下载的DRM相关详细流程
sequenceDiagram
participant User as 用户
participant Frontend as 前端
participant RestShopController as RestShopController
participant EnyanOrderService as EnyanOrderService
participant EnyanBookBuyService as EnyanBookBuyService
participant AuthUserService as AuthUserService
participant LCPServer as LCP服务器
participant Database as 数据库

    %% 购买流程
    User->>Frontend: 选择电子书并下单
    Frontend->>RestShopController: 提交订单请求 (orderSubmit)
    RestShopController->>EnyanOrderService: 创建订单 (addRecord)
    EnyanOrderService->>Database: 保存订单基本信息 (enyanOrderMapper.insert)
    
    alt 免费订单
        RestShopController->>RestShopController: 直接标记为已支付 (paySuccess)
    else 付费订单
        User->>Frontend: 提交支付信息
        Frontend->>RestShopController: 发起支付请求 (orderPayCard)
        RestShopController->>+LCPServer: 处理支付 (StripeUtil.getTokenInPair)
        LCPServer-->>-RestShopController: 返回支付结果
        RestShopController->>RestShopController: 处理支付成功 (paySuccess)
    end
    
    %% 订单处理流程
    RestShopController->>EnyanOrderService: 保存支付信息 (saveOrderHasPay)
    EnyanOrderService->>EnyanOrderService: 更新订单状态为已支付 (updateOrderToPaid)
    EnyanOrderService->>EnyanOrderService: 拆分订单 (splitOrder)
    
    %% 处理单本书
    EnyanOrderService->>EnyanOrderService: 处理单本书 (splitEBookSingleToDB)
    EnyanOrderService->>EnyanBookBuyService: 创建书籍购买记录 (enyanBookBuyMapper.insert)
    
    %% DRM信息处理流程 - 重点部分
    EnyanOrderService->>EnyanOrderService: 添加DRM信息 (addDrmInfoAndPurchaseInfoToBookBuy)
    EnyanOrderService->>AuthUserService: 获取LCP用户 (getLcpUserByEmail)
    
    alt 用户不存在
        AuthUserService->>AuthUserService: 创建新LCP用户
        AuthUserService->>Database: 保存LCP用户信息
    end
    
    %% 在本地数据库中创建购买记录和许可证
    EnyanOrderService->>Database: 创建购买记录 (purchaseMapper.insert)
    EnyanOrderService->>Database: 创建许可证记录 (insertLicense)
    Note over EnyanOrderService,Database: 注意：这里只是在本地数据库创建记录，<br/>不与LCP服务器交互
    
    %% 保存DRM信息
    EnyanOrderService->>EnyanOrderService: 构建DrmInfo和LcpInfo对象
    EnyanOrderService->>EnyanOrderService: 将DRM信息转为JSON并保存到BookBuy (bookBuy.setDrminfo)
    EnyanOrderService->>Database: 更新BookBuy记录
    
    %% 下载流程
    User->>Frontend: 请求下载电子书
    Frontend->>RestBookController: 下载请求 (downloadEpub)
    RestBookController->>EnyanBookBuyService: 查询用户购买记录 (findBookBuyListByEmailAndBookId)
    EnyanBookBuyService->>Database: 查询数据库
    Database-->>EnyanBookBuyService: 返回购买记录
    
    %% 验证DRM信息
    RestBookController->>RestBookController: 解析DRM信息 (JSONObject.parseObject)
    
    alt DRM信息不完整
        RestBookController->>EnyanOrderService: 获取许可证 (saveLicensesByDrmInfo)
        EnyanOrderService->>Database: 查询/创建许可证
        Database-->>EnyanOrderService: 返回许可证ID
        EnyanOrderService-->>RestBookController: 返回许可证ID
    end
    
    %% 与LCP服务器交互下载加密电子书
    RestBookController->>EnyanBookBuyService: 下载LCP文件 (downloadLcpFiles)
    EnyanBookBuyService->>AuthUserService: 获取LCP用户信息 (getLcpUserById)
    AuthUserService->>Database: 查询数据库
    Database-->>AuthUserService: 返回用户信息
    
    %% 构建LCP请求
    EnyanBookBuyService->>EnyanBookBuyService: 构建LCP下载请求 (DRMUtil.getJsonForPublicationByLicenseID)
    EnyanBookBuyService->>+LCPServer: 发送HTTP请求获取加密电子书 (HttpProtocolHandler.execute)
    Note over EnyanBookBuyService,LCPServer: 这里是与LCP服务器的实际交互<br/>发送用户凭证和许可证ID
    LCPServer-->>-EnyanBookBuyService: 返回加密的电子书文件
    EnyanBookBuyService-->>RestBookController: 返回加密电子书数据
    
    %% 更新设备状态
    LCPServer->>Database: 更新许可证状态 (status=2, deviceCount+1)
    
    %% 返回给用户
    RestBookController-->>Frontend: 返回加密电子书文件
    Frontend-->>User: 提供下载






%% 下单、下载的DRM相关概要流程
sequenceDiagram
participant User as 用户
participant Frontend as 前端
participant OrderController as 订单控制器
participant OrderService as 订单服务
participant DRMService as DRM服务
participant LCPServer as LCP服务器
participant Database as 数据库

    %% 1. 用户购买流程
    User->>+Frontend: 1. 选择电子书并下单
    Frontend->>+OrderController: 2. 提交订单请求 (POST /api/v4/shop/orderSubmit)
    OrderController->>+OrderService: 3. 创建订单 (OrderService.createOrder)
    OrderService->>+Database: 4. 保存订单信息 (OrderMapper.insert)
    Database-->>-OrderService: 5. 返回订单ID
    OrderService->>+DRMService: 6. 生成DRM信息 (EnyanOrderDetailService.addDrmInfoAndPurchaseInfoToOrderDetail)
    DRMService->>+LCPServer: 7. 请求创建许可证 (HttpProtocolHandler.execute)
    LCPServer-->>-DRMService: 8. 返回许可证ID
    DRMService->>+Database: 9. 保存DRM信息 (PurchaseMapper.insert)
    Database-->>-DRMService: 10. 确认保存
    DRMService-->>-OrderService: 11. 返回DRM信息
    OrderService-->>-OrderController: 12. 返回订单创建结果
    OrderController-->>-Frontend: 13. 返回订单成功
    Frontend-->>-User: 14. 显示购买成功

    %% 2. 下载和验证流程
    User->>+Frontend: 15. 点击下载电子书
    Frontend->>+DRMService: 16. 请求下载链接 (GET /api/drm/download/{licenseId})
    DRMService->>+Database: 17. 验证用户权限 (AuthUserService.validateUserPermission)
    Database-->>-DRMService: 18. 返回验证结果
    DRMService->>+LCPServer: 19. 请求下载授权 (HttpProtocolHandler.execute)
    LCPServer-->>-DRMService: 20. 返回下载令牌
    DRMService-->>-Frontend: 21. 返回下载URL和令牌
    Frontend->>+LCPServer: 22. 下载电子书(带令牌)
    LCPServer-->>-Frontend: 23. 返回加密的电子书
    Frontend->>+User: 24. 保存电子书到本地

    %% 3. 阅读时验证流程
    User->>+Frontend: 25. 打开电子书
    Frontend->>+DRMService: 26. 请求阅读许可 (GET /api/drm/license/{bookId})
    DRMService->>+LCPServer: 27. 验证用户权限 (HttpProtocolHandler.execute)
    LCPServer-->>-DRMService: 28. 返回验证结果
    DRMService-->>-Frontend: 29. 返回解密密钥
    Frontend->>User: 30. 解密并显示内容







## 电子书与LCP数据对象关系图

```mermaid
classDiagram
    %% 主要业务对象
    EnyanBookBuy "1" --> "1" DrmInfo : 包含(drminfo字段)
    DrmInfo "1" --> "1" LcpInfo : 包含
    
    %% 许可证状态跟踪
    LicenseStatus "1" --> "1" LcpInfo : 关联(licenseRef)
    
    %% LCP服务器交互对象
    Licenses "1" --> "1" User : 包含
    Licenses "1" --> "1" Encryption : 包含
    Encryption "1" --> "1" ContentKey : 包含
    Encryption "1" --> "1" UserKey : 包含
    
    %% 关键字段关联
    LcpInfo ..> Licenses : 关联(licenseUuid)
    
    class EnyanBookBuy {
        +Long bookBuyId
        +Long userId
        +String userEmail
        +Long bookId
        +String bookTitle
        +Date purchasedAt
        +String drminfo
    }
    
    class DrmInfo {
        +LcpInfo lcpInfo
    }
    
    class LcpInfo {
        +String bookId
        +Integer purchseId
        +Integer userId
        +Integer publicationId
        +String licenseUuid
    }
    
    class LicenseStatus {
        +Integer status
        +Integer deviceCount
        +Date licenseUpdated
        +Date statusUpdated
        +String licenseRef
    }
    
    class Licenses {
        +String provider
        +String id
        +String issued
        +User user
        +Encryption encryption
        +Rights rights
    }
    
    class User {
        +String id
        +String email
        +String name
        +String hint
        +String password
        +String uuid
    }
    
    class Encryption {
        +String profile
        +ContentKey contentKey
        +UserKey userKey
    }
    
    class ContentKey {
        +String algorithm
        +String encryptedValue
    }
    
    class UserKey {
        +String textHint
        +String keyCheck
        +String hexValue
        +String value
    }
```
## 数据流程说明

### 购买流程
1. 用户购买电子书时，创建 `EnyanBookBuy` 记录
2. 系统生成 `DrmInfo` 和 `LcpInfo`，序列化后存储在 `EnyanBookBuy.drminfo` 字段中
3. 系统创建 `LicenseStatus` 记录，初始状态为1(未下载)，`deviceCount` 为0

### 下载流程
1. 用户下载电子书时，系统从 `EnyanBookBuy` 中获取 `DrmInfo` 和 `LcpInfo`
2. 使用 `LcpInfo.licenseUuid` 与LCP服务器交互，获取加密电子书
3. 下载成功后，更新 `LicenseStatus.status` 为2(已下载)，并增加 `deviceCount` 计数

### LCP交互
1. 与LCP服务器交互时，使用 `Licenses` 对象封装所需信息
2. `Licenses` 包含 `User`(用户信息)和 `Encryption`(加密信息)
3. `Encryption` 又包含 `ContentKey`(内容密钥)和 `UserKey`(用户密钥)

## 重构建议

1. **数据一致性**
   - 规范化JSON序列化/反序列化过程
   - 修复 `LcpInfo.purchseId` 拼写错误

2. **关系明确化**
   - 建立 `EnyanBookBuy` 与 `LicenseStatus` 的明确关联
   - 使用外键或代码关联确保数据完整性

3. **模型简化**
   - 考虑合并 `DrmInfo` 和 `LcpInfo`，减少不必要的嵌套
   - 评估是否需要所有中间对象

4. **状态管理优化**
   - 将数字状态值替换为枚举类型
   - 完善状态转换逻辑和设备计数更新机制

5. **错误处理**
   - 增强与LCP服务器交互的错误处理机制
   - 添加重试和回滚机制

6. **安全性**
   - 审查加密相关代码，确保符合最新安全标准
   - 考虑添加更多安全检查和日志记录



## 创建的订单提交流程中核心对象关系图
classDiagram
class RestShopController {
+orderSubmit(RestOrder~Long~ restList, HttpServletRequest request)
+paySuccess(EnyanOrder order, OrderPayInfo orderPayInfo, String lang)
-sendLogOfOrder(EnyanOrder order)
}

    class RestOrder~T~ {
        +String email
        +List~T~ list
        +String couponCode
        +String lang
        +String area
        +String currency
        +initHeaderValue(HttpServletRequest request)
        +initFrom(EnyanOrder order, CurrencyType currencyType)
    }
    
    class EnyanOrder {
        +String orderNum
        +String orderBookHash
        +Byte isPaid
        +Byte isValid
        +Byte isCounted
        +Integer orderType
        +Long userId
        +String userEmail
        +Date purchasedAt
        +Date expiredAt
        +Byte orderCurrency
        +BigDecimal orderTotal
        +Integer orderFrom
        +OrderDetailInfo orderDetailInfo
        +OrderTitleInfo orderTitleInfo
        +OrderPayInfo orderPayInfo
        +Map~Long,EnyanBook~ bookMap
    }
    
    class OrderDetailInfo {
        +List~ProductInfo~ productInfoList
        +List~CartDiscountInfo~ cartDiscountInfoList
        +BigDecimal amountCny
        +BigDecimal amountUsd
        +BigDecimal amountHkd
        +BigDecimal amountHkdMiddle
        +BigDecimal amountCoupon
        +BigDecimal amountDiscount
        +String couponCode
        +String discountTitle
        +CartInfo cartInfo
        +String language
        +BigDecimal totalFeeBeforeCoupon
        +OrderDetailInfo(CartInfo cartInfo)
        +resetProductListByCoupon(EnyanCoupon enyanCoupon)
        -resetAmountHKD()
        +resetFromJson()
    }
    
    class CartInfo {
        +List~CartDiscountInfo~ cartDiscountInfoList
        +String language
        +BigDecimal amountHkd
        +BigDecimal amountHkdFix
        +BigDecimal totalFeeBeforeCoupon
        +addProduct(ProductInfo productInfo, int quantity)
        +resetCouponProducts(EnyanCoupon enyanCoupon)
    }
    
    class CartDiscountInfo {
        +List~ProductInfo~ productInfoList
        +String discountTitle
        +BigDecimal amountHkdDiscount
        +resetCumulateDiscount()
    }
    
    class ProductInfo {
        +Long code
        +String title
        +BigDecimal price
        +ProductInfo(EnyanBook book)
    }
    
    class OrderTitleInfo {
        +String title
        +OrderTitleInfo(CartInfo cartInfo)
    }
    
    class OrderPayInfo {
        +String payMethod
        +String payStatus
        +addFree()
    }
    
    class EnyanBook {
        +Long bookId
        +String bookTitle
        +BigDecimal price
        +Integer bookType
        +Byte shelfStatus
        +resetByArea(String area)
    }
    
    class EnyanCoupon {
        +String couponCode
        +Integer couponValue
        +Integer minLimitValue
    }
    
    class AuthUser {
        +Long userId
        +String email
    }
    
    class EnyanBookBuy {
        +Long bookBuyId
        +Long bookId
        +String bookTitle
        +String userEmail
        +Long userId
        +String orderNum
        +Date purchasedAt
        +Integer purchasedDay
        +String drminfo
    }
    
    class DrmInfo {
        +LcpInfo lcpInfo
    }
    
    class User {
        +String email
        +String name
        +String hint
        +String password
        +String uuid
    }
    
    class EnyanOrderService {
        +addRecord(EnyanOrder record)
        +updateRecord(EnyanOrder record)
        +saveOrderHasPay(EnyanOrder order)
        -splitOrder(EnyanOrder enyanOrder, User user, Boolean updateOrderCount)
    }
    
    class AuthUserService {
        +getUserByEmail(String email)
        +getLcpUserByEmail(String email)
        +addLcpUser(User user)
    }
    
    RestShopController --> RestOrder : 接收
    RestShopController --> EnyanOrder : 创建
    RestShopController --> EnyanOrderService : 调用
    RestShopController --> AuthUserService : 调用
    
    RestOrder --> EnyanOrder : 转换为
    
    EnyanOrder "1" *-- "1" OrderDetailInfo : 包含
    EnyanOrder "1" *-- "1" OrderTitleInfo : 包含
    EnyanOrder "1" *-- "1" OrderPayInfo : 包含
    EnyanOrder "1" *-- "N" EnyanBook : 关联
    
    OrderDetailInfo "1" *-- "N" ProductInfo : 包含
    OrderDetailInfo "1" *-- "N" CartDiscountInfo : 包含
    OrderDetailInfo --> CartInfo : 使用
    OrderDetailInfo --> EnyanCoupon : 应用
    
    CartInfo "1" *-- "N" CartDiscountInfo : 包含
    CartDiscountInfo "1" *-- "N" ProductInfo : 包含
    
    ProductInfo --> EnyanBook : 基于
    
    EnyanOrderService --> EnyanOrder : 处理
    EnyanOrderService --> EnyanBookBuy : 创建
    EnyanOrderService --> DrmInfo : 处理
    EnyanOrderService --> User : 需要
    
    AuthUserService --> AuthUser : 管理
    AuthUserService --> User : 管理

这个Mermaid类图展示了订单提交流程中的核心对象及其关系：

控制层：RestShopController 负责接收前端请求并协调整个订单处理流程
数据传输对象：RestOrder<T> 接收前端数据
核心业务对象：EnyanOrder 是整个订单的核心实体
组合对象：OrderDetailInfo、OrderTitleInfo、OrderPayInfo 组成订单的不同方面
服务层：EnyanOrderService 和 AuthUserService 提供核心业务逻辑
支持对象：CartInfo、CartDiscountInfo、ProductInfo 等支持订单处理流程
实体对象：EnyanBook、EnyanCoupon、AuthUser 等表示业务实体
结果对象：EnyanBookBuy、DrmInfo、User 等表示订单处理的结果
图中使用实线箭头表示依赖关系，菱形箭头表示组合关系，这清晰地展示了订单提交过程中各对象之间的交互和数据流转。

包含关系：通过类成员变量持有对象集合，完全控制被包含对象的生命周期
private List<ProductInfo> productInfoList;
private List<CartDiscountInfo> cartDiscountInfoList = new ArrayList<>();

使用关系：通过成员变量引用外部对象，不控制其生命周期，但依赖其功能
@JSONField(serialize = false)
private CartInfo cartInfo;

应用关系：通过方法参数临时使用外部对象，方法执行完毕后不再持有引用
public void resetProductListByCoupon(EnyanCoupon enyanCoupon){
// 使用 enyanCoupon 但不存储它
}

基于关系：通过构造函数从一个对象创建另一个对象，创建后无直接引用关系
public ProductInfo(EnyanBook book) {
this.code = book.getBookId();
// 从 book 提取数据但不存储 book 引用
}







%% 从RestShopController推测，普通电子书购买，支付、兑换码兑换流程
sequenceDiagram
participant 用户
participant 前端
participant RestShopController
participant EnyanBookService
participant EnyanCartService
participant EnyanOrderService
participant AuthUserService
participant EnyanBookBuyService
participant EnyanRedeemCodeService

    %% 普通电子书购买流程
    rect rgb(200, 220, 255)
    note right of 用户: 普通电子书购买流程
    用户->>前端: 浏览商城首页
    前端->>RestShopController: /api/v4/shop/base
    RestShopController->>EnyanBookService: initIndexAllInfo()
    EnyanBookService-->>RestShopController: 返回首页数据
    RestShopController-->>前端: 返回商城首页数据
    
    用户->>前端: 按分类浏览书籍
    前端->>RestShopController: /api/v4/shop/bookCategory
    RestShopController->>EnyanBookService: queryRecords()
    EnyanBookService-->>RestShopController: 返回分类书籍
    RestShopController-->>前端: 返回分类书籍列表
    
    用户->>前端: 查看书籍详情
    前端->>RestShopController: /api/v4/shop/bookInfo
    RestShopController->>EnyanBookService: queryRecordByPrimaryKey()
    RestShopController->>EnyanBookBuyService: countOfBookByEmailAndBookId()
    EnyanBookService-->>RestShopController: 返回书籍详情
    EnyanBookBuyService-->>RestShopController: 返回是否已购买
    RestShopController-->>前端: 返回书籍详细信息
    
    用户->>前端: 添加到购物车
    前端->>RestShopController: /api/v4/shop/cartAdd
    RestShopController->>EnyanCartService: addCart()
    EnyanCartService-->>RestShopController: 添加成功
    RestShopController-->>前端: 返回添加结果
    
    用户->>前端: 查看购物车
    前端->>RestShopController: /api/v4/shop/cart
    RestShopController->>EnyanCartService: findCartByEmail()
    EnyanCartService-->>RestShopController: 返回购物车数据
    RestShopController-->>前端: 返回购物车列表
    
    用户->>前端: 提交订单
    前端->>RestShopController: /api/v4/shop/orderSubmit
    RestShopController->>AuthUserService: getUserByEmail()
    AuthUserService-->>RestShopController: 返回用户信息
    RestShopController->>EnyanBookService: findBookByIds()
    EnyanBookService-->>RestShopController: 返回书籍信息
    RestShopController->>EnyanOrderService: addRecord()
    EnyanOrderService-->>RestShopController: 添加订单成功
    RestShopController->>EnyanCartService: deleteCarts()
    EnyanCartService-->>RestShopController: 清空购物车成功
    RestShopController-->>前端: 返回订单信息
    
    用户->>前端: 支付订单
    前端->>RestShopController: /api/v4/shop/cardPay
    RestShopController->>EnyanOrderService: findRecordsWithBLOBsByOrder()
    EnyanOrderService-->>RestShopController: 返回订单信息
    RestShopController->>RestShopController: paySuccess()
    RestShopController->>EnyanOrderService: updateRecord()
    EnyanOrderService-->>RestShopController: 更新订单状态成功
    RestShopController-->>前端: 返回支付结果
    end

    %% 兑换码兑换流程
    rect rgb(255, 220, 200)
    note right of 用户: 兑换码兑换流程
    用户->>前端: 输入兑换码
    前端->>RestShopController: /api/v4/shop/redeemCodeToExchange
    RestShopController->>AuthUserService: getUserByEmail()
    AuthUserService-->>RestShopController: 返回用户信息
    RestShopController->>EnyanRedeemCodeService: findRecordsByRedeemCode()
    EnyanRedeemCodeService-->>RestShopController: 返回兑换码信息
    RestShopController->>EnyanBookBuyService: findBookListHasBuyByEmailAndIds()
    EnyanBookBuyService-->>RestShopController: 返回已购书籍
    RestShopController-->>前端: 返回可兑换书籍列表
    
    用户->>前端: 确认兑换
    前端->>RestShopController: /api/v4/shop/redeemCodeExchangeMulti
    RestShopController->>EnyanRedeemCodeService: findRecordsByRedeemCode()
    EnyanRedeemCodeService-->>RestShopController: 返回兑换码信息
    RestShopController->>EnyanBookService: findBookByIdsArray()
    EnyanBookService-->>RestShopController: 返回书籍信息
    RestShopController->>EnyanOrderService: addRecord()
    EnyanOrderService-->>RestShopController: 添加订单成功
    RestShopController->>EnyanRedeemCodeService: updateRedeemCodeStatus()
    EnyanRedeemCodeService-->>RestShopController: 更新兑换码状态成功
    RestShopController->>EnyanBookBuyService: addBookBuyList()
    EnyanBookBuyService-->>RestShopController: 添加购买记录成功
    RestShopController-->>前端: 返回兑换结果
    end










%% 整个订单过程中，需要实体化的对象关系说明
classDiagram
direction TB

    class EnyanBook {
        +Long bookId
        +String bookTitle
        +String author
        +BigDecimal priceHkd
        +String bookCover
        +String bookFull
        +String bookHash
        +Integer bookType
        +String bookDrmRef
        note for EnyanBook "存储时机：上架时创建\n购买流程中只读取不修改\nbookDrmRef关联Publication表的id"
    }
    
    class EnyanCart {
        +Long cartId
        +Long userId
        +String userEmail
        +Long bookId
        +Date createdAt
        note for EnyanCart "存储时机：\n1.用户点击'添加到购物车'时创建\n2.提交订单后被清空"
    }
    
    class CartInfo {
        +List~CartDiscountInfo~ cartDiscountInfoList
        +BigDecimal amountHkd
        +BigDecimal amountHkdFix
        +String language
        note for CartInfo "内存对象：\n从EnyanCart数据构建\n用于购物车展示和订单创建"
    }
    
    class CartDiscountInfo {
        +ProductInfo productInfo
        +BigDecimal quantity
        +BigDecimal discount
        +String discountTitle
        note for CartDiscountInfo "内存对象：\n随CartInfo一起使用\n包含商品和折扣信息"
    }
    
    class ProductInfo {
        +Long productId
        +String productName
        +BigDecimal productPrice
        +String productType
        note for ProductInfo "内存对象：\n作为CartDiscountInfo的一部分\n表示购物车中的产品"
    }
    
    class OrderDetailInfo {
        +List~CartDiscountInfo~ cartDiscountInfoList
        +BigDecimal amountHkd
        +BigDecimal amountCoupon
        +BigDecimal amountDiscount
        +String couponCode
        note for OrderDetailInfo "内存对象：\n1.从CartInfo转换而来\n2.提交订单时序列化为JSON\n存入EnyanOrder.orderDetail"
    }
    
    class EnyanOrder {
        +Long orderId
        +String orderNum
        +String userEmail
        +BigDecimal orderTotal
        +Byte isPaid
        +String orderDetail
        +OrderDetailInfo orderDetailInfo
        note for EnyanOrder "存储时机：\n1.提交订单时创建\n2.支付成功后更新状态\n3.包含订单详情的JSON数据"
    }
    
    class EnyanOrderDetail {
        +Long orderDetailId
        +String orderNum
        +Long bookId
        +String bookTitle
        +BigDecimal priceSelling
        +Integer quantity
        +String drminfo
        note for EnyanOrderDetail "存储时机：\n1.订单拆分时创建\n2.支付成功后添加DRM信息\n每个订单项对应一本书"
    }
    class EnyanRentDetail {
        +Long detailId
        +String orderNum
        +String tradeNo
        +String userEmail
        +Integer rentType
        +Integer rentMonths
        +BigDecimal incomeTotal
        +Date purchasedAt
        +Date fromAt
        +Date expiredAt
        note for EnyanRentDetail "存储时机：\n1.租借订单支付成功后创建\n2.记录租借交易的详细信息\n3.包含收入分成和租期信息"
    }
        
    class EnyanBookBuy {
        +Long bookBuyId
        +String orderNum
        +String userEmail
        +Long bookId
        +String bookTitle
        +Date purchasedAt
        +String drminfo
        +DrmInfo drmInfoModel
        note for EnyanBookBuy "存储时机：\n1.支付成功后创建\n2.记录用户购买的书籍\n3.包含DRM信息"
    }
    
    class DrmInfo {
        +LcpInfo lcpInfo
        note for DrmInfo "内存对象：\n1.支付成功后创建\n2.序列化为JSON存储在\nEnyanBookBuy.drminfo和\nEnyanOrderDetail.drminfo中"
    }
    
    class LcpInfo {
        +String bookId
        +Integer userId
        +Integer publicationId
        +String licenseUuid
        +Integer purchseId
        note for LcpInfo "内存对象：\n1.DRM信息的核心部分\n2.包含许可证UUID和关联信息\n3.用于内容保护和授权\n4.关联Purchase表记录"
    }
    
    class License {
        +String id
        +String userId
        +String provider
        +Date issued
        +Integer rightsPrint
        +Integer rightsCopy
        +Date rightsStart
        +Date rightsEnd
        +String contentFk
        +Integer lsdStatus
        note for License "存储时机：\n1.创建DRM信息时通过JDBC直接插入\n2.记录内容许可证信息\n3.contentFk关联Publication\n4.无对应Java模型类，通过JDBC操作"
    }

    class LicenseStatus {
        +Integer id
        +Integer status
        +Date licenseUpdated
        +Date statusUpdated
        +Integer deviceCount
        +Date potentialRightsEnd
        +String licenseRef
        +Date rightsEnd
        note for LicenseStatus "存储时机：\n1.创建License时同步创建\n2.licenseRef关联License的id\n3.记录许可证状态和设备数量\n4.status=1表示默认状态，2表示有设备下载  4.deviceCount和licenseStatus是lcp服务写入的"
    }
    
    class EnyanRedeemCode {
        +Long redeemCodeId
        +String redeemCode
        +String userEmail
        +String bookIds
        +Integer redeemStatus
        +Date expiredAt
        note for EnyanRedeemCode "存储时机：\n1.购买兑换码时创建\n2.兑换成功后更新状态\n3.关联可兑换的书籍ID列表"
    }
    
    class EnyanRent {
        +Long rentId
        +String orderNum
        +String userEmail
        +Integer rentType
        +Date beginAt
        +Date expiredAt
        +String rentDetail
        +String baseLicense
        note for EnyanRent "存储时机：\n1.租借书籍时创建\n2.续租时更新过期时间\n3.包含租借详情和许可信息"
    }
    
    class RentInfo {
        +List~RentBookInfo~ rentBookInfoList
        +BigDecimal totalFee
        +Integer totalMonths
        note for RentInfo "内存对象：\n1.从EnyanRent.rentDetail反序列化\n2.包含租借的书籍列表和费用信息"
    }
    
    class RentBookInfo {
        +Long bookId
        +String bookTitle
        +BigDecimal rentPrice
        +String drminfo
        note for RentBookInfo "内存对象：\n作为RentInfo的一部分\n表示租借的单本书籍信息"
    }
    
    class Publication {
        +Integer id
        +String uuid
        +String title
        +String status
        note for Publication "存储时机：\n1.书籍上架时创建\n2.与EnyanBook关联\n3.用于DRM许可证管理\n4.通过bookDrmRef关联"
    }
    
    class Purchase {
        +Integer id
        +String uuid
        +Integer publicationId
        +Integer userId
        +String licenseUuid
        +String type
        +Date transactionDate
        +Date startDate
        +Date endDate
        +String status
        note for Purchase "存储时机：\n1.支付成功后创建\n2.记录购买或租借的交易\n3.关联Publication和User\n4.用于生成许可证"
    }
    
    class LicenseView {
        +Integer id
        +String uuid
        +Integer deviceCount
        +String status
        +String message
        note for LicenseView "存储时机：\n1.许可证创建后由LCP服务更新\n2.记录许可证状态和设备使用情况\n3.用于限制设备授权数量"
    }
    
    class Event {
        +Integer id
        +String deviceName
        +Date timestamp
        +Integer type
        +String deviceId
        +Integer licenseStatusFk
        note for Event "存储时机：\n1.用户设备访问内容时记录\n2.由LCP服务写入\n3.用于跟踪许可证使用情况"
    }
    
    %% 关系连接
    License --> LicenseStatus : 创建
    EnyanBook <-- EnyanCart : 引用
    EnyanCart <-- CartInfo : 构建
    CartInfo *-- CartDiscountInfo : 包含
    CartDiscountInfo *-- ProductInfo : 包含
    CartInfo --> OrderDetailInfo : 转换
    OrderDetailInfo --> EnyanOrder : 序列化存储
    EnyanOrder --> EnyanOrderDetail : 拆分
    EnyanOrderDetail --> EnyanBookBuy : 创建
    EnyanBookBuy *-- DrmInfo : 序列化存储
    DrmInfo *-- LcpInfo : 包含
    EnyanRedeemCode --> EnyanOrder : 创建
    EnyanRedeemCode --> EnyanBookBuy : 创建
    EnyanRent *-- RentInfo : 序列化存储
    RentInfo *-- RentBookInfo : 包含
    RentBookInfo *-- DrmInfo : 序列化存储
    DrmInfo --> License : 关联
    EnyanBook --> Publication : 关联(bookDrmRef)
    Publication <-- Purchase : 引用
    Purchase --> LcpInfo : 关联
    Purchase --> LicenseView : 创建
    LicenseView <-- Event : 引用
    EnyanRent --> EnyanRentDetail : 创建
    EnyanRentDetail <-- EnyanRent : 引用










