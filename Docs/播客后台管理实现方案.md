播客后台管理功能实现方案
这个方案将涵盖从前端 JSP 页面到底层数据操作的完整流程，并遵循项目现有的开发模式。

1. JSP 页面清单及功能描述
我们需要创建以下 JSP 页面来实现原型中的管理界面。这些页面将放置在 src/main/webapp/WEB-INF/views/admin/podcast/ 和 src/main/webapp/WEB-INF/views/admin/episode/ 目录下。

### 1.1 播客管理 (Podcast Management)

| 页面路径                                  | 功能描述                                                   | 主要元素                                       |
| :---------------------------------------- | :--------------------------------------------------------- | :--------------------------------------------- |
| `/WEB-INF/views/admin/podcast/list.jsp`   | 播客列表页面，展示所有播客信息，支持分页、搜索、批量操作 | 数据表格、搜索框、分页控件、新增/编辑/删除按钮 |
| `/WEB-INF/views/admin/podcast/form.jsp`   | 播客添加/编辑表单页面                                      | 表单字段、文件上传、富文本编辑器、保存/取消按钮  |
| `/WEB-INF/views/admin/podcast/detail.jsp` | 播客详情页面                                               | 详情展示、关联单集列表、返回按钮               |

### 1.2 单集管理 (Episode Management)

| 页面路径                                  | 功能描述                           | 主要元素                                       |
| :---------------------------------------- | :--------------------------------- | :--------------------------------------------- |
| `/WEB-INF/views/admin/episode/list.jsp`   | 单集列表页面，可按播客筛选         | 数据表格、播客筛选下拉框、分页、批量操作       |
| `/WEB-INF/views/admin/episode/form.jsp`   | 单集添加/编辑表单页面              | 表单字段、音频上传、富文本编辑器、保存/取消按钮  |
| `/WEB-INF/views/admin/episode/detail.jsp` | 单集详情页面                       | 详情展示、音频播放器、返回按钮                 |

### 1.3 专题管理 (Topic Management)

| 页面路径                                | 功能描述                 | 主要元素                                             |
| :-------------------------------------- | :----------------------- | :--------------------------------------------------- |
| `/WEB-INF/views/admin/topic/list.jsp`   | 专题列表页面             | 数据表格、搜索、分页、新增/编辑/删除按钮             |
| `/WEB-INF/views/admin/topic/form.jsp`   | 专题添加/编辑表单页面    | 表单字段、封面图上传、关联播客选择器、保存/取消按钮 |
| `/WEB-INF/views/admin/topic/detail.jsp` | 专题详情页面             | 专题详情、关联播客列表、返回按钮                     |
2. Controller 类及其主要方法
我们将创建一个新的 Controller PodcastController.java 来处理所有后台管理相关的 HTTP 请求。这个 Controller 会放在 com.endao.web.controller.admin 包下，并借鉴 BlogController 的设计。

```java
PodcastController.java
@RequestMapping("/admin/podcast")
list(Model model, PodcastQuery query):
@GetMapping("/list")
处理播客列表页面的请求，根据查询条件 query（如分页、搜索关键字）获取数据并渲染 podcast/list.jsp。
edit(Model model, Long id):
@GetMapping("/edit")
处理跳转到新增/编辑播客页面的请求。如果 id 存在，则查询播客信息并填充到表单中，渲染 podcast/edit.jsp。
save(Podcast podcast):
@PostMapping("/save")
处理保存播客的请求，根据 podcast 对象中是否有 ID 来执行新增或更新操作，然后重定向到列表页。
delete(Long[] ids):
@PostMapping("/delete")
处理批量删除播客的请求。
listEpisodes(Model model, EpisodeQuery query):
@GetMapping("/episode/list")
处理播客单集列表页面的请求，根据 podcastId 查询其下所有单集，并渲染 episode/list.jsp。
editEpisode(Model model, Long id, Long podcastId):
@GetMapping("/episode/edit")
处理跳转到新增/编辑单集页面的请求，并渲染 episode/edit.jsp。
saveEpisode(Episode episode):
@PostMapping("/episode/save")
处理保存单集的请求，执行新增或更新。
deleteEpisodes(Long[] ids):
@PostMapping("/episode/delete")
处理批量删除单集的请求。
```


3. Service 接口及实现类
我们需要扩展现有的 Service 层来支持后台管理的业务逻辑。我将扩展 IPodcastService 接口和 PodcastServiceImpl 实现类。
```java
IPodcastService.java (位于 com.endao.service 包)
List<Podcast> findPodcastList(PodcastQuery query): 根据后台查询条件获取播客列表。
int savePodcast(Podcast podcast): 保存（新增或更新）一个播客。
int deletePodcastByIds(Long[] ids): 批量删除播客。
List<Episode> findEpisodeList(EpisodeQuery query): 根据后台查询条件获取单集列表。
int saveEpisode(Episode episode): 保存（新增或更新）一个单集。
int deleteEpisodeByIds(Long[] ids): 批量删除单集。
（保留 RestPodcastController 中已有的方法，如 findPodcastById 等）
PodcastServiceImpl.java (位于 com.endao.service.impl 包)
实现上述接口中定义的新方法，调用 Mapper 层完成数据库操作。
4. Mapper 接口及 SQL XML 文件
数据访问层需要新增或修改 Mapper 接口和对应的 XML 文件，以支持新的查询和操作。

PodcastMapper.java (位于 com.endao.mapper 包)
List<Podcast> selectPodcastList(PodcastQuery query): 查询播客列表，支持动态 SQL（用于搜索）。
int insertPodcast(Podcast podcast): 插入新的播客数据。
int updatePodcast(Podcast podcast): 更新播客数据。
int deletePodcastByIds(Long[] ids): 批量删除播客。
PodcastMapper.xml (位于 src/main/resources/mapper/ 目录)
为上述 Mapper 接口方法提供对应的 <select>, <insert>, <update>, <delete> SQL 语句。
EpisodeMapper.java (位于 com.endao.mapper 包)
List<Episode> selectEpisodeList(EpisodeQuery query): 查询单集列表。
int insertEpisode(Episode episode): 插入新的单集数据。
int updateEpisode(Episode episode): 更新单集数据。
int deleteEpisodeByIds(Long[] ids): 批量删除单集。
```

EpisodeMapper.xml (位于 src/main/resources/mapper/ 目录)
为 EpisodeMapper 接口提供对应的 SQL 实现。
5. 数据库表修改
通过分析原型图和现有 DDL，我发现 pod_podcast 和 pod_episode 表的结构基本满足需求。原型中出现的“状态”和“推荐”字段，在 podcastDDL.sql 中已经定义为 status 和 is_recommend，可以直接使用。因此，当前无需新增或修改数据库表结构。


6. 工作量评估
### 1. 前端页面开发 (4.5人天)

| 模块 | 页面 | 工作量(人天) | 说明 |
| :--- | :--- | :--- | :--- |
| 播客管理 | 列表页 | 0.5 | 包含搜索、分页、批量操作 |
| 播客管理 | 新增/编辑页 | 1 | 表单验证、富文本、文件上传 |
| 单集管理 | 列表页 | 0.5 | 支持按播客筛选 |
| 单集管理 | 新增/编辑页 | 1 | 音频上传、富文本编辑 |
| 专题管理 | 列表页 | 0.5 | 基础CRUD |
| 专题管理 | 新增/编辑页 | 1 | 关联播客选择器 |

### 2. 后端开发 (2.5人天)

| 模块 | 内容 | 工作量(人天) |
| :--- | :--- |:--------|
| Controller | 播客相关接口 | 0.5     |
| Service | 播客业务逻辑 | 0.5       |
| Mapper | 数据访问层 | 0.5       |
| API文档 | Swagger集成 | 0.5     |
| 单元测试 | 核心功能测试 | 0.5      |

### 3. 联调与测试 (2人天)

| 阶段 | 内容 | 工作量(人天) |
| :--- | :--- | :--- |
| 接口联调 | 前后端联调 | 1 |
| 测试 | 功能测试与修复 | 1 |




