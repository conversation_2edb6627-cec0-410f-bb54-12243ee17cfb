
graph TB
    subgraph "External Systems"
        CMS["Content Management System"]
        ReadingApp["Reading Applications"]
        Publisher["Publisher/Content Provider"]
    end
    
    subgraph "LCP Server System"
        subgraph "Core Services"
            LCPEncrypt["lcpencrypt<br/>Content Encryption Tool"]
            LCPServer["lcpserver<br/>License Server<br/>Port: 8989"]
            LSDServer["lsdserver<br/>Status Server<br/>Port: 8990"]
        end
        
        subgraph "Frontend (Optional)"
            Frontend["frontend<br/>Test Server"]
        end
        
        subgraph "Storage Layer"
            ContentDB[(Content Database)]
            LicenseDB[(License Database)]
            StatusDB[(Status Database)]
            FileStorage[("File Storage<br/>(S3/FileSystem)")]
        end
    end
    
    %% External to Internal Data Flow
    Publisher -->|"1. Submit Publication"| LCPEncrypt
    CMS -->|"2. Trigger Encryption"| LCPEncrypt
    
    %% Internal Service Communication
    LCPEncrypt -->|"3. Notify Content Added<br/>PUT /contents/{id}"| LCPServer
    LCPEncrypt -->|"4. Store Encrypted Files"| FileStorage
    LCPEncrypt -->|"5. Optional CMS Notification"| CMS
    
    Frontend -->|"6. Request License<br/>POST /contents/{id}/license"| LCPServer
    LCPServer -->|"7. Notify License Created<br/>PUT /licenses"| LSDServer
    
    %% Database Interactions
    LCPServer <-->|"Read/Write Content & License Data"| ContentDB
    LCPServer <-->|"Read/Write License Data"| LicenseDB
    LSDServer <-->|"Read/Write Status & Events"| StatusDB
    LCPServer <-->|"Serve Encrypted Content"| FileStorage
    
    %% External Client Interactions
    ReadingApp -->|"8. Get License<br/>GET /licenses/{id}"| LCPServer
    ReadingApp -->|"9. Get Protected Publication<br/>POST /licenses/{id}/publication"| LCPServer
    ReadingApp -->|"10. Check Status<br/>GET /licenses/{id}/status"| LSDServer
    ReadingApp -->|"11. Register Device<br/>POST /licenses/{id}/register"| LSDServer
    ReadingApp -->|"12. Return/Renew<br/>PUT /licenses/{id}/return"| LSDServer
    
    %% Inter-service Communication
    LSDServer -->|"13. Update License<br/>PATCH /licenses/{id}"| LCPServer
    
    %% Response Flows
    LCPServer -->|"License Document"| ReadingApp
    LSDServer -->|"Status Document"| ReadingApp
    LCPServer -->|"Protected Publication"| ReadingApp
    
    %% Styling
    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef core fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef storage fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef frontend fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class Publisher,CMS,ReadingApp external
    class LCPEncrypt,LCPServer,LSDServer core
    class ContentDB,LicenseDB,StatusDB,FileStorage storage
    class Frontend frontend

