<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <title>WeChat</title>
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Favicon Icons-->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="icon" type="image/png" href="favicon.png">
    <!-- Main Template Styles-->
    <link rel="stylesheet" media="screen" href="css/vendor.min.css">
    <link id="mainStyles" rel="stylesheet" media="screen" href="css/styles.css">
    <script type="text/javascript">
        // 获取终端的相关信息，根据终端辨别下载地址
        var Terminal = {
            // 辨别移动终端类型
            platform : function(){
                var u = navigator.userAgent, app = navigator.appVersion;
                return {
                    //IE内核
                    windows: u.indexOf('Windows') > -1,
                    //苹果、谷歌内核
                    webKit: u.indexOf('AppleWebKit') > -1,
                    //是否为移动终端
                    mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/),
                    //ios终端
                    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
                    // android终端
                    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,
                    // 是否为iPhone
                    iPhone: u.indexOf('iPhone') > -1 ,
                    // 是否iPad
                    iPad: u.indexOf('iPad') > -1,
                    //是否为mac系统
                    Mac: u.indexOf('Macintosh') > -1,
                    //是否web应该程序
                    webApp: u.indexOf('Safari') == -1,
                    //是否微信
                    weixin: u.indexOf('MicroMessenger') > -1, 
                };
            }(),
        }

        // 根据不同的终端，跳转到不同的地址
        var theUrl = 'reader.html';
        if(Terminal.platform.weixin){
            theUrl = '#';
        }else if(Terminal.platform.android){
            theUrl = 'https://inspiratas3.blob.core.windows.net/ebook/Inspirata_eBooks.apk';
        }else if(Terminal.platform.iPhone){
            theUrl = 'https://apps.apple.com/it/app/%E6%81%A9%E9%81%93%E7%94%B5%E5%AD%90%E4%B9%A6-inspirata-ebooks/id1463909109';
        }else if(Terminal.platform.iPad){
            theUrl = 'https://apps.apple.com/it/app/%E6%81%A9%E9%81%93%E7%94%B5%E5%AD%90%E4%B9%A6-inspirata-ebooks/id1463909109';
        }else if(Terminal.platform.windows){
            theUrl = 'https://adedownload.adobe.com/pub/adobe/digitaleditions/ADE_4.5_Installer.exe';
        }else if(Terminal.platform.Mac){
            theUrl = 'https://apps.apple.com/it/app/%E6%81%A9%E9%81%93%E7%94%B5%E5%AD%90%E4%B9%A6-inspirata-ebooks/id1463909109';
        }
        location.href = theUrl;
    </script>
    
  </head>
  <!-- Body-->
  <body>

    <!-- 页面开始 -->
    <div class="offcanvas-wrapper">
      <!-- Main Slider-->
      <section class="hero-slider" style="background-image: url(img/hero-slider/main-bg.png); height:960px;">
        <!-- Slider 1 -->
        <div class="item">
          <div class="container">
            <div class="row justify-content-center">
              <div class="col-lg-6 col-md-6"></div>
              <div class="col-lg-6 col-md-6 text-right">
                <div class="top">
                  <img class="mb-4 w-270" src="img/top_tap.png"alt="log">
                </div>
              </div>
          </div>
          <div class="container padding-top-1x">
            <div class="row justify-content-center align-items-center">
              <div class="col-lg-5 col-md-5 text-md-left text-center">
                <div class="from-bottom">
                  <img class="mb-4 w-150" src="img/logo/logo.png" alt="log">
                  <img class="d-inline-block w-400 mb-4" src="img/icon/reader-banner.png" alt="Reader">
                </div>
              </div>
              <div class="col-lg-5 col-md-5 padding-top-1x text-left">
                <div class="text-body text-white mb-2 pb-1">恩道电子书APP</div>
                <div class="h3 text-body text-white mb-4 pb-1">华人基督徒专属的电子书阅读平台</div>
                <!--div class="from-bottom mb-3">
                    <a class="market-button apple-button" href="./app.html"><span class="mb-title">iOS 下载</span></a>
                    <a class="market-button android-button" href="./app.html"><span class="mb-title">安卓下载</span></a>
                </div-->
                <div class="from-bottom">
                  <div class="text-body text-white mb-4 pb-1">适用于iOS 10.0/Android 6.0.1以上系统</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>      
    </div>
  </body>
</html>
