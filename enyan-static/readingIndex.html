<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <title>读书伙伴 - 恩道电子书</title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书">
    <meta name="keywords" content="恩道电子书，2025年上半年读书会书目">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Favicon Icons-->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="icon" type="image/png" href="favicon.png">
    <!-- Main Template Styles-->
    <link rel="stylesheet" media="screen" href="css/vendor.min.css">
    <link id="mainStyles" rel="stylesheet" media="screen" href="css/styles.min.css">
    <!-- Modernizr-->
    <script src="js/modernizr.min.js"></script>
    <style>
      @media (min-width: 576px){
        .card-deck .card {
          margin-right: 0px;
          margin-left: 0px;
        }
      }
      @media (max-width: 768px){
        .product-card.product-list .product-thumb {
          width:120px;
          padding:0px 0px;
        }
      }
      @media (max-width: 576px){
        .product-card.product-list .product-thumb {
          width:100%;
        }
      }
      .owl-carousel .owl-dots {
        margin-top: -50px;
      }
      .txt-1 {
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      .txt-2 {
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .txt-3 {
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .txt-4 {
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
      }

    </style>
  </head>
  <!-- Body-->
  <body>
    <!-- Off-Canvas Category Menu-->
    <div class="offcanvas-container" id="shop-categories">
      <div class="offcanvas-header">
        <h3 class="offcanvas-title text-white">网站导航</h3>
      </div>
      <nav class="offcanvas-menu">
        <ul class="menu">
          <li class="has-children"><span><a href="index.html">首页</a></span></li>
          <li class="has-children"><span><a href="shop-grid-ls.html">全部书目</a></span></li>
          <li class="has-children"><span><a href="#">简体书</a></span></li>
          <li class="has-children"><span><a href="#">繁体书</a></span></li>
          <li class="has-children"><span><a href="reader.html">阅读器下载</a></span></li>
        </ul>
      </nav>
    </div>
    <!-- 手持设备菜单栏，未登录状态 -->
    <div class="offcanvas-container" id="mobile-menu">
      <nav class="offcanvas-menu">
        <ul>
          <li><a href="account-login.html"><span class="text-lg">注册&nbsp;/&nbsp;登录</span></a></li>
        </ul>
      </nav>
      <a class="account-link" href="index.html">
        <div class="user-info">
          <h6 class="user-name">网站导航</h6>
        </div>
      </a>
      <nav class="offcanvas-menu">
        <ul class="menu">
          <li><span><a href="index.html"><span>首页</span></a></span></li>
          <li class="has-children"><span><a href="#"><span>全部书目</span></a></span></li>
          <li class="has-children"><span><a href="#"><span>简体书</span></a></span></li>
          <li class="has-children"><span><a href="#"><span>繁体书</span></a></span></li>
          <li class="has-children"><span><a href="reader.html">阅读器下载</a></span></li>
        </ul>
      </nav>
      <a class="account-link" href="index.html">
        <div class="user-info">
          <h6 class="user-name">货币种类</h6>
        </div>
      </a>
      <!-- 货币切换 -->
      <div class="column text-center padding-top-1x">
        <div class="switcher-wrap">
          <div class="switcher">
            <a class="text-lg" href="#">$ USD</a> &nbsp;&#124;&nbsp;<a class="text-lg" href="#">&nbsp;¥ CNY</a>
          </div>
        </div>
      </div>
      <a class="account-link" href="index.html">
        <div class="user-info">
          <h6 class="user-name">界面语言</h6>
        </div>
      </a>
      <!-- 语言切换 -->
      <div class="column text-center padding-top-1x padding-bottom-10x">
        <div class="switcher-wrap">
          <div class="switcher">
            <a class="text-lg" href="#">简体</a> &nbsp;&#124;&nbsp;<a class="text-lg" href="#">&nbsp;繁体</a>
          </div>
        </div>
      </div>
    </div>
    <!-- Topbar-->
    <div class="topbar"></div>
    <!-- Navbar-->
    <!-- Remove "navbar-sticky" 导航栏与页面一起滚动-->
    <header class="navbar navbar-sticky">
      <!-- Search-->
      <form class="site-search" method="get">
        <input type="text" name="site_search" placeholder="书名／作者">
        <div class="search-tools"><span class="clear-search">Clear</span><span class="close-search"><i class="icon-cross"></i></span></div>
      </form>
      <div class="site-branding">
        <div class="inner">
          <!-- Off-Canvas Toggle (#shop-categories)--><a class="offcanvas-toggle cats-toggle" href="#shop-categories" data-toggle="offcanvas"></a>
          <!-- Off-Canvas Toggle (#mobile-menu)--><a class="offcanvas-toggle menu-toggle" href="#mobile-menu" data-toggle="offcanvas"></a>
          <!-- Site Logo--><a class="site-logo hidden-md-down" href="index.html"><img src="img/logo/logo.png" alt="log"></a>
          <!-- Site Logo(mobilbe)--><a class="site-logo hidden-lg-up" href="index.html"><img src="img/logo/logo.png" alt="log"></a>
        </div>
      </div>
      <!-- 导航栏 -->
      <nav class="site-menu">
        <ul>
          <li><a href="index.html"><span>首页</span></a></li>
          <li><a href="shop-grid-ls.html"><span>全部书目</span></a>
            <ul class="sub-menu">
              <li><a href="#">读经工具</a></li>
              <li><a href="#">圣经注释</a></li>
              <li><a href="#">福音真理</a></li>
            </ul>
          </li>
          <li><a href="#"><span>简体书</span></a></li>
          <li><a href="#"><span>繁体书</span></a></li>
          <li><a href="reader.html"><span>阅读器下载</span></a></li>
          <li>
          <!-- 货币切换 -->
          <!-- <div class="lang-currency-switcher-wrap">
            <div class="lang-currency-switcher dropdown-toggle"><span class="currency">¥ CNY</span></div>
            <div class="dropdown-menu">
              <a class="dropdown-item" href="#">$ HKD</a>
            </div>
          </div> -->
          <!-- 货币与语言切换合并 -->
          <div class="lang-currency-switcher-wrap">
            <div class="lang-currency-switcher dropdown-toggle"><span class="language"><img alt="English" src="img/flags/CN.png"></span><span class="currency">¥ CNY</span></div>
            <div class="dropdown-menu">
              <div class="currency-select">
                <select class="form-control form-control-rounded form-control-sm">
                  <option value="usd">¥ CNY</option>
                  <option value="usd">$ HKD</option>
                </select>
              </div><a class="dropdown-item" href="#"><img src="img/flags/CN.png" alt="zh-CN">简体</a><a class="dropdown-item" href="#"><img src="img/flags/HK.png" alt="zh-HK">繁体</a>
            </div>
          </div>
          </li>
        </ul>
      </nav>
      <!-- Toolbar-->
      <div class="toolbar">
        <div class="inner">
          <div class="tools">
            <div class="search"><i class="icon-search"></i></div>
            <div class="account"><a href="account-login.html"></a><i class="icon-head"></i>
              <!-- 修改宽度支持英文 -->
              <ul class="toolbar-dropdown w-150">
                <li><a href="account-register.html">Sign up</a></li>
                <li><a href="account-login.html">Sign in</a></li>
              </ul>
            </div>
            <div class="cart"><a href="account-cart.html"></a><i class="icon-bag"></i><span class="count">2</span><span class="subtotal">¥100.00</span></div>
          </div>
        </div>
      </div>
    </header>
    <!-- 开始页面 -->
    <div class="offcanvas-wrapper">
      <section class="container">
        <div class="owl-carousel" data-owl-carousel="{ &quot;dots&quot;: true, &quot;loop&quot;: true, &quot;autoplay&quot;: true, &quot;data-wrap&quot;: true, &quot;autoplayTimeout&quot;: 4000 }">
          <figure><img src="https://d1.endao.cloud/root/tmp/reading/main-banner.jpg" alt="Image"></figure>
        </div>
      </section>



      <!-- 2025上半年读书会书目 开始 -->
      <section class="container padding-top-2x padding-bottom-3x">
        <h3 class="text-center padding-top-1x padding-bottom-1x">2025年上半年读书会书目<br></h3>
        <div class="row" id="contentContainer">
          <div class="col-md-12 col-lg-6" style="border: 0;" id="schedule1">
            <div class="product-card product-list card"><a class="product-thumb" href="https://ebook.endao.co/blog-132#">
              <div class="product-badge text-danger text-left"></div><div class="product-badge text-danger text-left" id="content1" style="display: none;"><img src="https://dl.endao.co/fire.png" alt="new" style="width: 24px; height: auto; margin-bottom: 6px;;"><span style="letter-spacing: 0px;">全球共读报名中</span></div><img src="https://d1.endao.cloud/root/tmp/book_image/detail/1629448805594.png" alt="Product"></a>
              <div class="product-info" style="vertical-align:top;">
                <h3 class="product-title"><a href="https://ebook.endao.co/blog-132#">人灵魂中上帝的生命</a></h3><br>
                <p>
                  <span class="txt-1">亨利·斯库格尔（Henry Scougal）</span>
                  <span class="d-inline">橡树文字工作室</span>
                </p>
                <p class="txt-4">什么是真正的基督信仰？亨利·斯库格尔（Henry Scougal）在书中这样写道：“真正的基督信仰是人的灵魂与上帝的联合，是真实地有份于上帝的性情，是灵命穿上上帝的形象；或如使徒保罗所言，是‘基督成形’在我们心里。” 愿在一年伊始，通过一同阅读这本情词恳切、鞭辟入里的小书，使我们更加渴慕上帝，在成圣的道路上越跑越坚定、越来越喜乐。</p>
                <div class="product-buttons">
                  <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="人灵魂中上帝的生命" data-toast-message="已成功加入收藏" data-toggle="tooltip" title="收藏"><i class="icon-heart"></i></button>
                  <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="人灵魂中上帝的生命" data-toast-message="成功添加到购物车" data-toggle="tooltip" title="加入购物车"><i class="icon-bag"></i></button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12 col-lg-6" style="border: 0;" id="schedule2">
            <div class="product-card product-list card"><a class="product-thumb" href="https://ebook.endao.co/blog-133#">
              <div class="product-badge text-danger text-left"></div><div class="product-badge text-danger text-left" id="content2" style="display: none;"><img src="https://dl.endao.co/fire.png" alt="new" style="width: 24px; height: auto; margin-bottom: 6px;;"><span style="letter-spacing: 0px;">全球共读报名中</span></div><img src="https://d1.endao.cloud/root/images/book_img/2405/IP163_web.jpg" alt="Product"></a>
              <div class="product-info" style="vertical-align:top;">
                <h3 class="product-title"><a href="https://ebook.endao.co/blog-133#">学习循环(简)</a></h3><br>
                <p>
                  <span class="txt-1">杜安·埃尔默（Duane H. Elmer） 穆莉尔·埃尔默（Muriel I. Elmer）</span>
                  <span class="d-inline">恩道出版社</span>
                </p>
                <p class="txt-4">《学习循环》是为教育工作者而写，但又不局限于这一个群体。事实上所有想要促进更好的认知理解、行为改变和基督徒生命成长的人都可以来阅读。在这本书中，资深教育家杜安‧埃尔默和穆莉尔‧埃尔默夫妇提出了学习循环的模型。</p>
                <div class="product-buttons">
                  <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="学习循环(简)" data-toast-message="已成功加入收藏" data-toggle="tooltip" title="收藏"><i class="icon-heart"></i></button>
                  <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="学习循环(简)" data-toast-message="成功添加到购物车" data-toggle="tooltip" title="加入购物车"><i class="icon-bag"></i></button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12 col-lg-6" style="border: 0;" id="schedule3">
            <div class="product-card product-list card"><a class="product-thumb" href="https://ebook.endao.co/blog-134#">
              <div class="product-badge text-danger text-left"></div><div class="product-badge text-danger text-left" id="content3" style="display: none;"><img src="https://dl.endao.co/fire.png" alt="new" style="width: 24px; height: auto; margin-bottom: 6px;;"><span style="letter-spacing: 0px;">全球共读报名中</span></div><img src="https://d1.endao.cloud/root/images/book_img/2411/IP197_web.jpg" alt="Product"></a>
              <div class="product-info" style="vertical-align:top;">
                <h3 class="product-title"><a href="https://ebook.endao.co/blog-134#">复活的救主(简)</a></h3><br>
                <p>
                  <span class="txt-1">库马赫（F. W. Krummacher）</span>
                  <span class="d-inline">恩道出版社</span>
                </p>
                <p class="txt-4">使徒保罗大声宣告说：「若基督没有复活……你们所信的也是枉然。」（林前15:14）在论及基督复活后四十日的作品中，《复活的救主》是极为罕见的。本书探讨的是我们在这地上所能默想的最崇高和最令人愉悦之主题。不论这见证是以何等微弱的形式呈现出来，唯愿主的灵感动许多的心灵，确认这见证的真实！复活节将至，恩道邀请您共同阅读此书，一起默想主的复活，使信心日益坚固。</p>
                <div class="product-buttons">
                  <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="复活的救主(简)" data-toast-message="已成功加入收藏" data-toggle="tooltip" title="收藏"><i class="icon-heart"></i></button>
                  <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="复活的救主(简)" data-toast-message="成功添加到购物车" data-toggle="tooltip" title="加入购物车"><i class="icon-bag"></i></button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12 col-lg-6" style="border: 0;" id="schedule4">
            <div class="product-card product-list card"><a class="product-thumb" href="https://ebook.endao.co/blog-135#">
              <div class="product-badge text-danger text-left"></div><div class="product-badge text-danger text-left" id="content4" style="display: none;"><img src="https://dl.endao.co/fire.png" alt="new" style="width: 24px; height: auto; margin-bottom: 6px;;"><span style="letter-spacing: 0px;">全球共读报名中</span></div><img src="https://d1.endao.cloud/root/images/book_img/2405/IP181_web.jpg" alt="Product"></a>
              <div class="product-info" style="vertical-align:top;">
                <h3 class="product-title"><a href="https://ebook.endao.co/blog-135#">保罗和他转变生命的神学(简)</a></h3><br>
                <p>
                  <span class="txt-1">罗杰·莫朗（Roger Mohrlang）</span>
                  <span class="d-inline">恩道出版社</span>
                </p>
                <p class="txt-4">凡是基督徒，没有不知道保罗的。但你是否真的了解保罗？本书简要介绍了保罗的生活、思想和他受托向全世界宣扬的奇妙福音。作者莫朗博士基于多年的教学和研究成果，言简意深地勾勒出保罗竭诚尽忠的一生，缕述他丰富精深的神学思想，充分展现了福音转变生命的力量。本书在学术上富有启发，又为个人生命带来反思和挑战，切盼与您一起共读此书，更深认识保罗，亲历他心中的火焰，生命得以更新和坚固。</p>
                <div class="product-buttons">
                  <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="保罗和他转变生命的神学(简)" data-toast-message="已成功加入收藏" data-toggle="tooltip" title="收藏"><i class="icon-heart"></i></button>
                  <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="保罗和他转变生命的神学(简)" data-toast-message="成功添加到购物车" data-toggle="tooltip" title="加入购物车"><i class="icon-bag"></i></button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <script>
          document.addEventListener('DOMContentLoaded', function() {
            const currentDate = new Date();
            const contentContainer = document.getElementById('contentContainer');

            // 定义多个日期范围和对应的内容
            const dateRanges = [
              {
                startDate: new Date('2024-12-23'),
                endDate: new Date('2025-01-05'),
                contentId: 'content1',
                scheduleId: 'schedule1'
              },
              {
                startDate: new Date('2025-01-27'),
                endDate: new Date('2025-02-16'),
                contentId: 'content2',
                scheduleId: 'schedule2'
              },
              {
                startDate: new Date('2025-03-17'),
                endDate: new Date('2025-03-30'),
                contentId: 'content3',
                scheduleId: 'schedule3'
              },
              {
                startDate: new Date('2025-05-12'),
                endDate: new Date('2025-05-25'),
                contentId: 'content4',
                scheduleId: 'schedule4'
              }
            ];
            dateRanges.forEach(function(range) {
                const contentDiv = document.getElementById(range.contentId);
                const colDiv = document.getElementById(range.scheduleId);
                if (currentDate >= range.startDate && currentDate <= range.endDate) {
                    contentDiv.style.display = 'block';
                    // 将显示的 div 移动到容器的开始位置
                    contentContainer.prepend(colDiv);
                }
            });
          });

        </script>
      </section>
      <!-- Site Footer-->
      <footer class="site-footer-1">
        <div class="container">
          <div class="row padding-bottom-3x">
            <div class="col-lg-10 col-md-10" style="margin-left: 20px;">
              <span class="d-inline"><a class="navi-link" href="https://ebook.endao.co/blog-122#"> 往期读书会 &nbsp; | &nbsp;</a></span>
              <!-- <span class="d-inline"><a class="navi-link" href="#"> 读书点滴  &nbsp; | &nbsp;</a></span> -->
              <span class="d-inline"><a class="navi-link" href="https://ebook.endao.co/blog-103#"> 关于“读书伙伴”  &nbsp; | &nbsp;</a></span>
              <span class="d-inline"><a class="navi-link" href="https://ebook.endao.co/index-Reader"> 电子书App </a></span>
            </div>
          </div>
          <!-- Copyright-->
          <!-- <p class="footer-copyright">Copyright ©恩道电子书 All Rights Reserved</p> -->
        </div>
      </footer>

      <!-- 2024上半年读书会书目 结束 -->



    </div>
    <!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
    <script src="js/vendor.min.js"></script>
    <script src="js/scripts.min.js"></script>
  </body>
</html>
