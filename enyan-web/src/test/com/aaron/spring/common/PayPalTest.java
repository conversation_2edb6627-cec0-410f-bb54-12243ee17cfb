package com.aaron.spring.common;

import com.aaron.a4j.util.AaronDateUtils;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.util.DateUtil;
import com.paypal.http.HttpResponse;
import com.paypal.http.serializer.Json;
import com.paypal.orders.*;
//import com.paypal.PayPalClient;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.FastDateFormat;
//import org.json.JSONObject;
import org.junit.Test;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * https://github.com/paypal/Checkout-Java-SDK
 * https://developer.paypal.com/demo/checkout/#/pattern/server
 * https://developer.paypal.com/demo/checkout/#/pattern/client
 * https://www.jianshu.com/p/6e127af5f50a
 * http://www.codebaoku.com/it-java/it-java-232482.html
 *
 * @Date: Created in  2018/5/30
 * @Modified By:
 */
public class PayPalTest {
    @Test
    public void testWeek(){
        int weekTime = 201817;
        Calendar calendar = Calendar.getInstance();
        //calendar.setTimeZone(TimeZone.getTimeZone("Europe/Paris"));
        int year = weekTime/100;
        int week = weekTime%100;
        System.out.println(year+":"+week);
        calendar.setWeekDate(weekTime/100,weekTime%100,Calendar.MONDAY);
        //calendar.setFirstDayOfWeek(Calendar.MONDAY);
        System.out.println(DateFormatUtils.format(calendar.getTime(),"yyyyMMdd"));

        calendar.add(Calendar.DAY_OF_MONTH,6);
        System.out.println(DateFormatUtils.format(calendar.getTime(),"yyyyMMdd"));
    }

    /**
     * Method to create minimum required order body with <b>AUTHORIZE</b> intent
     *
     * @return OrderRequest with created order request
     */
    private OrderRequest buildMinimumRequestBody() {
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.checkoutPaymentIntent("AUTHORIZE");
        ApplicationContext applicationContext = new ApplicationContext()
                .cancelUrl("https://www.example.com").returnUrl("https://www.example.com");
        orderRequest.applicationContext(applicationContext);
        List<PurchaseUnitRequest> purchaseUnitRequests = new ArrayList<>();
        PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest()
                .amountWithBreakdown(new AmountWithBreakdown().currencyCode("USD").value("220.00"));
        purchaseUnitRequests.add(purchaseUnitRequest);
        orderRequest.purchaseUnits(purchaseUnitRequests);
        return orderRequest;
    }

    /**
     * Method to create order with minimum required payload
     *
     * @param debug true = print response data
     * @return HttpResponse<Order> response received from API
     * @throws IOException Exceptions from API if any
     */
    /*
    public HttpResponse<Order> createOrderWithMinimumPayload(boolean debug) throws IOException {
        OrdersCreateRequest request = new OrdersCreateRequest();
        request.header("prefer","return=representation");
        request.requestBody(buildMinimumRequestBody());
//        HttpResponse<Order> response = client().execute(request);
        // Call API with your client and get a response for your call
        HttpResponse<Order> response = Credentials.client.execute(request);
        if (debug) {
            if (response.statusCode() == 201) {
                System.out.println("Order with Minimum Payload: ");
                System.out.println("Status Code: " + response.statusCode());
                System.out.println("Status: " + response.result().status());
                System.out.println("Order ID: " + response.result().id());
                System.out.println("Intent: " + response.result().checkoutPaymentIntent());
                System.out.println("Links: ");
                for (LinkDescription link : response.result().links()) {
                    System.out.println("\t" + link.rel() + ": " + link.href() + "\tCall Type: " + link.method());
                }
                System.out.println("Total Amount: " + response.result().purchaseUnits().get(0).amountWithBreakdown().currencyCode()
                                           + " " + response.result().purchaseUnits().get(0).amountWithBreakdown().value());
                System.out.println("Full response body:");
                System.out.println(new JSONObject(new Json().serialize(response.result())).toString(4));
            }
        }
        return response;
    }*/

}
/*
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.OAuthTokenCredential;
import com.paypal.base.rest.PayPalRESTException;

@Configuration
public class PaypalConfig {

    @Value("${paypal.client.app}")
    private String clientId;
    @Value("${paypal.client.secret}")
    private String clientSecret;
    @Value("${paypal.mode}")
    private String mode;

    @Bean
    public Map<String, String> paypalSdkConfig(){
        Map<String, String> sdkConfig = new HashMap<>();
        sdkConfig.put("mode", mode);
        return sdkConfig;
    }

    @Bean
    public OAuthTokenCredential authTokenCredential(){
        return new OAuthTokenCredential(clientId, clientSecret, paypalSdkConfig());
    }

    @Bean
    public APIContext apiContext() throws PayPalRESTException{
        APIContext apiContext = new APIContext(authTokenCredential().getAccessToken());
        apiContext.setConfigurationMap(paypalSdkConfig());
        return apiContext;
    }
}
* */