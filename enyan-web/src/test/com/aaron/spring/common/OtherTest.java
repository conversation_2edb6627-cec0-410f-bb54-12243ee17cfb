package com.aaron.spring.common;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.aaron.spring.common.EBookConstant.RentConstant.*;

/**
 *
 *
 * @Date: Created in  2018/3/9
 * @Modified By:
 */
public class OtherTest {
    @Test
    public void testEmailReplace(){
        String email = "<EMAIL>";
        email = "<EMAIL>";
        Pattern p = Pattern.compile("(\\w{1})(\\w+)(\\w{1})(@\\w+)");
        Matcher m = p.matcher(email);
        System.out.println(m.replaceAll("$1...$3$4"));
    }
    @Test
    public void testTime(){
        Date dt = new Date();

        //dt.getTime()

        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

        dateFormatter.setTimeZone(TimeZone.getTimeZone("GMT"));

        String strUTCDate = dateFormatter.format(dt);

        System.out.println(strUTCDate);


        System.out.println(DateFormatUtils.formatUTC(new Date(),"yyyy-MM-dd hh:mm:ss"));

        Calendar calendar = DateUtils.toCalendar(new Date(), TimeZone.getTimeZone("UTC"));
        System.out.println(DateFormatUtils.format(new Date(),"yyyy-MM-dd hh:mm:ss",TimeZone.getTimeZone("GMT"),null));

        Date date = new Date(1527064681405L);
        System.out.println("time:"+DateFormatUtils.format(date,"yyyy-MM-dd HH:mm:ss"));
    }
    @Test
    public void testEmail(){
        if (Constant.TEST_ACCOUNT.contains("<EMAIL>")){
            System.out.println("pass");
        }else{
            System.out.println("pass not");
        }
    }

    @Test
    public void testArrayList(){
        List<Long> list = new ArrayList<>();
        list.add(1L);
        list.add(2L);
        list.add(3L);
        list.add(4L);

        String out = Arrays.toString(list.toArray());
        System.out.println(out);
        //String.join(",",list);

        List names=new ArrayList<String>();
        names.add("1");
        names.add("2");
        names.add("3");
        System.out.println(String.join(",", names));
    }

    @Test
    public void testArrayMapList(){
        List<Long> list = new ArrayList<>();
        list.add(1L);
        list.add(2L);
        list.add(3L);
        list.add(4L);

        String out = Arrays.toString(list.toArray());
        System.out.println(out);
        //String.join(",",list);

        List names=new ArrayList<String>();
        names.add("1");
        names.add("2");
        names.add("3");
        System.out.println(String.join(",", names));
        //list.stream().toArray()
    }

    @Test
    public void testLength(){
        String text = "思想觀念是思想世界內的存貨，從中引發出外在的表觀：繪畫、音樂、建築，對人的愛恨，以及對上帝敬愛或悖逆的種種表現……上帝藉著聖經將福音啟示給我們一樣。福音不是毫無內容的內在領受的經驗而已，而是將有內容的思想由內而外作用出來，這二者大不相同。因此，我們說明我們的教義時，必須傳講整體的思想，不是只用片片斷斷的言語來表達；我們不能把教義看成是拼圖遊戲裡的機械化圖片。真正的教義是上帝在聖經中所啟示出來的理念，適用於上帝所創造的";
        System.out.println("text:"+text.length());
    }

    @Test
    public void testPrintCount(){
        for (int i = 1; i < 37; i++) {
            System.out.println("http://www.fuyin.fm/content/download/movid/1/urlid/"+i+"/type/mp3");
        }
    }

    @Test
    public void testSwitch(){
        String sw = null;
        switch (sw){
            case "1":
                System.out.println("1");
                break;
            case "2":
                System.out.println("2");
                break;
        }
    }

    @Test
    public void testRent(){
        //System.out.println(String.join("," , CBCS_OT_BOOKS_Hans));
        //System.out.println(CBCS_OT_BOOKS_Hans);
        printLongToString(CBCS_OT_BOOKS_Hans);
        printLongToString(CBCS_OT_BOOKS_Hant);
        printLongToString(CBCS_NT_BOOKS_Hans);
        printLongToString(CBCS_NT_BOOKS_Hant);
    }

    @Test
    public void testNumber(){
        for (int i = 0; i < 31; i++) {
            System.out.print("【"+(i+1)+"】");
        }
    }

    private void printLongToString(List<Long> array){
        //List<String> newArray = array.stream().map(value -> String.valueOf(value)).collect(Collectors.toList());
        //System.out.println(newArray);

        for (int i = 0; i < array.size(); i++) {
            if (i != 0){
                System.out.print(", ");
            }
            System.out.print("\"");
            System.out.print(array.get(i));
            System.out.print("\"");
        }
        System.out.println();
    }
}
