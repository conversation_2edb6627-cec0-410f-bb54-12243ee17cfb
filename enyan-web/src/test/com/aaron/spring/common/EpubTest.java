package com.aaron.spring.common;

import co.endao.util.SpiritBookUtil;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  11/18/20
 * @Modified By:
 */
public class EpubTest {
    public static void main(String[] args) {
        //SingletonDemo.Companion.get().ok();
        SpiritBookUtil.Companion.get().ok();

        String fileName = "为了上帝-201205";
        fileName = "马太·亨利传";
        fileName = "信心加油站-简-加引用-201117";

        String epubPath = "/Users/<USER>/Documents/Test/epub/"+fileName+".epub";
        String destDir = "/Users/<USER>/Documents/Test/epub/"+fileName+"-灵修";
        String cssDir = "/Users/<USER>/Documents/Test/epub/"+fileName+"-css";

        SpiritBookUtil.Companion.get().decompressBookByPath(epubPath,destDir,cssDir);

        String dayPath = SpiritBookUtil.Companion.get().getPathForDayByPath(destDir,0);
        System.out.println("dayPath:"+dayPath);
    }
}
