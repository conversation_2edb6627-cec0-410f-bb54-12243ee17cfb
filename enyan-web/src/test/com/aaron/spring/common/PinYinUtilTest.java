package com.aaron.spring.common;

import com.github.houbb.pinyin.constant.enums.PinyinStyleEnum;
import com.github.houbb.pinyin.util.PinyinHelper;
import org.junit.Test;

/**
 *
 *
 * @Date: Created in  2018/1/17
 * @Modified By:
 */
public class PinYinUtilTest {

    @Test
    public void covertToPinYinString() {
        String chinese = "测试以下";
        String pinYin = PinYinUtil.covertToPinYinString(chinese);

        System.out.println(chinese+":"+pinYin);

        chinese = "测试以下11";
        pinYin = PinYinUtil.covertToPinYinString(chinese);

        System.out.println(chinese+":"+pinYin);

        chinese = "1111";
        pinYin = PinYinUtil.covertToPinYinString(chinese);

        System.out.println(chinese+":"+pinYin);

        chinese = "english hello world   hhh";
        pinYin = PinYinUtil.covertToPinYinString(chinese);

        System.out.println(chinese+":"+pinYin);
    }

    @Test
    public void covertToPinYinStringNew() {
        String chinese = "测试以下";
        String pinYin = PinyinHelper.toPinyin(chinese);

        System.out.println(chinese+":"+pinYin);

        chinese = "测试以下11";
        pinYin = PinyinHelper.toPinyin(chinese);

        System.out.println(chinese+":"+pinYin);

        chinese = "1111";
        pinYin = PinyinHelper.toPinyin(chinese);

        System.out.println(chinese+":"+pinYin);

        chinese = "english hello world   hhh";
        pinYin = PinyinHelper.toPinyin(chinese);

        System.out.println(chinese+":"+pinYin);
    }

    @Test
    public void covertToPinYinStringNew2() {
        String chinese = "测试测试女绿以下";
        String pinYin = PinyinHelper.toPinyin(chinese, PinyinStyleEnum.NORMAL);

        System.out.println(chinese+":"+pinYin);

        chinese = "测试测试女绿以下11";
        pinYin = PinyinHelper.toPinyin(chinese, PinyinStyleEnum.DEFAULT);

        System.out.println(chinese+":"+pinYin);

        chinese = "1111";
        pinYin = PinyinHelper.toPinyin(chinese, PinyinStyleEnum.NUM_LAST);

        System.out.println(chinese+":"+pinYin);

        chinese = "english hello world   hhh";
        pinYin = PinyinHelper.toPinyin(chinese, PinyinStyleEnum.FIRST_LETTER);

        System.out.println(chinese+":"+pinYin);

        chinese = "测试女绿11什么11什麽22重尋福音(繁)什麽[恩典·福音叢書]english hello world   hhh";
        pinYin = PinyinHelper.toPinyin(chinese, PinyinStyleEnum.INPUT,"-");
        System.out.println(chinese+":"+pinYin);
        System.out.println("length:"+pinYin.length());

        chinese = "什麽";
        pinYin = PinyinHelper.toPinyin(chinese, PinyinStyleEnum.INPUT,"-");
        System.out.println(chinese+":"+pinYin);
        System.out.println("length:"+pinYin.length());

        chinese = "自我成長GPS：一段由自覺到轉念的心路歷程 一個化荊蕀為果實的展翅人生 = Integrated self-optimizing process(繁)";
        pinYin = PinyinHelper.toPinyin(chinese, PinyinStyleEnum.INPUT,"-");
        System.out.println(chinese+":"+pinYin);
        System.out.println("length:"+chinese.length()+","+pinYin.length());
    }
}