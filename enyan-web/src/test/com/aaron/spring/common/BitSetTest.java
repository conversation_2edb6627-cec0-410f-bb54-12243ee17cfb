package com.aaron.spring.common;

import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.BitSet;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2020-10-29
 * @Modified By:
 */
public class BitSetTest {
    @Test
    public void testBitSetArray(){
        //int[] array = Arrays.asList(strings).stream().mapToInt(Integer::parseInt).toArray();
        //int[] array = Arrays.stream(strings).mapToInt(Integer::parseInt).toArray();

        BitSet sets = new BitSet(31);
        sets.set(0);
        //sets.set(2);

        for (int i = 0; i < 17; i++) {
            sets.set(i);
        }

        List<String> stringList = new ArrayList<>();
        for (int i = 0; i < sets.length(); i++) {
            System.out.println(sets.get(i));
            if (sets.get(i)){
                stringList.add(String.valueOf(i));
            }
        }
        //System.out.println(sets.toByteArray());
        System.out.println(BitSetUtil.encode(sets.toByteArray()));
        String joinString = String.join(",",stringList);
        System.out.println("joinString="+joinString);
    }

    @Test
    public void testBitSetFromString(){
        //int[] array = Arrays.asList(strings).stream().mapToInt(Integer::parseInt).toArray();
        //int[] array = Arrays.stream(strings).mapToInt(Integer::parseInt).toArray();

        BitSet sets = BitSetUtil.decode("//8B");

        this.printBitsets(sets);

        byte[] bytes = sets.toByteArray();

        BitSet bitSet = BitSet.valueOf(bytes);

        this.printBitsets(bitSet);
    }

    public void printBitsets(BitSet sets){
        List<String> stringList = new ArrayList<>();
        for (int i = 0; i < sets.length(); i++) {
            System.out.println(sets.get(i));
            if (sets.get(i)){
                stringList.add(String.valueOf(i));
            }
        }
        //System.out.println(sets.toByteArray());
        System.out.println(BitSetUtil.encode(sets.toByteArray()));
        String joinString = String.join(",",stringList);
        System.out.println("joinString="+joinString);
    }

    public static  void main(String[] args) throws UnsupportedEncodingException {
        final Base64.Decoder decoder = Base64.getDecoder();
        final Base64.Encoder encoder = Base64.getEncoder();

        BitSet sets = new BitSet(31);
        sets.set(0);
        sets.set(2);

        for (int i = 0; i < 500; i++) {
            sets.set(i);
        }
        //final String encodeToString = Base64.getEncoder().encodeToString(sets.toByteArray());

        //编码
        String encodedText = encoder.encodeToString(sets.toByteArray());
        System.out.println("BitSetTest.main");
        if ("////////////////////////////////////////////////////////////Hw==".equals(encodedText)){
            System.out.println("is equal:"+encodedText.length());
        }
        System.out.println(encodedText);
        System.out.println("lengh:"+encodedText.length());
        //System.out.println(encoder.encodeToString(sets.toString().getBytes()));


        //解码
        //System.out.println(new String(decoder.decode(encodedText), "UTF-8"));

        encodedText = "////////////////////////////////////////////////////////////Hw\\u003d\\u003d";
        BitSet newSets = BitSet.valueOf(decoder.decode(encodedText));
        System.out.println("newSets="+newSets);

        //newSets.toByteArray()

    }
}
