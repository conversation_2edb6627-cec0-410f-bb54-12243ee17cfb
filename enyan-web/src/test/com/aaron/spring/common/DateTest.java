package com.aaron.spring.common;

import com.aaron.a4j.util.AaronDateUtils;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.util.DateUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.junit.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 *
 *
 * @Date: Created in  2018/5/30
 * @Modified By:
 */
public class DateTest {

    @Test
    public void testGetNow(){
        System.out.println("date="+System.currentTimeMillis());
    }

    @Test
    public void testFromDate() throws ParseException {

        FastDateFormat dateFormat = FastDateFormat.getInstance(InterfaceContant.DateFormatCustom.DATETIME, TimeZone.getTimeZone("GMT+8"));
        Date currentDate3 = dateFormat.parse("2022-11-06 11:44:01");
        System.out.println(currentDate3);
        System.out.println(currentDate3.getTime());

        Date currentDate33 = dateFormat.parse("2023-02-20 11:44:01");
        System.out.println(currentDate33);
        System.out.println(currentDate33.getTime());


        FastDateFormat dateFormat2 = FastDateFormat.getInstance(InterfaceContant.DateFormatCustom.DATETIME, TimeZone.getTimeZone("GMT-8"));
        Date currentDate4 = dateFormat2.parse("2023-02-20 11:44:01");
        System.out.println(currentDate4);
        System.out.println(currentDate4.getTime());
    }

    @Test
    public void testWeek(){
        int weekTime = 201817;
        Calendar calendar = Calendar.getInstance();
        //calendar.setTimeZone(TimeZone.getTimeZone("Europe/Paris"));
        int year = weekTime/100;
        int week = weekTime%100;
        System.out.println(year+":"+week);
        calendar.setWeekDate(weekTime/100,weekTime%100,Calendar.MONDAY);
        //calendar.setFirstDayOfWeek(Calendar.MONDAY);
        System.out.println(DateFormatUtils.format(calendar.getTime(),"yyyyMMdd"));

        calendar.add(Calendar.DAY_OF_MONTH,6);
        System.out.println(DateFormatUtils.format(calendar.getTime(),"yyyyMMdd"));
    }

    @Test
    public void testWeek2(){
        int[] intReturn = AaronDateUtils.getWeekInfo(201817);
        System.out.println(intReturn[0]);
        System.out.println(intReturn[1]);
    }
    @Test
    public void testLastDay(){
        System.out.println(DateUtil.getLastDayThisMonth(new Date()));

        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.add(Calendar.MONTH, 1);
        c.add(Calendar.DAY_OF_MONTH,-1);

        Date newDate = c.getTime();
        System.out.println(newDate);
    }

    @Test
    public void testTime(){
        Date currentDate = new Date(1661174472410L);

        System.out.println(DateFormatUtils.format(currentDate,"yyyy-MM-dd HH:mm:ss"));
    }

    @Test
    public void testTimeYesterday(){
        Date currentDate = new Date();

        Calendar c = Calendar.getInstance();
        c.setTime(currentDate);
//        c.set(Calendar.DAY_OF_MONTH, 1);
//        c.add(Calendar.MONTH, 1);
        c.add(Calendar.DAY_OF_MONTH,-1);

        Date newDate = c.getTime();
        System.out.println(newDate);
        Date yesterdayDate = c.getTime();

        System.out.println("now="+currentDate.getTime());
        System.out.println("yesterday="+yesterdayDate.getTime());
    }

    @Test
    public void testTimeYesterday2(){
        Date currentDate = new Date();

        Date yesterdayDate = DateUtils.addDays(currentDate, -1);

        System.out.println("now="+currentDate.getTime());
        System.out.println("yesterday="+yesterdayDate.getTime());
    }

    @Test
    public void testGetWeekend(){
        int year = 2021;
        List<String> dateList=new ArrayList<>();
        SimpleDateFormat simdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = new GregorianCalendar(year, 0, 1);
        int i = 1;
        while (calendar.get(Calendar.YEAR) < year + 1) {
            calendar.set(Calendar.WEEK_OF_YEAR, i++);
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
            if (calendar.get(Calendar.YEAR) == year) {
                //System.out.println("周日："+simdf.format(calendar.getTime()));
                System.out.println(simdf.format(calendar.getTime()));
                dateList.add(simdf.format(calendar.getTime()));
            }
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
            if (calendar.get(Calendar.YEAR) == year) {
                //System.out.println("周六："+simdf.format(calendar.getTime()));
                //System.out.println(simdf.format(calendar.getTime()));
                dateList.add(simdf.format(calendar.getTime()));
            }
        }

        System.out.println(dateList.size());
    }

    @Test
    public void testLastMonth(){
//        Date date = DateUtils.setMonths(new Date(), -1);
        Date date = DateUtils.addMonths(new Date(), -12);
        System.out.println("date="+date);
    }

    @Test
    public void testLastMonthAdd() throws ParseException {
//        Date date = DateUtils.setMonths(new Date(), -1);
        Date date = DateUtils.parseDate("2022-01-31","yyyy-MM-dd");
        Date newDate = DateUtils.addMonths(date, 1);
        System.out.println("date="+date+",newDate="+newDate);
    }

    @Test
    public void testMonthBetweenDate() throws ParseException {
        LocalDate date1 = LocalDate.parse("2016-08-30");//2016-08-31
        LocalDate date2 = LocalDate.parse("2016-11-30");//2016-11-30

        long monthsBetween = ChronoUnit.MONTHS.between(
                date1,
                date2);
        if (date1.isBefore(date2)
                    && date2.getDayOfMonth() == date2.lengthOfMonth()
                    && date1.getDayOfMonth() > date2.getDayOfMonth()) {
            monthsBetween += 1;
        }
        System.out.println("months between:"+monthsBetween);
    }

    @Test
    public void testMonthBetweenDate2() throws ParseException {
        LocalDate date1 = LocalDate.parse("2016-08-30");//2016-08-31
        LocalDate date2 = LocalDate.parse("2016-11-30");//2016-11-30


        System.out.println("months between:"+DateUtil.betweenMonths("2022-08-01","2022-09-02"));
    }

    @Test
    public void testSecondToHour(){
        long seconds = TimeUnit.MINUTES.toSeconds(8);
        System.out.println("second="+seconds);

        long hour = TimeUnit.SECONDS.toHours(5400);
        long minutes = TimeUnit.SECONDS.toMinutes(5400);
        System.out.println("hour="+hour+",minutes="+minutes);

        System.out.println(convertSeconds(5400));

        System.out.println(convertSecondsNew(5400));
    }

    public static String convertSeconds(int seconds) {
        int h = seconds/ 3600;
        int m = (seconds % 3600) / 60;
        int s = seconds % 60;
        String sh = (h > 0 ? String.valueOf(h) + " " + "小时" : "");
        String sm = (m < 10 && m > 0 && h > 0 ? "0" : "") + (m > 0 ? (h > 0 && s == 0 ? String.valueOf(m) + " " + "分" : String.valueOf(m) + " " + "分") : "");
        String ss = (s == 0 && (h > 0 || m > 0) ? "" : (s < 10 && (h > 0 || m > 0) ? "0" : "") + String.valueOf(s) + " " + "秒");
        return sh + (h > 0 ? " " : "") + sm + (m > 0 ? " " : "") + ss;
    }

    public static String convertSecondsNew(int seconds) {
        int h = seconds/ 3600;
        int m = (seconds % 3600) / 60;
        int s = seconds % 60;
        String sh = (h > 0 ? String.valueOf(h) + "" + "小时" : "");
        String sm = (m < 10 && m > 0 && h > 0 ? "0" : "") + (m > 0 ? (h > 0 && s == 0 ? String.valueOf(m) + "" + "分" : String.valueOf(m) + "" + "分") : "");
        String ss = (s == 0 && (h > 0 || m > 0) ? "" : (s < 10 && (h > 0 || m > 0) ? "0" : "") + String.valueOf(s) + "" + "秒");
        return sh + (h > 0 ? "" : "") + sm + (m > 0 ? "" : "") + ss;
    }
}
