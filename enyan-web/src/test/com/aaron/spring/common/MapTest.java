package com.aaron.spring.common;

import com.aaron.a4j.util.AaronDateUtils;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.util.DateUtil;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.google.common.collect.TreeRangeMap;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.junit.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 *
 * @Date: Created in  2018/5/30
 * @Modified By:
 */
public class MapTest {
    @Test
    public void testMap(){
        String str ="SALES:0,SALE_PRODUCTS:1,EXPENSES:2,EXPENSES_ITEMS:3";
        HashMap<String, Integer> map = (HashMap<String, Integer>) Arrays.asList(str.split(",")).stream().map(s -> s.split(":")).collect(Collectors.toMap(e -> e[0], e -> Integer.parseInt(e[1])));
        System.out.println("MapTest.testMap="+map);
    }

    @Test
    public void testMap2(){
        String str ="SALES:0,SALE_PRODUCTS:1,EXPENSES:2,EXPENSES_ITEMS:3";
        HashMap<String, String> map = (HashMap<String, String>) Arrays.asList(str.split(",")).stream().map(s -> s.split(":")).collect(Collectors.toMap(e -> e[0], e -> e[1]));
        System.out.println("MapTest.testMap2="+map);
    }

    @Test
    public void testMap3(){
        String keySeperator = "=";
        String split = "&";
        String str ="SALES=0&SALE_PRODUCTS=1&EXPENSES=2&EXPENSES_ITEMS=3";
        HashMap<String, String> map = this.getSplitMap(str,keySeperator,split);
        System.out.println("MapTest.testMap3="+map);
    }
    @Test
    public void testMap4(){
        String keySeperator = "=";
        String split = ",";
        String str ="SALES=0,SALE_PRODUCTS=1,EXPENSES=2,EXPENSES_ITEMS=3";
        HashMap<String, String> map = this.getSplitMap(str,keySeperator,split);
        System.out.println("MapTest.testMap3="+map);
    }

    /**
     * <p>测试TreeMap根据 value排序</p>
     * @param
     * @return void
     * @since : 2023/5/26
     **/
    @Test
    public void testTreeMap(){
        TreeMap<String,Integer> map = new TreeMap<>();
        for (int i = 0; i < 6; i++) {
            map.put("数据"+i, (int) (RandomUtils.nextFloat(0, 1f)*10000));
        }
        System.out.println(map);

    }

    public HashMap<String,String> getSplitMap(String mapString, String keySplit, String split){
        HashMap<String, String> map = (HashMap<String, String>) Arrays.asList(mapString.split(split))
                .stream().map(s -> s.split(keySplit)).collect(Collectors.toMap(e -> e[0], e -> e[1]));
        return map;
    }

    /*
    // 使用 Guava 的 `TreeMap` 按值对地图进行排序的通用方法
    public static <K, V> TreeMap<K, V> sortByValues(Map<K, V> unsortedMap) {
        Comparator<K> comparator = new CustomComparator(unsortedMap);

        TreeMap<K, V> sortedMap = Maps.newTreeMap(comparator);
        sortedMap.putAll(unsortedMap);

        return sortedMap;
    }*/

}
