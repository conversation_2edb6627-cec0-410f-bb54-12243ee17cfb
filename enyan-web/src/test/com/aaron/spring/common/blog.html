<html lang="zh-cn">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="referrer" content="always">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>博客园 - 开发者的网上家园</title>
  <meta name="keywords"
    content="开发者,程序员,博客园,程序猿,程序媛,极客,码农,编程,代码,软件开发,开源,IT网站,技术社区,Developer,Programmer,Coder,Geek,Coding,Code">
  <meta name="description"
    content="博客园是一个面向开发者的知识分享社区。自创建以来，博客园一直致力并专注于为开发者打造一个纯净的技术交流社区，推动并帮助开发者通过互联网分享知识，从而让更多开发者从中受益。博客园的使命是帮助开发者用代码改变世界。">
  <link rel="shortcut icon" href="//common.cnblogs.com/favicon.svg" type="image/svg+xml">
  <link rel="Stylesheet" type="text/css" href="/css/aggsite-new.min.css?v=3CU8x1SwBi1eDU86rC4-mWkqo00GgzTcatqPJp6EVzY">
  <link rel="Stylesheet" type="text/css"
    href="/css/aggsite-mobile-new.min.css?v=jDLK6Zr8sphvw3TH3CpklHbTI5mF2K56Sbnsvq5_Gng"
    media="only screen and (max-width: 767px)">
  <link id="RSSLink" title="RSS" type="application/rss+xml" rel="alternate"
    href="http://feed.cnblogs.com/blog/sitehome/rss">
  <script src="https://www.googletagservices.com/activeview/js/current/osd.js?cb=%2Fr20100101"></script>
  <script async="" src="https://www.google-analytics.com/analytics.js"></script>
  <script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>
  <script src="/js/aggsite-new.min.js?v=C0Qm5c7a_ohaMgAxYVm7T_fwbGdOjQyC8JrSCw8ks-M"></script>
  <script async="async" src="https://www.googletagservices.com/tag/js/gpt.js"></script>
  <script>
    var googletag = googletag || {};
    googletag.cmd = googletag.cmd || [];
  </script>
  <script>
    googletag.cmd.push(function () {
      googletag.defineSlot('/1090369/A1', [300, 60], 'div-gpt-ad-1547816814884-0').addService(googletag.pubads());
      googletag.defineSlot('/1090369/B1', [300, 250], 'div-gpt-ad-1546331539224-0').addService(googletag.pubads());
      googletag.defineSlot('/1090369/B2', [300, 250], 'div-gpt-ad-1539007469525-0').addService(googletag.pubads());
      googletag.defineSlot('/1090369/B3', [300, 250], 'div-gpt-ad-1546331252242-0').addService(googletag.pubads());
      googletag.defineSlot('/1090369/B4', [300, 250], 'div-gpt-ad-1546331385104-0').addService(googletag.pubads());
      googletag.pubads().enableSingleRequest();
      googletag.enableServices();
    });
  </script>
  <script src="https://securepubads.g.doubleclick.net/gpt/pubads_impl_2020111801.js?21068793" async=""></script>
  <link rel="preload" href="https://adservice.google.com/adsid/integrator.js?domain=www.cnblogs.com" as="script">
  <script type="text/javascript" src="https://adservice.google.com/adsid/integrator.js?domain=www.cnblogs.com"></script>
</head>

<body>
  <div id="wrapper" class="flow">
    <div id="top_nav" class="navbar border-none">
      <nav id="nav_main" class="navbar-main">
        <ul id="nav_left" class="navbar-list navbar-left">
          <li class="navbar-branding">
            <a href="https://www.cnblogs.com/" title="开发者的网上家园"><img
                src="/images/logo.svg?v=R9M0WmLAIPVydmdzE2keuvnjl-bPR7_35oHqtiBzGsM" alt="博客园Logo"></a>
          </li>
          <li><a href="/">首页</a></li>
          <li><a href="https://news.cnblogs.com/">新闻</a></li>
          <li><a href="https://q.cnblogs.com/">博问</a></li>
          <li><a id="nav_brandzone" href="https://brands.cnblogs.com/">专区</a></li>
          <li><a href="https://ing.cnblogs.com/">闪存</a></li>
          <li><a href="https://edu.cnblogs.com/">班级</a></li>
          <li><a id="nav_legacy" href="/legacy">怀旧</a></li>
          <li class="dropdown">
            <div class="dropdown-button">
              <a href="javascript:void(0)">发现</a>
              <img src="/images/aggsite/pulldown-light.svg">
            </div>
            <div class="dropdown-menu">
              <a href="https://home.cnblogs.com/">园子</a>
              <a href="https://group.cnblogs.com/">小组</a>
              <a href="https://wz.cnblogs.com/">收藏</a>
              <a href="https://job.cnblogs.com/">招聘</a>
              <a href="https://zzk.cnblogs.com/">找找看</a>
            </div>
          </li>
        </ul>
        <ul id="nav_right" class="navbar-list navbar-right">
          <li>
            <form id="zzk_search" class="navbar-search" action="https://zzk.cnblogs.com/s" method="get">
              <input name="w" id="zzk_search_input" placeholder="代码改变世界" type="text" tabindex="3">
              <button type="submit" id="zzk_search_button">
                <img src="/images/aggsite/search.svg" alt="搜索">
              </button>
            </form>
          </li>
          <li id="navbar_login_status" class="navbar-list">
            <a id="navblog-myblog-icon" class="navbar-user-info navbar-blog"
              href="https://passport.cnblogs.com/GetBlogApplyStatus.aspx" alt="我的博客" title="我的博客"
              style="display: none;">
              <img id="myblog_icon" class="navbar-icon" src="/images/aggsite/myblog.svg" alt="我的博客">
            </a>
            <a class="navbar-user-info navbar-message navbar-icon-wrapper" href="https://msg.cnblogs.com/" alt="短消息"
              title="短消息" style="display: none;">
              <img id="msg_icon" class="navbar-icon"
                src="/images/aggsite/message.svg?v=oS4PkibyMjZ9rGD5XAcLt99uW_s76Javy2up4dbnZNY" alt="短消息">
              <span id="msg_count" style="display: none"></span>
            </a>
            <div id="user_info" class="navbar-user-info dropdown" style="display: none;">
              <a class="dropdown-button" href="https://home.cnblogs.com/">
                <img id="user_icon" class="navbar-avatar" src="/images/aggsite/avatar-default.svg" alt="用户头像">
              </a>
              <div class="dropdown-menu">
                <a id="navblog-myblog-text" href="https://passport.cnblogs.com/GetBlogApplyStatus.aspx">我的博客</a>
                <a href="https://home.cnblogs.com/">我的园子</a>
                <a href="https://account.cnblogs.com/settings/account">账号设置</a>
                <a href="javascript:void(0)" onclick="logout();">退出登录</a>
              </div>
            </div>
            <a class="navbar-anonymous" href="https://account.cnblogs.com/signup/" style="display: inline;">注册</a>
            <a class="navbar-anonymous" href="https://account.cnblogs.com/signin/?returnUrl=https://www.cnblogs.com/"
              style="display: inline;">登录</a>
          </li>
        </ul>
      </nav>
      <nav id="nav_mobile" class="navbar-mobile">
        <ul class="navbar-list">
          <li class="navbar-branding">
            <a href="https://www.cnblogs.com/" title="开发者的网上家园"><img
                src="/images/logo.svg?v=R9M0WmLAIPVydmdzE2keuvnjl-bPR7_35oHqtiBzGsM" alt="博客园Logo"></a>
          </li>
          <li class="dropdown">
            <div class="dropdown-button" onclick="toggleDropdownMenu()">
              <a href="javascript:void(0)">发现</a>
              <img src="/images/aggsite/pulldown-bold.svg" height="5">
            </div>
            <div id="nav_dropdown_menu" class="dropdown-menu">
              <a href="https://news.cnblogs.com/">新闻</a>
              <a href="https://q.cnblogs.com/">博问</a>
              <a href="https://brands.cnblogs.com/">专区</a>
              <a href="https://ing.cnblogs.com/">闪存</a>
              <a href="https://edu.cnblogs.com/">班级</a>
              <a href="/legacy">怀旧</a>
              <a href="https://home.cnblogs.com/">园子</a>
              <a href="https://wz.cnblogs.com/">收藏</a>
              <a href="https://job.cnblogs.com/">招聘</a>
              <a href="https://zzk.cnblogs.com/">找找看</a>
            </div>
          </li>
        </ul>
        <ul class="navbar-list">
          <li>
            <form id="zzk_search_mobile" class="navbar-search" action="https://zzk.cnblogs.com/s" method="get">
              <input name="w" id="zzk_search_input_mobile" placeholder="搜索" type="text" tabindex="3">
              <button type="submit" id="zzk_search_button_mobile">
                <img src="/images/aggsite/search.svg" alt="搜索">
              </button>
            </form>
          </li>
          <li id="user_info_mobile" class="navbar-user-info dropdown" style="display: none;">
            <a class="dropdown-button" href="https://home.cnblogs.com/">
              <img id="user_icon_mobile" class="navbar-icon avatar">
            </a>
            <div class="dropdown-menu">
              <a href="https://account.cnblogs.com/settings/account">设置</a>
              <a href="javascript:void(0)" onclick="logout();">退出</a>
            </div>
          </li>
          <li class="navbar-anonymous" id="login_area_mobile" style="display: list-item;">
            <a href="https://account.cnblogs.com/signup/">注册</a>
          </li>
          <li class="navbar-anonymous" style="display: list-item;">
            <a href="https://account.cnblogs.com/signin/?returnUrl=https://www.cnblogs.com/">登录</a>
          </li>
        </ul>
      </nav>
    </div>
    <div class="bannerbar aws forpc">
      <a href="https://brands.cnblogs.com/aws/free?source=aggsite-bannerbar"><img
          src="https://img2020.cnblogs.com/blog/35695/202011/35695-*****************-*********.png" alt=""></a>
    </div>
    <div class="bannerbar aws formobile">
      <a href="https://brands.cnblogs.com/aws/free?source=sitehome-mobile-banner" target="_blank"
        onclick="ga('send', 'event', 'Link', 'click', 'aws-mobile-bannerbar')"><img
          src="https://img2020.cnblogs.com/blog/35695/202011/35695-*****************-**********.png" alt=""></a>
    </div>
    <div id="main_content" class="hero">
      <div id="main" class="main">
        <div id="side_nav" class="side-left tab-bar">

          <ul class="sidenav">
            <li id="sidenav_pick" class="sidenav-item ">
              <a href="/pick/" title="精华区博文">
                <img src="/images/aggsite/picked.svg">
                <span>精华</span>
              </a>
            </li>
            <li id="sidenav_candidate" class="sidenav-item ">
              <a href="/candidate/" title="候选区博文">
                <img src="/images/aggsite/candidate.svg">
                <span>候选</span>
              </a>
            </li>
            <li class="sidenav-splitter"></li>
            <li id="sidenav_subscription" class="sidenav-item ">
              <a href="/subscription" title="我订阅的博客">
                <img src="/images/aggsite/subscription.svg">
                <span>订阅</span>
              </a>
            </li>
            <li id="sidenav_following" class="sidenav-item ">
              <a href="/following" title="我关注的园友">
                <img src="/images/aggsite/following.svg">
                <span>关注</span>
              </a>
            </li>
            <li id="sidenav_commented" class="sidenav-item ">
              <a href="/aggsite/mycommented" title="我评论过的博文">
                <img src="/images/aggsite/commented.svg">
                <span>我评</span>
              </a>
            </li>
            <li id="sidenav_digged" class="sidenav-item ">
              <a href="/aggsite/mydigged" title="我推荐过的博文">
                <img src="/images/aggsite/digged.svg">
                <span>我赞</span>
              </a>
            </li>
            <li id="sidenav_more" class="dropdown sidenav-item ">
              <div class="dropdown-button">
                <a href="javascript:void(0)">
                  <img src="/images/aggsite/more.svg">
                  <span>更多</span>
                </a>
              </div>
              <div class="dropdown-menu">
                <a href="/cate/all">所有随笔</a>
                <a href="/comment/">最新评论</a>
                <a href="/cmt/">官方博客</a>
                <a href="/skins.aspx">博客皮肤</a>
              </div>
            </li>
            <li class="sidenav-splitter"></li>
            <li class="sidenav-item feedback">
              <a href="https://group.cnblogs.com/forum/public/" class="feedback">
                <img src="/images/aggsite/feedback.svg">
                <span>反馈</span>
              </a>
            </li>
            <li id="dark_switch" onclick="dark()">
              <a href="javascript:void(0)"><img src="/images/aggsite/dark.svg"></a>
            </li>
          </ul>
        </div>

        <div id="main_flow" class="main-flow">
          <div class="card">

            <div class="card headline">
              <ul>
                <li>
                  <a href="https://www.cnblogs.com/xuanyuan/p/14003524.html" id="editor_pick_lnk" target="_blank"
                    title="推荐20, 阅读2429">
                    <span class="headline-label">【编辑推荐】</span>还不懂 Docker？一个故事安排的明明白白！<span id="editor_pick_count">
                      (20/2429)</span>
                  </a>
                  <a href="/aggsite/headline" title="查看更多编辑推荐" class="right_more">»</a>
                </li>
                <li>
                  <a href="https://www.cnblogs.com/lsjwq/p/14006756.html" target="_blank" title="评论3, 推荐29, 阅读1462">
                    <span class="headline-label">【最多推荐】</span>[开源]CSharpFlink（NET
                    5.0开发）分布式实时计算框架，PC机10万数据点秒级计算测试说明(3/29/1462)
                  </a>
                  <a href="/aggsite/topdigged24h" title="查看24小时推荐排行" class="right_more">»</a>
                </li>
                <li>
                  <a href="https://www.cnblogs.com/xhznl/p/14004992.html" target="_blank" title="评论25, 推荐15, 阅读1471">
                    <span class="headline-label">【最多评论】</span>使用electron+vue开发一个跨平台todolist（便签）桌面应用(25/15/1471)
                  </a>
                  <a href="/aggsite/topcommented24h" title="查看24小时推荐排行" class="right_more">»</a>
                </li>
                <li>
                  <a href="https://news.cnblogs.com/n/677776/" target="_blank" title="评论1, 推荐2, 阅读397">
                    <span class="headline-label">【新闻头条】</span>苹果高管：M1处理器完成了乔布斯的“遗志”(1/2/397)
                  </a>
                  <a href="https://news.cnblogs.com/" title="查看更多新闻" class="right_more">»</a>
                </li>
                <li>
                  <a href="https://news.cnblogs.com/n/677798/" target="_blank" title="评论0, 推荐0, 阅读140">
                    <span class="headline-label">【推荐新闻】</span>雷军下场“撕标签”：中低端、代工、没技术都是外界对小米的误解(0/0/140)
                  </a>
                  <a href="https://news.cnblogs.com/n/recommend" title="查看更多推荐新闻" class="right_more">»</a>
                </li>
              </ul>
            </div>

            <div id="pager_top" style="display: none"></div>
            <div id="post_list_tips" class="hide"></div>

            <div id="post_list" class="post-list">

              <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" style="display: none">
                <defs>
                  <g id="icon_digg" stroke-linejoin="round" fill="none">
                    <path
                      d="M12.197 7.005H9C10.239 3.195 8.146 3 8.146 3c-.886 0-.703.584-.77.681C7.376 5.545 5 7.005 5 7.005v5.285c0 .522.853.71 1.188.71h4.804c.452 0 .82-.987.82-.987C13 8.647 13 7.645 13 7.645c0-.695-.803-.64-.803-.64z">
                    </path>
                    <path
                      d="M4.48 7H2.43c-.423 0-.43.324-.43.324l.423 5.336c0 .34.437.34.437.34h1.774c.37 0 .366-.225.366-.225v-5.37C5 6.995 4.48 7 4.48 7z">
                    </path>
                  </g>
                  <g id="icon_comment">
                    <path fill="none"
                      d="M4.25 3.5C3.56 3.5 3 4.054 3 4.738v4.957c0 .684.56 1.238 1.25 1.238h2.454L8 12.5l1.293-1.567h2.457c.69 0 1.25-.554 1.25-1.238V4.738A1.242 1.242 0 0011.751 3.5H4.25z">
                    </path>
                    <path stroke="none"
                      d="M10.5 7.5a.5.5 0 110-1 .5.5 0 010 1zM8 7.5a.5.5 0 11.001-1.001A.5.5 0 018 7.5zm-2.5 0a.5.5 0 110-1 .5.5 0 010 1z">
                    </path>
                  </g>
                  <path id="icon_views" stroke="none"
                    d="M7.5 5.406c-1.23 0-2.231.94-2.231 2.094 0 1.156 1 2.097 2.23 2.097S9.73 8.657 9.73 7.5c0-1.155-1-2.094-2.23-2.094zm0 3.203c-.653 0-1.184-.497-1.184-1.11 0-.611.53-1.109 1.184-1.109.653 0 1.184.498 1.184 1.11 0 .612-.531 1.11-1.184 1.11zm6.482-1.265a1.069 1.069 0 00-.03-.148.27.27 0 00-.008-.018l-.003-.007a1.072 1.072 0 00-.024-.065C12.715 4.65 10.136 3 7.5 3 4.86 3 2.285 4.645 1.094 7.09a.518.518 0 00-.034.082l-.012.023a.355.355 0 00-.026.126c-.01.052-.018.127-.019.13a.374.374 0 000 .099l.013.094a.566.566 0 00.016.1c.007.027.015.058.032.091a.92.92 0 00.022.056C2.284 10.349 4.86 12 7.5 12c2.64 0 5.211-1.64 6.399-4.078a.562.562 0 00.042-.097l.005-.012.005-.011a.519.519 0 00.028-.122l.003-.017a.672.672 0 000-.32zM7.5 11.015c-2.211 0-4.399-1.404-5.447-3.497L2.05 7.51a.136.136 0 00-.003-.015V7.49a.064.064 0 00.002-.007c1.035-2.093 3.225-3.5 5.45-3.5 2.223 0 4.41 1.404 5.442 3.493l.002.004.002.007v.007l.001.002-.004.004v.02c-1.036 2.09-3.222 3.494-5.443 3.494z"
                    fill-rule="nonzero"></path>
                </defs>
              </svg>

              <article class="post-item" data-post-id="14011154">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/xionglaichuangyichuang/p/14011154.html"
                      target="_blank">C# 简易的串口监视上位机实现</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/2139213/20200827231802.png" class="avatar">
                      实现上位机和下位机之间的通信，通常使用的是串口通信，接下来实现一个通过上位机和串口调试助手来完成串口通信测试。
                      首先创建一个WInfrom窗体应用工程文件，创建过程可参考https://www.cnblogs.com/xionglaichuangyichuang/p/13734179.html； 在 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/xionglaichuangyichuang/"
                      class="post-item-author"><span>熊来闯一闯</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 15:31</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('xionglaichuangyichuang', 14011154, 626788, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14011154">0</span>
                    </a>
                    <a class="post-meta-item btn"
                      href="https://www.cnblogs.com/xionglaichuangyichuang/p/14011154.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/xionglaichuangyichuang/p/14011154.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>99</span>
                    </a>
                    <span id="digg_tip_14011154" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14011063">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/yourbatman/p/14011063.html"
                      target="_blank">6. 自定义容器类型元素验证，类级别验证（多字段联合验证）</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/682076/20200420113141.png" class="avatar">
                      今天搬砖不狠，明天地位不稳。本文已被 https://www.yourbatman.cn
                      收录，里面一并有Spring技术栈、MyBatis、JVM、中间件等小而美的专栏供以免费学习。关注公众号【BAT的乌托邦】逐个击破，深入掌握，拒绝浅尝辄止。 ✍前言
                      你好，我是YourBatman。 本文是上篇 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/yourbatman/" class="post-item-author"><span>YourBatman</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 15:16</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('yourbatman', 14011063, 360196, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14011063">0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/yourbatman/p/14011063.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>1</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/yourbatman/p/14011063.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>48</span>
                    </a>
                    <span id="digg_tip_14011063" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="13991648">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/haogj/p/13991648.html" target="_blank">理解
                      ASP.NET Core： 验证</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/u13475.png" class="avatar">
                      通常在应用程序中，安全分为前后两个步骤：验证和授权。验证负责检查当前请求者的身份，而授权则根据上一步得到的身份决定当前请求者是否能够访问期望的资源。 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/haogj/" class="post-item-author"><span>冠军</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 14:51</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('haogj', 13991648, 17033, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_13991648">0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/haogj/p/13991648.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/haogj/p/13991648.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>128</span>
                    </a>
                    <span id="digg_tip_13991648" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14010364">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/JulianHuang/p/14010364.html"
                      target="_blank">解锁环境变量在云原生应用中各种姿势</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/587720/20201104104844.png" class="avatar">
                      应用程序在某些时刻总是需要一些外挂配置，云原生应用的实践是在容器化之前就将应用程序配置保留在代码之外。 12-Factors App：Store config in the environment
                      ① 外挂配置文件：业务配置 appsettings.json 可以在代码中要求加载appsetting ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/JulianHuang/" class="post-item-author"><span>有态度的小码甲</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 14:10</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('JulianHuang', 14010364, 232636, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14010364">0</span>
                    </a>
                    <a class="post-meta-item btn"
                      href="https://www.cnblogs.com/JulianHuang/p/14010364.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/JulianHuang/p/14010364.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>80</span>
                    </a>
                    <span id="digg_tip_14010364" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14008145">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/cmt/p/14008145.html" target="_blank">《.NET
                      5.0 背锅案》第7集-大结局：捉拿真凶 StackExchange.Redis.Extensions 归案</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/35695/20201013225735.png" class="avatar">
                      随着第5集的播出，随着案情的突破,《.NET 5.0
                      背锅案》演变为《博客园技术团队甩锅记》，拍片不成却自曝家丑，这次对我们是一次深刻的教训。在这次甩锅丢丑过程中，我们过于自信，我们的博客系统身经百战，我们使用的开源 redis 客户端
                      StackExchange.Redis 更是身经千战，虽然 .... ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/cmt/" class="post-item-author"><span>博客园团队</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 14:05</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('cmt', 14008145, 39258, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14008145">34</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/cmt/p/14008145.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>39</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/cmt/p/14008145.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>1232</span>
                    </a>
                    <span id="digg_tip_14008145" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14010434">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/huaface/p/14010434.html"
                      target="_blank">基于gin的golang web开发：集成swagger</a>
                    <p class="post-item-summary">
                      在前后端分离的项目维护一份完整且及时更新的api文档会极大的提高我们的工作效率，传统项目中接口文档都是由后端开发手写的，这种文档很难保证及时性，久而久之便失去了参考意义。swagger给我们提供了一种新的维护文档的方式，在gin中只需要编写一些注释即可生成一份可交互的接口文档。
                      go get -u ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/huaface/" class="post-item-author"><span>陈宏博</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 13:41</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('huaface', 14010434, 287936, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14010434">0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/huaface/p/14010434.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/huaface/p/14010434.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>149</span>
                    </a>
                    <span id="digg_tip_14010434" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14010301">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/xingag/p/14010301.html"
                      target="_blank">最全总结 | 聊聊 Python 办公自动化之 Word（中）</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/1956326/20200301110845.png" class="avatar">
                      1. 前言 上一篇文章，对 Word 写入数据的一些常见操作进行了总结 最全总结 | 聊聊 Python 办公自动化之 Word（上） 相比写入数据，读取数据同样很实用！
                      本篇文章，将谈谈如何全面读取一个 Word 文档中的数据，并会指出一些要注意的点 2. 基本信息 我们同样使用&nbsp;python-d ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/xingag/" class="post-item-author"><span>AirPython</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 12:53</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('xingag', 14010301, 581577, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14010301">0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/xingag/p/14010301.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/xingag/p/14010301.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>214</span>
                    </a>
                    <span id="digg_tip_14010301" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14010257">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/Chenjiabing/p/14010257.html"
                      target="_blank">头秃了，Spring Boot 自动配置源码解析了解一波~</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/1119719/20200923094739.png" class="avatar">
                      持续原创输出,点击上方蓝字关注我 目录 前言源码版本@SpringBootApplication干了什么？@EnableAutoConfiguration干了什么？总结 前言 为什么Spring
                      Boot这么火？因为便捷，开箱即用，但是你思考过为什么会这么便捷吗？传统的SSM架构配置文件至少要写半天 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/Chenjiabing/" class="post-item-author"><span>爱撒谎的男孩</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 12:34</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('Chenjiabing', 14010257, 357835, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14010257">0</span>
                    </a>
                    <a class="post-meta-item btn"
                      href="https://www.cnblogs.com/Chenjiabing/p/14010257.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/Chenjiabing/p/14010257.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>212</span>
                    </a>
                    <span id="digg_tip_14010257" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14010233">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/kenkofox/p/14010233.html"
                      target="_blank">async await 你真的用对了吗？</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/80917/20160127172319.png" class="avatar">
                      大部分同学了解Promise，也知道async await可以实现同步化写法，但实际上对一些细节没有理解到位，就容易导致实际项目中遇到问题。 开始先抛结论，下文将针对主要问题点进行论述。
                      1、所有async方法调用，必须加await或catch，捕获错误；如果最上层的async方法是被框架（reac ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/kenkofox/" class="post-item-author"><span>拂晓风起-Kenko</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 12:20</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('kenkofox', 14010233, 59460, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14010233">0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/kenkofox/p/14010233.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>1</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/kenkofox/p/14010233.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>384</span>
                    </a>
                    <span id="digg_tip_14010233" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14010202">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/yinjihuan/p/14010202.html"
                      target="_blank">用了Redisson的Spring Boot Starter搞的我都想重写个</a>
                    <p class="post-item-summary">
                      在对接一个小程序推送的框架时，需要将 access_token 存储到 Redis 中，框架中提供了存储逻辑，只需要将 RedissonClient 对象传进去即可。 框架内部在用
                      Redisson 的 RBucket 进行数据存储时报错了，原因是 fst 里面的一个方法找不到了，这一看就是版本的问 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/yinjihuan/" class="post-item-author"><span>猿天地</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 12:05</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('yinjihuan', 14010202, 496691, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14010202">0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/yinjihuan/p/14010202.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/yinjihuan/p/14010202.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>113</span>
                    </a>
                    <span id="digg_tip_14010202" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="13993815">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/lm970585581/p/13993815.html"
                      target="_blank">从硬件级别再看可见性和有序性</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/1140467/20200917162740.png" class="avatar">
                      前言 王子之前的文章对于并发编程中的可见性问题已经有了一个初步的介绍，总结出来就是CPU的缓存会导致可见性问题。
                      这样的解释其实是没有问题的，但这里说的“缓存”其实一个笼统的概念，缓存其实指的是寄存器、高速缓存和写缓冲器。 今天我们就从硬件的级别再来探索一下出现可见性问题的原因，让小伙伴们有一个更深 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/lm970585581/" class="post-item-author"><span>H.U.C-王子</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 12:00</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('lm970585581', 13993815, 346152, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_13993815">0</span>
                    </a>
                    <a class="post-meta-item btn"
                      href="https://www.cnblogs.com/lm970585581/p/13993815.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>1</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/lm970585581/p/13993815.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>91</span>
                    </a>
                    <span id="digg_tip_13993815" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14010024">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/spec-dog/p/14010024.html"
                      target="_blank">像程序员一样思考——提高解决问题的能力</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/632381/20200518123152.png" class="avatar">
                      在以前的文章中，曾经提过“技术人员的价值，不在于你能写出多么优美的代码，也不在于你能设计出一个多么大而全的高屋建瓴的架构，而在于你实实在在的解决问题的能力，在于你使用技术手段服务于业务的能力”。
                      最近一段时间，因工作中遇到一些现象，让我重又想起这句话，并且试图思考如何来提高解决问题的能力，有没有一种 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/spec-dog/" class="post-item-author"><span>【雨歌】</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 11:30</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('spec-dog', 14010024, 185522, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14010024">1</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/spec-dog/p/14010024.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/spec-dog/p/14010024.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>390</span>
                    </a>
                    <span id="digg_tip_14010024" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14010014">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/maopneo/p/14010014.html"
                      target="_blank">MYSQL学习(三) --索引详解</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/520622/20201116180233.png" class="avatar">
                      索引高性能SQL的一个重要的优化方面。也是MYSQL必须掌握的知识点。其中用到数据结构中索引、散列、B树相关的知识。稍稍有点复杂。可以从以下几个重点来掌握。分别是
                      索引的类型（哈希和B树）、索引的存储（线性和B树）、数据的存储(块顺序存储、聚簇索引存储)、怎样建立高效索引等 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/maopneo/" class="post-item-author"><span>IT迷途小书童</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 11:28</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('maopneo', 14010014, 413070, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14010014">1</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/maopneo/p/14010014.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/maopneo/p/14010014.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>134</span>
                    </a>
                    <span id="digg_tip_14010014" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14009897">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/wzk153/p/14009897.html"
                      target="_blank">（7）ASP.NET Core3.1 Ocelot Swagger</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/u420913.jpg?id=22000427" class="avatar">
                      1.前言 前端与后端的联系更多是通过API接口对接，API文档变成了前后端开发人员联系的纽带，开始变得越来越重要，而Swagger就是一款让你更好的书写规范API文档的框架。在Ocelot
                      Swagger项目示例中，通过APIGateway项目路由配置网关、上下游服务Swagger。对解决方案中的示 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/wzk153/" class="post-item-author"><span>暗断肠</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 11:11</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('wzk153', 14009897, 120943, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14009897">1</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/wzk153/p/14009897.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>2</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/wzk153/p/14009897.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>244</span>
                    </a>
                    <span id="digg_tip_14009897" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14009796">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/siyuanwai/p/14009796.html"
                      target="_blank">面试官常问的“一致性哈希”，都在这 18 张图里</a>
                    <p class="post-item-summary">
                      大家好，好久不见啦。最近快年底了，公司、部门事情太多：冲刺 KPI、做部门预算……所以忙东忙西的，写文章就被耽搁了。再加上这篇文章比较硬，我想给大家讲得通俗易懂，着实花了很多时间琢磨怎么写。
                      话不多说，小故事开始。 前言 当架构师大刘看到实习生小李提交的记账流水乱序的问题的时候，他知道没错了：这一次 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/siyuanwai/" class="post-item-author"><span>四猿外</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 10:54</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('siyuanwai', 14009796, 579882, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14009796">1</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/siyuanwai/p/14009796.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/siyuanwai/p/14009796.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>289</span>
                    </a>
                    <span id="digg_tip_14009796" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14009712">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/xiaokek/p/14009712.html"
                      target="_blank">一种在【微服务体系】下的【参数配置神器】</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/711308/20150107092126.png" class="avatar">
                      最近工作上遇到了一类需求，就是要对接大量外部系统，而需要对接的外部系统又存在各种环境，比如开发，测试，正式环境，他们的配置不尽相同。
                      一般情况下，我们可以通过配置application-dev.yml、application-test.yml、application-prod.yml等springb ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/xiaokek/" class="post-item-author"><span>可少</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 10:45</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('xiaokek', 14009712, 211058, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14009712">0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/xiaokek/p/14009712.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>2</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/xiaokek/p/14009712.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>173</span>
                    </a>
                    <span id="digg_tip_14009712" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14009536">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/xiaokantianse/p/14009536.html"
                      target="_blank">12.java设计模式之代理模式</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/2093590/20201119140021.png" class="avatar">
                      基本介绍：
                      代理模式（Proxy）为一个对象提供一个替身，以控制对这个对象的访问。即通过代理对象访问目标对象.这样做的好处是:可以在目标对象实现的基础上,增强额外的功能操作,即扩展目标对象的功能，想在访问一个类时做一些控制
                      代理的对象可以是远程对象、创建开销大的对象或需要安全控制的对象，代理模式有不 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/xiaokantianse/"
                      class="post-item-author"><span>xiaokantianse</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 10:18</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('xiaokantianse', 14009536, 626740, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14009536">0</span>
                    </a>
                    <a class="post-meta-item btn"
                      href="https://www.cnblogs.com/xiaokantianse/p/14009536.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/xiaokantianse/p/14009536.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>129</span>
                    </a>
                    <span id="digg_tip_14009536" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14009506">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/huaweiyun/p/14009506.html"
                      target="_blank">MySQL 连接为什么挂死了？</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/2030258/20200509094357.png" class="avatar">
                      摘要：本次分享的是一次关于 MySQL 高可用问题的定位过程，其中曲折颇多但问题本身却比较有些代表性，遂将其记录以供参考。 一、背景
                      近期由测试反馈的问题有点多，其中关于系统可靠性测试提出的问题令人感到头疼，一来这类问题有时候属于“偶发”现象，难以在环境上快速复现；二来则是可靠性问题的定位链条有时候 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/huaweiyun/" class="post-item-author"><span>华为云开发者社区</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 10:14</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('huaweiyun', 14009506, 602073, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14009506">0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/huaweiyun/p/14009506.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/huaweiyun/p/14009506.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>276</span>
                    </a>
                    <span id="digg_tip_14009506" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14009432">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/tiaoshuidenong/p/14009432.html"
                      target="_blank">EF Core 二 、 入门 EF Core</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/591768/20180318200641.png" class="avatar">
                      入门EF Core 我们将开始真正的EF之旅了，这里使用SqlServer数据，然后DbFirst；
                      为嘛使用SqlServer，目前公司的整体业务全部在SqlSever，所以很多产品业务都是依托于这个，当然也在考虑做数据库切换，切换EF
                      Core就是开始，为后续做好准备，目前SqlServer的l ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/tiaoshuidenong/" class="post-item-author"><span>五行缺码</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 10:07</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('tiaoshuidenong', 14009432, 195575, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14009432">4</span>
                    </a>
                    <a class="post-meta-item btn"
                      href="https://www.cnblogs.com/tiaoshuidenong/p/14009432.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>3</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/tiaoshuidenong/p/14009432.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>272</span>
                    </a>
                    <span id="digg_tip_14009432" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
              <article class="post-item" data-post-id="14009468">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/wzp123456/p/14009468.html"
                      target="_blank">ThreadLocal应用及源码分析</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/2202901/20201103202111.png" class="avatar">
                      ThreadLocal 基本使用 ThreadLocal
                      的作用是：提供线程内的局部变量，不同的线程之间不会相互干扰，这种变量在线程的生命周期内起作用，减少同一个线程内多个函数或组件之间一些公共变量传递的复杂度，降低耦合性。 方法声明 描述
                      ThreadLocal() 创建ThreadLocal对象 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/wzp123456/" class="post-item-author"><span>西凉马戳戳</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 10:06</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('wzp123456', 14009468, 646330, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14009468">1</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/wzp123456/p/14009468.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/wzp123456/p/14009468.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>130</span>
                    </a>
                    <span id="digg_tip_14009468" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>


            </div>
            <script type="text/javascript">
              var aggSiteModel = { "CategoryType": "SiteHome", "ParentCategoryId": 0, "CategoryId": 808, "PageIndex": 1, "TotalPostCount": 4000, "ItemListActionName": "AggSitePostList" };
            </script>
            <div id="pager_bottom">
              <div id="paging_block">
                <div class="pager"><a href="/" class="p_1 current"
                    onclick="aggSite.loadCategoryPostList(1,20);buildPaging(1);return false;">1</a><a
                    href="/sitehome/p/2" class="p_2 middle"
                    onclick="aggSite.loadCategoryPostList(2,20);buildPaging(2);return false;">2</a><a
                    href="/sitehome/p/3" class="p_3 middle"
                    onclick="aggSite.loadCategoryPostList(3,20);buildPaging(3);return false;">3</a><span
                    class="ellipsis">···</span><a href="/sitehome/p/200" class="p_200 last"
                    onclick="aggSite.loadCategoryPostList(200,20);buildPaging(200);return false;">200</a><a
                    href="/sitehome/p/2"
                    onclick="aggSite.loadCategoryPostList(2,20);buildPaging(2);return false;">&gt;</a></div>
              </div>
              <script
                type="text/javascript">var pagingBuider = { "OnlyLinkText": false, "TotalCount": 4000, "PageIndex": 1, "PageSize": 20, "ShowPageCount": 1, "SkipCount": 0, "UrlFormat": "/sitehome/p/{0}", "OnClickJsFunc": "aggSite.loadCategoryPostList()", "FirstPageLink": "/", "AjaxUrl": "/AggSite/Pager", "AjaxCallbak": null, "TopPagerId": "pager_top", "IsRenderScript": true }; function buildPaging(pageIndex) { pagingBuider.PageIndex = pageIndex; $.ajax({ url: pagingBuider.AjaxUrl, data: JSON.stringify(pagingBuider), type: 'post', dataType: 'text', contentType: 'application/json; charset=utf-8', success: function (data) { $('#paging_block').html(data); var pagerTop = $('#pager_top'); if (pageIndex > 1) { $(pagerTop).html(data).show(); } else { $(pagerTop).hide(); } } }); }</script>
              <iframe
                src="https://6dd18e8e6e83751b9f4c6895aa5b3d78.safeframe.googlesyndication.com/safeframe/1-0-37/html/container.html"
                style="visibility: hidden; display: none;"></iframe>
            </div>
          </div>
        </div>

        <div id="side_right" class="side-right card-list sidebar">
          <div id="cnblogs_b1" class="sidebar-image"><a href="https://brands.cnblogs.com/aws/free?source=B1"
              target="_blank"><img width="300" height="250"
                src="https://img2020.cnblogs.com/blog/2016690/202011/2016690-20201107115840065-336851186.jpg"
                alt="aws免费套餐" onclick="ga('send', 'event', 'Link', 'click', 'B1-aws');"></a></div>
          <div id="sidebar_bh" class="sidebar-bh">
            <ul>
              <li><a href="https://brands.cnblogs.com/aws/free?source=BH" target="_blank"
                  onclick="ga('send', 'event', 'Link', 'click', 'T1-AWS')">【福利】AWS携手博客园为开发者送套餐与抵扣券</a></li>
            </ul>
          </div>
          <div class="card">
            <h4 class="card-title">
              <a href="/aggsite/topviews">48小时阅读排行</a>
            </h4>
            <ul class="item-list">
              <li><span class="number highlight">1</span><a href="https://www.cnblogs.com/cmt/p/14003277.html"
                  target="_blank" onmouseover="set_a_title(this)">写给园友们的一封求助信</a></li>
              <li><span class="number highlight">2</span><a href="https://www.cnblogs.com/willick/p/14003239.html"
                  target="_blank" onmouseover="set_a_title(this)">再聊 Blazor，它是否值得你花时间学习</a></li>
              <li><span class="number highlight">3</span><a href="https://www.cnblogs.com/xuanyuan/p/14003524.html"
                  target="_blank" onmouseover="set_a_title(this)">还不懂Docker？一个故事安排的明明白白！</a></li>
              <li><span class="number">4</span><a href="https://www.cnblogs.com/cmt/p/13999061.html" target="_blank"
                  onmouseover="set_a_title(this)">《.NET 5.0 背锅案》第6集-案发现场回顾：故障情况下 Kubernetes 部署表现</a></li>
              <li><span class="number">5</span><a href="https://www.cnblogs.com/xhznl/p/14004992.html" target="_blank"
                  onmouseover="set_a_title(this)">使用electron+vue开发一个跨平台todolist（便签）桌面应用</a></li>
              <li><span class="number">6</span><a href="https://www.cnblogs.com/lsjwq/p/14006756.html" target="_blank"
                  onmouseover="set_a_title(this)">[开源]CSharpFlink（NET 5.0开发）分布式实时计算框架，PC机10万数据点秒级计算测试说明</a></li>
            </ul>
          </div>
          <div class="card">
            <h4 class="card-title">
              <a href="/aggsite/topdiggs">10天推荐排行</a>
            </h4>
            <ul class="item-list">
              <li><span class="number highlight">1</span><a href="https://www.cnblogs.com/cmt/p/14003277.html"
                  target="_blank" onmouseover="set_a_title(this)">写给园友们的一封求助信</a></li>
              <li><span class="number highlight">2</span><a href="https://www.cnblogs.com/powertoolsteam/p/dotnet5.html"
                  target="_blank" onmouseover="set_a_title(this)">.NET 5.0正式发布，功能特性介绍(翻译)</a></li>
              <li><span class="number highlight">3</span><a href="https://www.cnblogs.com/cmt/p/13996747.html"
                  target="_blank" onmouseover="set_a_title(this)">《.NET 5.0 背锅案》第5集-案情大转弯：都是我们的错，让 .NET 5.0 背锅</a></li>
              <li><span class="number">4</span><a href="https://www.cnblogs.com/fulu/p/13983734.html" target="_blank"
                  onmouseover="set_a_title(this)">.NET5都来了，你还不知道怎么部署到linux？最全部署方案，总有一款适合你</a></li>
              <li><span class="number">5</span><a href="https://www.cnblogs.com/willick/p/14003239.html" target="_blank"
                  onmouseover="set_a_title(this)">再聊 Blazor，它是否值得你花时间学习</a></li>
              <li><span class="number">6</span><a href="https://www.cnblogs.com/willick/p/13967346.html" target="_blank"
                  onmouseover="set_a_title(this)">[C#.NET 拾遗补漏]12：死锁和活锁的发生及避免</a></li>
            </ul>
          </div>
          <div id="cnblogs_b2" class="sidebar-image">
            <div id="div-gpt-ad-1539007469525-0" style="height:250px; width:300px;"
              data-google-query-id="CIT6x4_VkO0CFZIIYAodUhcJsg">

              <div id="google_ads_iframe_/1090369/B2_0__container__" style="border: 0pt none;"><iframe
                  id="google_ads_iframe_/1090369/B2_0" title="3rd party ad content"
                  name="google_ads_iframe_/1090369/B2_0" width="300" height="250" scrolling="no" marginwidth="0"
                  marginheight="0" frameborder="0" srcdoc="" data-google-container-id="3" data-load-complete="true"
                  style="border: 0px; vertical-align: bottom;"></iframe></div>
            </div>
          </div>
          <div class="card">
            <h4 class="card-title">
              <a href="/aggsite/topcommented48h">48小时评论排行</a>
            </h4>
            <ul class="item-list">
              <li><span class="number highlight">1</span><a href="https://www.cnblogs.com/cmt/p/14003277.html"
                  target="_blank" onmouseover="set_a_title(this)">写给园友们的一封求助信</a></li>
              <li><span class="number highlight">2</span><a href="https://www.cnblogs.com/willick/p/14003239.html"
                  target="_blank" onmouseover="set_a_title(this)">再聊 Blazor，它是否值得你花时间学习</a></li>
              <li><span class="number highlight">3</span><a href="https://www.cnblogs.com/cmt/p/14008145.html"
                  target="_blank" onmouseover="set_a_title(this)">《.NET 5.0 背锅案》第7集-大结局：捉拿真凶
                  StackExchange.Redis.Extensions 归案</a></li>
              <li><span class="number">4</span><a href="https://www.cnblogs.com/cmt/p/13999061.html" target="_blank"
                  onmouseover="set_a_title(this)">《.NET 5.0 背锅案》第6集-案发现场回顾：故障情况下 Kubernetes 部署表现</a></li>
              <li><span class="number">5</span><a href="https://www.cnblogs.com/xhznl/p/14004992.html" target="_blank"
                  onmouseover="set_a_title(this)">使用electron+vue开发一个跨平台todolist（便签）桌面应用</a></li>
              <li><span class="number">6</span><a href="https://www.cnblogs.com/xuanyuan/p/14003524.html"
                  target="_blank" onmouseover="set_a_title(this)">还不懂Docker？一个故事安排的明明白白！</a></li>
            </ul>
          </div>
          <div class="card">
            <h4 class="card-title">
              <a href="/aggsite/headline">编辑推荐</a>
            </h4>
            <ul class="item-list">
              <li><span class="number highlight">1</span><a href="https://www.cnblogs.com/xuanyuan/p/14003524.html"
                  target="_blank" onmouseover="set_a_title(this)">还不懂 Docker？一个故事安排的明明白白！</a></li>
              <li><span class="number highlight">2</span><a
                  href="https://www.cnblogs.com/artech/p/asp-net-core-program-model-4.html" target="_blank"
                  onmouseover="set_a_title(this)">ASP.NET Core应用基本编程模式：基于承载环境的编程</a></li>
              <li><span class="number highlight">3</span><a href="https://www.cnblogs.com/willick/p/13967346.html"
                  target="_blank" onmouseover="set_a_title(this)"> [C#.NET 拾遗补漏]：死锁和活锁的发生及避免</a></li>
              <li><span class="number">4</span><a
                  href="https://www.cnblogs.com/edisonchou/p/my_experiences_on_digital_transformation_part2.html"
                  target="_blank" onmouseover="set_a_title(this)">我在传统行业做数字化转型 - 技术篇</a></li>
              <li><span class="number">5</span><a href="https://www.cnblogs.com/tianqing/p/13982098.html"
                  target="_blank" onmouseover="set_a_title(this)">深度探秘.NET 5.0</a></li>
              <li><span class="number">6</span><a href="https://www.cnblogs.com/huangxincheng/p/13974476.html"
                  target="_blank" onmouseover="set_a_title(this)">C# Span 源码解读和应用实践</a></li>
            </ul>
          </div>
          <div id="cnblogs_b3" class="sidebar-image">
            <div id="div-gpt-ad-1546331252242-0" style="height:250px; width:300px;"
              data-google-query-id="CIX6x4_VkO0CFZIIYAodUhcJsg">

              <div id="google_ads_iframe_/1090369/B3_0__container__" style="border: 0pt none;"><iframe
                  id="google_ads_iframe_/1090369/B3_0" title="3rd party ad content"
                  name="google_ads_iframe_/1090369/B3_0" width="300" height="250" scrolling="no" marginwidth="0"
                  marginheight="0" frameborder="0" srcdoc="" data-google-container-id="4" data-load-complete="true"
                  style="border: 0px; vertical-align: bottom;"></iframe></div>
            </div>
          </div>
          <div class="card">
            <h4 class="card-title">
              <a href="https://news.cnblogs.com/n/digg">热门新闻</a>
            </h4>
            <ul class="item-list">
              <li><span class="number highlight">1</span><a href="https://news.cnblogs.com/n/677662/" target="_blank"
                  onmouseover="set_a_title(this)">因未发项目奖金，一名程序员决定删代码泄愤</a></li>
              <li><span class="number highlight">2</span><a href="https://news.cnblogs.com/n/677608/" target="_blank"
                  onmouseover="set_a_title(this)">空气币操盘者口述：租豪宅、坑学妹，我割韭菜狂赚2000万元</a></li>
              <li><span class="number highlight">3</span><a href="https://news.cnblogs.com/n/677580/" target="_blank"
                  onmouseover="set_a_title(this)">GitHub重新上架星标7.2万热门开源项目YouTube-dl，哪来的底气？</a></li>
              <li><span class="number">4</span><a href="https://news.cnblogs.com/n/677685/" target="_blank"
                  onmouseover="set_a_title(this)">重磅！苹果M1曝出首个高危漏洞：中国人发现</a></li>
              <li><span class="number">5</span><a href="https://news.cnblogs.com/n/677623/" target="_blank"
                  onmouseover="set_a_title(this)">孔夫子旧书网：比淘宝创办还早的二手电商奇葩</a></li>
              <li><span class="number">6</span><a href="https://news.cnblogs.com/n/677692/" target="_blank"
                  onmouseover="set_a_title(this)">杀熟割韭菜要成历史？国家对互联网寡头们下手了！</a></li>
            </ul>
          </div>
          <div class="card">
            <h4 class="card-title">
              <a href="https://news.cnblogs.com/n/recommend">推荐新闻</a>
            </h4>
            <ul class="item-list">
              <li><span class="number highlight">1</span><a href="https://news.cnblogs.com/n/677764/" target="_blank"
                  onmouseover="set_a_title(this)">蛋壳“破碎”这一年：成也模式，衰也模式</a></li>
              <li><span class="number highlight">2</span><a href="https://news.cnblogs.com/n/677743/" target="_blank"
                  onmouseover="set_a_title(this)">任正非：做容千万家的天下英雄！</a></li>
              <li><span class="number highlight">3</span><a href="https://news.cnblogs.com/n/677742/" target="_blank"
                  onmouseover="set_a_title(this)">职业打假人：经检测，辛巴所卖燕窝就是糖水</a></li>
              <li><span class="number">4</span><a href="https://news.cnblogs.com/n/677730/" target="_blank"
                  onmouseover="set_a_title(this)">租金贷、培训贷... 盘点那些充满套路的分期消费贷</a></li>
              <li><span class="number">5</span><a href="https://news.cnblogs.com/n/677692/" target="_blank"
                  onmouseover="set_a_title(this)">杀熟割韭菜要成历史？国家对互联网寡头们下手了！</a></li>
              <li><span class="number">6</span><a href="https://news.cnblogs.com/n/677685/" target="_blank"
                  onmouseover="set_a_title(this)">重磅！苹果M1曝出首个高危漏洞：中国人发现</a></li>
            </ul>
          </div>
          <div id="cnblogs_b4" class="sidebar-image">
            <div id="div-gpt-ad-1546331385104-0" style="height:250px; width:300px;"
              data-google-query-id="CIb6x4_VkO0CFZIIYAodUhcJsg">

              <div id="google_ads_iframe_/1090369/B4_0__container__" style="border: 0pt none;"><iframe
                  id="google_ads_iframe_/1090369/B4_0" title="3rd party ad content"
                  name="google_ads_iframe_/1090369/B4_0" width="300" height="250" scrolling="no" marginwidth="0"
                  marginheight="0" frameborder="0" srcdoc="" data-google-container-id="5" data-load-complete="true"
                  style="border: 0px; vertical-align: bottom;"></iframe></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <footer id="footer" class="footer">
      <div id="friend_link" class="link-list friend-link">
        友情链接：<a href="https://www.vpsor.cn" target="_blank">硅云</a><a href="https://www.aliyun.com"
          target="_blank">阿里云</a><a
          href="https://cloud.tencent.com/act/developer?fromSource=gwzcw.3196334.3196334.3196334&amp;utm_medium=cpc&amp;utm_id=gwzcw.3196334.3196334.3196334"
          target="_blank">腾讯云</a><a href="https://www.huaweicloud.com/" target="_blank">华为云</a><a
          href="https://cloud.baidu.com" target="_blank">百度云</a><a href="https://www.jdcloud.com"
          target="_blank">京东云</a><a href="https://www.yisu.com/" target="_blank">亿速云</a><a href="https://www.163yun.com"
          target="_blank">网易云</a><a href="http://www.gcpowertools.com.cn" target="_blank">葡萄城控件</a><a
          href="http://www.chinaz.com/" target="_blank">站长之家</a><a href="http://dev.yesky.com" target="_blank">天极网</a><a
          href="http://www.hightopo.com/cn-index.html" target="_blank">图扑软件</a><a
          href="http://www.cnblogs.com/mipengine/" target="_blank">百度MIP博客</a><a
          href="http://wetest.qq.com/?from=links_cnblogs" target="_blank">腾讯WeTest</a><a href="http://yaq.qq.com/"
          target="_blank">腾讯御安全</a><a href="http://www.ucancode.com/index.html" target="_blank">工控组态源码</a><a
          href="https://163yun.cnblogs.com/" target="_blank">网易云博客</a><a href="https://kb.cnblogs.com"
          target="_blank">知识库</a>
      </div>
      <div class="footer-splitter"></div>
      <div id="footer_bottom">
        <div class="poweredby">Powered by .NET 5.0.0 on Kubernetes</div>
        <div class="about"><a href="https://about.cnblogs.com/">关于博客园</a><a
            href="https://about.cnblogs.com/contact">联系我们</a><a href="https://about.cnblogs.com/ad">广告服务</a><a
            href="https://about.cnblogs.com/job">人才服务</a>©2004-2020<a href="https://www.cnblogs.com/">博客园</a>保留所有权利<a
            href="http://www.beian.miit.gov.cn" target="_blank">沪ICP备09004260号</a></div>
        <div class="beian"><a
            href="https://ss.knet.cn/verifyseal.dll?sn=e131108110100433392itm000000&amp;ct=df&amp;a=1&amp;pa=0.25787803245785335"
            rel="nofollow" target="_blank"><img id="cnnic_img" src="//common.cnblogs.com/images/cnnic.png" alt=""
              width="64" height="23"></a><a target="_blank"
            href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011502001144"
            style="display:inline-block;text-decoration:none;height:20px;line-height:20px;"><img
              src="//common.cnblogs.com/images/ghs.png" alt=""><span
              style="float:left;height:20px;line-height:20px;margin: 0 5px 0 5px;">沪公网安备 31011502001144号</span></a>
        </div>
        <div class="report-contact">举报电话：0571-88079867，举报邮箱：<EMAIL> <a href="http://www.shjbzx.cn"
            target="_blank"><img src="/images/jblogo.png?v=20200730" alt=""></a></div>
        <div clas="weixin-mp">
          <img src="https://img2020.cnblogs.com/blog/35695/202011/35695-20201110200531630-9844.png" alt=""
            style="width:50px;">
          <p>博客园公众号</p>
        </div>
      </div>
    </footer>

  </div>
</body>

</html>