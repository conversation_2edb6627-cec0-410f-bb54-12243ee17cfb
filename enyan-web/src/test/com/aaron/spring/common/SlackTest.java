package com.aaron.spring.common;

import com.aaron.http.HttpMethod;
import com.aaron.http.HttpProtocolHandler;
import com.aaron.http.HttpResult;
import com.aaron.spring.model.SlackMsg;
import org.junit.Test;

/**
 *
 *
 * @Date: Created in  2020-03-28
 * @Modified By:
 */
public class SlackTest {
    public static final String SERVER_LOG_URL = "*******************************************************************************";
    public static final String ORDER_LOG_URL = "*******************************************************************************";

    @Test
    public void testOrderLog()throws Exception{
        SlackMsg slackMsg = new SlackMsg();
        SlackMsgBuild slackMsgBuild = new SlackMsgBuild();

        slackMsgBuild.addBoldText("订单号：");
        slackMsgBuild.addRedText("E10838415068");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("Email：");
        slackMsgBuild.addRedText("<EMAIL>");
        slackMsgBuild.addBrText();

        slackMsgBuild.addQuoteText("申命记(简)[房角石卷2下]");
        slackMsgBuild.addStrokeText("HK$ 160.00");
        slackMsgBuild.addText("HK$ 136.00");
        slackMsgBuild.addQuoteBrText();

        slackMsgBuild.addQuoteText("申命记(简)[房角石卷2下]");
        slackMsgBuild.addStrokeText("HK$ 160.00");
        slackMsgBuild.addText("HK$ 136.00");
        slackMsgBuild.addQuoteBrText();

        slackMsgBuild.addQuoteText("申命记(简)[房角石卷2下]");
        slackMsgBuild.addStrokeText("HK$ 160.00");
        slackMsgBuild.addText("HK$ 136.00");
        slackMsgBuild.addQuoteBrText();

        slackMsgBuild.addBoldText("支付：");
        slackMsgBuild.addRedText("支付宝");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("金额：");
        slackMsgBuild.addRedText("HK$297.50");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("时间：");
        slackMsgBuild.addRedText("2020-03-15 14:24:22");
        slackMsgBuild.addBrText();



        slackMsg.setText(slackMsgBuild.getText());

        HttpProtocolHandler.execute(null,ORDER_LOG_URL, HttpMethod.POST,null,slackMsg.jsonString());
    }

    @Test
    public void testNormalLog()throws Exception{
        SlackMsg slackMsg = new SlackMsg();
        SlackMsgBuild slackMsgBuild = new SlackMsgBuild();

        slackMsgBuild.addBoldText("订单号：");
        slackMsgBuild.addRedText("E10838415068");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("Email：");
        slackMsgBuild.addRedText("<EMAIL>");
        slackMsgBuild.addBrText();

        slackMsgBuild.addQuoteText("申命记(简)[房角石卷2下]");
        slackMsgBuild.addStrokeText("HK$ 160.00");
        slackMsgBuild.addText("HK$ 136.00");
        slackMsgBuild.addQuoteBrText();

        slackMsgBuild.addQuoteText("申命记(简)[房角石卷2下]");
        slackMsgBuild.addStrokeText("HK$ 160.00");
        slackMsgBuild.addText("HK$ 136.00");
        slackMsgBuild.addQuoteBrText();

        slackMsgBuild.addQuoteText("申命记(简)[房角石卷2下]");
        slackMsgBuild.addStrokeText("HK$ 160.00");
        slackMsgBuild.addText("HK$ 136.00");
        slackMsgBuild.addQuoteBrText();

        slackMsgBuild.addBoldText("支付：");
        slackMsgBuild.addRedText("支付宝");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("金额：");
        slackMsgBuild.addRedText("HK$297.50");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("时间：");
        slackMsgBuild.addRedText("2020-03-15 14:24:22");
        slackMsgBuild.addBrText();



        slackMsg.setText(slackMsgBuild.getText());

        HttpProtocolHandler.execute(null,SERVER_LOG_URL, HttpMethod.POST,null,slackMsg.jsonString());
    }

    @Test
    public void testBaiduLog()throws Exception{
        HttpResult result = HttpProtocolHandler.execute(null,"https://www.baidu.com",HttpMethod.GET);
        System.out.println(result.getStringResult());
    }

    @Test
    public void testSlackHelloWorld()throws Exception{
        String text = "{\"text\":\"Hello, World!\"}";
        HttpProtocolHandler.execute(null,SERVER_LOG_URL, HttpMethod.POST,null,text);
    }
}
