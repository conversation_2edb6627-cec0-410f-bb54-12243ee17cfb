package com.aaron.spring.common;

import com.aaron.excel.ExcelExportUtil;
import com.aaron.excel.ExcelTemplateUtil;
import com.aaron.excel.FileUtil;
import com.aaron.excel.GroupCode;
import com.google.common.collect.Lists;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;

import java.io.File;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2022/6/26
 * @Modified By:
 */
public class FileScanTest {
	@Test
	public void testPrint() throws Exception{
		String basePath = "/Users/<USER>/Documents/Workspace/iOS/EDBook";
		basePath = "/Volumes/Expansion/";
		String excelName = "电子书.xlsx";
		String excelPath = "/Users/<USER>/Documents/share/";

		List<List<String>> dataList = new ArrayList<>();
		File baseFile = new File(basePath);


		Collection<File> list =  FileUtils.listFiles(baseFile, null, true);
		for (File file:list){
//			System.out.print(file.getName());
//			System.out.print("--");
//			System.out.println(file.getPath());
			List<String> data = Lists.newArrayList(file.getName(),file.getPath());
			dataList.add(data);
		}

		XSSFWorkbook book = ExcelTemplateUtil.createHeaders(Lists.newArrayList("文件名称","文件路径"));
		if (book.getNumberOfSheets() > 0) {
			Row rowDel = book.getSheetAt(0).getRow(2);
			if (rowDel != null) {
				book.getSheetAt(0).removeRow(rowDel);
			}
		}


		SXSSFWorkbook workbook = new SXSSFWorkbook(book);
		String filePath = excelPath+excelName;
//		File file = new File(filePath);
		ExcelExportUtil.createExcel(dataList, filePath, 2, workbook);
	}

	@Test
	public void testVideo() throws Exception{
		String basePath = "/Volumes/Expansion/1视频音频";
		String excelName = "电子书-视频.xlsx";
		String excelPath = "/Users/<USER>/Documents/share/";

		listFiles(basePath, excelName, excelPath);
	}

	@Test
	public void testEbook() throws Exception{
		String basePath = "/Volumes/Expansion/2T/";///Volumes/Expansion/2T/文字/仓库等多个文件/
		String excelName = "电子书.xlsx";
		String excelPath = "/Users/<USER>/Documents/share/";

		listFiles(basePath, excelName, excelPath);
		///Volumes/Expansion/2T/文字/000/书库/书籍第四部/九洲书库/杂志/華傳/許佩珠(林安國師母)文章:
		///Volumes/Expansion/2T/文字/0001/各种教会史2/剖析“三自”
	}

	@Test
	public void testShi() throws Exception{
		String basePath = "/Volumes/Expansion/赞美诗/";
		String excelName = "电子书-诗歌.xlsx";
		String excelPath = "/Users/<USER>/Documents/share/";

		listFiles(basePath, excelName, excelPath);
	}

	List<File> ergodic(File file, List<File> resultFileName) {
		File[] files = file.listFiles();
		if (files == null)
			return resultFileName;// 判断目录下是不是空的
		for (File f : files) {
			if (f.exists() == false){
				continue;
			}
			try{
				if (f.isDirectory()) {// 判断是否文件夹
					//resultFileName.add(f.getPath());
					ergodic(f, resultFileName);// 调用自身,查找子目录
				} else{
					resultFileName.add(f);
				}
			}catch (Exception e){
				System.out.println(e.getMessage());
			}

		}
		return resultFileName;
	}

	private void listFiles(String basePath, String excelName, String excelPath) throws Exception{
		List<List<String>> dataList = new ArrayList<>();
		File baseFile = new File(basePath);


		Collection<File> list =  FileUtils.listFiles(baseFile, null, true);
		/*
		List<File> list = new ArrayList<>();
		ergodic(baseFile, list);*/

		for (File file:list){
//			System.out.print(file.getName());
//			System.out.print("--");
//			System.out.println(file.getPath());
			List<String> data = Lists.newArrayList(file.getName(),readableFileSize(file.length()),file.getPath());
			dataList.add(data);
		}
		
		XSSFWorkbook book = ExcelTemplateUtil.createHeaders(Lists.newArrayList("文件名称","文件大小","文件路径"));
		if (book.getNumberOfSheets() > 0) {
			Row rowDel = book.getSheetAt(0).getRow(2);
			if (rowDel != null) {
				book.getSheetAt(0).removeRow(rowDel);
			}
		}


		SXSSFWorkbook workbook = new SXSSFWorkbook(book);
		String filePath = excelPath+excelName;
//		File file = new File(filePath);
		ExcelExportUtil.createExcel(dataList, filePath, 2, workbook);
	}

	private void printDirList(String path){
	}

	public static String readableFileSize(long size) {
		if(size <= 0) return "0";
		final String[] units = new String[] { "B", "kB", "MB", "GB", "TB" };
		int digitGroups = (int) (Math.log10(size)/Math.log10(1024));
		return new DecimalFormat("#,##0.#").format(size/Math.pow(1024, digitGroups)) + " " + units[digitGroups];
	}
}
