package com.aaron.spring.common;

import com.aaron.model.Book;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/30
 * @Modified By:
 */
public class ArrayTest {
   @Test
   public void testArrayMapList(){
      List<Long> list = new ArrayList<>();
      list.add(1L);
      list.add(2L);
      list.add(3L);
      list.add(4L);

      String out = Arrays.toString(list.toArray());
      System.out.println(out);
      //String.join(",",list);

      List names=new ArrayList<String>();
      names.add("1");
      names.add("2");
      names.add("3");
      String join = String.join(",", names);
      System.out.println(join);

      List<String> myList = new ArrayList<>(Arrays.asList(join.split(",")));

      List<Long> longs = Arrays
              .stream(join.split(","))
              .map(Long::parseLong)
              .collect(Collectors.toList());
      System.out.println("longs:"+longs);

      //list.stream().toArray();

      Long[] listArray = list.toArray(new Long[0]);
      for (Long l:listArray){
         System.out.println(l);
      }
      System.out.println("array:"+listArray);


      Map<Integer, String> tree = new TreeMap<Integer, String>();
      List<String> listOrdre = Arrays.asList("white", "blue", "yellow", "orange", "black", "brown");
      List<String>   myList2  = Arrays.asList("orange", "white", "brown", "yellow", "black");

      for (String code : myList2) {
         tree.put(listOrdre.indexOf(code), code);
      }
      System.out.println(tree.values());

      //String[] ids = list.stream().toArray(String[]::new);
      //System.out.println(ids);
   }

   @Test
   public void testArrayMapList2(){
      List<Long> list = new ArrayList<>();
      list.add(1L);
      list.add(2L);
      list.add(3L);
      list.add(4L);

//      String[] ids = list.stream().toArray(String[]::new);
      //String[] ids = list.toArray(new String[0]);
      String[] ids = list.stream().map(String::valueOf).toArray(String[]::new);
      System.out.println(ids.length);
      for (int i = 0; i < ids.length; i++) {
         System.out.println("index="+i+","+ids[i]);
      }
      System.out.println(ids);
   }

   @Test
   public void testArrayListToStringList(){
      List<Long> list = new ArrayList<>();
      list.add(1L);
      list.add(2L);
      list.add(3L);
      list.add(4L);

      List<String> strings = list.stream().map(String::valueOf).toList();
      System.out.println(strings);
   }

   @Test
   public void testArraySortList(){
      Map<Integer, Long> tree = new TreeMap<>();
      List<Long> listOrdre = Arrays.asList(3345L,22L,44L,555L,6L,777L,23L,566L,66L);
      List<Long>   myList2  = Arrays.asList(44L,777L,3345L,66L,566L,555L,6L,23L,22L);

      for (long code : myList2) {
         tree.put(listOrdre.indexOf(code), code);
      }
      System.out.println(tree.values());
   }

   @Test
   public void testArrayToString(){
      Long[] arrays = new Long[]{3345L,22L,44L,555L,6L,777L,23L,566L,66L};
      System.out.println(StringUtils.join(arrays,","));
   }



   @Test
   public void testStringToArray(){
      String text = "3345,22,44,555,6,777,23,566,66";
      String[] array = StringUtils.split(text,",");
      for (int i = 0; i < array.length; i++) {
         System.out.println(array[i]);
      }
      System.out.println(array);
   }

   @Test
   public void testArraySortList2(){
      Map<Integer, Long> tree = new TreeMap<>();
      List<Long> listOrder = Arrays.asList(3345L,22L,44L,555L,6L,777L,23L,566L,66L);
      List<Long>   myList2  = Arrays.asList(44L,777L,3345L,66L,566L,555L,6L,23L,22L);

      List<Book> bookList = new ArrayList<>();
      for (Long bookId:
           myList2) {
         Book book = new Book();
         book.setBookId(bookId);
         book.setName("name-"+bookId);
         bookList.add(book);
      }
      System.out.println(bookList);

      Collections.sort(bookList, (o1, o2) -> {
         int io1 = listOrder.indexOf(o1.getBookId().longValue());
         int io2 = listOrder.indexOf(o2.getBookId().longValue());
         return (io1 == -1 || io2 == -1)?(io2-io1):(io1-io2);
      });

      System.out.println(bookList);
   }
}
