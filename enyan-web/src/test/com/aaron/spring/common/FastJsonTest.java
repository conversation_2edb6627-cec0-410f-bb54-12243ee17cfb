package com.aaron.spring.common;

import com.alibaba.fastjson2.JSONArray;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.junit.Test;

import java.util.Calendar;
import java.util.Random;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/3/9
 * @Modified By:
 */
public class FastJsonTest {
	@Test
	public void testWeek(){
		JSONArray arrayTotal = new JSONArray();
		JSONArray array1 = new JSONArray();
		JSONArray array2 = new JSONArray();
		for (int i = 0; i < 20; i++) {
			JSONArray tmp1 = new JSONArray(i, RandomUtils.nextInt());
			JSONArray tmp2 = new JSONArray(i, RandomUtils.nextInt());

			array1.add(tmp1);
			array2.add(tmp2);

			System.out.println(tmp1);
		}
		arrayTotal.add(array1);
		arrayTotal.add(array2);
		System.out.println("array1="+array1);
		System.out.println("arrayTotal="+arrayTotal);
		System.out.println(arrayTotal.toJSONString());
	}
}
