package com.aaron.spring.common;

import com.aaron.a4j.util.AaronDateUtils;
import com.aaron.model.RestAxis;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/9/4
 * @Modified By:
 */
class Employee{
	private String name;
	private String city;
	private Integer salary;

	public Employee() {
	}

	public Employee(String name, String city, Integer salary) {
		this.name = name;
		this.city = city;
		this.salary = salary;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public Integer getSalary() {
		return salary;
	}

	public void setSalary(Integer salary) {
		this.salary = salary;
	}

	@Override
	public String toString() {
		return "Employee{" +
				       "name='" + name + '\'' +
				       ", city='" + city + '\'' +
				       ", salary=" + salary +
				       '}';
	}
}
public class StreamGroupBy {
	@Test
	public void testWeek2(){
		List<Employee> employeeList = new ArrayList<>();
		Employee employee1 = new Employee("Alice","London",200);
		Employee employee2 = new Employee("Bob","London",150);
		Employee employee3 = new Employee("Charles","New York",160);
		Employee employee5 = new Employee("Charles","",300);
		Employee employee4 = new Employee("Dorothy","Hong Kong",190);
		Employee employee6 = new Employee("Dor","London",190);
		employeeList.add(employee1);employeeList.add(employee2);employeeList.add(employee3);employeeList.add(employee4);employeeList.add(employee5);
		employeeList.add(employee6);
		//groupBy
		Map<String, List<Employee>> employeesByCity = employeeList.stream().collect(groupingBy(Employee::getCity));
		System.out.println(employeesByCity);

		List<Employee> list = employeesByCity.get("London");
		List<Float> floatList = list.stream().map(data->data.getSalary().floatValue()).collect(Collectors.toList());
		System.out.println(floatList);

		//groupby count
		System.out.println(employeeList.stream().collect(Collectors.groupingBy(Employee::getCity,counting())));
	}
	@Test
	public void test2(){
		List<RestAxis> list = new ArrayList<>();
		RestAxis axis1 = new RestAxis();
		axis1.setAxisX("3");
		axis1.setAxisY(10f);
		list.add(axis1);

		RestAxis axis2 = new RestAxis();
		axis2.setAxisX("1");
		axis2.setAxisY(11f);
		list.add(axis2);

		RestAxis axis3 = new RestAxis();
		axis3.setAxisX("2");
		axis3.setAxisY(13f);
		list.add(axis3);

		List<RestAxis> sortedList = list.stream().sorted(Comparator.comparing(RestAxis::getAxisX)).collect(Collectors.toList());
		System.out.println(sortedList);
	}

	@Test
	public void test3(){
		List<RestAxis> list = new ArrayList<>();
		RestAxis axis1 = new RestAxis();
		axis1.setAxisX("3");
		axis1.setAxisY(10f);
		list.add(axis1);

		RestAxis axis2 = new RestAxis();
		axis2.setAxisX("1");
		axis2.setAxisY(11f);
		list.add(axis2);

		RestAxis axis3 = new RestAxis();
		axis3.setAxisX("2");
		axis3.setAxisY(13f);
		list.add(axis3);

		Float[] sortedArray = list.stream().sorted(Comparator.comparing(RestAxis::getAxisX)).map(RestAxis::getAxisY).toArray(Float[]::new);
		System.out.println(sortedArray);
		for (Float axisY : sortedArray) {
			System.out.println(axisY);
		}

		Float[] sortedArray2 = list.stream().map(RestAxis::getAxisY).toArray(Float[]::new);
		System.out.println(sortedArray2);
		for (Float axisY : sortedArray2) {
			System.out.println(axisY);
		}
	}
}
