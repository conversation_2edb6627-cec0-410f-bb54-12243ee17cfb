package com.aaron.spring.common;

import org.junit.Test;

import java.io.UnsupportedEncodingException;

/**
 *
 *
 * @Date: Created in  2018/6/2
 * @Modified By:
 */
public class CharTest {
    @Test
    public void testChar() throws UnsupportedEncodingException {
        String name = "中文";
        System.out.println(new String(name.getBytes("gbk"),"UTF-8"));

        System.out.println(new String(name.getBytes("utf-8"),"iso8859-1"));

        System.out.println(new String(name.getBytes("gbk"),"iso8859-1"));

        System.out.println(new String(name.getBytes("iso8859-1"),"utf-8"));

        String hash = "urn:uuid:5c31ba77-41f9-4493-aa6c-7d66be1772a0";
        System.out.println(hash.length());

        hash = "urn:uuid:049f09d6-a780-46f7-a9e6-7e85cd266ba0";
        System.out.println(hash.length());

    }
}
