package com.aaron.spring.common;

import com.aaron.http.HttpMethod;
import com.aaron.http.HttpProtocolHandler;
import com.aaron.http.HttpResult;
import com.aaron.spring.model.PayRateDTO;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 *
 *
 * @Date: Created in  2020-06-08
 * @Modified By:
 */
public class PayRateTest {
    @Test
    public void testFixer() throws Exception {
        //https://fixer.io/quickstart
        try {
            String FIXER_IO_API_KEY = "********************************";
            String FIXER_IO_API_SYMBOLS = "&symbols=USD,AUD,CAD,PLN,MXN,HKD,CNY,JPY,TWD,MYR,IDR,SGD,EUR";
            Map<String, String> map = new HashMap<>();
            map.put("account", "");
            map.put("password", "");

                /*
                HttpClient client = getConnection();
                HttpUriRequest post = getRequestMethod(map, "https://www.baidu.com", HttpMethod.GET);
                HttpResponse response = client.execute(post);

                if (response.getStatusLine().getStatusCode() == 200) {
                    HttpEntity entity = response.getEntity();
                    String message = EntityUtils.toString(entity, "utf-8");
                    System.out.println(message);
                } else {
                    System.out.println("请求失败");
                }
                */

            String url = "http://data.fixer.io/api/latest?access_key=" + FIXER_IO_API_KEY + FIXER_IO_API_SYMBOLS;
            System.out.println(url);
            //HttpResult result = HttpProtocolHandler.execute(map,url,HttpMethod.GET);
            //System.out.println(result.getStringResult());

            HttpResult result = HttpProtocolHandler.execute(null,url, HttpMethod.GET);
            String resultString = result.getStringResult();
            PayRateDTO payRateDTO = JSONObject.parseObject(resultString, PayRateDTO.class);
            System.out.println(resultString);
            String value = MethodUtils.invokeMethod(payRateDTO.getRates(),"getHKD").toString();
            System.out.println(value);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }
}
