package com.aaron.spring.common;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.excel.ExcelImportUtil;
import com.aaron.excel.ExcelRowReaderBatch;
import com.aaron.excel.util.ExcelReaderMutiFromHighlight;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.RedeemCodeNoteInfo;
import com.aaron.spring.service.EnyanRedeemCodeService;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * 从教务导入数据
 * @Author: Aaron <PERSON>
 * @Date: Created in  2019-11-12
 * @Modified By:
 */
public class ExcelReaderMutiTest extends ExcelRowReaderBatch {
    private String currentDate = DateFormatUtils.format(new Date(),"yyyyMMddHHmmss");
    private EnyanRedeemCodeService enyanRedeemCodeService;
    private List<EnyanBook> bookList;
    @Override
    public void getRows(int sheetIndex, int curRow, List<String> rowlist) {
        /*
        if (curRow<this.getBatchReadBeginRow()){
            System.out.println("--------------getRows--------------");
            System.out.println(rowlist);
        }
        if (curRow == 0){
            //
            //canInsert = false;
        }*/
    }
    @Override
    public void batchRead(List<List<String>> rowlists) {
        /*---------需要copy start-----------*/
        if (this.isInterrupted()){
            return;
        }
        super.batchRead(rowlists);
        /*---------需要copy end-----------*/
        //System.out.println("--------------batchRead--------------");
        /*
        for (List<String> rowlist : rowlists){
            if (this.getTotalRows() == 2){

                this.interruptImport();//实现中断

                this.saveRow(rowlist);//保存特定行
            }
            System.out.println(rowlist);
        }*/
        for (List<String> rowList : rowlists){
            String row1 = rowList.get(0);
            String row2 = rowList.get(1);
            String row3 = rowList.get(2);

            if (StringUtils.isBlank(row1)){
                continue;
            }

            String sql = String.format("update enyan_book set author='%s' where book_id = %s;",trim(row2) ,trim(row3));
            System.out.println(sql);
            //System.out.println(rowList);
        }
    }

    @Override
    public void readEnd() {
        /*---------需要copy start-----------*/
        super.readEnd();
        /*---------需要copy end-----------*/
        System.out.println("ExcelReaderMutiFromJW read end, 总共有"+this.getTotalRows()+"条数据。");
        System.out.println(""+this.getSaveRowLists());//获取保存的行信息
    }

    /**
     参考String.trim,加入了不连续空格nbsp;unicode160和汉字空格unicode12288
     **/
    public static String trim(String text){
        int len = text.length();
        int st = 0;
        char[] val = text.toCharArray();
        char p;
        while ((st < len) && ((p=val[st]) <= ' ' || p==160 || p==12288 )) {
            st++;
        }
        while ((st < len) && ((p =val[len - 1]) <= ' ' || p==160 || p==12288 )) {
            len--;
        }
        return ((st > 0) || (len < text.length())) ? text.substring(st, len) : text;
    }

    public static  void main(String[] args){
        try {
            String filePath = "/Users/<USER>/Documents/Test/211201-作者名更新.xlsx";

            ExcelReaderMutiTest mutiImport = new ExcelReaderMutiTest();
            mutiImport.setBatchReadBeginRow(1);//从1开始
            ExcelImportUtil.readExcel(filePath, mutiImport, mutiImport, mutiImport.getBatchReadBeginRow());
        }catch (Exception e){
            System.out.println(e);
        }
    }

}
