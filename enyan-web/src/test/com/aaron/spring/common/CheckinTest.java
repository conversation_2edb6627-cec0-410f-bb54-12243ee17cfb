package com.aaron.spring.common;

import com.aaron.spring.model.StuCheckin;
import com.aaron.spring.model.StuCheckinInfo;
import com.alibaba.fastjson2.JSON;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/12/22
 * @Modified By:
 */
public class CheckinTest {
	@Test
	public void testPageOfRecords(){
		StuCheckinInfo checkinInfo = new StuCheckinInfo();
		List<StuCheckin> list = new ArrayList<>();
		Long current = System.currentTimeMillis();
		List<Integer>  types= Arrays.asList(0,1,2,3,11,12,13,14,15,16,21,22,23,24);
		for (Integer type : types){
			StuCheckin checkin = new StuCheckin();
			checkin.setTime(-1L);
			checkin.setStatus(1);
			checkin.setType(type);
			list.add(checkin);
		}
		checkinInfo.setCheckinList(list);
		String json = JSON.toJSONString(checkinInfo);
		System.out.println(json);
		System.out.println(json.length());
	}
}
