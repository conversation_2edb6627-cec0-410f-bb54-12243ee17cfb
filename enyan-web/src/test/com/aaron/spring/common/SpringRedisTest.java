package com.aaron.spring.common;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 *
 *
 * @Date: Created in  2018/5/25
 * @Modified By:
 */
@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = { "classpath*:spring.xml,spring-mvc.xml,spring-mybatis.xml" })
@ContextConfiguration
        (
                {
                        "file:web/WEB-INF/classes/spring.xml",
                        "file:web/WEB-INF/classes/spring-mvc.xml",
                        "file:web/WEB-INF/classes/spring-mybatis.xml"
                }
        )
@WebAppConfiguration
public class SpringRedisTest {
    @Autowired
    private RedisTemplate<String, String> template;

    @Test
    public void testFirst() {
        // set username wlwlwlwl015
        template.opsForValue().set("username", "wlwlwlwl015");
        // get username
        System.out.println(template.opsForValue().get("username"));
    }
}
