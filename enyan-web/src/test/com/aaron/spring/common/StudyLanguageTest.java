package com.aaron.spring.common;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.aaron.a4j.util.AaronDateUtils;
import com.aaron.test.TestLogAppender;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/9/29
 * @Modified By:
 */
class StudyLanguage{
	private String word;
	private String meaning;
	private int section;

	public StudyLanguage(String word, String meaning, int section) {
		this.word = word;
		this.meaning = meaning;
		this.section = section;
	}

	public String getWord() {
		return word;
	}

	public void setWord(String word) {
		this.word = word;
	}

	public String getMeaning() {
		return meaning;
	}

	public void setMeaning(String meaning) {
		this.meaning = meaning;
	}

	public int getSection() {
		return section;
	}

	public void setSection(int section) {
		this.section = section;
	}
}
public class StudyLanguageTest {
	//,;
	String words = "אָדָם,人、亚当 562;אֶ֫רֶץ,世界、地 2505;אֱלֹהִים,上帝、神 2602;אָב,父亲、祖先 1211;אֵל,上帝、神 317;בֵּן,儿子;בַּ֫יִת,房子、家人 2047;דָּבָר,话、事、物 1454;יוֹם,日 2301;יִשְׂרָאֵל,以色列 2507;יְרוּשָׁלַ֫םִ,耶路撒冷 643;יְהוָה,雅威、主 6828;מִצְרַ֫יִם,埃及 682;מֹשֶׁה,摩西 766;מֶ֫לֶךְ,王 2530;סוּס,马 138;עֶ֫בֶד,仆人 803;פַּרְעֹה,法老 274;שָׁנָה,年 878;שֵׁם,名字 864;אֲדֹנָי,上主 774;אָח,兄弟 629;אִישׁ,男人、丈夫 2198;אִשָּׁה,女人、妻子 779;בַּת,女儿 603;גּוֹי,人民、国家 567;דֶּ֫רֶךְ,道路、路径 712 阳性/阴性;הַר,山、山脉 558;כֹּהֵן,祭司 750;לֵב,心 854;מַ֫יִם,水 586;נֶ֫פֶשׁ,生命、灵魂、脖子、喉咙 757 阴性;נָבִיא,先知 317;סֵ֫פֶר,书、书卷 191;עַ֫יִן,眼睛、泉源 900;עִיר,城市 1095 阴性;צָבָא,军队、部队、战争 487;קוֹל,声音、声响 505;רֹאשׁ,头、首领、顶部 600;תּוֹרָה,法律、训诲、妥拉 223;אֵשׁ,火 376 阴性;הֵיכָל,王宫、殿 80;וְ,和、但、也、甚至 50524;זָהָב,金 392;חֶ֫רֶב,剑 413 阴性;חַי,（adj.）活的 254;יֶ֫לֶד,男童、男孩 89;יָם,海、西 396;כֶּ֫סֶף,银 403;כֹּה,这样、如此 577;מָקוֹם,地方 401;מִשְׁפָּט,审判、传统、法律、习俗、规章 425;מִזְבֵּחַ,祭坛 403;נְאֻם,言词、宣言、启示 376;עוֹלָם,永远、永恒 439;עָנָן,云 87;רוּחַ,风、灵、气息 378 （通常是阴性）;שַׂר,官长、首领、王子 421;שַׁ֫עַר,城门 373;שָׁמַ֫יִם,天、天空 421;אַחַר,后面、之后 718;אֵת,与 890;אֵת,特定直接受词记号，不需翻译 10978;אֶל,对着、向着 5518;בֵּין,在......之间 409;בְּ,在之中、对着、藉由、同 15559;בְּתוֹךְ,在......之间、中间;חָכְמָה,智慧、经验、精明 153;כְּ,像、如同、按照 3053;לִפְנֵי,在......面前 1102;לְ,给、向着、为了 20321;לְמַ֫עַן,为了......缘故、因为、为了 272;מִן,从 7582;מַ֫עַל,在......以上 140;מִצְוָה,命令、诫命 184;עַד,直到、当......的时候 1263;עַל־,上面、在......之上、因为、按照 5777;עִם,与......一起 1048;פֶּה,嘴巴，开口 329;שָׂדֶה,田野、草场 329;תַּ֫חַת,在......之下、下面、代替 510;אֶחָד,一 976;גָּדוֹל,大、伟大 527;דַּל,贫穷 48;זָקֵן,（adj.)老；（n.)老人、长老 180;חָכָם,有智慧地 138;טוֹב,好 535;יָפֶה,漂亮 43;יָשָׁר,直、正义、正直、公正 119;כֵּן,这样、因此 741;מְאֹד,很、非常地 300;מְעַט,几个、少 101;עַתָּה,现在、最后，毕竟 435;צַדִּיק,公义地、公正地、无罪地 206;קֹ֫דֶשׁ,(n.)圣洁、圣物 470;קָדוֹשׁ,(adj.)圣洁的 117;קָטֹן,小、不重要、年轻 74;קָרוֹב,近、临近 75;קָשֶׁה,困难的、坚硬的、严重的 36;רָחוֹק,远的、遥远的 84;רַב,许多、好多 419;רַע,恶的、坏的 312;רָשָׁע,恶的、违法的 264;שִׁיר,歌 78;אֹ֫הֶל,帐棚 348;אַחֵר,另一个、外邦的 166;אֵיךְ,如何？ 61;אֶ֫לֶף,千 496;אֲשֶׁר,关系代名词 who, which, that;בְּהֵמָה,牛、动物 190;גַּם,也、的确 769;דָּם,血 361;הֲ,疑问质词;טָהוֹר,干净、纯洁 96;כִּי,因为、为了；（反义）但是，除了；（强调）确实地 4487;כְּסִיל,愚笨、迟钝 70;לָ֫מָּה,为什么？ 178;מָה,什么？如何? 571;מַדּוּעַ,为何？为什么？72;מִי,谁？424;עָנִי,贫穷、受苦 80;שֶׁ֫מֶן,油 193;שֹׁפֵט,审判官 68;אוֹ,或 321;אַ֫יִן,ʾ无、没有 791;אַף,鼻孔、鼻子、忿怒 277;בֹּ֫קֶר,早晨 213;בָּקָר,牛、牲畜、家畜 183;בְּרָכָה,祝福、礼物 71;הֵן,如果、看啊！ 107;הִנֵּה,看啊！ 1061;חַטָּאת,罪、赎罪祭 298 阴性;יֵשׁ,有 138;כָּבוֹד,荣耀、尊重 200;כְּלִי,器皿、武器 325;לֶ֫חֶם,面包、食物 340;לְבַד,（副词）单独地、独自；（介系词）除......之外 161;מִשְׁפָּחָה,家族、亲族 304;מִלְחָמָה,战争、争斗 319;סָבִיב,环绕、在......四周、邻近 338;עַמ,人民 1869;עֵץ,树、木材 329;אֶ֫בֶן,石头 276 阴性;אֲדָמָח,土地 222;אֹיֵב,敌人 285;בָּשָׂר,肉，皮，肉体 270;בְּרִית,约 287;גְבוּל,界限、领土 251;חַ֫יִל,力量、财富、军队 246;חֶ֫סֶד,信实、慈爱、献身、忠贞的爱 249;חֹ֫דֶשׁ,新月、月份 283;יָד,手；（暗喻）边、力量 1627 阴性;מָ֫וֶת,死亡 153;מַטֶּה,棍、杖、支派 252;מִדְבָּר,牧场、旷野 269;עוֹד,还、仍旧、再 491;עֵת,时间 296 阴性;עֹלַה,燔祭 286;פָּנֹים,脸 2126;צֹאן,羊群、小群的绵羊或山羊 274;רֶ֫גֶל,脚 251 阴性;שָׁלוֹם,平安、健康、拯救;שָׁם,那里、那时;אַמָּה,肘、前臂;אֵם,母亲(阴性);בֶּ֫גֶד,衣裳;זֶ֫רַע,种子、后裔、子孙、后代;חָצֵר,居所、院子、村庄;לַ֫יְלָה,夜晚;לָכֵן,因此;מוֹעֵד,指定的地方或时间、节期;מַחֲנֶה,营、军队;מַלְאָךְ,使者、天使;מַעֲשֶׂה,工作;נַ֫עַר,青少年、少年人、仆人;נַחֲלָה,遗产、财产、所有物;עָוֹן,罪恶、罪状、罪的刑罚;קֶ֫רֶב,内在、中间、身体（介系词）;רַק,只、但是、无论如何;תּמִיד,不断地、总是;אָכַל,吃;אָמַר,说、想;הָלַךְ,去、行走、（暗喻，行事为人）、死亡;הָיָה,是、发生、成为;יָצָה,出去（来）、前去（来）;יָשַׁב,坐、定居、居住;נָתַן,给;עָשָׂה,做、造成、创造;רָאָה,看见、理解、了解;שָׁבַת,停止、休息;שָׁמַע,听见、留心听、明白、顺服;אֲרוֹן,约柜、箱子;מִנְחָה,礼物、奉献;שַׁבָּת,安息日;אַךְ,只有、实在;אַל,不、不是;אִם,假如、然后;לֹא,不、不是;בָּרַךְ,祝福;זָכַר,记住、回想;זָקֵן,（变）老;חָזַק,（变）强、坚强、有勇气;יָדַע,知道、了解、发生性关系;כָּבֵד,重、尊荣;כָּתַב,写;מָלֵא,满、充满、满足;מָלַךְ,统治、作王;מָצָא,找、得到;פָּקַד,访问、计算、任命;שָׁכַב,躺下、性交;שָׁלַח,伸出、放开、差遣;שָׁמַר,遵守、看守、监视、观察;בּוֹא,去、进入、来;בָּנָה,建造;יָלַד,生产;יָרֵא,害怕、恐惧;יָרַד,下去;לָקַח,拿;מוּת,死亡;נָפַל,跌倒;נָשָׂא,举起、背负、带去;עָלָה,上去;עָבַר,逾越、犯法;עָמַד,站立;קוּם,起来、兴起、站立;קָרָא,呼叫、遇见、大声读;שִׂים,设置、安置、放置;שׁוּב,转、回转;חָיָה,活;יָכֹל,能够;כָּרַת,切断、立约;סוּר,转往一边离开;עָבַד,服事;עָנָה,回答、作证;אֹ֫זֶן,耳朵（阴性）;אַ֫יִל,公绵羊；（形）有力的;גִּבּוֹר,（形）有力的、有能的、像英雄般的；（名）勇士、强者;זֶ֫בַח,祭物;צְדָקָה,公义;צָפוֹן,北（阴性）;אָז,那时、之前;אַף,也、甚至;פֶּן־,免得;גָּאַל,救赎;חָטָא,没有命中（一个记号）、犯罪;יָסַף,增加;יָרַשׁ,继承、征服、拥有、剥夺;כָּפַר,遮盖;נָטָה,支搭帐棚、伸出;עָזַב,离开、抛弃;קָרַב,接近;רָבָה,（成为）多数，伟大;שָׁתָה,喝;תָּמַם,完成;כַּף,手、手掌（阴性）;רֵ֫עַ,朋友、伙伴、同伴;נֶ֫גֶד,在......对面、前面;אָבַד,消灭;אָהַב,爱;אָסַף,聚集、毁灭;בָּעַר,烧灭、烧;גָּלָה,揭露、揭开;טָהֵר,干净、洁净;כלה,停止、结束、完、完成;רוּם,（变）高、高举;שָׁפַט,审判、进行诉讼;אֱמֶת,真实、可靠（阴性）;כִּסֵּא,座位、宝座;מִסְפָּר,数目;עֶ֫שֶׂר,十;שֶׁ֫מֶשׁ,太阳;";
	String sql = "insert into %s (word,meaning,section) values('%s','%s',%d);";

	/*
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (1,'א','Aleph',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (2,'ב','Bet',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (3,'ג','Gimmel',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (4,'ד','Dalet',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (5,'ה','He',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (6,'ו','Waw',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (7,'ז','Zain',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (8,'ח','Het',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (9,'ט','Tet',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (10,'י','Yod',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (11,'כ','Kap',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (12,'ל','Lamed',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (13,'מ','Mem',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (14,'נ','Nun',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (15,'ס','Sameq',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (16,'ע','Ain',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (17,'פ','Pe',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (18,'צ','Ṣade',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (19,'ק','Qop',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (20,'ר','Resh',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (21,'שׂ','Sin',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (22,'שׁ','she',0);
INSERT INTO `Hebrew` (`dataId`,`word`,`meaning`,`section`) VALUES (23,'ת','Taw',0);
	* */

	@Test
	public void testWord(){
		List<StudyLanguage> list = new ArrayList<>();
//		Arrays.stream(words.split(";")).map(s->s.split(",")).collect(Collectors.toList(e -> new StudyLanguage(e[0],e[1],1)));
		List<String[]>  strings = Arrays.stream(words.split(";")).map(s->s.split(",")).collect(Collectors.toList());
//		List<String[]>  strings = Arrays.stream(words.split(";")).map(s->s.split(",")).collect(Collectors.toCollection());
		/*
		String[] ws = words.split(";");
		for (String w:ws){
			String[] temp = w.split(",");
			if (temp.length > 0){

			}
		}
		insert into Hebrew (word,meaning,section) values(אֱלֹהִים,上帝、神 2602,1)
		*/
		for (String[] temp: strings){
			if (temp.length == 0){
				continue;
			}
			StudyLanguage studyLanguage = new StudyLanguage(temp[0],temp[1],1);
			list.add(studyLanguage);
		}
		for (StudyLanguage studyLanguage:list){
			printSQL(studyLanguage, "Hebrew");
		}
	}

	public void printSQL(StudyLanguage study, String table){
		String sqlFormat = String.format(sql,table , study.getWord(),study.getMeaning(),study.getSection());
		System.out.println(sqlFormat);
	}
}
