package com.aaron.spring.common;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.junit.Test;

import java.util.Calendar;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/3/28
 * @Modified By:
 */
public class JsonTest {
	@Test
	public void testWeek(){
		String locatorText = "{\"highlight\":\"神莫大的恩典令人惊叹\"}";
		JSONObject object = JSONObject.parseObject(locatorText,JSONObject.class);
		System.out.println(object.get("highlight"));
	}
}
