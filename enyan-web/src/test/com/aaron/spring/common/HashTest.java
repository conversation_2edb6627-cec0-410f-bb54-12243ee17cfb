package com.aaron.spring.common;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/27
 * @Modified By:
 */
public class HashTest {
  @Test
  public void testHash(){
   List<Long> list = new ArrayList<>();
   for (int i = 0; i < 20000; i++) {
     list.add((long) i);
   }
   //System.out.println(list.toString());
   System.out.println(list.hashCode());
     String text1 = "You";
     System.out.println(text1.hashCode());
     String text2 = "You can test it, my results are for 4000000 compare operations including identical, equal and different strings1";
     System.out.println(text2.hashCode());
  }
}
