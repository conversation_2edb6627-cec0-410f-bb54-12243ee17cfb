package com.aaron.spring.common;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

/**
 *
 *
 * @Date: Created in  2018/5/30
 * @Modified By:
 */
public class RandomTest {
    @Test
    public void testLength(){
        for (int i = 0; i < 1000; i++) {
            Float f = RandomUtils.nextFloat(0, 1f)*10000;
//            System.out.println(f.intValue());
            String str=String.format("%04d", f.intValue());
            System.out.println(str);
        }
    }

    @Test
    public void testIndex(){
        String url = "http://localhost:8080/emailSuccess";
        int index = StringUtils.indexOf(url, "email");
        System.out.println("index:"+index);
    }
}
