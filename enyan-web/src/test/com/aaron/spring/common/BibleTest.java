package com.aaron.spring.common;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

class Bible implements Serializable{
    private static final long serialVersionUID = 6166947515482438302L;
    private List<BibleBook> bookList;

    public List<BibleBook> getBookList() {
        return bookList;
    }

    public void setBookList(List<BibleBook> bookList) {
        this.bookList = bookList;
    }
}

class BibleBook implements Serializable {
    private static final long serialVersionUID = -3699609907955365813L;
    private Integer index;
    private String bookNameChs;
    private Integer chapterNum;

    public Integer getChapterNum() {
        return chapterNum;
    }

    public void setChapterNum(Integer chapterNum) {
        this.chapterNum = chapterNum;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getBookNameChs() {
        return bookNameChs;
    }

    public void setBookNameChs(String bookNameChs) {
        this.bookNameChs = bookNameChs;
    }

    @Override
    public String toString() {
        return "BibleBook{" +
                "index=" + index +
                ", bookNameChs='" + bookNameChs + '\'' +
                ", chapterNum=" + chapterNum +
                '}';
    }
}

class BibleChapter implements Serializable{
    private static final long serialVersionUID = 7712406908776239667L;
    private Integer index;
    private String bookNameChs;
    private String chapterNameChs;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getBookNameChs() {
        return bookNameChs;
    }

    public void setBookNameChs(String bookNameChs) {
        this.bookNameChs = bookNameChs;
    }

    public String getChapterNameChs() {
        return chapterNameChs;
    }

    public void setChapterNameChs(String chapterNameChs) {
        this.chapterNameChs = chapterNameChs;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }
}

class BiblePlanFourMark implements Serializable{
    private static final long serialVersionUID = -6194228837927307428L;
    List<BibleBook> listGenesis = new ArrayList<>();
    List<BibleBook> listJob = new ArrayList<>();
    List<BibleBook> listIsaiah = new ArrayList<>();
    List<BibleBook> listMatthew = new ArrayList<>();
    List<BibleBook> listRomans = new ArrayList<>();

    List<BibleChapter> allDaysGenesis = new ArrayList<>();
    List<BibleChapter> allDaysJob = new ArrayList<>();
    List<BibleChapter> allDaysIsaiah = new ArrayList<>();
    List<BibleChapter> allDaysMatthew = new ArrayList<>();
    List<BibleChapter> allDaysRomans = new ArrayList<>();

    public static final String BASE_DATE_STRING = "2024-01-01 01:01:01";

    public static Date baseDate = new Date();

    int daysCount = 0;//打印计划的时候计数

    boolean skipSunday = true;

    public void initWithBible(Bible bible){
        for (int i = 0; i < bible.getBookList().size(); i++) {
            BibleBook book = bible.getBookList().get(i);
            int index = i + 1;
            if (index >= BibleStepIndex.stepRomans){
                listRomans.add(book);
                continue;
            }
            if (index >= BibleStepIndex.stepMatthew){
                listMatthew.add(book);
                continue;
            }
            if (index >= BibleStepIndex.stepIsaiah){
                listIsaiah.add(book);
                continue;
            }
            if (index >= BibleStepIndex.stepJob){
                listJob.add(book);
                continue;
            }
            if (index >= BibleStepIndex.stepGenesis){
                listGenesis.add(book);
                continue;
            }
        }
        this.initAllDaysWithBookListToDayList(listGenesis,allDaysGenesis);
        this.initAllDaysWithBookListToDayList(listJob,allDaysJob);
        this.initAllDaysWithBookListToDayList(listIsaiah,allDaysIsaiah);
        this.initAllDaysWithBookListToDayList(listMatthew,allDaysMatthew);
        this.initAllDaysWithBookListToDayList(listRomans,allDaysRomans);

        try {
            baseDate = DateFormat.getDateInstance().parse(BASE_DATE_STRING);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }
    /**
     * <p>将章节信息添加到相应书签list</p>
     * @param bookList
     * @param chapterList
     * @return: void
     * @since : 12/29/20
     */
    public void initAllDaysWithBookListToDayList(List<BibleBook> bookList, List<BibleChapter> chapterList){
        for (BibleBook book:bookList){
            for (int i = 0; i < book.getChapterNum(); i++) {
                int index = i + 1;
                BibleChapter chapter = new BibleChapter();
                chapter.setIndex(index);
                chapter.setBookNameChs(book.getBookNameChs());
                chapter.setChapterNameChs(book.getBookNameChs()+"#第"+index+"章;");
                chapterList.add(chapter);
            }
        }
//        System.out.println(bookList.getClass() + ":"+chapterList.size());
    }

    public void printPlanAllYear(){
        daysCount = 0;
        for (int i = 0; i < 18; i++) {
            printYearAndMonth(2024,i+1);
        }
    }

    /**
     * <p>打印所有书签段的天数计划信息</p>
     * @param day 从0开始
     * @return: void
     * @since : 12/29/20
     */
    public String printPlanWithDay(int day){
        StringBuffer sb = new StringBuffer();
        sb.append(this.printPlanWithMarkListAndDay(allDaysGenesis,day));
        sb.append(this.printPlanWithMarkListAndDay(allDaysJob,day));
        sb.append(this.printPlanWithMarkListAndDay(allDaysIsaiah,day));
        sb.append(this.printPlanWithMarkListAndDay(allDaysMatthew,day));
        sb.append(this.printPlanWithMarkListAndDay(allDaysRomans,day));
        return sb.toString();
    }

    /**
     * <p>打印各书签段的相应天数信息</p>
     * @param list
     * @param day 从0开始
     * @return: void
     * @since : 12/29/20
     */
    public String printPlanWithMarkListAndDay(List<BibleChapter> list, int day){
        int index = (day)%list.size();
        String result = list.get(index).getChapterNameChs();
//        System.out.println(result);
        return result;
    }
    /**
     * <p>展示年月日历并获取day信息</p>
     * <p>";"替换成"\n"</p>
     * <p>"#"替换成" "</p>
     * <p>按月copy到numbers进行替换</p>
     * @param year
     * @param month
     * @return: void
     * @since : 12/29/20
     */
    public void printYearAndMonth(int year, int month){
        /*
        1、计算当月总天数
         */
        int days;
        if(month==2){
            if(year%4==0&&year%100!=0||year%400==0){
                days=29;
            }else{
                days=28;
            }
        }else if (month==4||month==6||month==9||month==11){
            days=30;
        }else{
            days=31;
        }
        /*
        2、计算当月1号距离1900年1月1号的天数
         */
        int sum=0;
        //1.1对1900年到year之间的天数累加
        for(int i=1900;i<year;i++){
            if (i%4==0&&i%100!=0||i%400==0){
                sum+=366;
            }else {
                sum+=365;
            }
        }
        //对月份天数累加
        for(int i=1;i<month;i++){
            if(i==2){
                if(year%4== 0 && year%100!=0|| year%400==0){
                    sum+=29;
                }else{
                    sum+=28;
                }
            }else if(i==4||i==6||i==9||i==11){
                sum+=30;
            }else{
                sum+=31;
            }
        }
        //对天数累加
        sum+=1;

        /*
        3.打印当月日历
         */
        System.out.println(year+"年"+month+"月");

        //计算当月1号是星期几，前面打印几个空格
        System.out.println("日\t一\t二\t三\t四\t五\t六");
        for(int i=0;i<sum%7;i++){
            System.out.print("\t");
        }

        for(int i=1;i<=days;i++){
            int num = sum%7;
            if(num==6){//如果是星期六，换行
                System.out.println(i+";"+printPlanWithDay(daysCount));
                daysCount++;
            }else if (num == 0){//如果是星期日，判断是否跳过周日
                if (skipSunday == false){
                    System.out.print(i+";"+printPlanWithDay(daysCount)+"\t");
                    daysCount++;
                }else {
                    System.out.print(i+";"+"\t");
                }
            }else {
                System.out.print(i+";"+printPlanWithDay(daysCount)+"\t");
                daysCount++;
            }
            sum++;

        }
        System.out.println("\n");
    }

    public List<BibleBook> getListGenesis() {
        return listGenesis;
    }

    public void setListGenesis(List<BibleBook> listGenesis) {
        this.listGenesis = listGenesis;
    }

    public List<BibleBook> getListJob() {
        return listJob;
    }

    public void setListJob(List<BibleBook> listJob) {
        this.listJob = listJob;
    }

    public List<BibleBook> getListIsaiah() {
        return listIsaiah;
    }

    public void setListIsaiah(List<BibleBook> listIsaiah) {
        this.listIsaiah = listIsaiah;
    }

    public List<BibleBook> getListMatthew() {
        return listMatthew;
    }

    public void setListMatthew(List<BibleBook> listMatthew) {
        this.listMatthew = listMatthew;
    }

    public List<BibleBook> getListRomans() {
        return listRomans;
    }

    public void setListRomans(List<BibleBook> listRomans) {
        this.listRomans = listRomans;
    }

    public List<BibleChapter> getAllDaysGenesis() {
        return allDaysGenesis;
    }

    public void setAllDaysGenesis(List<BibleChapter> allDaysGenesis) {
        this.allDaysGenesis = allDaysGenesis;
    }

    public List<BibleChapter> getAllDaysJob() {
        return allDaysJob;
    }

    public void setAllDaysJob(List<BibleChapter> allDaysJob) {
        this.allDaysJob = allDaysJob;
    }

    public List<BibleChapter> getAllDaysIsaiah() {
        return allDaysIsaiah;
    }

    public void setAllDaysIsaiah(List<BibleChapter> allDaysIsaiah) {
        this.allDaysIsaiah = allDaysIsaiah;
    }

    public List<BibleChapter> getAllDaysMatthew() {
        return allDaysMatthew;
    }

    public void setAllDaysMatthew(List<BibleChapter> allDaysMatthew) {
        this.allDaysMatthew = allDaysMatthew;
    }

    public List<BibleChapter> getAllDaysRomans() {
        return allDaysRomans;
    }

    public void setAllDaysRomans(List<BibleChapter> allDaysRomans) {
        this.allDaysRomans = allDaysRomans;
    }
}

interface BibleStepIndex {
    int stepGenesis = 1;//创世记
    int stepJob = 18;//约伯记
    int stepIsaiah = 23;//以赛亚书
    int stepMatthew = 40;//马太福音
    int stepRomans = 45;//罗马书
}
/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  12/29/20
 * @Modified By:
 */
public class BibleTest {
    String bookText = "[{\"chapterNum\":50,\"nameChs\":\"创\"},{\"chapterNum\":40,\"nameChs\":\"出\"},{\"chapterNum\":27,\"nameChs\":\"利\"},{\"chapterNum\":36,\"nameChs\":\"民\"},{\"chapterNum\":34,\"nameChs\":\"申\"},{\"chapterNum\":24,\"nameChs\":\"书\"},{\"chapterNum\":21,\"nameChs\":\"士\"},{\"chapterNum\":4,\"nameChs\":\"得\"},{\"chapterNum\":31,\"nameChs\":\"撒上\"},{\"chapterNum\":24,\"nameChs\":\"撒下\"},{\"chapterNum\":22,\"nameChs\":\"王上\"},{\"chapterNum\":25,\"nameChs\":\"王下\"},{\"chapterNum\":29,\"nameChs\":\"代上\"},{\"chapterNum\":36,\"nameChs\":\"代下\"},{\"chapterNum\":10,\"nameChs\":\"拉\"},{\"chapterNum\":13,\"nameChs\":\"尼\"},{\"chapterNum\":10,\"nameChs\":\"斯\"},{\"chapterNum\":42,\"nameChs\":\"伯\"},{\"chapterNum\":150,\"nameChs\":\"诗\"},{\"chapterNum\":31,\"nameChs\":\"箴\"},{\"chapterNum\":12,\"nameChs\":\"传\"},{\"chapterNum\":8,\"nameChs\":\"歌\"},{\"chapterNum\":66,\"nameChs\":\"赛\"},{\"chapterNum\":52,\"nameChs\":\"耶\"},{\"chapterNum\":5,\"nameChs\":\"哀\"},{\"chapterNum\":48,\"nameChs\":\"结\"},{\"chapterNum\":12,\"nameChs\":\"但\"},{\"chapterNum\":14,\"nameChs\":\"何\"},{\"chapterNum\":3,\"nameChs\":\"珥\"},{\"chapterNum\":9,\"nameChs\":\"摩\"},{\"chapterNum\":1,\"nameChs\":\"俄\"},{\"chapterNum\":4,\"nameChs\":\"拿\"},{\"chapterNum\":7,\"nameChs\":\"弥\"},{\"chapterNum\":3,\"nameChs\":\"鸿\"},{\"chapterNum\":3,\"nameChs\":\"哈\"},{\"chapterNum\":3,\"nameChs\":\"番\"},{\"chapterNum\":2,\"nameChs\":\"该\"},{\"chapterNum\":14,\"nameChs\":\"亚\"},{\"chapterNum\":4,\"nameChs\":\"玛\"},{\"chapterNum\":28,\"nameChs\":\"太\"},{\"chapterNum\":16,\"nameChs\":\"可\"},{\"chapterNum\":24,\"nameChs\":\"路\"},{\"chapterNum\":21,\"nameChs\":\"约\"},{\"chapterNum\":28,\"nameChs\":\"徒\"},{\"chapterNum\":16,\"nameChs\":\"罗\"},{\"chapterNum\":16,\"nameChs\":\"林前\"},{\"chapterNum\":13,\"nameChs\":\"林后\"},{\"chapterNum\":6,\"nameChs\":\"加\"},{\"chapterNum\":6,\"nameChs\":\"弗\"},{\"chapterNum\":4,\"nameChs\":\"腓\"},{\"chapterNum\":4,\"nameChs\":\"西\"},{\"chapterNum\":5,\"nameChs\":\"帖前\"},{\"chapterNum\":3,\"nameChs\":\"帖后\"},{\"chapterNum\":6,\"nameChs\":\"提前\"},{\"chapterNum\":4,\"nameChs\":\"提后\"},{\"chapterNum\":3,\"nameChs\":\"多\"},{\"chapterNum\":1,\"nameChs\":\"门\"},{\"chapterNum\":13,\"nameChs\":\"来\"},{\"chapterNum\":5,\"nameChs\":\"雅\"},{\"chapterNum\":5,\"nameChs\":\"彼前\"},{\"chapterNum\":3,\"nameChs\":\"彼后\"},{\"chapterNum\":5,\"nameChs\":\"约壹\"},{\"chapterNum\":1,\"nameChs\":\"约贰\"},{\"chapterNum\":1,\"nameChs\":\"约叁\"},{\"chapterNum\":1,\"nameChs\":\"犹\"},{\"chapterNum\":22,\"nameChs\":\"启\"}]";

    String bibleText = "{\"bookList\":[{\"bookNameChs\":\"创\",\"chapterNum\":50,\"index\":1},{\"bookNameChs\":\"出\",\"chapterNum\":40,\"index\":2},{\"bookNameChs\":\"利\",\"chapterNum\":27,\"index\":3},{\"bookNameChs\":\"民\",\"chapterNum\":36,\"index\":4},{\"bookNameChs\":\"申\",\"chapterNum\":34,\"index\":5},{\"bookNameChs\":\"书\",\"chapterNum\":24,\"index\":6},{\"bookNameChs\":\"士\",\"chapterNum\":21,\"index\":7},{\"bookNameChs\":\"得\",\"chapterNum\":4,\"index\":8},{\"bookNameChs\":\"撒上\",\"chapterNum\":31,\"index\":9},{\"bookNameChs\":\"撒下\",\"chapterNum\":24,\"index\":10},{\"bookNameChs\":\"王上\",\"chapterNum\":22,\"index\":11},{\"bookNameChs\":\"王下\",\"chapterNum\":25,\"index\":12},{\"bookNameChs\":\"代上\",\"chapterNum\":29,\"index\":13},{\"bookNameChs\":\"代下\",\"chapterNum\":36,\"index\":14},{\"bookNameChs\":\"拉\",\"chapterNum\":10,\"index\":15},{\"bookNameChs\":\"尼\",\"chapterNum\":13,\"index\":16},{\"bookNameChs\":\"斯\",\"chapterNum\":10,\"index\":17},{\"bookNameChs\":\"伯\",\"chapterNum\":42,\"index\":18},{\"bookNameChs\":\"诗\",\"chapterNum\":150,\"index\":19},{\"bookNameChs\":\"箴\",\"chapterNum\":31,\"index\":20},{\"bookNameChs\":\"传\",\"chapterNum\":12,\"index\":21},{\"bookNameChs\":\"歌\",\"chapterNum\":8,\"index\":22},{\"bookNameChs\":\"赛\",\"chapterNum\":66,\"index\":23},{\"bookNameChs\":\"耶\",\"chapterNum\":52,\"index\":24},{\"bookNameChs\":\"哀\",\"chapterNum\":5,\"index\":25},{\"bookNameChs\":\"结\",\"chapterNum\":48,\"index\":26},{\"bookNameChs\":\"但\",\"chapterNum\":12,\"index\":27},{\"bookNameChs\":\"何\",\"chapterNum\":14,\"index\":28},{\"bookNameChs\":\"珥\",\"chapterNum\":3,\"index\":29},{\"bookNameChs\":\"摩\",\"chapterNum\":9,\"index\":30},{\"bookNameChs\":\"俄\",\"chapterNum\":1,\"index\":31},{\"bookNameChs\":\"拿\",\"chapterNum\":4,\"index\":32},{\"bookNameChs\":\"弥\",\"chapterNum\":7,\"index\":33},{\"bookNameChs\":\"鸿\",\"chapterNum\":3,\"index\":34},{\"bookNameChs\":\"哈\",\"chapterNum\":3,\"index\":35},{\"bookNameChs\":\"番\",\"chapterNum\":3,\"index\":36},{\"bookNameChs\":\"该\",\"chapterNum\":2,\"index\":37},{\"bookNameChs\":\"亚\",\"chapterNum\":14,\"index\":38},{\"bookNameChs\":\"玛\",\"chapterNum\":4,\"index\":39},{\"bookNameChs\":\"太\",\"chapterNum\":28,\"index\":40},{\"bookNameChs\":\"可\",\"chapterNum\":16,\"index\":41},{\"bookNameChs\":\"路\",\"chapterNum\":24,\"index\":42},{\"bookNameChs\":\"约\",\"chapterNum\":21,\"index\":43},{\"bookNameChs\":\"徒\",\"chapterNum\":28,\"index\":44},{\"bookNameChs\":\"罗\",\"chapterNum\":16,\"index\":45},{\"bookNameChs\":\"林前\",\"chapterNum\":16,\"index\":46},{\"bookNameChs\":\"林后\",\"chapterNum\":13,\"index\":47},{\"bookNameChs\":\"加\",\"chapterNum\":6,\"index\":48},{\"bookNameChs\":\"弗\",\"chapterNum\":6,\"index\":49},{\"bookNameChs\":\"腓\",\"chapterNum\":4,\"index\":50},{\"bookNameChs\":\"西\",\"chapterNum\":4,\"index\":51},{\"bookNameChs\":\"帖前\",\"chapterNum\":5,\"index\":52},{\"bookNameChs\":\"帖后\",\"chapterNum\":3,\"index\":53},{\"bookNameChs\":\"提前\",\"chapterNum\":6,\"index\":54},{\"bookNameChs\":\"提后\",\"chapterNum\":4,\"index\":55},{\"bookNameChs\":\"多\",\"chapterNum\":3,\"index\":56},{\"bookNameChs\":\"门\",\"chapterNum\":1,\"index\":57},{\"bookNameChs\":\"来\",\"chapterNum\":13,\"index\":58},{\"bookNameChs\":\"雅\",\"chapterNum\":5,\"index\":59},{\"bookNameChs\":\"彼前\",\"chapterNum\":5,\"index\":60},{\"bookNameChs\":\"彼后\",\"chapterNum\":3,\"index\":61},{\"bookNameChs\":\"约壹\",\"chapterNum\":5,\"index\":62},{\"bookNameChs\":\"约贰\",\"chapterNum\":1,\"index\":63},{\"bookNameChs\":\"约叁\",\"chapterNum\":1,\"index\":64},{\"bookNameChs\":\"犹\",\"chapterNum\":1,\"index\":65},{\"bookNameChs\":\"启\",\"chapterNum\":22,\"index\":66}]}";
    @Test
    public void testBibleBookInfo(){
        List<BibleBook> list = new ArrayList<>();
        for (int i = 0; i < 66; i++) {
            BibleBook book = new BibleBook();
            book.setBookNameChs("");
            book.setChapterNum(10);
            list.add(book);
        }
        System.out.println(JSONObject.toJSONString(list));
    }

    @Test
    public void testPrintBibleBookInfo(){
        JSONArray array = JSONArray.parseArray(bookText);
        List<BibleBook> list = array.toList(BibleBook.class);
        Bible bible = new Bible();
        bible.setBookList(list);
        for (int i = 0; i < list.size(); i++) {
            BibleBook book = list.get(i);
            book.setIndex(i+1);
        }
        System.out.println(JSONObject.toJSONString(bible));
    }

    @Test
    public void testPrintBible() {
        Bible bible = JSONObject.parseObject(bibleText,Bible.class);
        System.out.println(JSONObject.toJSONString(bible));
    }

    @Test
    public void testPrintSection() throws ParseException {
        Date date = DateFormat.getDateInstance().parse("2021-01-01");
        Bible bible = JSONObject.parseObject(bibleText,Bible.class);
        BiblePlanFourMark planFourMark = new BiblePlanFourMark();
        planFourMark.initWithBible(bible);

        System.out.println("创-斯 章节天数："+planFourMark.getAllDaysGenesis().size());
        System.out.println("伯-歌 章节天数："+planFourMark.getAllDaysJob().size());
        System.out.println("赛-玛 章节天数："+planFourMark.getAllDaysIsaiah().size());
        System.out.println("太-徒 章节天数："+planFourMark.getAllDaysMatthew().size());
        System.out.println("罗-启 章节天数："+planFourMark.getAllDaysRomans().size());
    }

    @Test
    public void testPrintDateAndBible() throws ParseException {
        Date date = DateFormat.getDateInstance().parse("2021-01-01");
        Bible bible = JSONObject.parseObject(bibleText,Bible.class);
        BiblePlanFourMark planFourMark = new BiblePlanFourMark();
        planFourMark.initWithBible(bible);

        for (int i = 0; i < 436; i++) {
            //BibleBook book = list.get(i);
            //book.setIndex(i+1);
            System.out.println(DateFormatUtils.format(date,"yyyy-MM-dd"));
            planFourMark.printPlanWithDay(i);
            date = DateUtils.addDays(date,1);
        }
        System.out.println(JSONObject.toJSONString(bible));
    }

    @Test
    public void testPrintCalendarAndBible() throws ParseException {
        Bible bible = JSONObject.parseObject(bibleText,Bible.class);
        BiblePlanFourMark planFourMark = new BiblePlanFourMark();
        planFourMark.initWithBible(bible);

        planFourMark.printPlanAllYear();
    }

    @Test
    public void testPrintWithCalendar() {
        int year= 2021;
        int month= 11;
        /*
        1、计算当月总天数
         */
        int days;
        if(month==2){
            if(year%4==0&&year%100!=0||year%400==0){
                days=29;
            }else{
                days=28;
            }
        }else if (month==4||month==6||month==9||month==11){
            days=30;
        }else{
            days=31;
        }
        /*
        2、计算当月1号距离1900年1月1号的天数
         */
        int sum=0;
        //1.1对1900年到year之间的天数累加
        for(int i=1900;i<year;i++){
            if (i%4==0&&i%100!=0||i%400==0){
                sum+=366;
            }else {
                sum+=365;
            }
        }
        //对月份天数累加
        for(int i=1;i<month;i++){
            if(i==2){
                if(year%4 == 0 && year%100!=0 || year%400==0){
                    sum+=29;
                }else{
                    sum+=28;
                }
            }else if(i==4||i==6||i==9||i==11){
                sum+=30;
            }else{
                sum+=31;
            }
        }
        //对天数累加
        sum+=1;

        /*
        3.打印当月日历
         */
        System.out.println("------"+year+"年"+month+"月"+"的日历为："+"------");

        //计算当月1号是星期几，前面打印几个空格
        System.out.println("日\t一\t二\t三\t四\t五\t六");
        for(int i=0;i<sum%7;i++){
            System.out.print("\t");
        }

        //如果是星期六，换行
        for(int i=1;i<=days;i++){
            if(sum%7==6){
                System.out.println(i+"周六日期");
            }else{
                System.out.print(i+"周间日期"+"\t");
            }
            sum++;
        }
    }
}

