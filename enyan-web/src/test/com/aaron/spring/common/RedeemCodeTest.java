package com.aaron.spring.common;

import org.junit.Test;

import java.util.UUID;

/**
 *
 *
 * @Date: Created in  2020-05-12
 * @Modified By:
 */
public class RedeemCodeTest {
    @Test
    public void testEmail(){
        if (Constant.TEST_ACCOUNT.contains("<EMAIL>")){
            System.out.println("pass");
        }else{
            System.out.println("pass not");
        }
    }

    @Test
    public void testGenerateRedeemCode(){
        for (int i = 0; i < 20; i++) {
            System.out.println("ZDL-"+ UUID.randomUUID().toString());
        }
    }
}
