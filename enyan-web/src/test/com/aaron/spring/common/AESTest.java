package com.aaron.spring.common;

import com.aaron.util.AESUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.junit.Test;

import java.util.Calendar;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/3/4
 * @Modified By:
 */
public class AESTest {
    @Test
    public void testWeek() throws Exception {
        String email = "{\"other\":\"other132\",\"email\":\"<EMAIL>\",\"random\":\"random34\",\"name\":\"aaron\",\"passwd\":\"123456\",\"device\":\"123456\"}";
        String encryptText = AESUtil.encrypt(email,Constant.REST_REG_API_AES_KEY,Constant.REST_REG_API_AES_IV);
        System.out.println("encryptText="+encryptText);

        String text = AESUtil.decrypt(encryptText,Constant.REST_REG_API_AES_KEY,Constant.REST_REG_API_AES_IV);
        System.out.println("text="+text);
    }

    @Test
    public void testEncode() throws Exception {
        String email = "<EMAIL>";
        String encryptText = AESUtil.encrypt(email,Constant.REST_REG_API_AES_KEY,Constant.REST_REG_API_AES_IV);
        System.out.println("encryptText="+encryptText);

        //encryptText = "DqLrSJ4aesKq/SaNfsUweIpV8aZIiJ3i7LZAnqCc8wD/MMqntUffV8RQ6noYTgy5ia67UqRhfFcsLYWC/eeddsRaSWJqKM9wLXF1Y2p56h43oWgoZfHcY34mQxEo5i65Mj9Op+1vUh+3ZpBXmRXMUw==";

        String text = AESUtil.decrypt(encryptText,Constant.REST_REG_API_AES_KEY,Constant.REST_REG_API_AES_IV);
        System.out.println("text="+text);
    }
    ///5YK9DQIZC45HiPUhUXoOjAQl+ry9IeOsV9ayrVqcwzFzkEIVreZd7zgczXSHOoqTX+x+FV2OwtVbCynZTa0pPMEgJoaRuDrNi6VT0/oPVH0v5dYjRKklZ/4UykPq3BE

    @Test
    public void testEncrypt() throws Exception {
        String text = "{\"name\": \"11\",\"random1\": 35,\"random2\": 57,\"email\": \"<EMAIL>\",\"random4\": 82,\"passwd\": \"111wuyusi\"}";

        String encryptText = AESUtil.encrypt(text,Constant.REST_REG_API_AES_KEY,Constant.REST_REG_API_AES_IV);
        //0syH4MhAWk3x3zOrvUYoG6Vl6WWMGkNhfOtWvfRhD25ZUq+82Cjt9UCgFG8TCGCddJDw2AazgxrwToW5wuL7ZLKUcrmiivq86az2IS84JkqrHbM0WhLUQTz0ejwHf5TBGrnDxdi1EAsiTDWWG3t0Ew==
        //KfbWA6Cq8x2uZvDHutfsmV6sK8OZjg24fvJPxx+QRAwgmS9DRmDpENTUc0gENEdU4jT6a/qCQEqv62/+r8Xx82EfLQ3icnnmKtatovG30ZFl7Zm/KVmdlvzC2r2r7+uyi2Nih8cLklMBmUUW3LBslw==

        //String encryptText = AESUtil.encrypt(text,"fkn#7ez%gRe#SDYa","eMC#TNa@8gz$NrKm");
        System.out.println("text="+encryptText);
    }

    @Test
    public void testDecrypt() throws Exception {
        String encryptText = "VyiibOjD/G0Kk2BeRdiJBb7bzqSQdjjifGPIMKRrvpJACVKd0ci/Is65OoapArAPDXrqwmbTD2goa0nOwS/myUmETn4ynPql3C9zJg7DKhJlK5panHD6xa6oNJh+nwvuitRFGrFaogYMu6ODY/s3cw==";

        String text = AESUtil.decrypt(encryptText,Constant.REST_REG_API_AES_KEY,Constant.REST_REG_API_AES_IV);
        System.out.println("text="+text);
    }
}
