package com.aaron.spring.common;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/30
 * @Modified By:
 */
public class Translate2Test {
   @Test
   public void testConvertText() throws IOException {
      String dirPath = "/Users/<USER>/Documents/document/Enyan/02学习/202207旧约神学/更正录音/txt2/";
      File rootFile = new File(dirPath);
      String[] extensions = {"txt"};
      Collection<File> fileList = FileUtils.listFiles(rootFile, extensions,false);
      for (File file:fileList){
//         System.out.println(file.getName());
         doFileText(file,dirPath);
      }
//      doFileText(fileList.stream().findFirst().get(),dirPath);
   }

   private void doFileText(File file, String parentDirPth) throws IOException {
      System.out.println("file name="+file.getName());
      List<String> textList = IOUtils.readLines(FileUtils.openInputStream(file),"UTF-8");
      int i = 0;
      String regEx= "(\\w)?正确(:|：)?";
      // 创建 Pattern 对象
//      Pattern r = Pattern.compile(pattern);
      Pattern pattern = Pattern.compile(regEx);

      StringBuffer buffer = new StringBuffer();
      for (String text: textList){
//         System.out.println(text);
         if (StringUtils.isBlank(text)){
            continue;
         }
         //System.out.println(text);
         Matcher matcher = pattern.matcher(text);
         boolean isMatch = matcher.find();
         if (isMatch == false){
            continue;
         }
         i++;
         String textEnd = matcher.replaceAll("").replaceAll("。","");
         //System.out.println(textEnd);
         buffer.append(textEnd);
         if (text.endsWith("？") || text.endsWith("：")){

         }else {
            buffer.append("，");
         }
      }
      String newPath = parentDirPth + "to/"+file.getName();
      FileUtils.write(new File(newPath), buffer.toString(), "UTF-8");
      System.out.println("总共有="+i);
   }

   @Test
   public void testPattern(){
//      String pattern = "(\\d+):(\\d+):(\\d+),(\\d+)";
      String pattern = "(\\d+):(\\d+):(\\d+),(\\d+)";
      pattern = ".*(\\d+).*";
      pattern = "重译*";
      // 创建 Pattern 对象
      Pattern r = Pattern.compile(pattern);
      String text = "【重译：讨论的要求，是用你们的新约知识来讨论创造主题。如果你们有词语索引工具，可以用它来搜索这个词，看看它表达了什么信息】";
      boolean isMatch = Pattern.matches(pattern, text);
      if (isMatch){
         System.out.println(text);
      }else {
         System.out.println("not match");
      }
   }

   @Test
   public void testPattern2(){
      // 要验证的字符串
      String str = "【重译：讨论的要求，是用你们的新约知识来讨论创造主题。如果你们有词语索引工具，可以用它来搜索这个词，看看它表达了什么信息】";
      // 正则表达式规则
      String regEx = "【重译(:|：)";
      regEx = "【重译(:|：)+\\w+(】)?";
      regEx = "【重译：.+?】";
      regEx = "【重译：\\[.*?\\]】";
      // 编译正则表达式
      Pattern pattern = Pattern.compile(regEx);
      // 忽略大小写的写法
      // Pattern pat = Pattern.compile(regEx, Pattern.CASE_INSENSITIVE);
      Matcher matcher = pattern.matcher(str);
      // 查找字符串中是否有匹配正则表达式的字符/字符串
      boolean rs = matcher.find();
      System.out.println(rs);
      if (rs == true){
         String textEnd = matcher.replaceAll("");
         System.out.println(textEnd);
      }

   }

   @Test
   public void testPattern3(){
      // 要验证的字符串
      String str = "baike.xsoftlab.net";
      // 正则表达式规则
      String regEx = "baike.*";
      // 编译正则表达式
      Pattern pattern = Pattern.compile(regEx);
      // 忽略大小写的写法
      // Pattern pat = Pattern.compile(regEx, Pattern.CASE_INSENSITIVE);
      Matcher matcher = pattern.matcher(str);
      // 查找字符串中是否有匹配正则表达式的字符/字符串
      boolean rs = matcher.find();
      System.out.println(rs);
   }

   @Test
   public void testArraySortList(){
      Map<Integer, Long> tree = new TreeMap<>();
      List<Long> listOrdre = Arrays.asList(3345L,22L,44L,555L,6L,777L,23L,566L,66L);
      List<Long>   myList2  = Arrays.asList(44L,777L,3345L,66L,566L,555L,6L,23L,22L);

      for (long code : myList2) {
         tree.put(listOrdre.indexOf(code), code);
      }
      System.out.println(tree.values());
   }

   
}
