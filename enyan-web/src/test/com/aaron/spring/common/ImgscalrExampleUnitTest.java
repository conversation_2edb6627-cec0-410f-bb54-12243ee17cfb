package com.aaron.spring.common;

import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/7/23
 * @Modified By:
 */
public class ImgscalrExampleUnitTest {
	@Test
	public void testMap(){
		String str ="SALES:0,SALE_PRODUCTS:1,EXPENSES:2,EXPENSES_ITEMS:3";
		HashMap<String, Integer> map = (HashMap<String, Integer>) Arrays.asList(str.split(",")).stream().map(s -> s.split(":")).collect(Collectors.toMap(e -> e[0], e -> Integer.parseInt(e[1])));
		System.out.println("MapTest.testMap="+map);
	}

	@Test(expected = Test.None.class)
	public void whenOriginalImageExistsAndTargetSizesAreNotZero_thenImageGeneratedWithoutError() throws IOException {
		int targetWidth = 200;
		int targetHeight = 200;
		BufferedImage originalImage = ImageIO.read(new File("src/main/resources/images/sampleImage.jpg"));
		BufferedImage outputImage = ImgscalrExample.resizeImage(originalImage, targetWidth, targetHeight);

		assertNotNull(outputImage);
	}

	@Test(expected = Test.None.class)
	public void whenOriginalImageExistsAndTargetSizesAreNotZero_thenOutputImageSizeIsValid() throws IOException {
		int targetWidth = 200;
		int targetHeight = 200;
		BufferedImage originalImage = ImageIO.read(new File("src/main/resources/images/sampleImage.jpg"));
		assertNotEquals(originalImage.getWidth(), targetWidth);
		assertNotEquals(originalImage.getHeight(), targetHeight);
		BufferedImage outputImage = ImgscalrExample.resizeImage(originalImage, targetWidth, targetHeight);

		assertEquals(outputImage.getWidth(), targetWidth);
		assertEquals(outputImage.getHeight(), targetHeight);
	}

	@Test(expected = Test.None.class)
	public void whenTargetWidthIsZero_thenImageIsCreated() throws IOException {
		int targetWidth = 0;
		int targetHeight = 200;
		BufferedImage originalImage = ImageIO.read(new File("src/main/resources/images/sampleImage.jpg"));
		BufferedImage outputImage = ImgscalrExample.resizeImage(originalImage, targetWidth, targetHeight);

		assertNotNull(outputImage);
	}

	@Test(expected = Test.None.class)
	public void whenTargetHeightIsZero_thenImageIsCreated() throws IOException {
		int targetWidth = 200;
		int targetHeight = 0;
		BufferedImage originalImage = ImageIO.read(new File("src/main/resources/images/sampleImage.jpg"));
		BufferedImage outputImage = ImgscalrExample.resizeImage(originalImage, targetWidth, targetHeight);

		assertNotNull(outputImage);
	}

	@Test(expected = Exception.class)
	public void whenOriginalImageDoesNotExist_thenErrorIsThrown() {
		int targetWidth = 200;
		int targetHeight = 200;
		BufferedImage outputImage = ImgscalrExample.resizeImage(null, targetWidth, targetHeight);

		assertNull(outputImage);
	}
}
