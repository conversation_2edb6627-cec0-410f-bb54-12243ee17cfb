package com.aaron.bible;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/12/8
 * @Modified By:
 */
public class BibleBook implements Serializable {
	private static final long serialVersionUID = -3699609907955365813L;
	private Integer index;
	private String bookNameChs;
	private Integer chapterNum;

	public Integer getChapterNum() {
		return chapterNum;
	}

	public void setChapterNum(Integer chapterNum) {
		this.chapterNum = chapterNum;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getBookNameChs() {
		return bookNameChs;
	}

	public void setBookNameChs(String bookNameChs) {
		this.bookNameChs = bookNameChs;
	}

	@Override
	public String toString() {
		return "BibleBook{" +
				       "index=" + index +
				       ", bookNameChs='" + bookNameChs + '\'' +
				       ", chapterNum=" + chapterNum +
				       '}';
	}
}
