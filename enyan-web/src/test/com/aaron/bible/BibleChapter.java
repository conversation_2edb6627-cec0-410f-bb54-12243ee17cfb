package com.aaron.bible;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/12/8
 * @Modified By:
 */
public class BibleChapter implements Serializable {
	private static final long serialVersionUID = 7712406908776239667L;
	private Integer index;
	private String bookNameChs;
	private String chapterNameChs;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getBookNameChs() {
		return bookNameChs;
	}

	public void setBookNameChs(String bookNameChs) {
		this.bookNameChs = bookNameChs;
	}

	public String getChapterNameChs() {
		return chapterNameChs;
	}

	public void setChapterNameChs(String chapterNameChs) {
		this.chapterNameChs = chapterNameChs;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}
}