package com.aaron.model;

import com.aaron.spring.model.ProductInfo;
import org.junit.Test;
import org.springframework.security.access.method.P;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2020-07-23
 * @Modified By:
 */
public class ProductInfoCloneTest {
    class Person implements Cloneable {
        private String name;
        private Integer age;

        @Override
        protected Object clone() throws CloneNotSupportedException {
            return super.clone();
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        @Override
        public String toString() {
            return "Person{" +
                    "name='" + name + '\'' +
                    ", age=" + age +
                    '}';
        }
    }

    //List 是浅copy，所以需要单独clone对象
    @Test
    public void testValueChange() throws CloneNotSupportedException {
        ArrayList<ProductInfo> productInfoList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            ProductInfo productInfo = new ProductInfo();
            productInfo.setPriceHKDDiscount(new BigDecimal(i));
            productInfo.setName("product"+i);
            productInfoList.add(productInfo);
        }

        for (ProductInfo productInfo:productInfoList){
            System.out.print("productList:name="+productInfo.getName());
            System.out.println(",value="+productInfo.getPriceHKDDiscount());
        }

        ArrayList<ProductInfo> newList = (ArrayList<ProductInfo>) productInfoList.clone();

        List<ProductInfo> newCopyList = new ArrayList<>();
        for (ProductInfo productInfo:productInfoList){
            newCopyList.add((ProductInfo) productInfo.clone());
        }


        productInfoList.get(0).setPriceHKDDiscount(new BigDecimal("100"));
        productInfoList.get(0).setName("xiugai");


        System.out.println("change productList");

        for (ProductInfo productInfo:newList){
            System.out.print("newList :name="+productInfo.getName());
            System.out.println(",value="+productInfo.getPriceHKDDiscount());
        }

        for (ProductInfo productInfo:newCopyList){
            System.out.print("newCopyList :name="+productInfo.getName());
            System.out.println(",value="+productInfo.getPriceHKDDiscount());
        }
    }


    @Test
    public void testShallowCopy() throws Exception {
        Person p1 = new Person();
        p1.setAge(31);
        p1.setName("Peter");

        Person p2 = (Person) p1.clone();
        System.out.println(p1 == p2);//false
        p2.setName("Jacky");
        System.out.println("p1=" + p1);//p1=Person [name=Peter, age=31]
        System.out.println("p2=" + p2);//p2=Person [name=Jacky, age=31]
    }

    @Test
    public void testProductInfoCopy() throws Exception{
        ProductInfo p1 = new ProductInfo();
        p1.setName("p1");
        p1.setPriceHKDDiscount(new BigDecimal(200));

        ProductInfo p2 = (ProductInfo) p1.clone();

        System.out.println(p1 == p2);//false
        p2.setName("Jacky");
        p2.setPriceHKDDiscount(new BigDecimal("3000"));
        System.out.println("p1=" + p1);//p1=Person [name=Peter, age=31]
        System.out.println("p2=" + p2);//p2=Person [name=Jacky, age=31]
    }

    @Test
    public void testBigDecimal(){
        BigDecimal value1 = new BigDecimal("100");
        BigDecimal value2 = new BigDecimal("100");

        System.out.println("value1 == value2 ?"+ (value1 == value2));
        System.out.println("value1.equals(value2) ?"+ (value1.equals(value2)));
    }
}
