package com.aaron.model;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/3/15
 * @Modified By:
 */
public class RestAxis implements Serializable {
    private static final long serialVersionUID = -730245563977368900L;
    private String axisX;
    private Float axisY;

    public String getAxisX() {
        return axisX;
    }

    public void setAxisX(String axisX) {
        this.axisX = axisX;
    }

    public Float getAxisY() {
        return axisY;
    }

    public void setAxisY(Float axisY) {
        this.axisY = axisY;
    }

    @Override
    public String toString() {
        return "RestAxis{" +
                       "axisX='" + axisX + '\'' +
                       ", axisY=" + axisY +
                       '}';
    }
}
