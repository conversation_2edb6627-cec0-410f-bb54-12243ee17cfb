package com.aaron.spring.api.v4.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.v4.model.PodcastHomeResult;
import com.aaron.spring.api.v4.model.RestPodcast;
import com.aaron.spring.model.PodEpisode;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.service.PodEpisodeService;
import com.aaron.spring.service.PodPodcastService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(MockitoJUnitRunner.class)
public class RestPodcastControllerTest {

    private MockMvc mockMvc;
    
    @InjectMocks
    private RestPodcastController restPodcastController;
    
    @Mock
    private PodPodcastService podPodcastService;
    
    @Mock
    private PodEpisodeService podEpisodeService;
    
    private ObjectMapper objectMapper = new ObjectMapper();

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        
        // 添加默认mock返回值
        Page<PodEpisode> defaultEpisodePage = new Page<>();
        defaultEpisodePage.setRecords(new ArrayList<>());
        when(podEpisodeService.queryRecords(any(), any()))
            .thenReturn(defaultEpisodePage);
        
        Page<PodPodcast> defaultPodcastPage = new Page<>();
        defaultPodcastPage.setRecords(new ArrayList<>());
        when(podPodcastService.queryRecords(any(), any()))
            .thenReturn(defaultPodcastPage);
        
        this.mockMvc = MockMvcBuilders.standaloneSetup(restPodcastController).build();
    }

    @Test
    public void home_whenPodcastCountIsMaxValue_shouldReturnAllPublishedPodcasts() throws Exception {
        List<PodPodcast> mockPodcasts = IntStream.range(0, 50)
            .mapToObj(i -> {
            PodPodcast podcast = new PodPodcast();
            podcast.setPodcastId((long) i);
            podcast.setTitle("Podcast " + i);
            return podcast;
        })
            .collect(Collectors.toList());

        Page<PodPodcast> mockPage = new Page<>();
        mockPage.setRecords(mockPodcasts);
        mockPage.setTotalRecord(50);

        when(podPodcastService.queryRecords(
            argThat(page -> page.getPageSize() == Integer.MAX_VALUE),
            argThat(search -> search.getIsPublished() == 1 && search.getIsDeleted() == 0)
        )).thenReturn(mockPage);

        RestPodcast request = new RestPodcast();
        request.setPodcastCount(Integer.MAX_VALUE);
        
        mockMvc.perform(post("/api/v4/podcast/home")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result.latestEpisodes").isArray())
            .andExpect(jsonPath("$.result.recommendedTopics").isArray());
    }

    @Test 
    public void home_withSpecificPodcastCount_shouldUseParamForPageSize() throws Exception {
        RestPodcast request = new RestPodcast();
        request.setPodcastCount(5);
        
        mockMvc.perform(post("/api/v4/podcast/home")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result.latestEpisodes").isArray())
            .andExpect(jsonPath("$.result.recommendedTopics").isArray());
        
        verify(podPodcastService).queryRecords(
            argThat(page -> page.getPageSize() == 5),
            any());
    }

    @Test
    public void home_whenNoRequestBody_shouldUseDefaultCounts() throws Exception {

        List<PodEpisode> mockEpisodes = java.util.Arrays.asList(
            new PodEpisode().setEpisodeId(1L),
            new PodEpisode().setEpisodeId(2L)
        );
        
        // 模拟栏目数据
        List<PodPodcast> mockPodcasts = IntStream.range(0, 5)
            .mapToObj(i -> new PodPodcast().setPodcastId((long)i))
            .collect(Collectors.toList());
        
        // 配置mock行为
        Page<PodEpisode> episodePage = new Page<>();
        episodePage.setRecords(mockEpisodes);
        when(podEpisodeService.queryRecords(
            argThat(page -> page.getPageSize() == 2),
            any(PodEpisode.class))
        ).thenReturn(episodePage);

        Page<PodPodcast> podcastPage = new Page<>();
        podcastPage.setRecords(mockPodcasts);
        when(podPodcastService.queryRecords(
            argThat(page -> page.getPageSize() == Integer.MAX_VALUE),
            any(PodPodcast.class))
        ).thenReturn(podcastPage);
        
        // 执行并验证
        mockMvc.perform(post("/api/v4/podcast/home")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result.latestEpisodes").isArray())
            .andExpect(jsonPath("$.result.recommendedTopics").isArray());
    }
}
