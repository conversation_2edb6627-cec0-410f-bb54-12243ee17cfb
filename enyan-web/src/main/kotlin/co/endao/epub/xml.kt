package co.endao.epub

import org.w3c.dom.Document
import org.w3c.dom.Element
import org.w3c.dom.Node
import org.w3c.dom.NodeList
import java.io.InputStream
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.xpath.XPathConstants
import javax.xml.xpath.XPathFactory

val Element.name:String
    get() {
        return localName ?: tagName
    }
fun Element.elements():List<Element>{
    val chs = childNodes
    val res = mutableListOf<Element>()
    for(ch in 0 until chs.length){
        val nd = chs.item(ch)
        if(nd is Element){
            res.add(nd)
        }
    }
    return res
}
fun Element.elements(t:String): List<Element> {
    val res = mutableListOf<Element>()
    val els = getElementsByTagName(t)
    for(i in 0 until els.length){
        res.add(els.item(i) as Element)
    }
    return res
}
var Node.text:String
    get() {
        if(this is Element){
            return firstChild?.nodeValue ?: ""
        }else{
            return nodeValue
        }
    }
    set(value) {
        if(this is Element){
            textContent = value
        }else{
            nodeValue = value
        }
    }
val Node.textTrim:String
    get() {
        return text.trim()
    }
fun Element.selectSingleNode(s:String): Node? {
    if(!s.contains("local-name()") && !s.contains("*")){
        println("Select:$s")
    }
    val xpath = XPathFactory.newInstance().newXPath()
    return xpath.evaluate(s, this, XPathConstants.NODE) as? Node
}
fun Document.selectSingleNode(s:String): Node? {
    if(!s.contains("local-name()") && !s.contains("*")){
        println("Select:$s")
    }
    return documentElement.selectSingleNode(s)
}
fun Element.selectNodes(s:String):List<Node>{
    if(!s.contains("local-name()") && !s.contains("*")){
        println("Select:$s")
    }
    val xpath = XPathFactory.newInstance().newXPath()
    val nds = xpath.evaluate(s, this, XPathConstants.NODESET) as NodeList
    val res = mutableListOf<Node>()
    for(i in 0 until nds.length){
        res.add(nds.item(i))
    }
    return res
}
fun Element.attrs():MutableMap<String,String>{
    val res = mutableMapOf<String,String>()
    for(i in 0 until attributes.length){
        val it = attributes.item(i)
        res[it.localName]=it.nodeValue
    }
    return res
}
fun Element.child(s: String): Element? {
    for(i in 0 until childNodes.length){
        val it = childNodes.item(i)
        if(it !is Element) continue
        if(it.localName==s) return it
    }
    return null
}
fun Document.selectNodes(s:String):List<Node>{
    if(!s.contains("local-name()") && !s.contains("*")){
        println("Select:$s")
    }
    return documentElement.selectNodes(s)
}
fun loadW3CDom(inp: InputStream):Document{
//    val LOAD_EXTERNAL_DTD_FEATURE = "http://apache.org/xml/features/nonvalidating/load-external-dtd";
    val XML_VALIDATION_FEATURE = "http://xml.org/sax/features/validation";

    val fact = DocumentBuilderFactory.newInstance()
    fact.isNamespaceAware = true
//    fact.setFeature(LOAD_EXTERNAL_DTD_FEATURE,false)
    fact.setFeature(XML_VALIDATION_FEATURE,false)
    val bld = fact.newDocumentBuilder()

    return bld.parse(inp)
}