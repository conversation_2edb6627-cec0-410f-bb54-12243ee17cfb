<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop" xsi:schemaLocation="
http://www.springframework.org/schema/beans 
http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
http://www.springframework.org/schema/tx 
http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
http://www.springframework.org/schema/aop 
http://www.springframework.org/schema/aop/spring-aop-3.2.xsd">

	<bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
		<property name="driverClassName" value="com.mysql.jdbc.Driver" />
		<property name="jdbcUrl" value="${jdbc_url}" />
		<property name="username" value="${jdbc_username}" />
		<property name="password" value="${jdbc_password}" />

		<property name="connectionTestQuery" value="SELECT 1" />
		<!-- 生效超时 -->
		<property name="validationTimeout" value="3000" />
		<!-- 连接只读数据库时配置为true， 保证安全 -->
		<property name="readOnly" value="false" />
		<!-- 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 缺省:30秒 -->
		<property name="connectionTimeout" value="60000" />
		<!-- 一个连接idle状态的最大时长（毫秒），超时则被释放（retired），缺省:10分钟 -->
		<property name="idleTimeout" value="600000" />
		<!-- 一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒，参考MySQL
            wait_timeout参数（show variables like '%timeout%';） -->
		<property name="maxLifetime" value="3600000" />
		<!-- 连接池中允许的最大连接数。缺省值：10；推荐的公式：((core_count * 2) + effective_spindle_count) -->
		<property name="maximumPoolSize" value="${dataSource.maxPoolSize}" />
<!--		<property name="javax.net.ssl.trustStore" value="${ssl.mysql.trustStore}" />-->
<!--		<property name="javax.net.ssl.trustStorePassword" value="${ssl.mysql.trustStorePassword}" />-->
		<property name="dataSourceProperties" >
			<props>
				<prop key="javax.net.ssl.trustStore">
					${ssl.mysql.trustStore}
				</prop>
				<prop key="javax.net.ssl.trustStorePassword">
					${ssl.mysql.trustStorePassword}
				</prop>
			</props>
		</property>
	</bean>

	<bean name="dataSourceUser" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
		<property name="driverClassName" value="com.mysql.jdbc.Driver" />
		<property name="jdbcUrl" value="${jdbc_url_user}" />
		<property name="username" value="${jdbc_user_username}" />
		<property name="password" value="${jdbc_user_password}" />

		<property name="connectionTestQuery" value="SELECT 1" />
		<!-- 生效超时 -->
		<property name="validationTimeout" value="3000" />
		<!-- 连接只读数据库时配置为true， 保证安全 -->
		<property name="readOnly" value="false" />
		<!-- 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 缺省:30秒 -->
		<property name="connectionTimeout" value="60000" />
		<!-- 一个连接idle状态的最大时长（毫秒），超时则被释放（retired），缺省:10分钟 -->
		<property name="idleTimeout" value="600000" />
		<!-- 一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒，参考MySQL
            wait_timeout参数（show variables like '%timeout%';） -->
		<property name="maxLifetime" value="3600000" />
		<!-- 连接池中允许的最大连接数。缺省值：10；推荐的公式：((core_count * 2) + effective_spindle_count) -->
		<property name="maximumPoolSize" value="10" />
		<!--		<property name="javax.net.ssl.trustStore" value="${ssl.mysql.trustStore}" />-->
		<!--		<property name="javax.net.ssl.trustStorePassword" value="${ssl.mysql.trustStorePassword}" />-->
		<property name="dataSourceProperties" >
			<props>
				<prop key="javax.net.ssl.trustStore">
					${ssl.mysql.trustStore}
				</prop>
				<prop key="javax.net.ssl.trustStorePassword">
					${ssl.mysql.trustStorePassword}
				</prop>
			</props>
		</property>
	</bean>
	<!--<bean id="paginationInterceptor" class="com.aaron.spring.interceptor.PaginationInterceptor">
		 
	</bean>-->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="dataSource" ref="dataSource" />
		<property name="configLocation" value="classpath:/mybatis/sqlMapConfig.xml" />
		<!--<property name="plugins">
            <ref bean="paginationInterceptor"/>
        </property>-->
		<property name="mapperLocations" >
			<list>
				<value>classpath:/mybatis/**/*Mapper.xml</value>
			</list>
		</property>
	</bean>
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="com.aaron.spring.**.mapper" />
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
	</bean>

	<bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>
	<tx:advice id="transactionAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="add*" propagation="REQUIRED" />
			<tx:method name="append*" propagation="REQUIRED" />
			<tx:method name="insert*" propagation="REQUIRED" />
			<tx:method name="save*" propagation="REQUIRED" />
			<tx:method name="update*" propagation="REQUIRED" />
			<tx:method name="modify*" propagation="REQUIRED" />
			<tx:method name="edit*" propagation="REQUIRED" />
			<tx:method name="delete*" propagation="REQUIRED" />
			<tx:method name="remove*" propagation="REQUIRED" />
			<tx:method name="repair" propagation="REQUIRED" />
			<tx:method name="delAndRepair" propagation="REQUIRED" />

			<tx:method name="get*" propagation="SUPPORTS" />
			<tx:method name="find*" propagation="SUPPORTS" />
			<tx:method name="query*" propagation="SUPPORTS" />
			<tx:method name="load*" propagation="SUPPORTS" />
			<tx:method name="search*" propagation="SUPPORTS" />
			<tx:method name="datagrid*" propagation="SUPPORTS" />

			<tx:method name="*" propagation="SUPPORTS" />
		</tx:attributes>
	</tx:advice>
	<aop:config>
		<aop:pointcut id="transactionPointcut" expression="execution(* com.aaron.spring.service..*Impl.*(..))" />
		<aop:advisor pointcut-ref="transactionPointcut" advice-ref="transactionAdvice" />
	</aop:config>

	<bean id="userJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<property name="dataSource" ref="dataSourceUser"></property>
	</bean>

	<bean id="eBookJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<property name="dataSource" ref="dataSource"></property>
	</bean>

</beans>