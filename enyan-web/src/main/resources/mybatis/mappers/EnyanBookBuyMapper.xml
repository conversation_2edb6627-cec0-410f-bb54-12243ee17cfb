<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanBookBuyMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBookBuy">
    <id column="book_buy_id" jdbcType="BIGINT" property="bookBuyId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
    <result column="purchased_at" jdbcType="TIMESTAMP" property="purchasedAt" />
    <result column="purchased_day" jdbcType="INTEGER" property="purchasedDay" />
    <result column="drmInfo" jdbcType="VARCHAR" property="drminfo" />
    <result column="read_info" jdbcType="VARCHAR" property="readInfo" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="read_time" jdbcType="BIGINT" property="readTime" />
    <result column="group_name_time" jdbcType="BIGINT" property="groupNameTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    book_buy_id, order_num, user_id, user_email, book_id, book_title, purchased_at, purchased_day, 
    drmInfo, read_info, group_name, read_time, group_name_time, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanBookBuyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_book_buy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_book_buy
    where book_buy_id = #{bookBuyId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_book_buy
    where book_buy_id = #{bookBuyId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanBookBuyExample">
    delete from enyan_book_buy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanBookBuy">
    <selectKey keyProperty="bookBuyId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book_buy (order_num, user_id, user_email, 
      book_id, book_title, purchased_at, 
      purchased_day, drmInfo, read_info, 
      group_name, read_time, group_name_time, 
      is_deleted)
    values (#{orderNum,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, 
      #{bookId,jdbcType=BIGINT}, #{bookTitle,jdbcType=VARCHAR}, #{purchasedAt,jdbcType=TIMESTAMP}, 
      #{purchasedDay,jdbcType=INTEGER}, #{drminfo,jdbcType=VARCHAR}, #{readInfo,jdbcType=VARCHAR}, 
      #{groupName,jdbcType=VARCHAR}, #{readTime,jdbcType=BIGINT}, #{groupNameTime,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanBookBuy">
    <selectKey keyProperty="bookBuyId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book_buy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="bookTitle != null">
        book_title,
      </if>
      <if test="purchasedAt != null">
        purchased_at,
      </if>
      <if test="purchasedDay != null">
        purchased_day,
      </if>
      <if test="drminfo != null">
        drmInfo,
      </if>
      <if test="readInfo != null">
        read_info,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="readTime != null">
        read_time,
      </if>
      <if test="groupNameTime != null">
        group_name_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="bookTitle != null">
        #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="purchasedAt != null">
        #{purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="purchasedDay != null">
        #{purchasedDay,jdbcType=INTEGER},
      </if>
      <if test="drminfo != null">
        #{drminfo,jdbcType=VARCHAR},
      </if>
      <if test="readInfo != null">
        #{readInfo,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="readTime != null">
        #{readTime,jdbcType=BIGINT},
      </if>
      <if test="groupNameTime != null">
        #{groupNameTime,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanBookBuyExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_book_buy
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_book_buy
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_book_buy
    <set>
      <if test="record.bookBuyId != null">
        book_buy_id = #{record.bookBuyId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNum != null">
        order_num = #{record.orderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.bookTitle != null">
        book_title = #{record.bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.purchasedAt != null">
        purchased_at = #{record.purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchasedDay != null">
        purchased_day = #{record.purchasedDay,jdbcType=INTEGER},
      </if>
      <if test="record.drminfo != null">
        drmInfo = #{record.drminfo,jdbcType=VARCHAR},
      </if>
      <if test="record.readInfo != null">
        read_info = #{record.readInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.readTime != null">
        read_time = #{record.readTime,jdbcType=BIGINT},
      </if>
      <if test="record.groupNameTime != null">
        group_name_time = #{record.groupNameTime,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_book_buy
    set book_buy_id = #{record.bookBuyId,jdbcType=BIGINT},
      order_num = #{record.orderNum,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      book_title = #{record.bookTitle,jdbcType=VARCHAR},
      purchased_at = #{record.purchasedAt,jdbcType=TIMESTAMP},
      purchased_day = #{record.purchasedDay,jdbcType=INTEGER},
      drmInfo = #{record.drminfo,jdbcType=VARCHAR},
      read_info = #{record.readInfo,jdbcType=VARCHAR},
      group_name = #{record.groupName,jdbcType=VARCHAR},
      read_time = #{record.readTime,jdbcType=BIGINT},
      group_name_time = #{record.groupNameTime,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanBookBuy">
    update enyan_book_buy
    <set>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="bookTitle != null">
        book_title = #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="purchasedAt != null">
        purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="purchasedDay != null">
        purchased_day = #{purchasedDay,jdbcType=INTEGER},
      </if>
      <if test="drminfo != null">
        drmInfo = #{drminfo,jdbcType=VARCHAR},
      </if>
      <if test="readInfo != null">
        read_info = #{readInfo,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="readTime != null">
        read_time = #{readTime,jdbcType=BIGINT},
      </if>
      <if test="groupNameTime != null">
        group_name_time = #{groupNameTime,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where book_buy_id = #{bookBuyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanBookBuy">
    update enyan_book_buy
    set order_num = #{orderNum,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      user_email = #{userEmail,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      book_title = #{bookTitle,jdbcType=VARCHAR},
      purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
      purchased_day = #{purchasedDay,jdbcType=INTEGER},
      drmInfo = #{drminfo,jdbcType=VARCHAR},
      read_info = #{readInfo,jdbcType=VARCHAR},
      group_name = #{groupName,jdbcType=VARCHAR},
      read_time = #{readTime,jdbcType=BIGINT},
      group_name_time = #{groupNameTime,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where book_buy_id = #{bookBuyId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>