<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanPlanNoteMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanPlanNote">
    <id column="plan_note_id" jdbcType="BIGINT" property="planNoteId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="day" jdbcType="INTEGER" property="day" />
    <result column="day_name" jdbcType="VARCHAR" property="dayName" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    plan_note_id, user_email, book_id, day, day_name, note, is_deleted, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanPlanNoteExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_plan_note
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_plan_note
    where plan_note_id = #{planNoteId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_plan_note
    where plan_note_id = #{planNoteId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanPlanNoteExample">
    delete from enyan_plan_note
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanPlanNote">
    insert into enyan_plan_note (plan_note_id, user_email, book_id, 
      day, day_name, note, 
      is_deleted, create_time, update_time
      )
    values (#{planNoteId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, 
      #{day,jdbcType=INTEGER}, #{dayName,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanPlanNote">
    insert into enyan_plan_note
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="planNoteId != null">
        plan_note_id,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="day != null">
        day,
      </if>
      <if test="dayName != null">
        day_name,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="planNoteId != null">
        #{planNoteId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="day != null">
        #{day,jdbcType=INTEGER},
      </if>
      <if test="dayName != null">
        #{dayName,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanPlanNoteExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_plan_note
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_plan_note
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_plan_note
    <set>
      <if test="record.planNoteId != null">
        plan_note_id = #{record.planNoteId,jdbcType=BIGINT},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.day != null">
        day = #{record.day,jdbcType=INTEGER},
      </if>
      <if test="record.dayName != null">
        day_name = #{record.dayName,jdbcType=VARCHAR},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_plan_note
    set plan_note_id = #{record.planNoteId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      day = #{record.day,jdbcType=INTEGER},
      day_name = #{record.dayName,jdbcType=VARCHAR},
      note = #{record.note,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanPlanNote">
    update enyan_plan_note
    <set>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="day != null">
        day = #{day,jdbcType=INTEGER},
      </if>
      <if test="dayName != null">
        day_name = #{dayName,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where plan_note_id = #{planNoteId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanPlanNote">
    update enyan_plan_note
    set user_email = #{userEmail,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      day = #{day,jdbcType=INTEGER},
      day_name = #{dayName,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where plan_note_id = #{planNoteId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>