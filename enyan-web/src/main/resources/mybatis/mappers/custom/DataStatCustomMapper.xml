<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.DataStatCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.DataStat">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="sales_volume" jdbcType="INTEGER" property="salesVolume" />
    <result column="income_total" jdbcType="DECIMAL" property="incomeTotal" />
    <result column="order_count" jdbcType="INTEGER" property="orderCount" />
    <result column="order_fee_count" jdbcType="INTEGER" property="orderFeeCount" />
    <result column="order_free_count" jdbcType="INTEGER" property="orderFreeCount" />
    <result column="user_buy_count" jdbcType="INTEGER" property="userBuyCount" />
    <result column="user_new_count" jdbcType="INTEGER" property="userNewCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="dateString" jdbcType="VARCHAR" property="dateString" />
    <result column="count" jdbcType="BIGINT" property="count" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, sales_volume, income_total, order_count, order_fee_count, order_free_count, 
    user_buy_count, user_new_count, create_time
  </sql>

</mapper>