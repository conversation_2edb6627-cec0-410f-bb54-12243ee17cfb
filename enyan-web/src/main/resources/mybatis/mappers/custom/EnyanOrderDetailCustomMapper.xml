<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanOrderDetailCustomMapper">
    <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanOrderDetail">
        <id column="order_detail_id" jdbcType="BIGINT" property="orderDetailId" />
        <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
        <result column="book_id" jdbcType="BIGINT" property="bookId" />
        <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
        <result column="book_pub_code" jdbcType="VARCHAR" property="bookPubCode" />
        <result column="book_esin" jdbcType="VARCHAR" property="bookEsin" />
        <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
        <result column="rate_value" jdbcType="VARCHAR" property="rateValue" />
        <result column="price_fixed" jdbcType="DECIMAL" property="priceFixed" />
        <result column="price_selling" jdbcType="DECIMAL" property="priceSelling" />
        <result column="quantity" jdbcType="INTEGER" property="quantity" />
        <result column="vendor_percent" jdbcType="INTEGER" property="vendorPercent" />
        <result column="income_vendor" jdbcType="DECIMAL" property="incomeVendor" />
        <result column="income_plat" jdbcType="DECIMAL" property="incomePlat" />
        <result column="income_total" jdbcType="DECIMAL" property="incomeTotal" />
        <result column="income_real" jdbcType="DECIMAL" property="incomeReal" />
        <result column="pay_fee" jdbcType="DECIMAL" property="payFee" />
        <result column="net_sales" jdbcType="DECIMAL" property="netSales" />
        <result column="order_currency" jdbcType="TINYINT" property="orderCurrency" />
        <result column="purchased_at" jdbcType="TIMESTAMP" property="purchasedAt" />
        <result column="purchased_day" jdbcType="INTEGER" property="purchasedDay" />
        <result column="order_type" jdbcType="INTEGER" property="orderType" />
        <result column="pay_type" jdbcType="INTEGER" property="payType" />
        <result column="pay_country" jdbcType="VARCHAR" property="payCountry" />
        <result column="is_counted" jdbcType="TINYINT" property="isCounted" />
        <result column="order_from" jdbcType="INTEGER" property="orderFrom" />
        <result column="sales_model" jdbcType="INTEGER" property="salesModel" />
        <result column="drmInfo" jdbcType="VARCHAR" property="drminfo" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
        <result column="purchased_day_start" jdbcType="INTEGER" property="purchasedDayStart" />
        <result column="purchased_day_end" jdbcType="INTEGER" property="purchasedDayEnd" />
    </resultMap>
    <resultMap id="BookResultMap" type="com.aaron.spring.model.EnyanBook">
        <id column="book_id" jdbcType="BIGINT" property="bookId" />
        <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
        <result column="author" jdbcType="VARCHAR" property="author" />
        <result column="author_bio" jdbcType="VARCHAR" property="authorBio" />
        <result column="translator" jdbcType="VARCHAR" property="translator" />
        <result column="word_count" jdbcType="VARCHAR" property="wordCount" />
        <result column="product_web" jdbcType="VARCHAR" property="productWeb" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="sales_volume" jdbcType="BIGINT" property="salesVolume" />
        <result column="price_cny" jdbcType="DECIMAL" property="priceCny" />
        <result column="price_usd" jdbcType="DECIMAL" property="priceUsd" />
        <result column="price_hkd" jdbcType="DECIMAL" property="priceHkd" />
        <result column="book_cost" jdbcType="INTEGER" property="bookCost" />
        <result column="category_id" jdbcType="BIGINT" property="categoryId" />
        <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
        <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
        <result column="publisher_name" jdbcType="VARCHAR" property="publisherName" />
        <result column="published_at" jdbcType="VARCHAR" property="publishedAt" />
        <result column="book_abstract" jdbcType="VARCHAR" property="bookAbstract" />
        <result column="book_cover" jdbcType="VARCHAR" property="bookCover" />
        <result column="book_cover_app" jdbcType="VARCHAR" property="bookCoverApp" />
        <result column="book_sample" jdbcType="VARCHAR" property="bookSample" />
        <result column="book_full" jdbcType="VARCHAR" property="bookFull" />
        <result column="book_hash" jdbcType="VARCHAR" property="bookHash" />
        <result column="book_isbn" jdbcType="VARCHAR" property="bookIsbn" />
        <result column="book_esin" jdbcType="VARCHAR" property="bookEsin" />
        <result column="ebook_isbn" jdbcType="VARCHAR" property="ebookIsbn" />
        <result column="has_book_pagination" jdbcType="TINYINT" property="hasBookPagination" />
        <result column="book_pub_code" jdbcType="VARCHAR" property="bookPubCode" />
        <result column="book_keywords" jdbcType="VARCHAR" property="bookKeywords" />
        <result column="book_type" jdbcType="INTEGER" property="bookType" />
        <result column="special_offer" jdbcType="INTEGER" property="specialOffer" />
        <result column="area_discount" jdbcType="INTEGER" property="areaDiscount" />
        <result column="can_tts" jdbcType="INTEGER" property="canTts" />
        <result column="size" jdbcType="VARCHAR" property="size" />
        <result column="book_drm_ref" jdbcType="VARCHAR" property="bookDrmRef" />
        <result column="discount_single_id" jdbcType="BIGINT" property="discountSingleId" />
        <result column="discount_single_type" jdbcType="TINYINT" property="discountSingleType" />
        <result column="discount_single_value" jdbcType="INTEGER" property="discountSingleValue" />
        <result column="discount_single_start_time" jdbcType="DATE" property="discountSingleStartTime" />
        <result column="discount_single_end_time" jdbcType="DATE" property="discountSingleEndTime" />
        <result column="discount_single_is_valid" jdbcType="TINYINT" property="discountSingleIsValid" />
        <result column="discount_single_description" jdbcType="VARCHAR" property="discountSingleDescription" />
        <result column="discount_id" jdbcType="BIGINT" property="discountId" />
        <result column="discount_description" jdbcType="VARCHAR" property="discountDescription" />
        <result column="discount_is_valid" jdbcType="TINYINT" property="discountIsValid" />
        <result column="is_in_cn" jdbcType="TINYINT" property="isInCn" />
        <result column="recommended_order" jdbcType="INTEGER" property="recommendedOrder" />
        <result column="recommended_caption" jdbcType="VARCHAR" property="recommendedCaption" />
        <result column="print_permission" jdbcType="TINYINT" property="printPermission" />
        <result column="copy_permission" jdbcType="TINYINT" property="copyPermission" />
        <result column="ebook_format" jdbcType="TINYINT" property="ebookFormat" />
        <result column="shelf_status" jdbcType="TINYINT" property="shelfStatus" />
        <result column="vendor_percent" jdbcType="INTEGER" property="vendorPercent" />
        <result column="sales_model" jdbcType="INTEGER" property="salesModel" />
        <result column="opensale_at" jdbcType="TIMESTAMP" property="opensaleAt" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        order_detail_id, order_num, user_id, user_email, book_id, book_title, book_pub_code,
    book_esin, publisher_id, rate_value, price_fixed, price_selling, quantity, vendor_percent,
    income_vendor, income_plat, income_total, income_real, pay_fee, net_sales, order_currency,
    purchased_at, purchased_day, order_type, pay_type, pay_country, is_counted, order_from,
    sales_model, drmInfo, is_deleted
    </sql>

    <select id="queryOrderDetailByBook" parameterType="map" resultMap="BaseResultMap">
        SELECT od.book_id,od.book_title,od.book_pub_code,od.publisher_id,od.book_esin,sum(od.quantity) as quantity,sum(od.income_vendor) as income_vendor,
          sum(od.income_plat) as income_plat,sum(od.income_total) as income_total,sum(od.pay_fee) as pay_fee,sum(od.net_sales) as net_sales
        FROM enyan_order_detail od
        WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
        <if test="record.bookTitle != null">
            AND od.book_title REGEXP #{record.bookTitle,jdbcType=VARCHAR}
        </if>
        GROUP BY od.book_id,od.book_title,od.book_pub_code,od.publisher_id,od.book_esin
        <if test="record.orderByClause != null">
            order by ${record.orderByClause}
        </if>
        <if test="record.page != null">
            <![CDATA[ LIMIT #{record.page.pageSize,jdbcType=INTEGER} OFFSET #{record.page.recordIndex,jdbcType=INTEGER}]]>
        </if>
    </select>

    <select id="countByBook" parameterType="map" resultType="java.lang.Long">
        SELECT count(*) FROM (SELECT od.book_id FROM enyan_order_detail od
        WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
        <if test="record.bookTitle != null">
            AND od.book_title REGEXP #{record.bookTitle,jdbcType=VARCHAR}
        </if>
        GROUP BY od.book_id) T
    </select>

    <select id="queryOrderDetailByVendor" parameterType="map" resultMap="BaseResultMap">
        SELECT od.publisher_id,sum(od.quantity) as quantity,sum(od.income_vendor) as income_vendor,
        sum(od.income_plat) as income_plat,sum(od.income_total) as income_total
        FROM enyan_order_detail od
        WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
        GROUP BY publisher_id
        <if test="record.orderByClause != null">
            order by ${record.orderByClause}
        </if>
        <if test="record.page != null">
            <![CDATA[ LIMIT #{record.page.pageSize,jdbcType=INTEGER} OFFSET #{record.page.recordIndex,jdbcType=INTEGER}]]>
        </if>
    </select>

    <select id="countByVendor" parameterType="map" resultType="java.lang.Long">
        SELECT count(DISTINCT publisher_id)
        FROM enyan_order_detail od
        WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="queryOrderDetailByDay" parameterType="map" resultMap="BaseResultMap">
      SELECT od.purchased_day as purchased_day_start,od.purchased_day as purchased_day_end,sum(od.quantity) as quantity,sum(od.income_vendor) as income_vendor,
        sum(od.income_plat) as income_plat,sum(od.income_total) as income_total,sum(od.pay_fee) as pay_fee,sum(od.net_sales) as net_sales
      FROM enyan_order_detail od
      WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
        AND od.is_deleted = 0
      <if test="record.publisherId != null">
        AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
      </if>
        GROUP BY purchased_day
        <if test="record.orderByClause != null">
            order by ${record.orderByClause}
        </if>
    </select>

    <select id="queryOrderDetailByWeek" parameterType="map" resultMap="BaseResultMap">
        SELECT DATE_FORMAT(od.purchased_at,'%Y%u') as purchased_day_start,sum(od.quantity) as quantity,sum(od.income_vendor) as income_vendor,
               sum(od.income_plat) as income_plat,sum(od.income_total) as income_total,sum(od.pay_fee) as pay_fee,sum(od.net_sales) as net_sales
        FROM enyan_order_detail od
        WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
        GROUP BY purchased_day_start
        <if test="record.orderByClause != null">
            order by ${record.orderByClause}
        </if>
    </select>

    <select id="queryOrderDetailByMonth" parameterType="map" resultMap="BaseResultMap">
        SELECT DATE_FORMAT(od.purchased_at,'%Y%m') as purchased_day_start,sum(od.quantity) as quantity,sum(od.income_vendor) as income_vendor,
               sum(od.income_plat) as income_plat,sum(od.income_total) as income_total,sum(od.pay_fee) as pay_fee,sum(od.net_sales) as net_sales
        FROM enyan_order_detail od
        WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
        GROUP BY purchased_day_start
        <if test="record.orderByClause != null">
            order by ${record.orderByClause}
        </if>
    </select>

    <select id="queryOrderDetailByYear" parameterType="map" resultMap="BaseResultMap">
        SELECT DATE_FORMAT(od.purchased_at,'%Y') as purchased_day_start,sum(od.quantity) as quantity,sum(od.income_vendor) as income_vendor,
               sum(od.income_plat) as income_plat,sum(od.income_total) as income_total,sum(od.pay_fee) as pay_fee,sum(od.net_sales) as net_sales
        FROM enyan_order_detail od
        WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
        GROUP BY purchased_day_start
        <if test="record.orderByClause != null">
            order by ${record.orderByClause}
        </if>
    </select>

    <select id="queryOrderDetailBySpecialMonth" parameterType="map" resultMap="BaseResultMap">
        SELECT od.order_num,od.purchased_at,od.publisher_id,od.book_id,od.book_title,od.book_pub_code,od.price_selling,od.price_fixed,od.book_esin,od.quantity,od.income_vendor,od.income_plat,od.income_total,od.pay_type
        FROM enyan_order_detail od
        WHERE DATE_FORMAT(od.purchased_at,'%Y%m')= #{record.startDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
        <if test="record.page != null">
            <![CDATA[ LIMIT #{record.page.pageSize} OFFSET #{record.page.recordIndex}]]>
        </if>
    </select>

    <select id="countBySpecialMonth" parameterType="map" resultType="java.lang.Long">
        SELECT count(*)
        FROM enyan_order_detail od
        WHERE DATE_FORMAT(od.purchased_at,'%Y%m')= #{record.startDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="queryOrderDetailByUser" parameterType="map" resultMap="BaseResultMap">
        SELECT od.book_id,od.book_title,od.book_pub_code,od.publisher_id,od.book_esin,sum(od.quantity) as quantity,sum(od.income_vendor) as income_vendor,
        sum(od.income_plat) as income_plat,sum(od.income_total) as income_total
        FROM enyan_order_detail od
        WHERE od.purchased_day BETWEEN #{record.startDate,jdbcType=VARCHAR} AND #{record.endDate,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
        GROUP BY book_id
        <if test="record.page != null">
            <![CDATA[ LIMIT #{record.page.pageSize,jdbcType=INTEGER} OFFSET #{record.page.recordIndex,jdbcType=INTEGER}]]>
        </if>
    </select>

    <select id="countByUser" parameterType="map" resultType="java.lang.Long">
        SELECT count(od.order_detail_id)
        FROM enyan_order_detail od
        WHERE od.user_email = #{record.userEmail,jdbcType=VARCHAR}
         AND od.is_deleted = 0
        <if test="record.publisherId != null">
            AND od.publisher_id = #{record.publisherId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="searchRecordsByUserAndBookAndAuthorList" parameterType="map" resultMap="BaseResultMap">
        SELECT od.book_id,od.book_title,od.book_pub_code,od.publisher_id,od.book_esin,od.purchased_at
        FROM enyan_order_detail od, enyan_book b
        WHERE od.user_email = #{record.userEmail,jdbcType=VARCHAR} AND od.book_id = b.book_id
         AND od.is_deleted = 0
        AND (b.book_title REGEXP #{record.bookTitle,jdbcType=VARCHAR} or b.author REGEXP #{record.bookTitle,jdbcType=VARCHAR})
        <if test="record.page != null">
            <![CDATA[ LIMIT #{record.page.pageSize,jdbcType=INTEGER} OFFSET #{record.page.recordIndex,jdbcType=INTEGER}]]>
        </if>
    </select>

    <select id="countRecordsByUserAndBookAndAuthorList" parameterType="map" resultType="java.lang.Long">
        SELECT count(b.book_id)
        FROM enyan_order_detail od, enyan_book b
        WHERE od.user_email = #{record.userEmail,jdbcType=VARCHAR} AND od.book_id = b.book_id
         AND od.is_deleted = 0
          AND (b.book_title REGEXP #{record.bookTitle,jdbcType=VARCHAR} or b.author REGEXP #{record.bookTitle,jdbcType=VARCHAR})
        <if test="record.page != null">
            <![CDATA[ LIMIT #{record.page.pageSize,jdbcType=INTEGER} OFFSET #{record.page.recordIndex,jdbcType=INTEGER}]]>
        </if>
    </select>

    <select id="searchBooksByUserAndBookAndAuthorList" parameterType="map" resultMap="BookResultMap">
        SELECT od.book_id,od.book_title,od.book_pub_code,od.publisher_id,od.book_esin,od.purchased_at
        FROM enyan_order_detail od, enyan_book b
        WHERE od.user_email = #{record.userEmail,jdbcType=VARCHAR} AND od.book_id = b.book_id
         AND od.is_deleted = 0
        AND (b.book_title REGEXP #{record.bookTitle,jdbcType=VARCHAR} or b.author REGEXP #{record.bookTitle,jdbcType=VARCHAR})
        <if test="record.page != null">
            <![CDATA[ LIMIT #{record.page.pageSize,jdbcType=INTEGER} OFFSET #{record.page.recordIndex,jdbcType=INTEGER}]]>
        </if>
        <if test="record.page != null">
            <![CDATA[ LIMIT #{record.page.pageSize,jdbcType=INTEGER} OFFSET #{record.page.recordIndex,jdbcType=INTEGER}]]>
        </if>
    </select>

    <select id="countBooksByUserAndBookAndAuthorList" parameterType="map" resultType="java.lang.Long">
        SELECT count(b.book_id)
        FROM enyan_order_detail od, enyan_book b
        WHERE od.user_email = #{record.userEmail,jdbcType=VARCHAR} AND od.book_id = b.book_id
          AND od.is_deleted = 0
          AND (b.book_title REGEXP #{record.bookTitle,jdbcType=VARCHAR} or b.author REGEXP #{record.bookTitle,jdbcType=VARCHAR})
    </select>
    <update id="updateByOrderAndBookKeySelective" parameterType="com.aaron.spring.model.EnyanOrderDetail">
        update enyan_order_detail
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userEmail != null">
                user_email = #{userEmail,jdbcType=VARCHAR},
            </if>
            <if test="bookTitle != null">
                book_title = #{bookTitle,jdbcType=VARCHAR},
            </if>
            <if test="bookPubCode != null">
                book_pub_code = #{bookPubCode,jdbcType=VARCHAR},
            </if>
            <if test="bookEsin != null">
                book_esin = #{bookEsin,jdbcType=VARCHAR},
            </if>
            <if test="publisherId != null">
                publisher_id = #{publisherId,jdbcType=BIGINT},
            </if>
            <if test="rateValue != null">
                rate_value = #{rateValue,jdbcType=VARCHAR},
            </if>
            <if test="priceFixed != null">
                price_fixed = #{priceFixed,jdbcType=DECIMAL},
            </if>
            <if test="priceSelling != null">
                price_selling = #{priceSelling,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="vendorPercent != null">
                vendor_percent = #{vendorPercent,jdbcType=INTEGER},
            </if>
            <if test="incomeVendor != null">
                income_vendor = #{incomeVendor,jdbcType=DECIMAL},
            </if>
            <if test="incomePlat != null">
                income_plat = #{incomePlat,jdbcType=DECIMAL},
            </if>
            <if test="incomeTotal != null">
                income_total = #{incomeTotal,jdbcType=DECIMAL},
            </if>
            <if test="incomeReal != null">
                income_real = #{incomeReal,jdbcType=DECIMAL},
            </if>
            <if test="payFee != null">
                pay_fee = #{payFee,jdbcType=DECIMAL},
            </if>
            <if test="netSales != null">
                net_sales = #{netSales,jdbcType=DECIMAL},
            </if>
            <if test="orderCurrency != null">
                order_currency = #{orderCurrency,jdbcType=TINYINT},
            </if>
            <if test="purchasedAt != null">
                purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="purchasedDay != null">
                purchased_day = #{purchasedDay,jdbcType=INTEGER},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=INTEGER},
            </if>
            <if test="isCounted != null">
                is_counted = #{isCounted,jdbcType=TINYINT},
            </if>
            <if test="drminfo != null">
                drmInfo = #{drminfo,jdbcType=VARCHAR},
            </if>
        </set>
        where order_num = #{orderNum,jdbcType=VARCHAR} and book_id = #{bookId,jdbcType=BIGINT}
    </update>

    <update id="updateOrderDetailPublicationIdByBookId" parameterType="map">
        update enyan_order_detail
        <set>
            drmInfo=JSON_SET(drmInfo,'$.lInfo.pubId', #{newPublicationId,jdbcType=INTEGER},'$.lInfo.lId','')
        </set>
        where book_id = #{bookId,jdbcType=BIGINT}
    </update>

  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>