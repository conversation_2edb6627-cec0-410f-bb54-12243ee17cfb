<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanBalanceCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBalance">
    <id column="balance_id" jdbcType="BIGINT" property="balanceId" />
    <result column="purchased_month" jdbcType="INTEGER" property="purchasedMonth" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="price_fixed" jdbcType="DECIMAL" property="priceFixed" />
    <result column="price_selling" jdbcType="DECIMAL" property="priceSelling" />
    <result column="income_vendor" jdbcType="DECIMAL" property="incomeVendor" />
    <result column="income_plat" jdbcType="DECIMAL" property="incomePlat" />
    <result column="income_total" jdbcType="DECIMAL" property="incomeTotal" />
    <result column="income_real" jdbcType="DECIMAL" property="incomeReal" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="is_counted" jdbcType="TINYINT" property="isCounted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    balance_id, purchased_month, publisher_id, quantity, price_fixed, price_selling, 
    income_vendor, income_plat, income_total, income_real, order_type, is_counted
  </sql>

  <select id="findBalanceByIds" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from enyan_balance od
    where
     1 = 1
      <if test="publisherId != null">
          AND od.publisher_id = #{publisherId,jdbcType=BIGINT}
      </if>
      AND od.balance_id in
    <foreach item="item" collection="balanceIdList" separator="," open="(" close=")" index="">
      #{item, jdbcType=BIGINT}
    </foreach>
  </select>

  <update id="updateBalanceStatusByIds" parameterType="map">
    UPDATE enyan_balance od SET od.is_counted = #{isCounted,jdbcType=TINYINT}
    where
    1 = 1
    <if test="publisherId != null">
      AND od.publisher_id = #{publisherId,jdbcType=BIGINT}
    </if>
    AND od.balance_id in
    <foreach item="item" collection="balanceIdList" separator="," open="(" close=")" index="">
      #{item, jdbcType=BIGINT}
    </foreach>
  </update>

  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>