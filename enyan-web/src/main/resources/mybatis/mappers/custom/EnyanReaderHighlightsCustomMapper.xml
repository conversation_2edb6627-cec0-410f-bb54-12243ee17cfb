<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanReaderHighlightsCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanReaderHighlights">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="highlight_id" jdbcType="VARCHAR" property="highlightId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="publication_id" jdbcType="VARCHAR" property="publicationId" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="resource_index" jdbcType="INTEGER" property="resourceIndex" />
    <result column="resource_href" jdbcType="VARCHAR" property="resourceHref" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="resource_title" jdbcType="VARCHAR" property="resourceTitle" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="locator_text" jdbcType="VARCHAR" property="locatorText" />
    <result column="color" jdbcType="INTEGER" property="color" />
    <result column="annotation" jdbcType="VARCHAR" property="annotation" />
    <result column="creation_date" jdbcType="BIGINT" property="creationDate" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, highlight_id, user_id, user_email, publication_id, book_id, resource_index, resource_href,
    resource_type, resource_title, location, locator_text, color, annotation, creation_date,
    is_deleted, update_time
  </sql>

  <insert id="add" parameterType="com.aaron.spring.model.EnyanReaderHighlights">
    insert into enyan_reader_highlights (id, highlight_id, user_id, user_email,
                                         publication_id, book_id, resource_index,
                                         resource_href, resource_type, resource_title,
                                         location, locator_text, color,
                                         annotation, creation_date, is_deleted,
                                         update_time)
    values (#{id,jdbcType=VARCHAR}, #{highlightId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR},
            #{publicationId,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, #{resourceIndex,jdbcType=INTEGER},
            #{resourceHref,jdbcType=VARCHAR}, #{resourceType,jdbcType=VARCHAR}, #{resourceTitle,jdbcType=VARCHAR},
            #{location,jdbcType=VARCHAR}, #{locatorText,jdbcType=VARCHAR}, #{color,jdbcType=INTEGER},
            #{annotation,jdbcType=VARCHAR}, #{creationDate,jdbcType=BIGINT}, #{isDeleted,jdbcType=INTEGER},
            #{updateTime,jdbcType=BIGINT})
  </insert>
</mapper>