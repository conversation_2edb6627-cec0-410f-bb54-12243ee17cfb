<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.ContentMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.Content">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="length" jdbcType="BIGINT" property="length" />
    <result column="sha256" jdbcType="VARCHAR" property="sha256" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.Content">
    <result column="encryption_key" jdbcType="VARBINARY" property="encryptionKey" />
    <result column="location" jdbcType="LONGVARCHAR" property="location" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, length, sha256
  </sql>
  <sql id="Blob_Column_List">
    encryption_key, location
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.ContentExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.ContentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from content
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from content
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.ContentExample">
    delete from content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.Content">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into content (length, sha256, encryption_key, 
      location)
    values (#{length,jdbcType=BIGINT}, #{sha256,jdbcType=VARCHAR}, #{encryptionKey,jdbcType=VARBINARY}, 
      #{location,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.Content">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="length != null">
        length,
      </if>
      <if test="sha256 != null">
        sha256,
      </if>
      <if test="encryptionKey != null">
        encryption_key,
      </if>
      <if test="location != null">
        location,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="length != null">
        #{length,jdbcType=BIGINT},
      </if>
      <if test="sha256 != null">
        #{sha256,jdbcType=VARCHAR},
      </if>
      <if test="encryptionKey != null">
        #{encryptionKey,jdbcType=VARBINARY},
      </if>
      <if test="location != null">
        #{location,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.ContentExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from content
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          content
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update content
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.length != null">
        length = #{record.length,jdbcType=BIGINT},
      </if>
      <if test="record.sha256 != null">
        sha256 = #{record.sha256,jdbcType=VARCHAR},
      </if>
      <if test="record.encryptionKey != null">
        encryption_key = #{record.encryptionKey,jdbcType=VARBINARY},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update content
    set id = #{record.id,jdbcType=VARCHAR},
      length = #{record.length,jdbcType=BIGINT},
      sha256 = #{record.sha256,jdbcType=VARCHAR},
      encryption_key = #{record.encryptionKey,jdbcType=VARBINARY},
      location = #{record.location,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update content
    set id = #{record.id,jdbcType=VARCHAR},
      length = #{record.length,jdbcType=BIGINT},
      sha256 = #{record.sha256,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.Content">
    update content
    <set>
      <if test="length != null">
        length = #{length,jdbcType=BIGINT},
      </if>
      <if test="sha256 != null">
        sha256 = #{sha256,jdbcType=VARCHAR},
      </if>
      <if test="encryptionKey != null">
        encryption_key = #{encryptionKey,jdbcType=VARBINARY},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.Content">
    update content
    set length = #{length,jdbcType=BIGINT},
      sha256 = #{sha256,jdbcType=VARCHAR},
      encryption_key = #{encryptionKey,jdbcType=VARBINARY},
      location = #{location,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.Content">
    update content
    set length = #{length,jdbcType=BIGINT},
      sha256 = #{sha256,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>