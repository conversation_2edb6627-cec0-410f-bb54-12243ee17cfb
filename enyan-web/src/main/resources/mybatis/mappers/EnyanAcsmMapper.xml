<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanAcsmMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanAcsm">
    <id column="acsm_id" jdbcType="BIGINT" property="acsmId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="transaction_id" jdbcType="VARCHAR" property="transactionId" />
    <result column="is_fulfilled" jdbcType="TINYINT" property="isFulfilled" />
    <result column="acsm_info" jdbcType="VARCHAR" property="acsmInfo" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    acsm_id, order_num, book_id, user_id, transaction_id, is_fulfilled, acsm_info, create_at
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanAcsmExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_acsm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_acsm
    where acsm_id = #{acsmId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_acsm
    where acsm_id = #{acsmId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanAcsmExample">
    delete from enyan_acsm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanAcsm">
    <selectKey keyProperty="acsmId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_acsm (order_num, book_id, user_id, 
      transaction_id, is_fulfilled, acsm_info, 
      create_at)
    values (#{orderNum,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, 
      #{transactionId,jdbcType=VARCHAR}, #{isFulfilled,jdbcType=TINYINT}, #{acsmInfo,jdbcType=VARCHAR}, 
      #{createAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanAcsm">
    <selectKey keyProperty="acsmId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_acsm
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="transactionId != null">
        transaction_id,
      </if>
      <if test="isFulfilled != null">
        is_fulfilled,
      </if>
      <if test="acsmInfo != null">
        acsm_info,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="transactionId != null">
        #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="isFulfilled != null">
        #{isFulfilled,jdbcType=TINYINT},
      </if>
      <if test="acsmInfo != null">
        #{acsmInfo,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanAcsmExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_acsm
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_acsm
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_acsm
    <set>
      <if test="record.acsmId != null">
        acsm_id = #{record.acsmId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNum != null">
        order_num = #{record.orderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.transactionId != null">
        transaction_id = #{record.transactionId,jdbcType=VARCHAR},
      </if>
      <if test="record.isFulfilled != null">
        is_fulfilled = #{record.isFulfilled,jdbcType=TINYINT},
      </if>
      <if test="record.acsmInfo != null">
        acsm_info = #{record.acsmInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_acsm
    set acsm_id = #{record.acsmId,jdbcType=BIGINT},
      order_num = #{record.orderNum,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      transaction_id = #{record.transactionId,jdbcType=VARCHAR},
      is_fulfilled = #{record.isFulfilled,jdbcType=TINYINT},
      acsm_info = #{record.acsmInfo,jdbcType=VARCHAR},
      create_at = #{record.createAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanAcsm">
    update enyan_acsm
    <set>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="transactionId != null">
        transaction_id = #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="isFulfilled != null">
        is_fulfilled = #{isFulfilled,jdbcType=TINYINT},
      </if>
      <if test="acsmInfo != null">
        acsm_info = #{acsmInfo,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where acsm_id = #{acsmId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanAcsm">
    update enyan_acsm
    set order_num = #{orderNum,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      transaction_id = #{transactionId,jdbcType=VARCHAR},
      is_fulfilled = #{isFulfilled,jdbcType=TINYINT},
      acsm_info = #{acsmInfo,jdbcType=VARCHAR},
      create_at = #{createAt,jdbcType=TIMESTAMP}
    where acsm_id = #{acsmId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>