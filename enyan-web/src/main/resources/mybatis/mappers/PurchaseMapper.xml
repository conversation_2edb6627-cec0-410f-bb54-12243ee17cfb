<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.PurchaseMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.Purchase">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="publication_id" jdbcType="INTEGER" property="publicationId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="license_uuid" jdbcType="VARCHAR" property="licenseUuid" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="transaction_date" jdbcType="TIMESTAMP" property="transactionDate" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, uuid, publication_id, user_id, license_uuid, type, transaction_date, start_date, 
    end_date, status
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.PurchaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from purchase
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.PurchaseExample">
    delete from purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.Purchase" useGeneratedKeys="true" keyProperty="id">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into purchase (uuid, publication_id, user_id, 
      license_uuid, type, transaction_date, 
      start_date, end_date, status
      )
    values (#{uuid,jdbcType=VARCHAR}, #{publicationId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, 
      #{licenseUuid,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{transactionDate,jdbcType=TIMESTAMP}, 
      #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.Purchase">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into purchase
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uuid != null">
        uuid,
      </if>
      <if test="publicationId != null">
        publication_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="licenseUuid != null">
        license_uuid,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="transactionDate != null">
        transaction_date,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="publicationId != null">
        #{publicationId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="licenseUuid != null">
        #{licenseUuid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="transactionDate != null">
        #{transactionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.PurchaseExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from purchase
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          purchase
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update purchase
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.uuid != null">
        uuid = #{record.uuid,jdbcType=VARCHAR},
      </if>
      <if test="record.publicationId != null">
        publication_id = #{record.publicationId,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.licenseUuid != null">
        license_uuid = #{record.licenseUuid,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.transactionDate != null">
        transaction_date = #{record.transactionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update purchase
    set id = #{record.id,jdbcType=INTEGER},
      uuid = #{record.uuid,jdbcType=VARCHAR},
      publication_id = #{record.publicationId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      license_uuid = #{record.licenseUuid,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      transaction_date = #{record.transactionDate,jdbcType=TIMESTAMP},
      start_date = #{record.startDate,jdbcType=TIMESTAMP},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.Purchase">
    update purchase
    <set>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="publicationId != null">
        publication_id = #{publicationId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="licenseUuid != null">
        license_uuid = #{licenseUuid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="transactionDate != null">
        transaction_date = #{transactionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.Purchase">
    update purchase
    set uuid = #{uuid,jdbcType=VARCHAR},
      publication_id = #{publicationId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      license_uuid = #{licenseUuid,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      transaction_date = #{transactionDate,jdbcType=TIMESTAMP},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>