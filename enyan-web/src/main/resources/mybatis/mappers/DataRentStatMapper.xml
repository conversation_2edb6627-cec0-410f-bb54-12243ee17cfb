<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.DataRentStatMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.DataRentStat">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="rent_sc_all_sum" jdbcType="INTEGER" property="rentScAllSum" />
    <result column="rent_sc_ot_sum" jdbcType="INTEGER" property="rentScOtSum" />
    <result column="rent_sc_nt_sum" jdbcType="INTEGER" property="rentScNtSum" />
    <result column="rent_sc_all_new" jdbcType="INTEGER" property="rentScAllNew" />
    <result column="rent_sc_ot_new" jdbcType="INTEGER" property="rentScOtNew" />
    <result column="rent_sc_nt_new" jdbcType="INTEGER" property="rentScNtNew" />
    <result column="rent_sc_all_leave" jdbcType="INTEGER" property="rentScAllLeave" />
    <result column="rent_sc_ot_leave" jdbcType="INTEGER" property="rentScOtLeave" />
    <result column="rent_sc_nt_leave" jdbcType="INTEGER" property="rentScNtLeave" />
    <result column="rent_sc_all_buy" jdbcType="INTEGER" property="rentScAllBuy" />
    <result column="rent_sc_ot_buy" jdbcType="INTEGER" property="rentScOtBuy" />
    <result column="rent_sc_nt_buy" jdbcType="INTEGER" property="rentScNtBuy" />
    <result column="rent_sc_all_active" jdbcType="INTEGER" property="rentScAllActive" />
    <result column="rent_sc_ot_active" jdbcType="INTEGER" property="rentScOtActive" />
    <result column="rent_sc_nt_active" jdbcType="INTEGER" property="rentScNtActive" />
    <result column="rent_sc_all_time" jdbcType="VARCHAR" property="rentScAllTime" />
    <result column="rent_sc_ot_time" jdbcType="VARCHAR" property="rentScOtTime" />
    <result column="rent_sc_nt_time" jdbcType="VARCHAR" property="rentScNtTime" />
    <result column="rent_tc_all_sum" jdbcType="INTEGER" property="rentTcAllSum" />
    <result column="rent_tc_ot_sum" jdbcType="INTEGER" property="rentTcOtSum" />
    <result column="rent_tc_nt_sum" jdbcType="INTEGER" property="rentTcNtSum" />
    <result column="rent_tc_all_new" jdbcType="INTEGER" property="rentTcAllNew" />
    <result column="rent_tc_ot_new" jdbcType="INTEGER" property="rentTcOtNew" />
    <result column="rent_tc_nt_new" jdbcType="INTEGER" property="rentTcNtNew" />
    <result column="rent_tc_all_leave" jdbcType="INTEGER" property="rentTcAllLeave" />
    <result column="rent_tc_ot_leave" jdbcType="INTEGER" property="rentTcOtLeave" />
    <result column="rent_tc_nt_leave" jdbcType="INTEGER" property="rentTcNtLeave" />
    <result column="rent_tc_all_buy" jdbcType="INTEGER" property="rentTcAllBuy" />
    <result column="rent_tc_ot_buy" jdbcType="INTEGER" property="rentTcOtBuy" />
    <result column="rent_tc_nt_buy" jdbcType="INTEGER" property="rentTcNtBuy" />
    <result column="rent_tc_all_active" jdbcType="INTEGER" property="rentTcAllActive" />
    <result column="rent_tc_ot_active" jdbcType="INTEGER" property="rentTcOtActive" />
    <result column="rent_tc_nt_active" jdbcType="INTEGER" property="rentTcNtActive" />
    <result column="rent_tc_all_time" jdbcType="VARCHAR" property="rentTcAllTime" />
    <result column="rent_tc_ot_time" jdbcType="VARCHAR" property="rentTcOtTime" />
    <result column="rent_tc_nt_time" jdbcType="VARCHAR" property="rentTcNtTime" />
    <result column="data_at" jdbcType="TIMESTAMP" property="dataAt" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, rent_sc_all_sum, rent_sc_ot_sum, rent_sc_nt_sum, rent_sc_all_new, rent_sc_ot_new, 
    rent_sc_nt_new, rent_sc_all_leave, rent_sc_ot_leave, rent_sc_nt_leave, rent_sc_all_buy, 
    rent_sc_ot_buy, rent_sc_nt_buy, rent_sc_all_active, rent_sc_ot_active, rent_sc_nt_active, 
    rent_sc_all_time, rent_sc_ot_time, rent_sc_nt_time, rent_tc_all_sum, rent_tc_ot_sum, 
    rent_tc_nt_sum, rent_tc_all_new, rent_tc_ot_new, rent_tc_nt_new, rent_tc_all_leave, 
    rent_tc_ot_leave, rent_tc_nt_leave, rent_tc_all_buy, rent_tc_ot_buy, rent_tc_nt_buy, 
    rent_tc_all_active, rent_tc_ot_active, rent_tc_nt_active, rent_tc_all_time, rent_tc_ot_time, 
    rent_tc_nt_time, data_at, create_time
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.DataRentStatExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from data_rent_stat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_rent_stat
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_rent_stat
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.DataRentStatExample">
    delete from data_rent_stat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.DataRentStat">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into data_rent_stat (rent_sc_all_sum, rent_sc_ot_sum, rent_sc_nt_sum, 
      rent_sc_all_new, rent_sc_ot_new, rent_sc_nt_new, 
      rent_sc_all_leave, rent_sc_ot_leave, rent_sc_nt_leave, 
      rent_sc_all_buy, rent_sc_ot_buy, rent_sc_nt_buy, 
      rent_sc_all_active, rent_sc_ot_active, rent_sc_nt_active, 
      rent_sc_all_time, rent_sc_ot_time, rent_sc_nt_time, 
      rent_tc_all_sum, rent_tc_ot_sum, rent_tc_nt_sum, 
      rent_tc_all_new, rent_tc_ot_new, rent_tc_nt_new, 
      rent_tc_all_leave, rent_tc_ot_leave, rent_tc_nt_leave, 
      rent_tc_all_buy, rent_tc_ot_buy, rent_tc_nt_buy, 
      rent_tc_all_active, rent_tc_ot_active, rent_tc_nt_active, 
      rent_tc_all_time, rent_tc_ot_time, rent_tc_nt_time, 
      data_at, create_time)
    values (#{rentScAllSum,jdbcType=INTEGER}, #{rentScOtSum,jdbcType=INTEGER}, #{rentScNtSum,jdbcType=INTEGER}, 
      #{rentScAllNew,jdbcType=INTEGER}, #{rentScOtNew,jdbcType=INTEGER}, #{rentScNtNew,jdbcType=INTEGER}, 
      #{rentScAllLeave,jdbcType=INTEGER}, #{rentScOtLeave,jdbcType=INTEGER}, #{rentScNtLeave,jdbcType=INTEGER}, 
      #{rentScAllBuy,jdbcType=INTEGER}, #{rentScOtBuy,jdbcType=INTEGER}, #{rentScNtBuy,jdbcType=INTEGER}, 
      #{rentScAllActive,jdbcType=INTEGER}, #{rentScOtActive,jdbcType=INTEGER}, #{rentScNtActive,jdbcType=INTEGER}, 
      #{rentScAllTime,jdbcType=VARCHAR}, #{rentScOtTime,jdbcType=VARCHAR}, #{rentScNtTime,jdbcType=VARCHAR}, 
      #{rentTcAllSum,jdbcType=INTEGER}, #{rentTcOtSum,jdbcType=INTEGER}, #{rentTcNtSum,jdbcType=INTEGER}, 
      #{rentTcAllNew,jdbcType=INTEGER}, #{rentTcOtNew,jdbcType=INTEGER}, #{rentTcNtNew,jdbcType=INTEGER}, 
      #{rentTcAllLeave,jdbcType=INTEGER}, #{rentTcOtLeave,jdbcType=INTEGER}, #{rentTcNtLeave,jdbcType=INTEGER}, 
      #{rentTcAllBuy,jdbcType=INTEGER}, #{rentTcOtBuy,jdbcType=INTEGER}, #{rentTcNtBuy,jdbcType=INTEGER}, 
      #{rentTcAllActive,jdbcType=INTEGER}, #{rentTcOtActive,jdbcType=INTEGER}, #{rentTcNtActive,jdbcType=INTEGER}, 
      #{rentTcAllTime,jdbcType=VARCHAR}, #{rentTcOtTime,jdbcType=VARCHAR}, #{rentTcNtTime,jdbcType=VARCHAR}, 
      #{dataAt,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.DataRentStat">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into data_rent_stat
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rentScAllSum != null">
        rent_sc_all_sum,
      </if>
      <if test="rentScOtSum != null">
        rent_sc_ot_sum,
      </if>
      <if test="rentScNtSum != null">
        rent_sc_nt_sum,
      </if>
      <if test="rentScAllNew != null">
        rent_sc_all_new,
      </if>
      <if test="rentScOtNew != null">
        rent_sc_ot_new,
      </if>
      <if test="rentScNtNew != null">
        rent_sc_nt_new,
      </if>
      <if test="rentScAllLeave != null">
        rent_sc_all_leave,
      </if>
      <if test="rentScOtLeave != null">
        rent_sc_ot_leave,
      </if>
      <if test="rentScNtLeave != null">
        rent_sc_nt_leave,
      </if>
      <if test="rentScAllBuy != null">
        rent_sc_all_buy,
      </if>
      <if test="rentScOtBuy != null">
        rent_sc_ot_buy,
      </if>
      <if test="rentScNtBuy != null">
        rent_sc_nt_buy,
      </if>
      <if test="rentScAllActive != null">
        rent_sc_all_active,
      </if>
      <if test="rentScOtActive != null">
        rent_sc_ot_active,
      </if>
      <if test="rentScNtActive != null">
        rent_sc_nt_active,
      </if>
      <if test="rentScAllTime != null">
        rent_sc_all_time,
      </if>
      <if test="rentScOtTime != null">
        rent_sc_ot_time,
      </if>
      <if test="rentScNtTime != null">
        rent_sc_nt_time,
      </if>
      <if test="rentTcAllSum != null">
        rent_tc_all_sum,
      </if>
      <if test="rentTcOtSum != null">
        rent_tc_ot_sum,
      </if>
      <if test="rentTcNtSum != null">
        rent_tc_nt_sum,
      </if>
      <if test="rentTcAllNew != null">
        rent_tc_all_new,
      </if>
      <if test="rentTcOtNew != null">
        rent_tc_ot_new,
      </if>
      <if test="rentTcNtNew != null">
        rent_tc_nt_new,
      </if>
      <if test="rentTcAllLeave != null">
        rent_tc_all_leave,
      </if>
      <if test="rentTcOtLeave != null">
        rent_tc_ot_leave,
      </if>
      <if test="rentTcNtLeave != null">
        rent_tc_nt_leave,
      </if>
      <if test="rentTcAllBuy != null">
        rent_tc_all_buy,
      </if>
      <if test="rentTcOtBuy != null">
        rent_tc_ot_buy,
      </if>
      <if test="rentTcNtBuy != null">
        rent_tc_nt_buy,
      </if>
      <if test="rentTcAllActive != null">
        rent_tc_all_active,
      </if>
      <if test="rentTcOtActive != null">
        rent_tc_ot_active,
      </if>
      <if test="rentTcNtActive != null">
        rent_tc_nt_active,
      </if>
      <if test="rentTcAllTime != null">
        rent_tc_all_time,
      </if>
      <if test="rentTcOtTime != null">
        rent_tc_ot_time,
      </if>
      <if test="rentTcNtTime != null">
        rent_tc_nt_time,
      </if>
      <if test="dataAt != null">
        data_at,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rentScAllSum != null">
        #{rentScAllSum,jdbcType=INTEGER},
      </if>
      <if test="rentScOtSum != null">
        #{rentScOtSum,jdbcType=INTEGER},
      </if>
      <if test="rentScNtSum != null">
        #{rentScNtSum,jdbcType=INTEGER},
      </if>
      <if test="rentScAllNew != null">
        #{rentScAllNew,jdbcType=INTEGER},
      </if>
      <if test="rentScOtNew != null">
        #{rentScOtNew,jdbcType=INTEGER},
      </if>
      <if test="rentScNtNew != null">
        #{rentScNtNew,jdbcType=INTEGER},
      </if>
      <if test="rentScAllLeave != null">
        #{rentScAllLeave,jdbcType=INTEGER},
      </if>
      <if test="rentScOtLeave != null">
        #{rentScOtLeave,jdbcType=INTEGER},
      </if>
      <if test="rentScNtLeave != null">
        #{rentScNtLeave,jdbcType=INTEGER},
      </if>
      <if test="rentScAllBuy != null">
        #{rentScAllBuy,jdbcType=INTEGER},
      </if>
      <if test="rentScOtBuy != null">
        #{rentScOtBuy,jdbcType=INTEGER},
      </if>
      <if test="rentScNtBuy != null">
        #{rentScNtBuy,jdbcType=INTEGER},
      </if>
      <if test="rentScAllActive != null">
        #{rentScAllActive,jdbcType=INTEGER},
      </if>
      <if test="rentScOtActive != null">
        #{rentScOtActive,jdbcType=INTEGER},
      </if>
      <if test="rentScNtActive != null">
        #{rentScNtActive,jdbcType=INTEGER},
      </if>
      <if test="rentScAllTime != null">
        #{rentScAllTime,jdbcType=VARCHAR},
      </if>
      <if test="rentScOtTime != null">
        #{rentScOtTime,jdbcType=VARCHAR},
      </if>
      <if test="rentScNtTime != null">
        #{rentScNtTime,jdbcType=VARCHAR},
      </if>
      <if test="rentTcAllSum != null">
        #{rentTcAllSum,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtSum != null">
        #{rentTcOtSum,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtSum != null">
        #{rentTcNtSum,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllNew != null">
        #{rentTcAllNew,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtNew != null">
        #{rentTcOtNew,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtNew != null">
        #{rentTcNtNew,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllLeave != null">
        #{rentTcAllLeave,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtLeave != null">
        #{rentTcOtLeave,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtLeave != null">
        #{rentTcNtLeave,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllBuy != null">
        #{rentTcAllBuy,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtBuy != null">
        #{rentTcOtBuy,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtBuy != null">
        #{rentTcNtBuy,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllActive != null">
        #{rentTcAllActive,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtActive != null">
        #{rentTcOtActive,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtActive != null">
        #{rentTcNtActive,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllTime != null">
        #{rentTcAllTime,jdbcType=VARCHAR},
      </if>
      <if test="rentTcOtTime != null">
        #{rentTcOtTime,jdbcType=VARCHAR},
      </if>
      <if test="rentTcNtTime != null">
        #{rentTcNtTime,jdbcType=VARCHAR},
      </if>
      <if test="dataAt != null">
        #{dataAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.DataRentStatExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from data_rent_stat
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          data_rent_stat
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update data_rent_stat
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.rentScAllSum != null">
        rent_sc_all_sum = #{record.rentScAllSum,jdbcType=INTEGER},
      </if>
      <if test="record.rentScOtSum != null">
        rent_sc_ot_sum = #{record.rentScOtSum,jdbcType=INTEGER},
      </if>
      <if test="record.rentScNtSum != null">
        rent_sc_nt_sum = #{record.rentScNtSum,jdbcType=INTEGER},
      </if>
      <if test="record.rentScAllNew != null">
        rent_sc_all_new = #{record.rentScAllNew,jdbcType=INTEGER},
      </if>
      <if test="record.rentScOtNew != null">
        rent_sc_ot_new = #{record.rentScOtNew,jdbcType=INTEGER},
      </if>
      <if test="record.rentScNtNew != null">
        rent_sc_nt_new = #{record.rentScNtNew,jdbcType=INTEGER},
      </if>
      <if test="record.rentScAllLeave != null">
        rent_sc_all_leave = #{record.rentScAllLeave,jdbcType=INTEGER},
      </if>
      <if test="record.rentScOtLeave != null">
        rent_sc_ot_leave = #{record.rentScOtLeave,jdbcType=INTEGER},
      </if>
      <if test="record.rentScNtLeave != null">
        rent_sc_nt_leave = #{record.rentScNtLeave,jdbcType=INTEGER},
      </if>
      <if test="record.rentScAllBuy != null">
        rent_sc_all_buy = #{record.rentScAllBuy,jdbcType=INTEGER},
      </if>
      <if test="record.rentScOtBuy != null">
        rent_sc_ot_buy = #{record.rentScOtBuy,jdbcType=INTEGER},
      </if>
      <if test="record.rentScNtBuy != null">
        rent_sc_nt_buy = #{record.rentScNtBuy,jdbcType=INTEGER},
      </if>
      <if test="record.rentScAllActive != null">
        rent_sc_all_active = #{record.rentScAllActive,jdbcType=INTEGER},
      </if>
      <if test="record.rentScOtActive != null">
        rent_sc_ot_active = #{record.rentScOtActive,jdbcType=INTEGER},
      </if>
      <if test="record.rentScNtActive != null">
        rent_sc_nt_active = #{record.rentScNtActive,jdbcType=INTEGER},
      </if>
      <if test="record.rentScAllTime != null">
        rent_sc_all_time = #{record.rentScAllTime,jdbcType=VARCHAR},
      </if>
      <if test="record.rentScOtTime != null">
        rent_sc_ot_time = #{record.rentScOtTime,jdbcType=VARCHAR},
      </if>
      <if test="record.rentScNtTime != null">
        rent_sc_nt_time = #{record.rentScNtTime,jdbcType=VARCHAR},
      </if>
      <if test="record.rentTcAllSum != null">
        rent_tc_all_sum = #{record.rentTcAllSum,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcOtSum != null">
        rent_tc_ot_sum = #{record.rentTcOtSum,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcNtSum != null">
        rent_tc_nt_sum = #{record.rentTcNtSum,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcAllNew != null">
        rent_tc_all_new = #{record.rentTcAllNew,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcOtNew != null">
        rent_tc_ot_new = #{record.rentTcOtNew,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcNtNew != null">
        rent_tc_nt_new = #{record.rentTcNtNew,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcAllLeave != null">
        rent_tc_all_leave = #{record.rentTcAllLeave,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcOtLeave != null">
        rent_tc_ot_leave = #{record.rentTcOtLeave,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcNtLeave != null">
        rent_tc_nt_leave = #{record.rentTcNtLeave,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcAllBuy != null">
        rent_tc_all_buy = #{record.rentTcAllBuy,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcOtBuy != null">
        rent_tc_ot_buy = #{record.rentTcOtBuy,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcNtBuy != null">
        rent_tc_nt_buy = #{record.rentTcNtBuy,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcAllActive != null">
        rent_tc_all_active = #{record.rentTcAllActive,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcOtActive != null">
        rent_tc_ot_active = #{record.rentTcOtActive,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcNtActive != null">
        rent_tc_nt_active = #{record.rentTcNtActive,jdbcType=INTEGER},
      </if>
      <if test="record.rentTcAllTime != null">
        rent_tc_all_time = #{record.rentTcAllTime,jdbcType=VARCHAR},
      </if>
      <if test="record.rentTcOtTime != null">
        rent_tc_ot_time = #{record.rentTcOtTime,jdbcType=VARCHAR},
      </if>
      <if test="record.rentTcNtTime != null">
        rent_tc_nt_time = #{record.rentTcNtTime,jdbcType=VARCHAR},
      </if>
      <if test="record.dataAt != null">
        data_at = #{record.dataAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update data_rent_stat
    set data_id = #{record.dataId,jdbcType=BIGINT},
      rent_sc_all_sum = #{record.rentScAllSum,jdbcType=INTEGER},
      rent_sc_ot_sum = #{record.rentScOtSum,jdbcType=INTEGER},
      rent_sc_nt_sum = #{record.rentScNtSum,jdbcType=INTEGER},
      rent_sc_all_new = #{record.rentScAllNew,jdbcType=INTEGER},
      rent_sc_ot_new = #{record.rentScOtNew,jdbcType=INTEGER},
      rent_sc_nt_new = #{record.rentScNtNew,jdbcType=INTEGER},
      rent_sc_all_leave = #{record.rentScAllLeave,jdbcType=INTEGER},
      rent_sc_ot_leave = #{record.rentScOtLeave,jdbcType=INTEGER},
      rent_sc_nt_leave = #{record.rentScNtLeave,jdbcType=INTEGER},
      rent_sc_all_buy = #{record.rentScAllBuy,jdbcType=INTEGER},
      rent_sc_ot_buy = #{record.rentScOtBuy,jdbcType=INTEGER},
      rent_sc_nt_buy = #{record.rentScNtBuy,jdbcType=INTEGER},
      rent_sc_all_active = #{record.rentScAllActive,jdbcType=INTEGER},
      rent_sc_ot_active = #{record.rentScOtActive,jdbcType=INTEGER},
      rent_sc_nt_active = #{record.rentScNtActive,jdbcType=INTEGER},
      rent_sc_all_time = #{record.rentScAllTime,jdbcType=VARCHAR},
      rent_sc_ot_time = #{record.rentScOtTime,jdbcType=VARCHAR},
      rent_sc_nt_time = #{record.rentScNtTime,jdbcType=VARCHAR},
      rent_tc_all_sum = #{record.rentTcAllSum,jdbcType=INTEGER},
      rent_tc_ot_sum = #{record.rentTcOtSum,jdbcType=INTEGER},
      rent_tc_nt_sum = #{record.rentTcNtSum,jdbcType=INTEGER},
      rent_tc_all_new = #{record.rentTcAllNew,jdbcType=INTEGER},
      rent_tc_ot_new = #{record.rentTcOtNew,jdbcType=INTEGER},
      rent_tc_nt_new = #{record.rentTcNtNew,jdbcType=INTEGER},
      rent_tc_all_leave = #{record.rentTcAllLeave,jdbcType=INTEGER},
      rent_tc_ot_leave = #{record.rentTcOtLeave,jdbcType=INTEGER},
      rent_tc_nt_leave = #{record.rentTcNtLeave,jdbcType=INTEGER},
      rent_tc_all_buy = #{record.rentTcAllBuy,jdbcType=INTEGER},
      rent_tc_ot_buy = #{record.rentTcOtBuy,jdbcType=INTEGER},
      rent_tc_nt_buy = #{record.rentTcNtBuy,jdbcType=INTEGER},
      rent_tc_all_active = #{record.rentTcAllActive,jdbcType=INTEGER},
      rent_tc_ot_active = #{record.rentTcOtActive,jdbcType=INTEGER},
      rent_tc_nt_active = #{record.rentTcNtActive,jdbcType=INTEGER},
      rent_tc_all_time = #{record.rentTcAllTime,jdbcType=VARCHAR},
      rent_tc_ot_time = #{record.rentTcOtTime,jdbcType=VARCHAR},
      rent_tc_nt_time = #{record.rentTcNtTime,jdbcType=VARCHAR},
      data_at = #{record.dataAt,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.DataRentStat">
    update data_rent_stat
    <set>
      <if test="rentScAllSum != null">
        rent_sc_all_sum = #{rentScAllSum,jdbcType=INTEGER},
      </if>
      <if test="rentScOtSum != null">
        rent_sc_ot_sum = #{rentScOtSum,jdbcType=INTEGER},
      </if>
      <if test="rentScNtSum != null">
        rent_sc_nt_sum = #{rentScNtSum,jdbcType=INTEGER},
      </if>
      <if test="rentScAllNew != null">
        rent_sc_all_new = #{rentScAllNew,jdbcType=INTEGER},
      </if>
      <if test="rentScOtNew != null">
        rent_sc_ot_new = #{rentScOtNew,jdbcType=INTEGER},
      </if>
      <if test="rentScNtNew != null">
        rent_sc_nt_new = #{rentScNtNew,jdbcType=INTEGER},
      </if>
      <if test="rentScAllLeave != null">
        rent_sc_all_leave = #{rentScAllLeave,jdbcType=INTEGER},
      </if>
      <if test="rentScOtLeave != null">
        rent_sc_ot_leave = #{rentScOtLeave,jdbcType=INTEGER},
      </if>
      <if test="rentScNtLeave != null">
        rent_sc_nt_leave = #{rentScNtLeave,jdbcType=INTEGER},
      </if>
      <if test="rentScAllBuy != null">
        rent_sc_all_buy = #{rentScAllBuy,jdbcType=INTEGER},
      </if>
      <if test="rentScOtBuy != null">
        rent_sc_ot_buy = #{rentScOtBuy,jdbcType=INTEGER},
      </if>
      <if test="rentScNtBuy != null">
        rent_sc_nt_buy = #{rentScNtBuy,jdbcType=INTEGER},
      </if>
      <if test="rentScAllActive != null">
        rent_sc_all_active = #{rentScAllActive,jdbcType=INTEGER},
      </if>
      <if test="rentScOtActive != null">
        rent_sc_ot_active = #{rentScOtActive,jdbcType=INTEGER},
      </if>
      <if test="rentScNtActive != null">
        rent_sc_nt_active = #{rentScNtActive,jdbcType=INTEGER},
      </if>
      <if test="rentScAllTime != null">
        rent_sc_all_time = #{rentScAllTime,jdbcType=VARCHAR},
      </if>
      <if test="rentScOtTime != null">
        rent_sc_ot_time = #{rentScOtTime,jdbcType=VARCHAR},
      </if>
      <if test="rentScNtTime != null">
        rent_sc_nt_time = #{rentScNtTime,jdbcType=VARCHAR},
      </if>
      <if test="rentTcAllSum != null">
        rent_tc_all_sum = #{rentTcAllSum,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtSum != null">
        rent_tc_ot_sum = #{rentTcOtSum,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtSum != null">
        rent_tc_nt_sum = #{rentTcNtSum,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllNew != null">
        rent_tc_all_new = #{rentTcAllNew,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtNew != null">
        rent_tc_ot_new = #{rentTcOtNew,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtNew != null">
        rent_tc_nt_new = #{rentTcNtNew,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllLeave != null">
        rent_tc_all_leave = #{rentTcAllLeave,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtLeave != null">
        rent_tc_ot_leave = #{rentTcOtLeave,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtLeave != null">
        rent_tc_nt_leave = #{rentTcNtLeave,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllBuy != null">
        rent_tc_all_buy = #{rentTcAllBuy,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtBuy != null">
        rent_tc_ot_buy = #{rentTcOtBuy,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtBuy != null">
        rent_tc_nt_buy = #{rentTcNtBuy,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllActive != null">
        rent_tc_all_active = #{rentTcAllActive,jdbcType=INTEGER},
      </if>
      <if test="rentTcOtActive != null">
        rent_tc_ot_active = #{rentTcOtActive,jdbcType=INTEGER},
      </if>
      <if test="rentTcNtActive != null">
        rent_tc_nt_active = #{rentTcNtActive,jdbcType=INTEGER},
      </if>
      <if test="rentTcAllTime != null">
        rent_tc_all_time = #{rentTcAllTime,jdbcType=VARCHAR},
      </if>
      <if test="rentTcOtTime != null">
        rent_tc_ot_time = #{rentTcOtTime,jdbcType=VARCHAR},
      </if>
      <if test="rentTcNtTime != null">
        rent_tc_nt_time = #{rentTcNtTime,jdbcType=VARCHAR},
      </if>
      <if test="dataAt != null">
        data_at = #{dataAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.DataRentStat">
    update data_rent_stat
    set rent_sc_all_sum = #{rentScAllSum,jdbcType=INTEGER},
      rent_sc_ot_sum = #{rentScOtSum,jdbcType=INTEGER},
      rent_sc_nt_sum = #{rentScNtSum,jdbcType=INTEGER},
      rent_sc_all_new = #{rentScAllNew,jdbcType=INTEGER},
      rent_sc_ot_new = #{rentScOtNew,jdbcType=INTEGER},
      rent_sc_nt_new = #{rentScNtNew,jdbcType=INTEGER},
      rent_sc_all_leave = #{rentScAllLeave,jdbcType=INTEGER},
      rent_sc_ot_leave = #{rentScOtLeave,jdbcType=INTEGER},
      rent_sc_nt_leave = #{rentScNtLeave,jdbcType=INTEGER},
      rent_sc_all_buy = #{rentScAllBuy,jdbcType=INTEGER},
      rent_sc_ot_buy = #{rentScOtBuy,jdbcType=INTEGER},
      rent_sc_nt_buy = #{rentScNtBuy,jdbcType=INTEGER},
      rent_sc_all_active = #{rentScAllActive,jdbcType=INTEGER},
      rent_sc_ot_active = #{rentScOtActive,jdbcType=INTEGER},
      rent_sc_nt_active = #{rentScNtActive,jdbcType=INTEGER},
      rent_sc_all_time = #{rentScAllTime,jdbcType=VARCHAR},
      rent_sc_ot_time = #{rentScOtTime,jdbcType=VARCHAR},
      rent_sc_nt_time = #{rentScNtTime,jdbcType=VARCHAR},
      rent_tc_all_sum = #{rentTcAllSum,jdbcType=INTEGER},
      rent_tc_ot_sum = #{rentTcOtSum,jdbcType=INTEGER},
      rent_tc_nt_sum = #{rentTcNtSum,jdbcType=INTEGER},
      rent_tc_all_new = #{rentTcAllNew,jdbcType=INTEGER},
      rent_tc_ot_new = #{rentTcOtNew,jdbcType=INTEGER},
      rent_tc_nt_new = #{rentTcNtNew,jdbcType=INTEGER},
      rent_tc_all_leave = #{rentTcAllLeave,jdbcType=INTEGER},
      rent_tc_ot_leave = #{rentTcOtLeave,jdbcType=INTEGER},
      rent_tc_nt_leave = #{rentTcNtLeave,jdbcType=INTEGER},
      rent_tc_all_buy = #{rentTcAllBuy,jdbcType=INTEGER},
      rent_tc_ot_buy = #{rentTcOtBuy,jdbcType=INTEGER},
      rent_tc_nt_buy = #{rentTcNtBuy,jdbcType=INTEGER},
      rent_tc_all_active = #{rentTcAllActive,jdbcType=INTEGER},
      rent_tc_ot_active = #{rentTcOtActive,jdbcType=INTEGER},
      rent_tc_nt_active = #{rentTcNtActive,jdbcType=INTEGER},
      rent_tc_all_time = #{rentTcAllTime,jdbcType=VARCHAR},
      rent_tc_ot_time = #{rentTcOtTime,jdbcType=VARCHAR},
      rent_tc_nt_time = #{rentTcNtTime,jdbcType=VARCHAR},
      data_at = #{dataAt,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>