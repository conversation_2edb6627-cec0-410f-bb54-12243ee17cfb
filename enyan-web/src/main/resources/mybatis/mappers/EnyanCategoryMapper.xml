<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanCategoryMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanCategory">
    <id column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="category_name_tc" jdbcType="VARCHAR" property="categoryNameTc" />
    <result column="category_name_en" jdbcType="VARCHAR" property="categoryNameEn" />
    <result column="is_index" jdbcType="TINYINT" property="isIndex" />
    <result column="is_hidden" jdbcType="INTEGER" property="isHidden" />
    <result column="category_order" jdbcType="INTEGER" property="categoryOrder" />
    <result column="book_recommended" jdbcType="VARCHAR" property="bookRecommended" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    category_id, category_name, category_name_tc, category_name_en, is_index, is_hidden, 
    category_order, book_recommended
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanCategoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_category
    where category_id = #{categoryId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_category
    where category_id = #{categoryId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanCategoryExample">
    delete from enyan_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanCategory">
    <selectKey keyProperty="categoryId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_category (category_name, category_name_tc, category_name_en, 
      is_index, is_hidden, category_order, 
      book_recommended)
    values (#{categoryName,jdbcType=VARCHAR}, #{categoryNameTc,jdbcType=VARCHAR}, #{categoryNameEn,jdbcType=VARCHAR}, 
      #{isIndex,jdbcType=TINYINT}, #{isHidden,jdbcType=INTEGER}, #{categoryOrder,jdbcType=INTEGER}, 
      #{bookRecommended,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanCategory">
    <selectKey keyProperty="categoryId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="categoryNameTc != null">
        category_name_tc,
      </if>
      <if test="categoryNameEn != null">
        category_name_en,
      </if>
      <if test="isIndex != null">
        is_index,
      </if>
      <if test="isHidden != null">
        is_hidden,
      </if>
      <if test="categoryOrder != null">
        category_order,
      </if>
      <if test="bookRecommended != null">
        book_recommended,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameTc != null">
        #{categoryNameTc,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameEn != null">
        #{categoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="isIndex != null">
        #{isIndex,jdbcType=TINYINT},
      </if>
      <if test="isHidden != null">
        #{isHidden,jdbcType=INTEGER},
      </if>
      <if test="categoryOrder != null">
        #{categoryOrder,jdbcType=INTEGER},
      </if>
      <if test="bookRecommended != null">
        #{bookRecommended,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanCategoryExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_category
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_category
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_category
    <set>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryNameTc != null">
        category_name_tc = #{record.categoryNameTc,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryNameEn != null">
        category_name_en = #{record.categoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.isIndex != null">
        is_index = #{record.isIndex,jdbcType=TINYINT},
      </if>
      <if test="record.isHidden != null">
        is_hidden = #{record.isHidden,jdbcType=INTEGER},
      </if>
      <if test="record.categoryOrder != null">
        category_order = #{record.categoryOrder,jdbcType=INTEGER},
      </if>
      <if test="record.bookRecommended != null">
        book_recommended = #{record.bookRecommended,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_category
    set category_id = #{record.categoryId,jdbcType=BIGINT},
      category_name = #{record.categoryName,jdbcType=VARCHAR},
      category_name_tc = #{record.categoryNameTc,jdbcType=VARCHAR},
      category_name_en = #{record.categoryNameEn,jdbcType=VARCHAR},
      is_index = #{record.isIndex,jdbcType=TINYINT},
      is_hidden = #{record.isHidden,jdbcType=INTEGER},
      category_order = #{record.categoryOrder,jdbcType=INTEGER},
      book_recommended = #{record.bookRecommended,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanCategory">
    update enyan_category
    <set>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameTc != null">
        category_name_tc = #{categoryNameTc,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameEn != null">
        category_name_en = #{categoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="isIndex != null">
        is_index = #{isIndex,jdbcType=TINYINT},
      </if>
      <if test="isHidden != null">
        is_hidden = #{isHidden,jdbcType=INTEGER},
      </if>
      <if test="categoryOrder != null">
        category_order = #{categoryOrder,jdbcType=INTEGER},
      </if>
      <if test="bookRecommended != null">
        book_recommended = #{bookRecommended,jdbcType=VARCHAR},
      </if>
    </set>
    where category_id = #{categoryId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanCategory">
    update enyan_category
    set category_name = #{categoryName,jdbcType=VARCHAR},
      category_name_tc = #{categoryNameTc,jdbcType=VARCHAR},
      category_name_en = #{categoryNameEn,jdbcType=VARCHAR},
      is_index = #{isIndex,jdbcType=TINYINT},
      is_hidden = #{isHidden,jdbcType=INTEGER},
      category_order = #{categoryOrder,jdbcType=INTEGER},
      book_recommended = #{bookRecommended,jdbcType=VARCHAR}
    where category_id = #{categoryId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>