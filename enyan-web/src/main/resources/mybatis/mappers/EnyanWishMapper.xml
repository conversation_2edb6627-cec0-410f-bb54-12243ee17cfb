<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanWishMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanWish">
    <id column="wish_id" jdbcType="VARCHAR" property="wishId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="wished_at" jdbcType="TIMESTAMP" property="wishedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    wish_id, user_email, book_id, wished_at
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanWishExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_wish
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_wish
    where wish_id = #{wishId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from enyan_wish
    where wish_id = #{wishId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanWishExample">
    delete from enyan_wish
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanWish">
    <selectKey keyProperty="wishId" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_wish (user_email, book_id, wished_at
      )
    values (#{userEmail,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, #{wishedAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanWish">
    <selectKey keyProperty="wishId" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_wish
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="wishedAt != null">
        wished_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="wishedAt != null">
        #{wishedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanWishExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_wish
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_wish
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_wish
    <set>
      <if test="record.wishId != null">
        wish_id = #{record.wishId,jdbcType=VARCHAR},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.wishedAt != null">
        wished_at = #{record.wishedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_wish
    set wish_id = #{record.wishId,jdbcType=VARCHAR},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      wished_at = #{record.wishedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanWish">
    update enyan_wish
    <set>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="wishedAt != null">
        wished_at = #{wishedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where wish_id = #{wishId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanWish">
    update enyan_wish
    set user_email = #{userEmail,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      wished_at = #{wishedAt,jdbcType=TIMESTAMP}
    where wish_id = #{wishId,jdbcType=VARCHAR}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>