<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.PodPodcastMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.PodPodcast">
    <id column="podcast_id" jdbcType="BIGINT" property="podcastId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="author_name" jdbcType="VARCHAR" property="authorName" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="cover_image_url" jdbcType="VARCHAR" property="coverImageUrl" />
    <result column="cover_image_url2" jdbcType="VARCHAR" property="coverImageUrl2" />
    <result column="display_order" jdbcType="INTEGER" property="displayOrder" />
    <result column="episode_count" jdbcType="INTEGER" property="episodeCount" />
    <result column="is_published" jdbcType="TINYINT" property="isPublished" />
    <result column="publication_date" jdbcType="TIMESTAMP" property="publicationDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  
  <sql id="Base_Column_List">
    podcast_id, title, author_name, description, cover_image_url, cover_image_url2, display_order, episode_count, 
    is_published, publication_date, is_deleted, created_at
  </sql>
  
  <select id="selectByExample" parameterType="com.aaron.spring.model.PodPodcastExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pod_podcast
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pod_podcast
    where podcast_id = #{podcastId,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pod_podcast
    where podcast_id = #{podcastId,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.PodPodcastExample">
    delete from pod_podcast
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  
  <insert id="insert" parameterType="com.aaron.spring.model.PodPodcast">
    insert into pod_podcast (podcast_id, title, author_name, 
      description, cover_image_url, cover_image_url2, display_order, 
      episode_count, is_published, publication_date, 
      is_deleted, created_at)
    values (#{podcastId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{authorName,jdbcType=VARCHAR}, 
      #{description,jdbcType=LONGVARCHAR}, #{coverImageUrl,jdbcType=VARCHAR}, #{coverImageUrl2,jdbcType=VARCHAR}, #{displayOrder,jdbcType=INTEGER}, 
      #{episodeCount,jdbcType=INTEGER}, #{isPublished,jdbcType=TINYINT}, #{publicationDate,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{createdAt,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.aaron.spring.model.PodPodcast">
    insert into pod_podcast
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="podcastId != null">
        podcast_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="authorName != null">
        author_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="coverImageUrl != null">
        cover_image_url,
      </if>
      <if test="coverImageUrl2 != null">
        cover_image_url2,
      </if>
      <if test="displayOrder != null">
        display_order,
      </if>
      <if test="episodeCount != null">
        episode_count,
      </if>
      <if test="isPublished != null">
        is_published,
      </if>
      <if test="publicationDate != null">
        publication_date,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="podcastId != null">
        #{podcastId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="authorName != null">
        #{authorName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="coverImageUrl != null">
        #{coverImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="coverImageUrl2 != null">
        #{coverImageUrl2,jdbcType=VARCHAR},
      </if>
      <if test="displayOrder != null">
        #{displayOrder,jdbcType=INTEGER},
      </if>
      <if test="episodeCount != null">
        #{episodeCount,jdbcType=INTEGER},
      </if>
      <if test="isPublished != null">
        #{isPublished,jdbcType=TINYINT},
      </if>
      <if test="publicationDate != null">
        #{publicationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.PodPodcast">
    update pod_podcast
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="authorName != null">
        author_name = #{authorName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="coverImageUrl != null">
        cover_image_url = #{coverImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="coverImageUrl2 != null">
        cover_image_url2 = #{coverImageUrl2,jdbcType=VARCHAR},
      </if>
      <if test="displayOrder != null">
        display_order = #{displayOrder,jdbcType=INTEGER},
      </if>
      <if test="episodeCount != null">
        episode_count = #{episodeCount,jdbcType=INTEGER},
      </if>
      <if test="isPublished != null">
        is_published = #{isPublished,jdbcType=TINYINT},
      </if>
      <if test="publicationDate != null">
        publication_date = #{publicationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where podcast_id = #{podcastId,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.PodPodcast">
    update pod_podcast
    set title = #{title,jdbcType=VARCHAR},
      author_name = #{authorName,jdbcType=VARCHAR},
      description = #{description,jdbcType=LONGVARCHAR},
      cover_image_url = #{coverImageUrl,jdbcType=VARCHAR},
      cover_image_url2 = #{coverImageUrl2,jdbcType=VARCHAR},
      display_order = #{displayOrder,jdbcType=INTEGER},
      episode_count = #{episodeCount,jdbcType=INTEGER},
      is_published = #{isPublished,jdbcType=TINYINT},
      publication_date = #{publicationDate,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP}
    where podcast_id = #{podcastId,jdbcType=BIGINT}
  </update>
  
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
  
  <update id="updateByExampleSelective" parameterType="map">
    update pod_podcast
    <set>
      <if test="record.podcastId != null">
        podcast_id = #{record.podcastId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.authorName != null">
        author_name = #{record.authorName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.coverImageUrl != null">
        cover_image_url = #{record.coverImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.coverImageUrl2 != null">
        cover_image_url2 = #{record.coverImageUrl2,jdbcType=VARCHAR},
      </if>
      <if test="record.displayOrder != null">
        display_order = #{record.displayOrder,jdbcType=INTEGER},
      </if>
      <if test="record.episodeCount != null">
        episode_count = #{record.episodeCount,jdbcType=INTEGER},
      </if>
      <if test="record.isPublished != null">
        is_published = #{record.isPublished,jdbcType=TINYINT},
      </if>
      <if test="record.publicationDate != null">
        publication_date = #{record.publicationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByExample" parameterType="map">
    update pod_podcast
    set podcast_id = #{record.podcastId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      author_name = #{record.authorName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=LONGVARCHAR},
      cover_image_url = #{record.coverImageUrl,jdbcType=VARCHAR},
      cover_image_url2 = #{record.coverImageUrl2,jdbcType=VARCHAR},
      display_order = #{record.displayOrder,jdbcType=INTEGER},
      episode_count = #{record.episodeCount,jdbcType=INTEGER},
      is_published = #{record.isPublished,jdbcType=TINYINT},
      publication_date = #{record.publicationDate,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <select id="countByExample" parameterType="com.aaron.spring.model.PodPodcastExample" resultType="java.lang.Long">
    select count(*) from pod_podcast
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
</mapper>
