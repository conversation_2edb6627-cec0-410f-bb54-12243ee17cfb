<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanBookCostMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBookCost">
    <id column="book_cost_id" jdbcType="BIGINT" property="bookCostId" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="book_cost" jdbcType="INTEGER" property="bookCost" />
    <result column="purchased_at" jdbcType="TIMESTAMP" property="purchasedAt" />
    <result column="is_counted" jdbcType="TINYINT" property="isCounted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    book_cost_id, book_id, book_title, publisher_id, book_cost, purchased_at, is_counted
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanBookCostExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_book_cost
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_book_cost
    where book_cost_id = #{bookCostId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_book_cost
    where book_cost_id = #{bookCostId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanBookCostExample">
    delete from enyan_book_cost
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanBookCost">
    <selectKey keyProperty="bookCostId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book_cost (book_id, book_title, publisher_id, 
      book_cost, purchased_at, is_counted
      )
    values (#{bookId,jdbcType=BIGINT}, #{bookTitle,jdbcType=VARCHAR}, #{publisherId,jdbcType=BIGINT}, 
      #{bookCost,jdbcType=INTEGER}, #{purchasedAt,jdbcType=TIMESTAMP}, #{isCounted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanBookCost">
    <selectKey keyProperty="bookCostId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book_cost
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bookId != null">
        book_id,
      </if>
      <if test="bookTitle != null">
        book_title,
      </if>
      <if test="publisherId != null">
        publisher_id,
      </if>
      <if test="bookCost != null">
        book_cost,
      </if>
      <if test="purchasedAt != null">
        purchased_at,
      </if>
      <if test="isCounted != null">
        is_counted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="bookTitle != null">
        #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="bookCost != null">
        #{bookCost,jdbcType=INTEGER},
      </if>
      <if test="purchasedAt != null">
        #{purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isCounted != null">
        #{isCounted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanBookCostExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_book_cost
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_book_cost
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_book_cost
    <set>
      <if test="record.bookCostId != null">
        book_cost_id = #{record.bookCostId,jdbcType=BIGINT},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.bookTitle != null">
        book_title = #{record.bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.bookCost != null">
        book_cost = #{record.bookCost,jdbcType=INTEGER},
      </if>
      <if test="record.purchasedAt != null">
        purchased_at = #{record.purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isCounted != null">
        is_counted = #{record.isCounted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_book_cost
    set book_cost_id = #{record.bookCostId,jdbcType=BIGINT},
      book_id = #{record.bookId,jdbcType=BIGINT},
      book_title = #{record.bookTitle,jdbcType=VARCHAR},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      book_cost = #{record.bookCost,jdbcType=INTEGER},
      purchased_at = #{record.purchasedAt,jdbcType=TIMESTAMP},
      is_counted = #{record.isCounted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanBookCost">
    update enyan_book_cost
    <set>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="bookTitle != null">
        book_title = #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="bookCost != null">
        book_cost = #{bookCost,jdbcType=INTEGER},
      </if>
      <if test="purchasedAt != null">
        purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isCounted != null">
        is_counted = #{isCounted,jdbcType=TINYINT},
      </if>
    </set>
    where book_cost_id = #{bookCostId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanBookCost">
    update enyan_book_cost
    set book_id = #{bookId,jdbcType=BIGINT},
      book_title = #{bookTitle,jdbcType=VARCHAR},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      book_cost = #{bookCost,jdbcType=INTEGER},
      purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
      is_counted = #{isCounted,jdbcType=TINYINT}
    where book_cost_id = #{bookCostId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>