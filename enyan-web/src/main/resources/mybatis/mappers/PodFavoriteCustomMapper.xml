<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aaron.spring.mapper.custom.PodFavoriteCustomMapper">
    
    <!-- 查询用户收藏的播客列表（包含播客信息） -->
    <select id="queryFavoritesWithPodcast" resultType="com.aaron.spring.model.PodFavoriteWithPodcast">
        SELECT 
            pf.favorite_id AS favoriteId,
            pf.user_id AS userId,
            pf.user_email AS userEmail,
            pf.podcast_id AS podcastId,
            pf.favorited_at AS favoritedAt,
            pp.title AS podcastTitle,
            pp.description AS podcastDescription,
            pp.cover_image_url AS podcastCoverImageUrl,
            pp.cover_image_url2 AS podcastCoverImageUrl2,
            pp.author_name AS authorName,
            pp.episode_count AS episodeCount,
            pp.display_order AS displayOrder,
            pp.publication_date AS publicationDate
        FROM 
            pod_favorite pf
        INNER JOIN 
            pod_podcast pp ON pf.podcast_id = pp.podcast_id
        WHERE 
            pf.user_email = #{params.userEmail}
            AND pp.is_published = 1  -- 只查询已发布的播客
            AND pp.is_deleted = 0     -- 只查询未删除的播客
        ORDER BY 
            pf.favorited_at DESC
        <if test="page != null ">
            LIMIT #{page.recordIndex}, #{page.pageSize}
        </if>
    </select>
    
    <!-- 统计用户收藏的播客数量 -->
    <select id="countFavoritesWithPodcast" resultType="long">
        SELECT 
            COUNT(*)
        FROM 
            pod_favorite pf
        INNER JOIN 
            pod_podcast pp ON pf.podcast_id = pp.podcast_id
        WHERE 
            pf.user_email = #{params.userEmail}
            AND pp.is_published = 1  -- 只统计已发布的播客
            AND pp.is_deleted = 0     -- 只统计未删除的播客
    </select>
    
</mapper>
