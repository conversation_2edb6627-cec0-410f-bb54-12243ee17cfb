<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.AuthUserMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.AuthUser">
    <id column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_password" jdbcType="VARCHAR" property="userPassword" />
    <result column="salt" jdbcType="VARCHAR" property="salt" />
    <result column="role_type" jdbcType="TINYINT" property="roleType" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="is_staff" jdbcType="TINYINT" property="isStaff" />
    <result column="is_active" jdbcType="TINYINT" property="isActive" />
    <result column="date_joined" jdbcType="TIMESTAMP" property="dateJoined" />
    <result column="last_login" jdbcType="TIMESTAMP" property="lastLogin" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    user_id, user_password, salt, role_type, user_name, nick_name, email, is_staff, is_active, 
    date_joined, last_login
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.AuthUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auth_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auth_user
    where user_id = #{userId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_user
    where user_id = #{userId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.AuthUserExample">
    delete from auth_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.AuthUser">
    <selectKey keyProperty="userId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into auth_user (user_password, salt, role_type, 
      user_name, nick_name, email, 
      is_staff, is_active, date_joined, 
      last_login)
    values (#{userPassword,jdbcType=VARCHAR}, #{salt,jdbcType=VARCHAR}, #{roleType,jdbcType=TINYINT}, 
      #{userName,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{isStaff,jdbcType=TINYINT}, #{isActive,jdbcType=TINYINT}, #{dateJoined,jdbcType=TIMESTAMP}, 
      #{lastLogin,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.AuthUser">
    <selectKey keyProperty="userId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into auth_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userPassword != null">
        user_password,
      </if>
      <if test="salt != null">
        salt,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="nickName != null">
        nick_name,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="isStaff != null">
        is_staff,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="dateJoined != null">
        date_joined,
      </if>
      <if test="lastLogin != null">
        last_login,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userPassword != null">
        #{userPassword,jdbcType=VARCHAR},
      </if>
      <if test="salt != null">
        #{salt,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=TINYINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="isStaff != null">
        #{isStaff,jdbcType=TINYINT},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=TINYINT},
      </if>
      <if test="dateJoined != null">
        #{dateJoined,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLogin != null">
        #{lastLogin,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.AuthUserExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from auth_user
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          auth_user
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update auth_user
    <set>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userPassword != null">
        user_password = #{record.userPassword,jdbcType=VARCHAR},
      </if>
      <if test="record.salt != null">
        salt = #{record.salt,jdbcType=VARCHAR},
      </if>
      <if test="record.roleType != null">
        role_type = #{record.roleType,jdbcType=TINYINT},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.nickName != null">
        nick_name = #{record.nickName,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.isStaff != null">
        is_staff = #{record.isStaff,jdbcType=TINYINT},
      </if>
      <if test="record.isActive != null">
        is_active = #{record.isActive,jdbcType=TINYINT},
      </if>
      <if test="record.dateJoined != null">
        date_joined = #{record.dateJoined,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastLogin != null">
        last_login = #{record.lastLogin,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update auth_user
    set user_id = #{record.userId,jdbcType=BIGINT},
      user_password = #{record.userPassword,jdbcType=VARCHAR},
      salt = #{record.salt,jdbcType=VARCHAR},
      role_type = #{record.roleType,jdbcType=TINYINT},
      user_name = #{record.userName,jdbcType=VARCHAR},
      nick_name = #{record.nickName,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      is_staff = #{record.isStaff,jdbcType=TINYINT},
      is_active = #{record.isActive,jdbcType=TINYINT},
      date_joined = #{record.dateJoined,jdbcType=TIMESTAMP},
      last_login = #{record.lastLogin,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.AuthUser">
    update auth_user
    <set>
      <if test="userPassword != null">
        user_password = #{userPassword,jdbcType=VARCHAR},
      </if>
      <if test="salt != null">
        salt = #{salt,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=TINYINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        nick_name = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="isStaff != null">
        is_staff = #{isStaff,jdbcType=TINYINT},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=TINYINT},
      </if>
      <if test="dateJoined != null">
        date_joined = #{dateJoined,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLogin != null">
        last_login = #{lastLogin,jdbcType=TIMESTAMP},
      </if>
    </set>
    where user_id = #{userId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.AuthUser">
    update auth_user
    set user_password = #{userPassword,jdbcType=VARCHAR},
      salt = #{salt,jdbcType=VARCHAR},
      role_type = #{roleType,jdbcType=TINYINT},
      user_name = #{userName,jdbcType=VARCHAR},
      nick_name = #{nickName,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      is_staff = #{isStaff,jdbcType=TINYINT},
      is_active = #{isActive,jdbcType=TINYINT},
      date_joined = #{dateJoined,jdbcType=TIMESTAMP},
      last_login = #{lastLogin,jdbcType=TIMESTAMP}
    where user_id = #{userId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>