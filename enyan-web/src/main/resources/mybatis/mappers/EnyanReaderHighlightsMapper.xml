<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanReaderHighlightsMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanReaderHighlights">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="highlight_id" jdbcType="VARCHAR" property="highlightId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="publication_id" jdbcType="VARCHAR" property="publicationId" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="resource_index" jdbcType="INTEGER" property="resourceIndex" />
    <result column="resource_href" jdbcType="VARCHAR" property="resourceHref" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="resource_title" jdbcType="VARCHAR" property="resourceTitle" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="locator_text" jdbcType="VARCHAR" property="locatorText" />
    <result column="color" jdbcType="INTEGER" property="color" />
    <result column="annotation" jdbcType="VARCHAR" property="annotation" />
    <result column="creation_date" jdbcType="BIGINT" property="creationDate" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, highlight_id, user_id, user_email, publication_id, book_id, resource_index, resource_href, 
    resource_type, resource_title, location, locator_text, color, annotation, creation_date, 
    is_deleted, update_time
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanReaderHighlightsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_reader_highlights
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_reader_highlights
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from enyan_reader_highlights
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanReaderHighlightsExample">
    delete from enyan_reader_highlights
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanReaderHighlights">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_reader_highlights (highlight_id, user_id, user_email, 
      publication_id, book_id, resource_index, 
      resource_href, resource_type, resource_title, 
      location, locator_text, color, 
      annotation, creation_date, is_deleted, 
      update_time)
    values (#{highlightId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, 
      #{publicationId,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, #{resourceIndex,jdbcType=INTEGER}, 
      #{resourceHref,jdbcType=VARCHAR}, #{resourceType,jdbcType=VARCHAR}, #{resourceTitle,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{locatorText,jdbcType=VARCHAR}, #{color,jdbcType=INTEGER}, 
      #{annotation,jdbcType=VARCHAR}, #{creationDate,jdbcType=BIGINT}, #{isDeleted,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanReaderHighlights">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_reader_highlights
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="highlightId != null">
        highlight_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="publicationId != null">
        publication_id,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="resourceIndex != null">
        resource_index,
      </if>
      <if test="resourceHref != null">
        resource_href,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="resourceTitle != null">
        resource_title,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="locatorText != null">
        locator_text,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="annotation != null">
        annotation,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="highlightId != null">
        #{highlightId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="publicationId != null">
        #{publicationId,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="resourceIndex != null">
        #{resourceIndex,jdbcType=INTEGER},
      </if>
      <if test="resourceHref != null">
        #{resourceHref,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceTitle != null">
        #{resourceTitle,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="locatorText != null">
        #{locatorText,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=INTEGER},
      </if>
      <if test="annotation != null">
        #{annotation,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanReaderHighlightsExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_reader_highlights
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_reader_highlights
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_reader_highlights
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.highlightId != null">
        highlight_id = #{record.highlightId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.publicationId != null">
        publication_id = #{record.publicationId,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceIndex != null">
        resource_index = #{record.resourceIndex,jdbcType=INTEGER},
      </if>
      <if test="record.resourceHref != null">
        resource_href = #{record.resourceHref,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceTitle != null">
        resource_title = #{record.resourceTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.locatorText != null">
        locator_text = #{record.locatorText,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null">
        color = #{record.color,jdbcType=INTEGER},
      </if>
      <if test="record.annotation != null">
        annotation = #{record.annotation,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null">
        creation_date = #{record.creationDate,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_reader_highlights
    set id = #{record.id,jdbcType=VARCHAR},
      highlight_id = #{record.highlightId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      publication_id = #{record.publicationId,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      resource_index = #{record.resourceIndex,jdbcType=INTEGER},
      resource_href = #{record.resourceHref,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      resource_title = #{record.resourceTitle,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      locator_text = #{record.locatorText,jdbcType=VARCHAR},
      color = #{record.color,jdbcType=INTEGER},
      annotation = #{record.annotation,jdbcType=VARCHAR},
      creation_date = #{record.creationDate,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanReaderHighlights">
    update enyan_reader_highlights
    <set>
      <if test="highlightId != null">
        highlight_id = #{highlightId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="publicationId != null">
        publication_id = #{publicationId,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="resourceIndex != null">
        resource_index = #{resourceIndex,jdbcType=INTEGER},
      </if>
      <if test="resourceHref != null">
        resource_href = #{resourceHref,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceTitle != null">
        resource_title = #{resourceTitle,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="locatorText != null">
        locator_text = #{locatorText,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=INTEGER},
      </if>
      <if test="annotation != null">
        annotation = #{annotation,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanReaderHighlights">
    update enyan_reader_highlights
    set highlight_id = #{highlightId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      user_email = #{userEmail,jdbcType=VARCHAR},
      publication_id = #{publicationId,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      resource_index = #{resourceIndex,jdbcType=INTEGER},
      resource_href = #{resourceHref,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_title = #{resourceTitle,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      locator_text = #{locatorText,jdbcType=VARCHAR},
      color = #{color,jdbcType=INTEGER},
      annotation = #{annotation,jdbcType=VARCHAR},
      creation_date = #{creationDate,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>