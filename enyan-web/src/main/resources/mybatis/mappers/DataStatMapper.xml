<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.DataStatMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.DataStat">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="sales_volume" jdbcType="INTEGER" property="salesVolume" />
    <result column="income_total" jdbcType="DECIMAL" property="incomeTotal" />
    <result column="order_count" jdbcType="INTEGER" property="orderCount" />
    <result column="order_fee_count" jdbcType="INTEGER" property="orderFeeCount" />
    <result column="order_free_count" jdbcType="INTEGER" property="orderFreeCount" />
    <result column="user_buy_count" jdbcType="INTEGER" property="userBuyCount" />
    <result column="user_new_count" jdbcType="INTEGER" property="userNewCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, sales_volume, income_total, order_count, order_fee_count, order_free_count, 
    user_buy_count, user_new_count, create_time
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.DataStatExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from data_stat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_stat
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_stat
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.DataStatExample">
    delete from data_stat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.DataStat">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into data_stat (sales_volume, income_total, order_count, 
      order_fee_count, order_free_count, user_buy_count, 
      user_new_count, create_time)
    values (#{salesVolume,jdbcType=INTEGER}, #{incomeTotal,jdbcType=DECIMAL}, #{orderCount,jdbcType=INTEGER}, 
      #{orderFeeCount,jdbcType=INTEGER}, #{orderFreeCount,jdbcType=INTEGER}, #{userBuyCount,jdbcType=INTEGER}, 
      #{userNewCount,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.DataStat">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into data_stat
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="salesVolume != null">
        sales_volume,
      </if>
      <if test="incomeTotal != null">
        income_total,
      </if>
      <if test="orderCount != null">
        order_count,
      </if>
      <if test="orderFeeCount != null">
        order_fee_count,
      </if>
      <if test="orderFreeCount != null">
        order_free_count,
      </if>
      <if test="userBuyCount != null">
        user_buy_count,
      </if>
      <if test="userNewCount != null">
        user_new_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="salesVolume != null">
        #{salesVolume,jdbcType=INTEGER},
      </if>
      <if test="incomeTotal != null">
        #{incomeTotal,jdbcType=DECIMAL},
      </if>
      <if test="orderCount != null">
        #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="orderFeeCount != null">
        #{orderFeeCount,jdbcType=INTEGER},
      </if>
      <if test="orderFreeCount != null">
        #{orderFreeCount,jdbcType=INTEGER},
      </if>
      <if test="userBuyCount != null">
        #{userBuyCount,jdbcType=INTEGER},
      </if>
      <if test="userNewCount != null">
        #{userNewCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.DataStatExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from data_stat
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          data_stat
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update data_stat
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.salesVolume != null">
        sales_volume = #{record.salesVolume,jdbcType=INTEGER},
      </if>
      <if test="record.incomeTotal != null">
        income_total = #{record.incomeTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.orderCount != null">
        order_count = #{record.orderCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderFeeCount != null">
        order_fee_count = #{record.orderFeeCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderFreeCount != null">
        order_free_count = #{record.orderFreeCount,jdbcType=INTEGER},
      </if>
      <if test="record.userBuyCount != null">
        user_buy_count = #{record.userBuyCount,jdbcType=INTEGER},
      </if>
      <if test="record.userNewCount != null">
        user_new_count = #{record.userNewCount,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update data_stat
    set data_id = #{record.dataId,jdbcType=BIGINT},
      sales_volume = #{record.salesVolume,jdbcType=INTEGER},
      income_total = #{record.incomeTotal,jdbcType=DECIMAL},
      order_count = #{record.orderCount,jdbcType=INTEGER},
      order_fee_count = #{record.orderFeeCount,jdbcType=INTEGER},
      order_free_count = #{record.orderFreeCount,jdbcType=INTEGER},
      user_buy_count = #{record.userBuyCount,jdbcType=INTEGER},
      user_new_count = #{record.userNewCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.DataStat">
    update data_stat
    <set>
      <if test="salesVolume != null">
        sales_volume = #{salesVolume,jdbcType=INTEGER},
      </if>
      <if test="incomeTotal != null">
        income_total = #{incomeTotal,jdbcType=DECIMAL},
      </if>
      <if test="orderCount != null">
        order_count = #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="orderFeeCount != null">
        order_fee_count = #{orderFeeCount,jdbcType=INTEGER},
      </if>
      <if test="orderFreeCount != null">
        order_free_count = #{orderFreeCount,jdbcType=INTEGER},
      </if>
      <if test="userBuyCount != null">
        user_buy_count = #{userBuyCount,jdbcType=INTEGER},
      </if>
      <if test="userNewCount != null">
        user_new_count = #{userNewCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.DataStat">
    update data_stat
    set sales_volume = #{salesVolume,jdbcType=INTEGER},
      income_total = #{incomeTotal,jdbcType=DECIMAL},
      order_count = #{orderCount,jdbcType=INTEGER},
      order_fee_count = #{orderFeeCount,jdbcType=INTEGER},
      order_free_count = #{orderFreeCount,jdbcType=INTEGER},
      user_buy_count = #{userBuyCount,jdbcType=INTEGER},
      user_new_count = #{userNewCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>