<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanDailyWordsMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanDailyWords">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="data_content" jdbcType="VARCHAR" property="dataContent" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
    <result column="book_author" jdbcType="VARCHAR" property="bookAuthor" />
    <result column="data_img_url" jdbcType="VARCHAR" property="dataImgUrl" />
    <result column="like_count" jdbcType="INTEGER" property="likeCount" />
    <result column="data_at" jdbcType="INTEGER" property="dataAt" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, data_content, book_id, book_title, book_author, data_img_url, like_count, 
    data_at, create_at
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanDailyWordsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_daily_words
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_daily_words
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_daily_words
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanDailyWordsExample">
    delete from enyan_daily_words
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanDailyWords">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_daily_words (data_content, book_id, book_title, 
      book_author, data_img_url, like_count, 
      data_at, create_at)
    values (#{dataContent,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, #{bookTitle,jdbcType=VARCHAR}, 
      #{bookAuthor,jdbcType=VARCHAR}, #{dataImgUrl,jdbcType=VARCHAR}, #{likeCount,jdbcType=INTEGER}, 
      #{dataAt,jdbcType=INTEGER}, #{createAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanDailyWords">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_daily_words
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataContent != null">
        data_content,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="bookTitle != null">
        book_title,
      </if>
      <if test="bookAuthor != null">
        book_author,
      </if>
      <if test="dataImgUrl != null">
        data_img_url,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
      <if test="dataAt != null">
        data_at,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataContent != null">
        #{dataContent,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="bookTitle != null">
        #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="bookAuthor != null">
        #{bookAuthor,jdbcType=VARCHAR},
      </if>
      <if test="dataImgUrl != null">
        #{dataImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=INTEGER},
      </if>
      <if test="dataAt != null">
        #{dataAt,jdbcType=INTEGER},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanDailyWordsExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_daily_words
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_daily_words
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_daily_words
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.dataContent != null">
        data_content = #{record.dataContent,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.bookTitle != null">
        book_title = #{record.bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.bookAuthor != null">
        book_author = #{record.bookAuthor,jdbcType=VARCHAR},
      </if>
      <if test="record.dataImgUrl != null">
        data_img_url = #{record.dataImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.likeCount != null">
        like_count = #{record.likeCount,jdbcType=INTEGER},
      </if>
      <if test="record.dataAt != null">
        data_at = #{record.dataAt,jdbcType=INTEGER},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_daily_words
    set data_id = #{record.dataId,jdbcType=BIGINT},
      data_content = #{record.dataContent,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      book_title = #{record.bookTitle,jdbcType=VARCHAR},
      book_author = #{record.bookAuthor,jdbcType=VARCHAR},
      data_img_url = #{record.dataImgUrl,jdbcType=VARCHAR},
      like_count = #{record.likeCount,jdbcType=INTEGER},
      data_at = #{record.dataAt,jdbcType=INTEGER},
      create_at = #{record.createAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanDailyWords">
    update enyan_daily_words
    <set>
      <if test="dataContent != null">
        data_content = #{dataContent,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="bookTitle != null">
        book_title = #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="bookAuthor != null">
        book_author = #{bookAuthor,jdbcType=VARCHAR},
      </if>
      <if test="dataImgUrl != null">
        data_img_url = #{dataImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="likeCount != null">
        like_count = #{likeCount,jdbcType=INTEGER},
      </if>
      <if test="dataAt != null">
        data_at = #{dataAt,jdbcType=INTEGER},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanDailyWords">
    update enyan_daily_words
    set data_content = #{dataContent,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      book_title = #{bookTitle,jdbcType=VARCHAR},
      book_author = #{bookAuthor,jdbcType=VARCHAR},
      data_img_url = #{dataImgUrl,jdbcType=VARCHAR},
      like_count = #{likeCount,jdbcType=INTEGER},
      data_at = #{dataAt,jdbcType=INTEGER},
      create_at = #{createAt,jdbcType=TIMESTAMP}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>