<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanBookMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBook">
    <id column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
    <result column="book_pinyin" jdbcType="VARCHAR" property="bookPinyin" />
    <result column="author" jdbcType="VARCHAR" property="author" />
    <result column="author_bio" jdbcType="VARCHAR" property="authorBio" />
    <result column="translator" jdbcType="VARCHAR" property="translator" />
    <result column="word_count" jdbcType="VARCHAR" property="wordCount" />
    <result column="word_count_show" jdbcType="VARCHAR" property="wordCountShow" />
    <result column="product_web" jdbcType="VARCHAR" property="productWeb" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="sales_volume" jdbcType="BIGINT" property="salesVolume" />
    <result column="price_cny" jdbcType="DECIMAL" property="priceCny" />
    <result column="price_usd" jdbcType="DECIMAL" property="priceUsd" />
    <result column="price_hkd" jdbcType="DECIMAL" property="priceHkd" />
    <result column="book_cost" jdbcType="INTEGER" property="bookCost" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="publisher_name" jdbcType="VARCHAR" property="publisherName" />
    <result column="published_at" jdbcType="VARCHAR" property="publishedAt" />
    <result column="book_catalogue" jdbcType="VARCHAR" property="bookCatalogue" />
    <result column="book_abstract" jdbcType="VARCHAR" property="bookAbstract" />
    <result column="book_cover" jdbcType="VARCHAR" property="bookCover" />
    <result column="book_cover_app" jdbcType="VARCHAR" property="bookCoverApp" />
    <result column="book_sample" jdbcType="VARCHAR" property="bookSample" />
    <result column="book_full" jdbcType="VARCHAR" property="bookFull" />
    <result column="book_hash" jdbcType="VARCHAR" property="bookHash" />
    <result column="book_isbn" jdbcType="VARCHAR" property="bookIsbn" />
    <result column="ebook_isbn" jdbcType="VARCHAR" property="ebookIsbn" />
    <result column="book_esin" jdbcType="VARCHAR" property="bookEsin" />
    <result column="has_book_pagination" jdbcType="TINYINT" property="hasBookPagination" />
    <result column="book_pub_code" jdbcType="VARCHAR" property="bookPubCode" />
    <result column="book_keywords" jdbcType="VARCHAR" property="bookKeywords" />
    <result column="book_type" jdbcType="INTEGER" property="bookType" />
    <result column="special_offer" jdbcType="INTEGER" property="specialOffer" />
    <result column="area_discount" jdbcType="INTEGER" property="areaDiscount" />
    <result column="can_tts" jdbcType="INTEGER" property="canTts" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="book_drm_ref" jdbcType="VARCHAR" property="bookDrmRef" />
    <result column="discount_single_id" jdbcType="BIGINT" property="discountSingleId" />
    <result column="discount_single_type" jdbcType="TINYINT" property="discountSingleType" />
    <result column="discount_single_value" jdbcType="INTEGER" property="discountSingleValue" />
    <result column="discount_single_start_time" jdbcType="DATE" property="discountSingleStartTime" />
    <result column="discount_single_end_time" jdbcType="DATE" property="discountSingleEndTime" />
    <result column="discount_single_is_valid" jdbcType="TINYINT" property="discountSingleIsValid" />
    <result column="discount_single_description" jdbcType="VARCHAR" property="discountSingleDescription" />
    <result column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="discount_description" jdbcType="VARCHAR" property="discountDescription" />
    <result column="discount_is_valid" jdbcType="TINYINT" property="discountIsValid" />
    <result column="set_name" jdbcType="VARCHAR" property="setName" />
    <result column="set_id" jdbcType="BIGINT" property="setId" />
    <result column="is_in_cn" jdbcType="TINYINT" property="isInCn" />
    <result column="bible_version" jdbcType="INTEGER" property="bibleVersion" />
    <result column="can_member" jdbcType="INTEGER" property="canMember" />
    <result column="show_publisher" jdbcType="TINYINT" property="showPublisher" />
    <result column="recommended_order" jdbcType="INTEGER" property="recommendedOrder" />
    <result column="recommended_caption" jdbcType="VARCHAR" property="recommendedCaption" />
    <result column="print_permission" jdbcType="TINYINT" property="printPermission" />
    <result column="copy_permission" jdbcType="TINYINT" property="copyPermission" />
    <result column="ebook_format" jdbcType="TINYINT" property="ebookFormat" />
    <result column="shelf_status" jdbcType="TINYINT" property="shelfStatus" />
    <result column="vendor_percent" jdbcType="INTEGER" property="vendorPercent" />
    <result column="sales_model" jdbcType="INTEGER" property="salesModel" />
    <result column="star" jdbcType="VARCHAR" property="star" />
    <result column="star_count" jdbcType="INTEGER" property="starCount" />
    <result column="opensale_at" jdbcType="TIMESTAMP" property="opensaleAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanBook">
    <result column="book_description" jdbcType="LONGVARCHAR" property="bookDescription" />
    <result column="book_web" jdbcType="LONGVARCHAR" property="bookWeb" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    book_id, book_title, book_pinyin, author, author_bio, translator, word_count, word_count_show, 
    product_web, price, sales_volume, price_cny, price_usd, price_hkd, book_cost, category_id, 
    category_name, publisher_id, publisher_name, published_at, book_catalogue, book_abstract, 
    book_cover, book_cover_app, book_sample, book_full, book_hash, book_isbn, ebook_isbn, 
    book_esin, has_book_pagination, book_pub_code, book_keywords, book_type, special_offer, 
    area_discount, can_tts, size, book_drm_ref, discount_single_id, discount_single_type, 
    discount_single_value, discount_single_start_time, discount_single_end_time, discount_single_is_valid, 
    discount_single_description, discount_id, discount_description, discount_is_valid, 
    set_name, set_id, is_in_cn, bible_version, can_member, show_publisher, recommended_order, 
    recommended_caption, print_permission, copy_permission, ebook_format, shelf_status, 
    vendor_percent, sales_model, star, star_count, opensale_at
  </sql>
  <sql id="Blob_Column_List">
    book_description, book_web
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.EnyanBookExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_book
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanBookExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_book
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_book
    where book_id = #{bookId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_book
    where book_id = #{bookId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanBookExample">
    delete from enyan_book
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanBook">
    <selectKey keyProperty="bookId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book (book_title, book_pinyin, author, 
      author_bio, translator, word_count, 
      word_count_show, product_web, price, 
      sales_volume, price_cny, price_usd, 
      price_hkd, book_cost, category_id, 
      category_name, publisher_id, publisher_name, 
      published_at, book_catalogue, book_abstract, 
      book_cover, book_cover_app, book_sample, 
      book_full, book_hash, book_isbn, 
      ebook_isbn, book_esin, has_book_pagination, 
      book_pub_code, book_keywords, book_type, 
      special_offer, area_discount, can_tts, 
      size, book_drm_ref, discount_single_id, 
      discount_single_type, discount_single_value, 
      discount_single_start_time, discount_single_end_time, 
      discount_single_is_valid, discount_single_description, 
      discount_id, discount_description, discount_is_valid, 
      set_name, set_id, is_in_cn, 
      bible_version, can_member, show_publisher, 
      recommended_order, recommended_caption, print_permission, 
      copy_permission, ebook_format, shelf_status, 
      vendor_percent, sales_model, star, 
      star_count, opensale_at, book_description, 
      book_web)
    values (#{bookTitle,jdbcType=VARCHAR}, #{bookPinyin,jdbcType=VARCHAR}, #{author,jdbcType=VARCHAR}, 
      #{authorBio,jdbcType=VARCHAR}, #{translator,jdbcType=VARCHAR}, #{wordCount,jdbcType=VARCHAR}, 
      #{wordCountShow,jdbcType=VARCHAR}, #{productWeb,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, 
      #{salesVolume,jdbcType=BIGINT}, #{priceCny,jdbcType=DECIMAL}, #{priceUsd,jdbcType=DECIMAL}, 
      #{priceHkd,jdbcType=DECIMAL}, #{bookCost,jdbcType=INTEGER}, #{categoryId,jdbcType=BIGINT}, 
      #{categoryName,jdbcType=VARCHAR}, #{publisherId,jdbcType=BIGINT}, #{publisherName,jdbcType=VARCHAR}, 
      #{publishedAt,jdbcType=VARCHAR}, #{bookCatalogue,jdbcType=VARCHAR}, #{bookAbstract,jdbcType=VARCHAR}, 
      #{bookCover,jdbcType=VARCHAR}, #{bookCoverApp,jdbcType=VARCHAR}, #{bookSample,jdbcType=VARCHAR}, 
      #{bookFull,jdbcType=VARCHAR}, #{bookHash,jdbcType=VARCHAR}, #{bookIsbn,jdbcType=VARCHAR}, 
      #{ebookIsbn,jdbcType=VARCHAR}, #{bookEsin,jdbcType=VARCHAR}, #{hasBookPagination,jdbcType=TINYINT}, 
      #{bookPubCode,jdbcType=VARCHAR}, #{bookKeywords,jdbcType=VARCHAR}, #{bookType,jdbcType=INTEGER}, 
      #{specialOffer,jdbcType=INTEGER}, #{areaDiscount,jdbcType=INTEGER}, #{canTts,jdbcType=INTEGER}, 
      #{size,jdbcType=VARCHAR}, #{bookDrmRef,jdbcType=VARCHAR}, #{discountSingleId,jdbcType=BIGINT}, 
      #{discountSingleType,jdbcType=TINYINT}, #{discountSingleValue,jdbcType=INTEGER}, 
      #{discountSingleStartTime,jdbcType=DATE}, #{discountSingleEndTime,jdbcType=DATE}, 
      #{discountSingleIsValid,jdbcType=TINYINT}, #{discountSingleDescription,jdbcType=VARCHAR}, 
      #{discountId,jdbcType=BIGINT}, #{discountDescription,jdbcType=VARCHAR}, #{discountIsValid,jdbcType=TINYINT}, 
      #{setName,jdbcType=VARCHAR}, #{setId,jdbcType=BIGINT}, #{isInCn,jdbcType=TINYINT}, 
      #{bibleVersion,jdbcType=INTEGER}, #{canMember,jdbcType=INTEGER}, #{showPublisher,jdbcType=TINYINT}, 
      #{recommendedOrder,jdbcType=INTEGER}, #{recommendedCaption,jdbcType=VARCHAR}, #{printPermission,jdbcType=TINYINT}, 
      #{copyPermission,jdbcType=TINYINT}, #{ebookFormat,jdbcType=TINYINT}, #{shelfStatus,jdbcType=TINYINT}, 
      #{vendorPercent,jdbcType=INTEGER}, #{salesModel,jdbcType=INTEGER}, #{star,jdbcType=VARCHAR}, 
      #{starCount,jdbcType=INTEGER}, #{opensaleAt,jdbcType=TIMESTAMP}, #{bookDescription,jdbcType=LONGVARCHAR}, 
      #{bookWeb,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanBook">
    <selectKey keyProperty="bookId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bookTitle != null">
        book_title,
      </if>
      <if test="bookPinyin != null">
        book_pinyin,
      </if>
      <if test="author != null">
        author,
      </if>
      <if test="authorBio != null">
        author_bio,
      </if>
      <if test="translator != null">
        translator,
      </if>
      <if test="wordCount != null">
        word_count,
      </if>
      <if test="wordCountShow != null">
        word_count_show,
      </if>
      <if test="productWeb != null">
        product_web,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="salesVolume != null">
        sales_volume,
      </if>
      <if test="priceCny != null">
        price_cny,
      </if>
      <if test="priceUsd != null">
        price_usd,
      </if>
      <if test="priceHkd != null">
        price_hkd,
      </if>
      <if test="bookCost != null">
        book_cost,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="publisherId != null">
        publisher_id,
      </if>
      <if test="publisherName != null">
        publisher_name,
      </if>
      <if test="publishedAt != null">
        published_at,
      </if>
      <if test="bookCatalogue != null">
        book_catalogue,
      </if>
      <if test="bookAbstract != null">
        book_abstract,
      </if>
      <if test="bookCover != null">
        book_cover,
      </if>
      <if test="bookCoverApp != null">
        book_cover_app,
      </if>
      <if test="bookSample != null">
        book_sample,
      </if>
      <if test="bookFull != null">
        book_full,
      </if>
      <if test="bookHash != null">
        book_hash,
      </if>
      <if test="bookIsbn != null">
        book_isbn,
      </if>
      <if test="ebookIsbn != null">
        ebook_isbn,
      </if>
      <if test="bookEsin != null">
        book_esin,
      </if>
      <if test="hasBookPagination != null">
        has_book_pagination,
      </if>
      <if test="bookPubCode != null">
        book_pub_code,
      </if>
      <if test="bookKeywords != null">
        book_keywords,
      </if>
      <if test="bookType != null">
        book_type,
      </if>
      <if test="specialOffer != null">
        special_offer,
      </if>
      <if test="areaDiscount != null">
        area_discount,
      </if>
      <if test="canTts != null">
        can_tts,
      </if>
      <if test="size != null">
        size,
      </if>
      <if test="bookDrmRef != null">
        book_drm_ref,
      </if>
      <if test="discountSingleId != null">
        discount_single_id,
      </if>
      <if test="discountSingleType != null">
        discount_single_type,
      </if>
      <if test="discountSingleValue != null">
        discount_single_value,
      </if>
      <if test="discountSingleStartTime != null">
        discount_single_start_time,
      </if>
      <if test="discountSingleEndTime != null">
        discount_single_end_time,
      </if>
      <if test="discountSingleIsValid != null">
        discount_single_is_valid,
      </if>
      <if test="discountSingleDescription != null">
        discount_single_description,
      </if>
      <if test="discountId != null">
        discount_id,
      </if>
      <if test="discountDescription != null">
        discount_description,
      </if>
      <if test="discountIsValid != null">
        discount_is_valid,
      </if>
      <if test="setName != null">
        set_name,
      </if>
      <if test="setId != null">
        set_id,
      </if>
      <if test="isInCn != null">
        is_in_cn,
      </if>
      <if test="bibleVersion != null">
        bible_version,
      </if>
      <if test="canMember != null">
        can_member,
      </if>
      <if test="showPublisher != null">
        show_publisher,
      </if>
      <if test="recommendedOrder != null">
        recommended_order,
      </if>
      <if test="recommendedCaption != null">
        recommended_caption,
      </if>
      <if test="printPermission != null">
        print_permission,
      </if>
      <if test="copyPermission != null">
        copy_permission,
      </if>
      <if test="ebookFormat != null">
        ebook_format,
      </if>
      <if test="shelfStatus != null">
        shelf_status,
      </if>
      <if test="vendorPercent != null">
        vendor_percent,
      </if>
      <if test="salesModel != null">
        sales_model,
      </if>
      <if test="star != null">
        star,
      </if>
      <if test="starCount != null">
        star_count,
      </if>
      <if test="opensaleAt != null">
        opensale_at,
      </if>
      <if test="bookDescription != null">
        book_description,
      </if>
      <if test="bookWeb != null">
        book_web,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bookTitle != null">
        #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="bookPinyin != null">
        #{bookPinyin,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        #{author,jdbcType=VARCHAR},
      </if>
      <if test="authorBio != null">
        #{authorBio,jdbcType=VARCHAR},
      </if>
      <if test="translator != null">
        #{translator,jdbcType=VARCHAR},
      </if>
      <if test="wordCount != null">
        #{wordCount,jdbcType=VARCHAR},
      </if>
      <if test="wordCountShow != null">
        #{wordCountShow,jdbcType=VARCHAR},
      </if>
      <if test="productWeb != null">
        #{productWeb,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="salesVolume != null">
        #{salesVolume,jdbcType=BIGINT},
      </if>
      <if test="priceCny != null">
        #{priceCny,jdbcType=DECIMAL},
      </if>
      <if test="priceUsd != null">
        #{priceUsd,jdbcType=DECIMAL},
      </if>
      <if test="priceHkd != null">
        #{priceHkd,jdbcType=DECIMAL},
      </if>
      <if test="bookCost != null">
        #{bookCost,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="publisherName != null">
        #{publisherName,jdbcType=VARCHAR},
      </if>
      <if test="publishedAt != null">
        #{publishedAt,jdbcType=VARCHAR},
      </if>
      <if test="bookCatalogue != null">
        #{bookCatalogue,jdbcType=VARCHAR},
      </if>
      <if test="bookAbstract != null">
        #{bookAbstract,jdbcType=VARCHAR},
      </if>
      <if test="bookCover != null">
        #{bookCover,jdbcType=VARCHAR},
      </if>
      <if test="bookCoverApp != null">
        #{bookCoverApp,jdbcType=VARCHAR},
      </if>
      <if test="bookSample != null">
        #{bookSample,jdbcType=VARCHAR},
      </if>
      <if test="bookFull != null">
        #{bookFull,jdbcType=VARCHAR},
      </if>
      <if test="bookHash != null">
        #{bookHash,jdbcType=VARCHAR},
      </if>
      <if test="bookIsbn != null">
        #{bookIsbn,jdbcType=VARCHAR},
      </if>
      <if test="ebookIsbn != null">
        #{ebookIsbn,jdbcType=VARCHAR},
      </if>
      <if test="bookEsin != null">
        #{bookEsin,jdbcType=VARCHAR},
      </if>
      <if test="hasBookPagination != null">
        #{hasBookPagination,jdbcType=TINYINT},
      </if>
      <if test="bookPubCode != null">
        #{bookPubCode,jdbcType=VARCHAR},
      </if>
      <if test="bookKeywords != null">
        #{bookKeywords,jdbcType=VARCHAR},
      </if>
      <if test="bookType != null">
        #{bookType,jdbcType=INTEGER},
      </if>
      <if test="specialOffer != null">
        #{specialOffer,jdbcType=INTEGER},
      </if>
      <if test="areaDiscount != null">
        #{areaDiscount,jdbcType=INTEGER},
      </if>
      <if test="canTts != null">
        #{canTts,jdbcType=INTEGER},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="bookDrmRef != null">
        #{bookDrmRef,jdbcType=VARCHAR},
      </if>
      <if test="discountSingleId != null">
        #{discountSingleId,jdbcType=BIGINT},
      </if>
      <if test="discountSingleType != null">
        #{discountSingleType,jdbcType=TINYINT},
      </if>
      <if test="discountSingleValue != null">
        #{discountSingleValue,jdbcType=INTEGER},
      </if>
      <if test="discountSingleStartTime != null">
        #{discountSingleStartTime,jdbcType=DATE},
      </if>
      <if test="discountSingleEndTime != null">
        #{discountSingleEndTime,jdbcType=DATE},
      </if>
      <if test="discountSingleIsValid != null">
        #{discountSingleIsValid,jdbcType=TINYINT},
      </if>
      <if test="discountSingleDescription != null">
        #{discountSingleDescription,jdbcType=VARCHAR},
      </if>
      <if test="discountId != null">
        #{discountId,jdbcType=BIGINT},
      </if>
      <if test="discountDescription != null">
        #{discountDescription,jdbcType=VARCHAR},
      </if>
      <if test="discountIsValid != null">
        #{discountIsValid,jdbcType=TINYINT},
      </if>
      <if test="setName != null">
        #{setName,jdbcType=VARCHAR},
      </if>
      <if test="setId != null">
        #{setId,jdbcType=BIGINT},
      </if>
      <if test="isInCn != null">
        #{isInCn,jdbcType=TINYINT},
      </if>
      <if test="bibleVersion != null">
        #{bibleVersion,jdbcType=INTEGER},
      </if>
      <if test="canMember != null">
        #{canMember,jdbcType=INTEGER},
      </if>
      <if test="showPublisher != null">
        #{showPublisher,jdbcType=TINYINT},
      </if>
      <if test="recommendedOrder != null">
        #{recommendedOrder,jdbcType=INTEGER},
      </if>
      <if test="recommendedCaption != null">
        #{recommendedCaption,jdbcType=VARCHAR},
      </if>
      <if test="printPermission != null">
        #{printPermission,jdbcType=TINYINT},
      </if>
      <if test="copyPermission != null">
        #{copyPermission,jdbcType=TINYINT},
      </if>
      <if test="ebookFormat != null">
        #{ebookFormat,jdbcType=TINYINT},
      </if>
      <if test="shelfStatus != null">
        #{shelfStatus,jdbcType=TINYINT},
      </if>
      <if test="vendorPercent != null">
        #{vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="salesModel != null">
        #{salesModel,jdbcType=INTEGER},
      </if>
      <if test="star != null">
        #{star,jdbcType=VARCHAR},
      </if>
      <if test="starCount != null">
        #{starCount,jdbcType=INTEGER},
      </if>
      <if test="opensaleAt != null">
        #{opensaleAt,jdbcType=TIMESTAMP},
      </if>
      <if test="bookDescription != null">
        #{bookDescription,jdbcType=LONGVARCHAR},
      </if>
      <if test="bookWeb != null">
        #{bookWeb,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanBookExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_book
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_book
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_book
    <set>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.bookTitle != null">
        book_title = #{record.bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.bookPinyin != null">
        book_pinyin = #{record.bookPinyin,jdbcType=VARCHAR},
      </if>
      <if test="record.author != null">
        author = #{record.author,jdbcType=VARCHAR},
      </if>
      <if test="record.authorBio != null">
        author_bio = #{record.authorBio,jdbcType=VARCHAR},
      </if>
      <if test="record.translator != null">
        translator = #{record.translator,jdbcType=VARCHAR},
      </if>
      <if test="record.wordCount != null">
        word_count = #{record.wordCount,jdbcType=VARCHAR},
      </if>
      <if test="record.wordCountShow != null">
        word_count_show = #{record.wordCountShow,jdbcType=VARCHAR},
      </if>
      <if test="record.productWeb != null">
        product_web = #{record.productWeb,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.salesVolume != null">
        sales_volume = #{record.salesVolume,jdbcType=BIGINT},
      </if>
      <if test="record.priceCny != null">
        price_cny = #{record.priceCny,jdbcType=DECIMAL},
      </if>
      <if test="record.priceUsd != null">
        price_usd = #{record.priceUsd,jdbcType=DECIMAL},
      </if>
      <if test="record.priceHkd != null">
        price_hkd = #{record.priceHkd,jdbcType=DECIMAL},
      </if>
      <if test="record.bookCost != null">
        book_cost = #{record.bookCost,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.publisherName != null">
        publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      </if>
      <if test="record.publishedAt != null">
        published_at = #{record.publishedAt,jdbcType=VARCHAR},
      </if>
      <if test="record.bookCatalogue != null">
        book_catalogue = #{record.bookCatalogue,jdbcType=VARCHAR},
      </if>
      <if test="record.bookAbstract != null">
        book_abstract = #{record.bookAbstract,jdbcType=VARCHAR},
      </if>
      <if test="record.bookCover != null">
        book_cover = #{record.bookCover,jdbcType=VARCHAR},
      </if>
      <if test="record.bookCoverApp != null">
        book_cover_app = #{record.bookCoverApp,jdbcType=VARCHAR},
      </if>
      <if test="record.bookSample != null">
        book_sample = #{record.bookSample,jdbcType=VARCHAR},
      </if>
      <if test="record.bookFull != null">
        book_full = #{record.bookFull,jdbcType=VARCHAR},
      </if>
      <if test="record.bookHash != null">
        book_hash = #{record.bookHash,jdbcType=VARCHAR},
      </if>
      <if test="record.bookIsbn != null">
        book_isbn = #{record.bookIsbn,jdbcType=VARCHAR},
      </if>
      <if test="record.ebookIsbn != null">
        ebook_isbn = #{record.ebookIsbn,jdbcType=VARCHAR},
      </if>
      <if test="record.bookEsin != null">
        book_esin = #{record.bookEsin,jdbcType=VARCHAR},
      </if>
      <if test="record.hasBookPagination != null">
        has_book_pagination = #{record.hasBookPagination,jdbcType=TINYINT},
      </if>
      <if test="record.bookPubCode != null">
        book_pub_code = #{record.bookPubCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bookKeywords != null">
        book_keywords = #{record.bookKeywords,jdbcType=VARCHAR},
      </if>
      <if test="record.bookType != null">
        book_type = #{record.bookType,jdbcType=INTEGER},
      </if>
      <if test="record.specialOffer != null">
        special_offer = #{record.specialOffer,jdbcType=INTEGER},
      </if>
      <if test="record.areaDiscount != null">
        area_discount = #{record.areaDiscount,jdbcType=INTEGER},
      </if>
      <if test="record.canTts != null">
        can_tts = #{record.canTts,jdbcType=INTEGER},
      </if>
      <if test="record.size != null">
        size = #{record.size,jdbcType=VARCHAR},
      </if>
      <if test="record.bookDrmRef != null">
        book_drm_ref = #{record.bookDrmRef,jdbcType=VARCHAR},
      </if>
      <if test="record.discountSingleId != null">
        discount_single_id = #{record.discountSingleId,jdbcType=BIGINT},
      </if>
      <if test="record.discountSingleType != null">
        discount_single_type = #{record.discountSingleType,jdbcType=TINYINT},
      </if>
      <if test="record.discountSingleValue != null">
        discount_single_value = #{record.discountSingleValue,jdbcType=INTEGER},
      </if>
      <if test="record.discountSingleStartTime != null">
        discount_single_start_time = #{record.discountSingleStartTime,jdbcType=DATE},
      </if>
      <if test="record.discountSingleEndTime != null">
        discount_single_end_time = #{record.discountSingleEndTime,jdbcType=DATE},
      </if>
      <if test="record.discountSingleIsValid != null">
        discount_single_is_valid = #{record.discountSingleIsValid,jdbcType=TINYINT},
      </if>
      <if test="record.discountSingleDescription != null">
        discount_single_description = #{record.discountSingleDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.discountId != null">
        discount_id = #{record.discountId,jdbcType=BIGINT},
      </if>
      <if test="record.discountDescription != null">
        discount_description = #{record.discountDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.discountIsValid != null">
        discount_is_valid = #{record.discountIsValid,jdbcType=TINYINT},
      </if>
      <if test="record.setName != null">
        set_name = #{record.setName,jdbcType=VARCHAR},
      </if>
      <if test="record.setId != null">
        set_id = #{record.setId,jdbcType=BIGINT},
      </if>
      <if test="record.isInCn != null">
        is_in_cn = #{record.isInCn,jdbcType=TINYINT},
      </if>
      <if test="record.bibleVersion != null">
        bible_version = #{record.bibleVersion,jdbcType=INTEGER},
      </if>
      <if test="record.canMember != null">
        can_member = #{record.canMember,jdbcType=INTEGER},
      </if>
      <if test="record.showPublisher != null">
        show_publisher = #{record.showPublisher,jdbcType=TINYINT},
      </if>
      <if test="record.recommendedOrder != null">
        recommended_order = #{record.recommendedOrder,jdbcType=INTEGER},
      </if>
      <if test="record.recommendedCaption != null">
        recommended_caption = #{record.recommendedCaption,jdbcType=VARCHAR},
      </if>
      <if test="record.printPermission != null">
        print_permission = #{record.printPermission,jdbcType=TINYINT},
      </if>
      <if test="record.copyPermission != null">
        copy_permission = #{record.copyPermission,jdbcType=TINYINT},
      </if>
      <if test="record.ebookFormat != null">
        ebook_format = #{record.ebookFormat,jdbcType=TINYINT},
      </if>
      <if test="record.shelfStatus != null">
        shelf_status = #{record.shelfStatus,jdbcType=TINYINT},
      </if>
      <if test="record.vendorPercent != null">
        vendor_percent = #{record.vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="record.salesModel != null">
        sales_model = #{record.salesModel,jdbcType=INTEGER},
      </if>
      <if test="record.star != null">
        star = #{record.star,jdbcType=VARCHAR},
      </if>
      <if test="record.starCount != null">
        star_count = #{record.starCount,jdbcType=INTEGER},
      </if>
      <if test="record.opensaleAt != null">
        opensale_at = #{record.opensaleAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bookDescription != null">
        book_description = #{record.bookDescription,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.bookWeb != null">
        book_web = #{record.bookWeb,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update enyan_book
    set book_id = #{record.bookId,jdbcType=BIGINT},
      book_title = #{record.bookTitle,jdbcType=VARCHAR},
      book_pinyin = #{record.bookPinyin,jdbcType=VARCHAR},
      author = #{record.author,jdbcType=VARCHAR},
      author_bio = #{record.authorBio,jdbcType=VARCHAR},
      translator = #{record.translator,jdbcType=VARCHAR},
      word_count = #{record.wordCount,jdbcType=VARCHAR},
      word_count_show = #{record.wordCountShow,jdbcType=VARCHAR},
      product_web = #{record.productWeb,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      sales_volume = #{record.salesVolume,jdbcType=BIGINT},
      price_cny = #{record.priceCny,jdbcType=DECIMAL},
      price_usd = #{record.priceUsd,jdbcType=DECIMAL},
      price_hkd = #{record.priceHkd,jdbcType=DECIMAL},
      book_cost = #{record.bookCost,jdbcType=INTEGER},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      category_name = #{record.categoryName,jdbcType=VARCHAR},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      published_at = #{record.publishedAt,jdbcType=VARCHAR},
      book_catalogue = #{record.bookCatalogue,jdbcType=VARCHAR},
      book_abstract = #{record.bookAbstract,jdbcType=VARCHAR},
      book_cover = #{record.bookCover,jdbcType=VARCHAR},
      book_cover_app = #{record.bookCoverApp,jdbcType=VARCHAR},
      book_sample = #{record.bookSample,jdbcType=VARCHAR},
      book_full = #{record.bookFull,jdbcType=VARCHAR},
      book_hash = #{record.bookHash,jdbcType=VARCHAR},
      book_isbn = #{record.bookIsbn,jdbcType=VARCHAR},
      ebook_isbn = #{record.ebookIsbn,jdbcType=VARCHAR},
      book_esin = #{record.bookEsin,jdbcType=VARCHAR},
      has_book_pagination = #{record.hasBookPagination,jdbcType=TINYINT},
      book_pub_code = #{record.bookPubCode,jdbcType=VARCHAR},
      book_keywords = #{record.bookKeywords,jdbcType=VARCHAR},
      book_type = #{record.bookType,jdbcType=INTEGER},
      special_offer = #{record.specialOffer,jdbcType=INTEGER},
      area_discount = #{record.areaDiscount,jdbcType=INTEGER},
      can_tts = #{record.canTts,jdbcType=INTEGER},
      size = #{record.size,jdbcType=VARCHAR},
      book_drm_ref = #{record.bookDrmRef,jdbcType=VARCHAR},
      discount_single_id = #{record.discountSingleId,jdbcType=BIGINT},
      discount_single_type = #{record.discountSingleType,jdbcType=TINYINT},
      discount_single_value = #{record.discountSingleValue,jdbcType=INTEGER},
      discount_single_start_time = #{record.discountSingleStartTime,jdbcType=DATE},
      discount_single_end_time = #{record.discountSingleEndTime,jdbcType=DATE},
      discount_single_is_valid = #{record.discountSingleIsValid,jdbcType=TINYINT},
      discount_single_description = #{record.discountSingleDescription,jdbcType=VARCHAR},
      discount_id = #{record.discountId,jdbcType=BIGINT},
      discount_description = #{record.discountDescription,jdbcType=VARCHAR},
      discount_is_valid = #{record.discountIsValid,jdbcType=TINYINT},
      set_name = #{record.setName,jdbcType=VARCHAR},
      set_id = #{record.setId,jdbcType=BIGINT},
      is_in_cn = #{record.isInCn,jdbcType=TINYINT},
      bible_version = #{record.bibleVersion,jdbcType=INTEGER},
      can_member = #{record.canMember,jdbcType=INTEGER},
      show_publisher = #{record.showPublisher,jdbcType=TINYINT},
      recommended_order = #{record.recommendedOrder,jdbcType=INTEGER},
      recommended_caption = #{record.recommendedCaption,jdbcType=VARCHAR},
      print_permission = #{record.printPermission,jdbcType=TINYINT},
      copy_permission = #{record.copyPermission,jdbcType=TINYINT},
      ebook_format = #{record.ebookFormat,jdbcType=TINYINT},
      shelf_status = #{record.shelfStatus,jdbcType=TINYINT},
      vendor_percent = #{record.vendorPercent,jdbcType=INTEGER},
      sales_model = #{record.salesModel,jdbcType=INTEGER},
      star = #{record.star,jdbcType=VARCHAR},
      star_count = #{record.starCount,jdbcType=INTEGER},
      opensale_at = #{record.opensaleAt,jdbcType=TIMESTAMP},
      book_description = #{record.bookDescription,jdbcType=LONGVARCHAR},
      book_web = #{record.bookWeb,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_book
    set book_id = #{record.bookId,jdbcType=BIGINT},
      book_title = #{record.bookTitle,jdbcType=VARCHAR},
      book_pinyin = #{record.bookPinyin,jdbcType=VARCHAR},
      author = #{record.author,jdbcType=VARCHAR},
      author_bio = #{record.authorBio,jdbcType=VARCHAR},
      translator = #{record.translator,jdbcType=VARCHAR},
      word_count = #{record.wordCount,jdbcType=VARCHAR},
      word_count_show = #{record.wordCountShow,jdbcType=VARCHAR},
      product_web = #{record.productWeb,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      sales_volume = #{record.salesVolume,jdbcType=BIGINT},
      price_cny = #{record.priceCny,jdbcType=DECIMAL},
      price_usd = #{record.priceUsd,jdbcType=DECIMAL},
      price_hkd = #{record.priceHkd,jdbcType=DECIMAL},
      book_cost = #{record.bookCost,jdbcType=INTEGER},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      category_name = #{record.categoryName,jdbcType=VARCHAR},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      published_at = #{record.publishedAt,jdbcType=VARCHAR},
      book_catalogue = #{record.bookCatalogue,jdbcType=VARCHAR},
      book_abstract = #{record.bookAbstract,jdbcType=VARCHAR},
      book_cover = #{record.bookCover,jdbcType=VARCHAR},
      book_cover_app = #{record.bookCoverApp,jdbcType=VARCHAR},
      book_sample = #{record.bookSample,jdbcType=VARCHAR},
      book_full = #{record.bookFull,jdbcType=VARCHAR},
      book_hash = #{record.bookHash,jdbcType=VARCHAR},
      book_isbn = #{record.bookIsbn,jdbcType=VARCHAR},
      ebook_isbn = #{record.ebookIsbn,jdbcType=VARCHAR},
      book_esin = #{record.bookEsin,jdbcType=VARCHAR},
      has_book_pagination = #{record.hasBookPagination,jdbcType=TINYINT},
      book_pub_code = #{record.bookPubCode,jdbcType=VARCHAR},
      book_keywords = #{record.bookKeywords,jdbcType=VARCHAR},
      book_type = #{record.bookType,jdbcType=INTEGER},
      special_offer = #{record.specialOffer,jdbcType=INTEGER},
      area_discount = #{record.areaDiscount,jdbcType=INTEGER},
      can_tts = #{record.canTts,jdbcType=INTEGER},
      size = #{record.size,jdbcType=VARCHAR},
      book_drm_ref = #{record.bookDrmRef,jdbcType=VARCHAR},
      discount_single_id = #{record.discountSingleId,jdbcType=BIGINT},
      discount_single_type = #{record.discountSingleType,jdbcType=TINYINT},
      discount_single_value = #{record.discountSingleValue,jdbcType=INTEGER},
      discount_single_start_time = #{record.discountSingleStartTime,jdbcType=DATE},
      discount_single_end_time = #{record.discountSingleEndTime,jdbcType=DATE},
      discount_single_is_valid = #{record.discountSingleIsValid,jdbcType=TINYINT},
      discount_single_description = #{record.discountSingleDescription,jdbcType=VARCHAR},
      discount_id = #{record.discountId,jdbcType=BIGINT},
      discount_description = #{record.discountDescription,jdbcType=VARCHAR},
      discount_is_valid = #{record.discountIsValid,jdbcType=TINYINT},
      set_name = #{record.setName,jdbcType=VARCHAR},
      set_id = #{record.setId,jdbcType=BIGINT},
      is_in_cn = #{record.isInCn,jdbcType=TINYINT},
      bible_version = #{record.bibleVersion,jdbcType=INTEGER},
      can_member = #{record.canMember,jdbcType=INTEGER},
      show_publisher = #{record.showPublisher,jdbcType=TINYINT},
      recommended_order = #{record.recommendedOrder,jdbcType=INTEGER},
      recommended_caption = #{record.recommendedCaption,jdbcType=VARCHAR},
      print_permission = #{record.printPermission,jdbcType=TINYINT},
      copy_permission = #{record.copyPermission,jdbcType=TINYINT},
      ebook_format = #{record.ebookFormat,jdbcType=TINYINT},
      shelf_status = #{record.shelfStatus,jdbcType=TINYINT},
      vendor_percent = #{record.vendorPercent,jdbcType=INTEGER},
      sales_model = #{record.salesModel,jdbcType=INTEGER},
      star = #{record.star,jdbcType=VARCHAR},
      star_count = #{record.starCount,jdbcType=INTEGER},
      opensale_at = #{record.opensaleAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanBook">
    update enyan_book
    <set>
      <if test="bookTitle != null">
        book_title = #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="bookPinyin != null">
        book_pinyin = #{bookPinyin,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        author = #{author,jdbcType=VARCHAR},
      </if>
      <if test="authorBio != null">
        author_bio = #{authorBio,jdbcType=VARCHAR},
      </if>
      <if test="translator != null">
        translator = #{translator,jdbcType=VARCHAR},
      </if>
      <if test="wordCount != null">
        word_count = #{wordCount,jdbcType=VARCHAR},
      </if>
      <if test="wordCountShow != null">
        word_count_show = #{wordCountShow,jdbcType=VARCHAR},
      </if>
      <if test="productWeb != null">
        product_web = #{productWeb,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="salesVolume != null">
        sales_volume = #{salesVolume,jdbcType=BIGINT},
      </if>
      <if test="priceCny != null">
        price_cny = #{priceCny,jdbcType=DECIMAL},
      </if>
      <if test="priceUsd != null">
        price_usd = #{priceUsd,jdbcType=DECIMAL},
      </if>
      <if test="priceHkd != null">
        price_hkd = #{priceHkd,jdbcType=DECIMAL},
      </if>
      <if test="bookCost != null">
        book_cost = #{bookCost,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="publisherName != null">
        publisher_name = #{publisherName,jdbcType=VARCHAR},
      </if>
      <if test="publishedAt != null">
        published_at = #{publishedAt,jdbcType=VARCHAR},
      </if>
      <if test="bookCatalogue != null">
        book_catalogue = #{bookCatalogue,jdbcType=VARCHAR},
      </if>
      <if test="bookAbstract != null">
        book_abstract = #{bookAbstract,jdbcType=VARCHAR},
      </if>
      <if test="bookCover != null">
        book_cover = #{bookCover,jdbcType=VARCHAR},
      </if>
      <if test="bookCoverApp != null">
        book_cover_app = #{bookCoverApp,jdbcType=VARCHAR},
      </if>
      <if test="bookSample != null">
        book_sample = #{bookSample,jdbcType=VARCHAR},
      </if>
      <if test="bookFull != null">
        book_full = #{bookFull,jdbcType=VARCHAR},
      </if>
      <if test="bookHash != null">
        book_hash = #{bookHash,jdbcType=VARCHAR},
      </if>
      <if test="bookIsbn != null">
        book_isbn = #{bookIsbn,jdbcType=VARCHAR},
      </if>
      <if test="ebookIsbn != null">
        ebook_isbn = #{ebookIsbn,jdbcType=VARCHAR},
      </if>
      <if test="bookEsin != null">
        book_esin = #{bookEsin,jdbcType=VARCHAR},
      </if>
      <if test="hasBookPagination != null">
        has_book_pagination = #{hasBookPagination,jdbcType=TINYINT},
      </if>
      <if test="bookPubCode != null">
        book_pub_code = #{bookPubCode,jdbcType=VARCHAR},
      </if>
      <if test="bookKeywords != null">
        book_keywords = #{bookKeywords,jdbcType=VARCHAR},
      </if>
      <if test="bookType != null">
        book_type = #{bookType,jdbcType=INTEGER},
      </if>
      <if test="specialOffer != null">
        special_offer = #{specialOffer,jdbcType=INTEGER},
      </if>
      <if test="areaDiscount != null">
        area_discount = #{areaDiscount,jdbcType=INTEGER},
      </if>
      <if test="canTts != null">
        can_tts = #{canTts,jdbcType=INTEGER},
      </if>
      <if test="size != null">
        size = #{size,jdbcType=VARCHAR},
      </if>
      <if test="bookDrmRef != null">
        book_drm_ref = #{bookDrmRef,jdbcType=VARCHAR},
      </if>
      <if test="discountSingleId != null">
        discount_single_id = #{discountSingleId,jdbcType=BIGINT},
      </if>
      <if test="discountSingleType != null">
        discount_single_type = #{discountSingleType,jdbcType=TINYINT},
      </if>
      <if test="discountSingleValue != null">
        discount_single_value = #{discountSingleValue,jdbcType=INTEGER},
      </if>
      <if test="discountSingleStartTime != null">
        discount_single_start_time = #{discountSingleStartTime,jdbcType=DATE},
      </if>
      <if test="discountSingleEndTime != null">
        discount_single_end_time = #{discountSingleEndTime,jdbcType=DATE},
      </if>
      <if test="discountSingleIsValid != null">
        discount_single_is_valid = #{discountSingleIsValid,jdbcType=TINYINT},
      </if>
      <if test="discountSingleDescription != null">
        discount_single_description = #{discountSingleDescription,jdbcType=VARCHAR},
      </if>
      <if test="discountId != null">
        discount_id = #{discountId,jdbcType=BIGINT},
      </if>
      <if test="discountDescription != null">
        discount_description = #{discountDescription,jdbcType=VARCHAR},
      </if>
      <if test="discountIsValid != null">
        discount_is_valid = #{discountIsValid,jdbcType=TINYINT},
      </if>
      <if test="setName != null">
        set_name = #{setName,jdbcType=VARCHAR},
      </if>
      <if test="setId != null">
        set_id = #{setId,jdbcType=BIGINT},
      </if>
      <if test="isInCn != null">
        is_in_cn = #{isInCn,jdbcType=TINYINT},
      </if>
      <if test="bibleVersion != null">
        bible_version = #{bibleVersion,jdbcType=INTEGER},
      </if>
      <if test="canMember != null">
        can_member = #{canMember,jdbcType=INTEGER},
      </if>
      <if test="showPublisher != null">
        show_publisher = #{showPublisher,jdbcType=TINYINT},
      </if>
      <if test="recommendedOrder != null">
        recommended_order = #{recommendedOrder,jdbcType=INTEGER},
      </if>
      <if test="recommendedCaption != null">
        recommended_caption = #{recommendedCaption,jdbcType=VARCHAR},
      </if>
      <if test="printPermission != null">
        print_permission = #{printPermission,jdbcType=TINYINT},
      </if>
      <if test="copyPermission != null">
        copy_permission = #{copyPermission,jdbcType=TINYINT},
      </if>
      <if test="ebookFormat != null">
        ebook_format = #{ebookFormat,jdbcType=TINYINT},
      </if>
      <if test="shelfStatus != null">
        shelf_status = #{shelfStatus,jdbcType=TINYINT},
      </if>
      <if test="vendorPercent != null">
        vendor_percent = #{vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="salesModel != null">
        sales_model = #{salesModel,jdbcType=INTEGER},
      </if>
      <if test="star != null">
        star = #{star,jdbcType=VARCHAR},
      </if>
      <if test="starCount != null">
        star_count = #{starCount,jdbcType=INTEGER},
      </if>
      <if test="opensaleAt != null">
        opensale_at = #{opensaleAt,jdbcType=TIMESTAMP},
      </if>
      <if test="bookDescription != null">
        book_description = #{bookDescription,jdbcType=LONGVARCHAR},
      </if>
      <if test="bookWeb != null">
        book_web = #{bookWeb,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where book_id = #{bookId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.EnyanBook">
    update enyan_book
    set book_title = #{bookTitle,jdbcType=VARCHAR},
      book_pinyin = #{bookPinyin,jdbcType=VARCHAR},
      author = #{author,jdbcType=VARCHAR},
      author_bio = #{authorBio,jdbcType=VARCHAR},
      translator = #{translator,jdbcType=VARCHAR},
      word_count = #{wordCount,jdbcType=VARCHAR},
      word_count_show = #{wordCountShow,jdbcType=VARCHAR},
      product_web = #{productWeb,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      sales_volume = #{salesVolume,jdbcType=BIGINT},
      price_cny = #{priceCny,jdbcType=DECIMAL},
      price_usd = #{priceUsd,jdbcType=DECIMAL},
      price_hkd = #{priceHkd,jdbcType=DECIMAL},
      book_cost = #{bookCost,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=BIGINT},
      category_name = #{categoryName,jdbcType=VARCHAR},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      publisher_name = #{publisherName,jdbcType=VARCHAR},
      published_at = #{publishedAt,jdbcType=VARCHAR},
      book_catalogue = #{bookCatalogue,jdbcType=VARCHAR},
      book_abstract = #{bookAbstract,jdbcType=VARCHAR},
      book_cover = #{bookCover,jdbcType=VARCHAR},
      book_cover_app = #{bookCoverApp,jdbcType=VARCHAR},
      book_sample = #{bookSample,jdbcType=VARCHAR},
      book_full = #{bookFull,jdbcType=VARCHAR},
      book_hash = #{bookHash,jdbcType=VARCHAR},
      book_isbn = #{bookIsbn,jdbcType=VARCHAR},
      ebook_isbn = #{ebookIsbn,jdbcType=VARCHAR},
      book_esin = #{bookEsin,jdbcType=VARCHAR},
      has_book_pagination = #{hasBookPagination,jdbcType=TINYINT},
      book_pub_code = #{bookPubCode,jdbcType=VARCHAR},
      book_keywords = #{bookKeywords,jdbcType=VARCHAR},
      book_type = #{bookType,jdbcType=INTEGER},
      special_offer = #{specialOffer,jdbcType=INTEGER},
      area_discount = #{areaDiscount,jdbcType=INTEGER},
      can_tts = #{canTts,jdbcType=INTEGER},
      size = #{size,jdbcType=VARCHAR},
      book_drm_ref = #{bookDrmRef,jdbcType=VARCHAR},
      discount_single_id = #{discountSingleId,jdbcType=BIGINT},
      discount_single_type = #{discountSingleType,jdbcType=TINYINT},
      discount_single_value = #{discountSingleValue,jdbcType=INTEGER},
      discount_single_start_time = #{discountSingleStartTime,jdbcType=DATE},
      discount_single_end_time = #{discountSingleEndTime,jdbcType=DATE},
      discount_single_is_valid = #{discountSingleIsValid,jdbcType=TINYINT},
      discount_single_description = #{discountSingleDescription,jdbcType=VARCHAR},
      discount_id = #{discountId,jdbcType=BIGINT},
      discount_description = #{discountDescription,jdbcType=VARCHAR},
      discount_is_valid = #{discountIsValid,jdbcType=TINYINT},
      set_name = #{setName,jdbcType=VARCHAR},
      set_id = #{setId,jdbcType=BIGINT},
      is_in_cn = #{isInCn,jdbcType=TINYINT},
      bible_version = #{bibleVersion,jdbcType=INTEGER},
      can_member = #{canMember,jdbcType=INTEGER},
      show_publisher = #{showPublisher,jdbcType=TINYINT},
      recommended_order = #{recommendedOrder,jdbcType=INTEGER},
      recommended_caption = #{recommendedCaption,jdbcType=VARCHAR},
      print_permission = #{printPermission,jdbcType=TINYINT},
      copy_permission = #{copyPermission,jdbcType=TINYINT},
      ebook_format = #{ebookFormat,jdbcType=TINYINT},
      shelf_status = #{shelfStatus,jdbcType=TINYINT},
      vendor_percent = #{vendorPercent,jdbcType=INTEGER},
      sales_model = #{salesModel,jdbcType=INTEGER},
      star = #{star,jdbcType=VARCHAR},
      star_count = #{starCount,jdbcType=INTEGER},
      opensale_at = #{opensaleAt,jdbcType=TIMESTAMP},
      book_description = #{bookDescription,jdbcType=LONGVARCHAR},
      book_web = #{bookWeb,jdbcType=LONGVARCHAR}
    where book_id = #{bookId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanBook">
    update enyan_book
    set book_title = #{bookTitle,jdbcType=VARCHAR},
      book_pinyin = #{bookPinyin,jdbcType=VARCHAR},
      author = #{author,jdbcType=VARCHAR},
      author_bio = #{authorBio,jdbcType=VARCHAR},
      translator = #{translator,jdbcType=VARCHAR},
      word_count = #{wordCount,jdbcType=VARCHAR},
      word_count_show = #{wordCountShow,jdbcType=VARCHAR},
      product_web = #{productWeb,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      sales_volume = #{salesVolume,jdbcType=BIGINT},
      price_cny = #{priceCny,jdbcType=DECIMAL},
      price_usd = #{priceUsd,jdbcType=DECIMAL},
      price_hkd = #{priceHkd,jdbcType=DECIMAL},
      book_cost = #{bookCost,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=BIGINT},
      category_name = #{categoryName,jdbcType=VARCHAR},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      publisher_name = #{publisherName,jdbcType=VARCHAR},
      published_at = #{publishedAt,jdbcType=VARCHAR},
      book_catalogue = #{bookCatalogue,jdbcType=VARCHAR},
      book_abstract = #{bookAbstract,jdbcType=VARCHAR},
      book_cover = #{bookCover,jdbcType=VARCHAR},
      book_cover_app = #{bookCoverApp,jdbcType=VARCHAR},
      book_sample = #{bookSample,jdbcType=VARCHAR},
      book_full = #{bookFull,jdbcType=VARCHAR},
      book_hash = #{bookHash,jdbcType=VARCHAR},
      book_isbn = #{bookIsbn,jdbcType=VARCHAR},
      ebook_isbn = #{ebookIsbn,jdbcType=VARCHAR},
      book_esin = #{bookEsin,jdbcType=VARCHAR},
      has_book_pagination = #{hasBookPagination,jdbcType=TINYINT},
      book_pub_code = #{bookPubCode,jdbcType=VARCHAR},
      book_keywords = #{bookKeywords,jdbcType=VARCHAR},
      book_type = #{bookType,jdbcType=INTEGER},
      special_offer = #{specialOffer,jdbcType=INTEGER},
      area_discount = #{areaDiscount,jdbcType=INTEGER},
      can_tts = #{canTts,jdbcType=INTEGER},
      size = #{size,jdbcType=VARCHAR},
      book_drm_ref = #{bookDrmRef,jdbcType=VARCHAR},
      discount_single_id = #{discountSingleId,jdbcType=BIGINT},
      discount_single_type = #{discountSingleType,jdbcType=TINYINT},
      discount_single_value = #{discountSingleValue,jdbcType=INTEGER},
      discount_single_start_time = #{discountSingleStartTime,jdbcType=DATE},
      discount_single_end_time = #{discountSingleEndTime,jdbcType=DATE},
      discount_single_is_valid = #{discountSingleIsValid,jdbcType=TINYINT},
      discount_single_description = #{discountSingleDescription,jdbcType=VARCHAR},
      discount_id = #{discountId,jdbcType=BIGINT},
      discount_description = #{discountDescription,jdbcType=VARCHAR},
      discount_is_valid = #{discountIsValid,jdbcType=TINYINT},
      set_name = #{setName,jdbcType=VARCHAR},
      set_id = #{setId,jdbcType=BIGINT},
      is_in_cn = #{isInCn,jdbcType=TINYINT},
      bible_version = #{bibleVersion,jdbcType=INTEGER},
      can_member = #{canMember,jdbcType=INTEGER},
      show_publisher = #{showPublisher,jdbcType=TINYINT},
      recommended_order = #{recommendedOrder,jdbcType=INTEGER},
      recommended_caption = #{recommendedCaption,jdbcType=VARCHAR},
      print_permission = #{printPermission,jdbcType=TINYINT},
      copy_permission = #{copyPermission,jdbcType=TINYINT},
      ebook_format = #{ebookFormat,jdbcType=TINYINT},
      shelf_status = #{shelfStatus,jdbcType=TINYINT},
      vendor_percent = #{vendorPercent,jdbcType=INTEGER},
      sales_model = #{salesModel,jdbcType=INTEGER},
      star = #{star,jdbcType=VARCHAR},
      star_count = #{starCount,jdbcType=INTEGER},
      opensale_at = #{opensaleAt,jdbcType=TIMESTAMP}
    where book_id = #{bookId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>