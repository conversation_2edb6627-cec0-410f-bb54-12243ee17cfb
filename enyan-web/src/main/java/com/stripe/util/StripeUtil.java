package com.stripe.util;

import com.aaron.api.Pair;
import com.aaron.common.CreditInfo;
import com.stripe.Stripe;
import com.stripe.config.StripeConfig;
import com.stripe.exception.*;
import com.stripe.model.Token;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/12/14
 * @Modified By:
 */
public class StripeUtil {
    private StripeUtil(){

    }

    public static String getToken(CreditInfo creditInfo, String email){
        Stripe.apiKey = StripeConfig.SECRET_KEY;

        Map<String, Object> tokenParams = new HashMap<>();
        Map<String, Object> cardParams = new HashMap<>();
        cardParams.put("number", creditInfo.getPayNumber());
        cardParams.put("exp_month", creditInfo.getPayExpireMonth());
        cardParams.put("exp_year", creditInfo.getPayExpireYear().length() ==2 ? "20"+creditInfo.getPayExpireYear():creditInfo.getPayExpireYear());
        cardParams.put("cvc", creditInfo.getPayCvc());
        tokenParams.put("card", cardParams);
        tokenParams.put("email",email);
        try {
            Token token = Token.create(tokenParams);

            return token.getId();
        }  catch (StripeException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Pair<Boolean,String> getTokenInPair(CreditInfo creditInfo, String email){
        Stripe.apiKey = StripeConfig.SECRET_KEY;

        Map<String, Object> tokenParams = new HashMap<>();
        Map<String, Object> cardParams = new HashMap<>();
        cardParams.put("number", creditInfo.getPayNumber());
        cardParams.put("exp_month", creditInfo.getPayExpireMonth());
        cardParams.put("exp_year", creditInfo.getPayExpireYear().length() ==2 ? "20"+creditInfo.getPayExpireYear():creditInfo.getPayExpireYear());
        cardParams.put("cvc", creditInfo.getPayCvc());
        tokenParams.put("card", cardParams);
        tokenParams.put("email",email);
        try {
            Token token = Token.create(tokenParams);
            if (StringUtils.isNotBlank(token.getId())){
                return new Pair<>(true,token.getId());
            }
            return new Pair<>(false,"token is null");
        }  catch (StripeException e) {
            e.printStackTrace();
            return new Pair<>(false,e.getMessage());
        }
    }
}
