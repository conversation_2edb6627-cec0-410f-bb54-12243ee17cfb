package com.aaron.drm.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-04
 * @Modified By:
 */
public class Licenses {
    private String provider;
    private String id;
    private String issued;
    private Encryption encryption;
    private List<Link> links;
    private User user;
    private Rights rights;
    private Signature signature;

    @JsonProperty("provider")
    public String getProvider() {
        return provider;
    }

    @JsonProperty("provider")
    public void setProvider(String value) {
        this.provider = value;
    }

    @JsonProperty("id")
    public String getID() {
        return id;
    }

    @JsonProperty("id")
    public void setID(String value) {
        this.id = value;
    }

    @JsonProperty("issued")
    public String getIssued() {
        return issued;
    }

    @JsonProperty("issued")
    public void setIssued(String value) {
        this.issued = value;
    }

    @JsonProperty("encryption")
    public Encryption getEncryption() {
        return encryption;
    }

    @JsonProperty("encryption")
    public void setEncryption(Encryption value) {
        this.encryption = value;
    }

    @JsonProperty("links")
    public List<Link> getLinks() {
        return links;
    }

    @JsonProperty("links")
    public void setLinks(List<Link> value) {
        this.links = value;
    }

    @JsonProperty("user")
    public User getUser() {
        return user;
    }

    @JsonProperty("user")
    public void setUser(User value) {
        this.user = value;
    }

    @JsonProperty("rights")
    public Rights getRights() {
        return rights;
    }

    @JsonProperty("rights")
    public void setRights(Rights value) {
        this.rights = value;
    }

    @JsonProperty("signature")
    public Signature getSignature() {
        return signature;
    }

    @JsonProperty("signature")
    public void setSignature(Signature value) {
        this.signature = value;
    }

    @Override
    public String toString() {
        return "Licenses{" +
                "provider='" + provider + '\'' +
                ", id='" + id + '\'' +
                ", issued='" + issued + '\'' +
                ", encryption=" + encryption +
                ", links=" + links +
                ", user=" + user +
                ", rights=" + rights +
                ", signature=" + signature +
                '}';
    }
}
