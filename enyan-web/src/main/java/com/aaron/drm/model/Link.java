package com.aaron.drm.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-04
 * @Modified By:
 */
public class Link {
    private String rel;
    private String href;
    private String type;
    private String title;
    private Long length;
    private String hash;

    @JsonProperty("rel")
    public String getRel() { return rel; }
    @JsonProperty("rel")
    public void setRel(String value) { this.rel = value; }

    @JsonProperty("href")
    public String getHref() { return href; }
    @JsonProperty("href")
    public void setHref(String value) { this.href = value; }

    @JsonProperty("type")
    public String getType() { return type; }
    @JsonProperty("type")
    public void setType(String value) { this.type = value; }

    @JsonProperty("title")
    public String getTitle() { return title; }
    @JsonProperty("title")
    public void setTitle(String value) { this.title = value; }

    @JsonProperty("length")
    public Long getLength() { return length; }
    @JsonProperty("length")
    public void setLength(Long value) { this.length = value; }

    @JsonProperty("hash")
    public String getHash() { return hash; }
    @JsonProperty("hash")
    public void setHash(String value) { this.hash = value; }
}
