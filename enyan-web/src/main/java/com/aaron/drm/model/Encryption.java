package com.aaron.drm.model;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-04
 * @Modified By:
 */
public class Encryption {
    @<PERSON><PERSON><PERSON><PERSON>(name = "profile")
    private String profile;
    @<PERSON><PERSON><PERSON>ield(name = "content_key")
    private ContentKey contentKey;
    @JSONField(name = "user_key")
    private UserKey userKey;

    @JsonProperty("profile")
    public String getProfile() {
        return profile;
    }

    @JsonProperty("profile")
    public void setProfile(String value) {
        this.profile = value;
    }

    @JsonProperty("content_key")
    public ContentKey getContentKey() {
        return contentKey;
    }

    @JsonProperty("content_key")
    public void setContentKey(ContentKey value) {
        this.contentKey = value;
    }

    @JsonProperty("user_key")
    public UserKey getUserKey() {
        return userKey;
    }

    @JsonProperty("user_key")
    public void setUserKey(UserKey value) {
        this.userKey = value;
    }
}
