package com.aaron.drm.model;

import com.alibaba.fastjson2.annotation.J<PERSON><PERSON>ield;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-04
 * @Modified By:
 */
public class User {
    @J<PERSON><PERSON>ield(name = "id")
    private String id;
    @JSONField(name = "email")
    private String email;
    @JSONField(name = "name")
    private String name;
    @J<PERSON><PERSON>ield(name = "encrypted")
    private List<String> encrypted = new ArrayList<>();

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String value) {
        this.id = value;
    }

    @JsonProperty("email")
    public String getEmail() {
        return email;
    }

    @JsonProperty("email")
    public void setEmail(String value) {
        this.email = value;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String value) {
        this.name = value;
    }

    @JsonProperty("encrypted")
    public List<String> getEncrypted() {
        if (StringUtils.isNotEmpty(name)){
            encrypted.add("name");
        }
        if (StringUtils.isNotEmpty(email)){
            encrypted.add("email");
        }
        return encrypted;
    }

    @JsonProperty("encrypted")
    public void setEncrypted(List<String> value) {
        this.encrypted = value;
    }
}
