package com.aaron.drm.model;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-04
 * @Modified By:
 */
public class UserKey {
    private String algorithm;
    @JSONField(name = "text_hint")
    private String textHint;
    @JSONField(name = "key_check")
    private String keyCheck;
    @JSONField(name = "hex_value")
    private String hexValue;
    @JSONField(name = "value")
    private String value;

    @JsonProperty("text_hint")
    public String getTextHint() {
        return textHint;
    }

    @JsonProperty("text_hint")
    public void setTextHint(String value) {
        this.textHint = value;
    }

    @JsonProperty("hex_value")
    public String getHexValue() {
        return hexValue;
    }

    @JsonProperty("hex_value")
    public void setHexValue(String value) {
        this.hexValue = value;
    }
    @JSONField(name = "key_check")
    public String getKeyCheck() {
        return keyCheck;
    }
    @J<PERSON><PERSON>ield(name = "key_check")
    public void setKeyCheck(String keyCheck) {
        this.keyCheck = keyCheck;
    }

    @JSONField(name = "value")
    public String getValue() {
        return value;
    }

    @JSONField(name = "value")
    public void setValue(String value) {
        this.value = value;
    }
}
