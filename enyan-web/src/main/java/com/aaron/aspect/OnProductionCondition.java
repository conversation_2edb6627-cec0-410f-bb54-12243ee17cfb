package com.aaron.aspect;

import com.aaron.spring.common.Constant;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * @Author: <PERSON>
 * @Description: https://cloud.tencent.com/developer/article/1954054
 * @Date: Created in  2023/3/28
 * @Modified By:
 */
public class OnProductionCondition implements Condition {
	@Override
	public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
		return Constant.IS_PRODUCT;
	}
}
