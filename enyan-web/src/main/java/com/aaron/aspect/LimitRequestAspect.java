package com.aaron.aspect;

import com.aaron.annotation.LimitRequest;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.util.ExecuteResult;
import net.jodah.expiringmap.ExpirationPolicy;
import net.jodah.expiringmap.ExpiringMap;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/3/9
 * @Modified By:
 */
@Aspect
@Component
public class LimitRequestAspect {
	private static ConcurrentHashMap<String, ExpiringMap<String, Integer>> book = new ConcurrentHashMap<>();

	//    定义切点
//    让所有@LimitRequest注解的方法都执行切面方法
	@Pointcut("@annotation(limitRequest)")
	public void excudeService(LimitRequest limitRequest) {
	}

	@Around("excudeService(limitRequest)")
	public Object doAround(ProceedingJoinPoint pjp, LimitRequest limitRequest) throws Throwable {
		RequestAttributes ra = RequestContextHolder.getRequestAttributes();
		ServletRequestAttributes sra = (ServletRequestAttributes) ra;
		HttpServletRequest request = sra.getRequest();

//        获取Map对象，如果没有则返回默认值
//        第一个参数是key，第二个参数是默认值
		ExpiringMap<String, Integer> uc = book.getOrDefault(request.getRequestURI(), ExpiringMap.builder().variableExpiration().build());
		Integer uCount = uc.getOrDefault(request.getRemoteAddr(), 0);
		if (uCount>=limitRequest.count()) {
			ExecuteResult<String> result = new ExecuteResult<>();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.REQUEST_ERROR_OUTLIMIT_CODE);
//			ResponsePackage responsePackage = new ResponsePackage();
//			responsePackage.setStatusCode(OpCode.LIMIT_REQUEST_STATUS_CODE);
//			responsePackage.setMessage(OpCode.LIMIT_REQUEST_STATUS_MESSAGE);
			return result;
		} else if (uCount==0) {
//            第一次请求，设置有效时间
			uc.put(request.getRemoteAddr(), uCount+1, ExpirationPolicy.CREATED, limitRequest.time(), TimeUnit.MILLISECONDS);
		} else  {
//            不超过次数， +1
			uc.put(request.getRemoteAddr(), uCount+1);
		}
		book.put(request.getRequestURI(), uc);

//        proceed就是被方法拦截的返回值
		Object proceed = pjp.proceed();
		return proceed;
	}

	/*
	@RequestLimit(count=10,time=60000)
	* */
	/*
	ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = request.getRemoteAddr();
            String url = request.getRequestURL().toString();
            String key = "ifovertimes".concat(url).concat(ip);
            //访问次数加一
            long count = redisTemplate.opsForValue().increment(key, 1);
            //如果是第一次，则设置过期时间
            if (count == 1) {
                redisTemplate.expire(key, times.time(), TimeUnit.MILLISECONDS);
            }
            if (count > times.count()) {
                request.setAttribute("ifovertimes", "true");
            } else {
                request.setAttribute("ifovertimes", "false");
            }
	* */
}
