package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.drm.model.DrmInfo;
import com.aaron.drm.model.LcpInfo;
import com.aaron.drm.util.DRMUtil;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.v4.model.RestRent;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.mapper.*;
import com.aaron.spring.mapper.custom.EnyanRentCustomMapper;
import com.aaron.spring.mapper.custom.EnyanRentDetailCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.AuthUserService;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanRentService;
import com.aaron.spring.common.OrderUtil;
import com.aaron.spring.service.PurchaseService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2022/10/31
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanRentServiceImpl implements EnyanRentService {
	@Resource
	private EnyanRentMapper enyanRentMapper;

	@Resource
	private EnyanRentCustomMapper enyanRentCustomMapper;

	@Resource
	private EnyanPublisherMapper enyanPublisherMapper;

	@Resource
	private EnyanRentDetailMapper enyanRentDetailMapper;

	@Resource
	private EnyanRentDetailCustomMapper enyanRentDetailCustomMapper;

	@Resource
	protected PurchaseMapper purchaseMapper;

	@Resource
	private AuthUserService authUserService;

	@Resource
	protected PublicationMapper publicationMapper;

	@Resource
	private EnyanBookService enyanBookService;

	@Resource
	private PurchaseService purchaseService;

	@Override
	public Page queryRecords(Page<EnyanRent> page, EnyanRent record) {
		if (null == record){
			record = new EnyanRent();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanRentExample example = new EnyanRentExample();
			EnyanRentExample.Criteria criteria = example.createCriteria();

			if (StringUtils.isNotBlank(record.getUserEmail())){
				criteria.andUserEmailEqualTo(record.getUserEmail());
			}
			if (null != record.getIsValid()){
				criteria.andIsValidEqualTo(record.getIsValid());
			}
			if (null != record.getIsPaid()){
				criteria.andIsPaidEqualTo(record.getIsPaid());
			}
			if (null != record.getRentType()){
				criteria.andRentTypeEqualTo(record.getRentType());
			}
			if (null != record.getRentLang()){
				criteria.andRentLangEqualTo(record.getRentLang());
			}
			if (StringUtils.isNotBlank(record.getOrderNum())){
				criteria.andOrderNumEqualTo(record.getOrderNum());
			}
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			criteria.andIsDeletedEqualTo(0);

			example.setPage(page);

			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanRentMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanRent> list;
			if (count > 0){
				list = enyanRentMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public List<EnyanRent> findRecords(EnyanRent record) {
		try {
			EnyanRentExample example = new EnyanRentExample();
			EnyanRentExample.Criteria criteria = example.createCriteria();

			if (StringUtils.isNotBlank(record.getUserEmail())){
				criteria.andUserEmailEqualTo(record.getUserEmail());
			}
			if (null != record.getIsValid()){
				criteria.andIsValidEqualTo(record.getIsValid());
			}
			if (null != record.getIsPaid()){
				criteria.andIsPaidEqualTo(record.getIsPaid());
			}
			if (null != record.getRentType()){
				criteria.andRentTypeEqualTo(record.getRentType());
			}
			if (null != record.getRentLang()){
				criteria.andRentLangEqualTo(record.getRentLang());
			}
			if (StringUtils.isNotBlank(record.getOrderNum())){
				criteria.andOrderNumEqualTo(record.getOrderNum());
			}

			criteria.andIsDeletedEqualTo(0);

			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}

			return enyanRentMapper.selectByExample(example);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public EnyanRent queryRecordBaseInfoByPrimaryKey(Long dataId) {
		return enyanRentCustomMapper.queryRecordBaseInfoByPrimaryKey(dataId);
	}

	@Override
	public List<EnyanRent> findRecordsWithBlob(EnyanRent record) {
		try {
			EnyanRentExample example = new EnyanRentExample();
			EnyanRentExample.Criteria criteria = example.createCriteria();

			if (StringUtils.isNotBlank(record.getUserEmail())){
				criteria.andUserEmailEqualTo(record.getUserEmail());
			}
			if (null != record.getIsValid()){
				criteria.andIsValidEqualTo(record.getIsValid());
			}
			if (null != record.getIsPaid()){
				criteria.andIsPaidEqualTo(record.getIsPaid());
			}
			if (null != record.getRentType()){
				criteria.andRentTypeEqualTo(record.getRentType());
			}
			if (null != record.getRentLang()){
				criteria.andRentLangEqualTo(record.getRentLang());
			}
			if (StringUtils.isNotBlank(record.getOrderNum())){
				criteria.andOrderNumEqualTo(record.getOrderNum());
			}

			criteria.andIsDeletedEqualTo(0);

			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}

			List<EnyanRent> list = enyanRentMapper.selectByExampleWithBLOBs(example);
			for (EnyanRent obj:list) {
				if (StringUtils.isNotBlank(obj.getRentDetail())){
					RentInfo rentInfo = JSONObject.parseObject(obj.getRentDetail(),RentInfo.class);
					obj.setRentInfo(rentInfo);
				}else{
					obj.setRentInfo(new RentInfo());
				}
			}
			return list;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public void leaveRent(String orderNum, boolean isAuto) {
		int rentStatus = EBookConstant.RentStatus.leave;
		if (isAuto){
			rentStatus = EBookConstant.RentStatus.expired;
		}

	}

	@Override
	public long countOfRecords(EnyanRent record) {
		EnyanRentExample example = new EnyanRentExample();
		EnyanRentExample.Criteria criteria = example.createCriteria();

		if (StringUtils.isNotBlank(record.getUserEmail())){
			criteria.andUserEmailEqualTo(record.getUserEmail());
		}
		if (null != record.getIsValid()){
			criteria.andIsValidEqualTo(record.getIsValid());
		}
		if (null != record.getIsPaid()){
			criteria.andIsPaidEqualTo(record.getIsPaid());
		}
		if (null != record.getRentType()){
			criteria.andRentTypeEqualTo(record.getRentType());
		}
		if (null != record.getRentLang()){
			criteria.andRentLangEqualTo(record.getRentLang());
		}
		criteria.andIsDeletedEqualTo(0);

		if (null != record.getOrderObjList()){
			StringBuffer buffer = new StringBuffer();
			for (int i = 0; i < record.getOrderObjList().size(); i++) {
				OrderObj orderObj = record.getOrderObjList().get(i);
				if (i!=0){
					buffer.append(",");
				}
				buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
			}
			example.setOrderByClause(buffer.toString());
		}
		return enyanRentMapper.countByExample(example);
	}

	@Override
	public void initRentsInfo() {
		List<RestRent> autoList = this.getRentListByAuto(1);
		List<RestRent> manualList = this.getRentListByAuto(0);
		Constant.DEFAULT_REST_CONFIG.setToBuyRentsAuto(autoList);
		Constant.DEFAULT_REST_CONFIG.setToBuyRentsManual(manualList);
	}

	private List<RestRent> getRentListByAuto(int auto){
		List<RestRent> list = new ArrayList<>();
		for (int i = 1; i <= 3; i++) {//rentType
			for (int j = 1; j <= 2; j++) {//rentLang
				RestRent rent = new RestRent();
				rent.setRentType(i);
				rent.setRentLang(j);
				rent.setTotalFee(BookUtil.getRentPrice(i, auto));
				list.add(rent);
			}
		}
		return list;
	}

	@Override
	public EnyanRentDetail saveOrderHasPay(EnyanRent record, OrderPayInfo orderPayInfo, Integer toRentMonths, Date newExpireDate) {
		Date oldExpiredAt = record.getBeginAt();//默认开始时间
		//计算累计的租赁月数
		int totalMonths = toRentMonths;
		BigDecimal totalFee = new BigDecimal(orderPayInfo.getCharge().getAmount());

		List<EnyanRentDetail> totalDetailList = enyanRentDetailCustomMapper.findSumMonthsByRent(record.getOrderNum(), record.getUserEmail());
		if (totalDetailList.isEmpty() == false){
			EnyanRentDetail tmp = totalDetailList.get(0);
			if (null != tmp ){
				if (null != tmp.getRentMonths()){
					oldExpiredAt = DateUtils.addMonths(record.getBeginAt(), tmp.getRentMonths());//从开始时间重算一下具体的结束时间
					totalMonths += tmp.getRentMonths();
				}
				if (null != tmp.getIncomeTotal()){
					totalFee = totalFee.add(tmp.getIncomeTotal());
				}
			}
		}
		record.setTotalMonths(totalMonths);
		record.setTotalFee(totalFee);

		Date today = new Date();

		newExpireDate = DateUtils.addMonths(record.getBeginAt(), totalMonths);
		record.setNewExpiredAt(newExpireDate);//设置新的过期时间
		record.setExpiredAt(newExpireDate);

		if (newExpireDate.after(today)){//续费在今天之后,一切就绪
			enyanRentCustomMapper.updateRecordToPaid(record);
		}else{//没有抵达最新时间
			enyanRentCustomMapper.updateRecordToPaidOnly(record);
		}

		//enyanRentCustomMapper.updateRecordToPaid(record.getOrderNum(), record.getNewExpiredAt(), record.getTotalMonths());


		/*
		EnyanPublisher publisher = enyanPublisherMapper.selectByPrimaryKey(record.getPublisherId());
		if (null == publisher){
			return null;
		}*/

		BigDecimal currentTotalFee = new BigDecimal(orderPayInfo.getCharge().getAmount());
		Date date = new Date();
		EnyanRentDetail detail = new EnyanRentDetail();
		detail.setCreateAt(date);
		detail.setFromAt(oldExpiredAt);//上次到期时间
		detail.setExpiredAt(newExpireDate);//
		detail.setOrderFrom(record.getOrderFrom());
		detail.setOrderNum(record.getOrderNum());
		detail.setVendorPercent(BookUtil.getRentVendorPercent(record.getPublisherId()));

		detail.setPayCountry(orderPayInfo.getCharge().getCountry());
		detail.setIncomeTotal(currentTotalFee);
		detail.setIncomeReal(currentTotalFee);
		detail.setPayType(orderPayInfo.getCharge().getPayType());
		detail.setPublisherId(record.getPublisherId());
		detail.setPurchasedAt(date);
		//detail.setRateValue(orderPayInfo.getCharge().ger);
		detail.setRentLang(record.getRentLang());
		detail.setRentMonths(toRentMonths);
		detail.setRentType(record.getRentType());
		detail.setUserEmail(record.getUserEmail());

		detail.setIsAuto(record.getIsAuto());
		detail.setTradeNo(orderPayInfo.getCharge().getTradeNO());

		EnyanRentDetail rentDetailNew = OrderUtil.getCalcRentOrder(detail);
		detail.setPayFee(rentDetailNew.getPayFee());
		detail.setIncomePlat(rentDetailNew.getIncomePlat());
		detail.setIncomeVendor(rentDetailNew.getIncomeVendor());
		detail.setNetSales(rentDetailNew.getNetSales());

		detail.setIsDeleted(0);
		enyanRentDetailMapper.insert(detail);

		return detail;
	}

	@Override
	public void addDRMInfoToRent(EnyanRent rent, Date newExpireDate) {
		Date current = new Date();
		String licenseId = UUID.randomUUID().toString();
		List<Long> bookIds = BookUtil.getBooksInRentType(rent.getRentType(), rent.getRentLang());
		User user = authUserService.getLcpUserByEmail(rent.getUserEmail()).getResult();
		if (null == user){
			user = new User();
			user.setEmail(rent.getUserEmail().toLowerCase());
			user.setHint(DRMUtil.DEFAULT_HINT);
			user.setName(rent.getUserEmail().toLowerCase());
			user.setPassword(DRMUtil.getDefaultHintPasswd(rent.getUserEmail().toLowerCase()));
			user.setUuid(DRMUtil.getRandomUUID());
			authUserService.addLcpUser(user);
		}
		RentInfo rentInfo = rent.getRentInfo();
		if (null == rentInfo){
			rentInfo = new RentInfo();
		}
		rentInfo.setUserId(user.getId());
		Map<String,LcpInfo> lcpInfoMap = new HashMap<>();
		Date licenseBeginDate = BookUtil.getRentLicenseBeginDateByRent(rent.getBeginAt());
		Date licenseExpiredDate = BookUtil.getRentLicenseExpiredDateByRent(newExpireDate);
		for(Long bookId:bookIds){
			EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
			if (null == enyanBook){
				continue;
			}
			//DrmInfo drmInfo = new DrmInfo();
			LcpInfo lcpInfo = new LcpInfo();

			//drmInfo.setLcpInfo(lcpInfo);
			Purchase purchase = new Purchase();
			purchase.setEndDate(current);
			purchase.setStartDate(current);
			purchase.setStatus("ok");
			purchase.setPublicationId(Integer.parseInt(enyanBook.getBookDrmRef()));
			purchase.setTransactionDate(current);
			purchase.setType(EBookConstant.DRMPurchaseType.BUY);//租赁和购买都使用相同的标识，只是用结束日期来进行license限制
			purchase.setUserId(user.getId());//LCP的UserId
			purchase.setUuid(UUID.randomUUID().toString());
			try {
				purchaseMapper.insert(purchase);
			}catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

			}
			Publication publication = publicationMapper.selectByPrimaryKey(purchase.getPublicationId());
			if (null == publication){
				continue;
			}

			String licenseIdNew = BookUtil.getRentLicenseByBaseLicense(licenseId,bookId);
			lcpInfo.setPurchseId(purchase.getId());//这个值可能会为空
			//lcpInfo.setUserId(user.getId());//LCP的UserId 直接存储在rentInfo内
			lcpInfo.setPublicationId(purchase.getPublicationId());
			//lcpInfo.setLicenseUuid(licenseIdNew);//直接存储在rentInfo内 licenseId_bookId

			purchaseService.insertLicense(licenseIdNew, user.getUuid(),licenseBeginDate ,licenseExpiredDate,publication.getUuid());
			lcpInfoMap.put(bookId+"", lcpInfo);
		}
		rentInfo.setLcpInfoMap(lcpInfoMap);

		EnyanRent rentUpdate = new EnyanRent();
		rentUpdate.setRentId(rent.getRentId());
		rentUpdate.setBaseLicense(licenseId);
		rentUpdate.setRentDetail(JSON.toJSONString(rentInfo));
		enyanRentMapper.updateByPrimaryKeySelective(rentUpdate);
	}

	@Override
	public void updateDRMLicenseToNewExpiredTime(EnyanRent rent, Date newExpireDate) {
		Date licenseExpiredDate = BookUtil.getRentLicenseExpiredDateByRent(newExpireDate);
		List<Long> bookIds = BookUtil.getBooksInRentType(rent.getRentType(),rent.getRentLang());
		List<String> bookLicenseList = bookIds.stream().map(id -> BookUtil.getRentLicenseByBaseLicense(rent.getBaseLicense(),id)).collect(Collectors.toList());
		//String[] ids = bookLicenseList.toArray(String[]::new);
		String[] ids = bookLicenseList.toArray(new String[bookLicenseList.size()]);
		Date now = new Date();
		enyanRentCustomMapper.updateDRMLicenseToNewExpiredTime(now,licenseExpiredDate,ids);
		enyanRentCustomMapper.updateDRMLicenseStatusToNewExpiredTime(now,licenseExpiredDate,ids);
	}

	@Override
	public int updateRentToExpired(Integer graceDays) {
		return enyanRentCustomMapper.updateRentToExpired(graceDays);
	}

	@Override
	public int updateRent36MonthsYesterdayToExpired() {
		return enyanRentCustomMapper.updateRent36MonthsYesterdayToExpired();
	}

	@Override
	public int updateRecordByExampleClause(EnyanRent record) {
		//EnyanRent update = new EnyanRent();
		EnyanRentExample example = new EnyanRentExample();
		EnyanRentExample.Criteria criteria = example.createCriteria();

		if (null != record.getRentId()){
			criteria.andRentIdEqualTo(record.getRentId());
			record.setRentId(null);
		}

		if (StringUtils.isNotBlank(record.getUserEmail())){
			criteria.andUserEmailEqualTo(record.getUserEmail());
			record.setUserEmail(null);
		}
		if (StringUtils.isNotBlank(record.getOrderNum())){
			criteria.andOrderNumEqualTo(record.getOrderNum());
			record.setOrderNum(null);
		}

		if (null != record.getIsDeleted()){
			criteria.andIsDeletedEqualTo(record.getIsDeleted());
		}

		return enyanRentMapper.updateByExampleSelective(record,example);
	}

	@Override
	public int updateRentManualToExpired(Integer graceDays) {
		return enyanRentCustomMapper.updateRentManualToExpired(graceDays);
	}

	@Override
	public int resetDateToNull() {
		return enyanRentCustomMapper.resetDateToNull();
	}

	@Override
	public List<EnyanRent> findRecordWillExpired(Integer beforeDays) {
		return enyanRentCustomMapper.findRecordWillExpired(beforeDays);
	}

	@Override
	public List<EnyanRent> findRecordAutoWillExpired(Integer beforeDays) {
		return enyanRentCustomMapper.findRecordAutoWillExpired(beforeDays);
	}

	@Override
	public List<EnyanRent> findRecordHasExpired(Integer afterDays) {
		return enyanRentCustomMapper.findRecordHasExpired(afterDays);
	}

	@Override
	public List<EnyanRent> findRecordExpiredToday(Integer totalMonths) {
		return enyanRentCustomMapper.findRecordExpiredToday(totalMonths);
	}

	@Override
	public List<EnyanRent> findRecordHasExpiredGEDaysAndIsValid(Integer afterDays) {
		return enyanRentCustomMapper.findRecordHasExpiredGEDaysAndIsValid(afterDays);
	}

	@Override
	public List<EnyanRent> findRecordExpiredYesterday(Integer totalMonths) {
		return enyanRentCustomMapper.findRecordExpiredYesterday(totalMonths);
	}

	@Override
	public List<EnyanRent> findRecordLeavedYesterday(Integer totalMonths) {
		return enyanRentCustomMapper.findRecordLeavedYesterday(totalMonths);
	}

	@Override
	public List<EnyanRent> findRecordExpiredInDate(String dataAt) {
		return enyanRentCustomMapper.findRecordExpiredInDate(dataAt);
	}

	@Override
	public List<EnyanRent> findRecordHasLeaved(Integer afterDays) {
		return enyanRentCustomMapper.findRecordHasLeaved(afterDays);
	}

	@Override
	public List<EnyanRent> findRecordLeaveInDate(String dataAt) {
		return enyanRentCustomMapper.findRecordLeaveInDate(dataAt);
	}

	@Override
	public ExecuteResult<EnyanRent> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanRent> result = new ExecuteResult<>();
		try {
			EnyanRent record = enyanRentMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanRent();
			}
			if (StringUtils.isNotBlank(record.getRentDetail())){
				RentInfo rentInfo = JSONObject.parseObject(record.getRentDetail(),RentInfo.class);
				record.setRentInfo(rentInfo);
			}else{
				record.setRentInfo(new RentInfo());
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanRent> addRecord(EnyanRent record) {
		ExecuteResult<EnyanRent> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			record.setIsDeleted(0);
			int saveFlag = enyanRentMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanRent> updateRecord(EnyanRent record) {
		ExecuteResult<EnyanRent> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanRentMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = EnyanRentCustomMapper.updateRecordToDeletedById(pkId);
			int deleteFlag = 0;
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanRent record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanRent record) {
		return null;
	}


}
