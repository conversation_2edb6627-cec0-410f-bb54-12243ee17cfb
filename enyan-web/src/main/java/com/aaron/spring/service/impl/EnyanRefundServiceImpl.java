package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.drm.model.DrmInfo;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanRefundMapper;
import com.aaron.spring.mapper.custom.EnyanBookBuyCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.EnyanRefundService;
import com.aaron.util.DateUtil;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/3/25
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanRefundServiceImpl implements EnyanRefundService {
    @Resource
    private EnyanRefundMapper enyanRefundMapper;

    @Resource
    private EnyanBookBuyCustomMapper enyanBookBuyCustomMapper;

    @Override
    public Page queryRecords(Page<EnyanRefund> page, EnyanRefund record) {
        if (null == record){
            record = new EnyanRefund();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanRefundExample example = new EnyanRefundExample();
            EnyanRefundExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            if (StringUtils.isNotBlank(record.getOrderNum())){
                criteria.andOrderNumEqualTo(record.getOrderNum());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (StringUtils.isNotBlank(record.getBookTitle())){
                criteria.andBookTitleEqualTo(record.getBookTitle());
            }
            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }
            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andPurchasedAtGreaterThanOrEqualTo(DateUtil.getDateTimeTrim(record.getStartDate()+"000000"));
            }

            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andPurchasedAtLessThanOrEqualTo(DateUtil.getDateTimeTrim(record.getEndDate()+"235959"));
            }
            criteria.andIsDeletedEqualTo(0);
            //example.setOrderByClause("create_time desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanRefundMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanRefund> list;
            if (count > 0){
                list = enyanRefundMapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanRefund> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanRefund> result = new ExecuteResult<>();
        try {
            EnyanRefund record = enyanRefundMapper.selectByPrimaryKey(pkId);
            if (null == record){
                return result;
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanRefund> addRecord(EnyanRefund record) {
        ExecuteResult<EnyanRefund> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            String orderNum = record.getOrderNum();
            Long bookId = record.getBookId();
            if (StringUtils.isNotBlank(record.getLicenseId())){
                String licenseSql = "update license set rights_end = now() where id = '"+record.getLicenseId()+"'";
                enyanBookBuyCustomMapper.updateRecord(licenseSql);

                licenseSql = "update license_status set status = 4,rights_end = now() where license_ref = '"+record.getLicenseId()+"'";
                enyanBookBuyCustomMapper.updateRecord(licenseSql);
            }

            String buySql = "update enyan_book_buy set is_deleted = 1 where order_num = '"+orderNum+"' and book_id = "+bookId;
            String orderDetailSql = "update enyan_order_detail set is_deleted = 1 where order_num = '"+orderNum+"' and book_id = "+bookId;
            enyanBookBuyCustomMapper.updateRecord(buySql);
            enyanBookBuyCustomMapper.updateRecord(orderDetailSql);

            int saveFlag = enyanRefundMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanRefund> updateRecord(EnyanRefund record) {
        ExecuteResult<EnyanRefund> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanRefundMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            EnyanRefund record = new EnyanRefund();
            record.setRefundId(pkId);
            record.setIsDeleted(1);
            int deleteFlag = enyanRefundMapper.updateByPrimaryKeySelective(record);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanRefund record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanRefund record) {
        return null;
    }

    @Override
    public List<EnyanRefund> findRecordByRefund(EnyanRefund record) {
        if (null == record){
            record = new EnyanRefund();
        }
        try {
            EnyanRefundExample example = new EnyanRefundExample();
            EnyanRefundExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(record.getOrderNum())){
                criteria.andOrderNumEqualTo(record.getOrderNum());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (StringUtils.isNotBlank(record.getBookTitle())){
                criteria.andBookTitleEqualTo(record.getBookTitle());
            }
            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }
            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andPurchasedAtGreaterThanOrEqualTo(DateUtil.getDateTimeTrim(record.getStartDate()));
            }

            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andPurchasedAtLessThanOrEqualTo(DateUtil.getDateTimeTrim(record.getEndDate()));
            }
            criteria.andIsDeletedEqualTo(0);
            //example.setOrderByClause("create_time desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            List<EnyanRefund> list = enyanRefundMapper.selectByExample(example);
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
