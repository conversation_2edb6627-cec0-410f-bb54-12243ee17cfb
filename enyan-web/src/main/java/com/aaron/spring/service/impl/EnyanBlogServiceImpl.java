package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanBlogMapper;
import com.aaron.spring.mapper.custom.EnyanBlogCustomMapper;
import com.aaron.spring.model.DataStat;
import com.aaron.spring.model.EnyanBlog;
import com.aaron.spring.model.EnyanBlogExample;
import com.aaron.spring.service.EnyanBlogService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2022/1/21
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanBlogServiceImpl  implements EnyanBlogService {

	@Resource
	private EnyanBlogMapper enyanBlogMapper;

	@Resource
	private EnyanBlogCustomMapper enyanBlogCustomMapper;

	@Override
	public Page queryRecords(Page<EnyanBlog> page, EnyanBlog record) {
		if (null == record){
			record = new EnyanBlog();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanBlogExample example = new EnyanBlogExample();
			EnyanBlogExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}*/
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			if (null != record.getCategoryId()){
				criteria.andCategoryIdEqualTo(record.getCategoryId());
			}
			if (null != record.getIsDeleted()){
				criteria.andIsDeletedEqualTo(record.getIsDeleted());
			}
			if (StringUtils.isNotBlank(record.getBlogTitle())){
				criteria.andBlogTitleLike("%"+record.getBlogTitle()+"%");
			}
			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanBlogMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanBlog> list;
			if (count > 0){
				list = enyanBlogMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<EnyanBlog> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanBlog> result = new ExecuteResult<>();
		try {
			EnyanBlog record = enyanBlogMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanBlog();
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanBlog> addRecord(EnyanBlog record) {
		ExecuteResult<EnyanBlog> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = enyanBlogMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanBlog> updateRecord(EnyanBlog record) {
		ExecuteResult<EnyanBlog> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanBlogMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = enyanBlogMapper.deleteByPrimaryKey(pkId);
			int deleteFlag = enyanBlogCustomMapper.updateBlogToDeletedById(pkId);
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanBlog record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanBlog record) {
		return null;
	}

	@Override
	public Page queryRecordsBasic(Page<EnyanBlog> page, EnyanBlog record) {
		if (null == record){
			record = new EnyanBlog();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanBlogExample example = new EnyanBlogExample();
			EnyanBlogExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}*/
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			criteria.andIsDeletedEqualTo(0);
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanBlogMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanBlog> list;
			if (count > 0){
				list = enyanBlogMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public int updateBlogLikeCountById(Long id) {
		return enyanBlogCustomMapper.updateBlogLikeCountById(id);
	}

	@Override
	public int updateBlogReadCountById(Long id) {
		return enyanBlogCustomMapper.updateBlogReadCountById(id);
	}

}
