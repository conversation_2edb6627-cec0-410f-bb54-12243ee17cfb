package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.PodFavoriteMapper;
import com.aaron.spring.mapper.custom.PodFavoriteCustomMapper;
import com.aaron.spring.model.PodFavorite;
import com.aaron.spring.model.PodFavoriteExample;
import com.aaron.spring.model.PodFavoriteWithPodcast;
import com.aaron.spring.service.PodFavoriteService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: <PERSON>
 * @Description: 播客收藏服务实现类
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Slf4j
@Service
public class PodFavoriteServiceImpl implements PodFavoriteService {

    @Resource
    private PodFavoriteMapper podFavoriteMapper;
    
    @Resource
    private PodFavoriteCustomMapper podFavoriteCustomMapper;

    @Override
    public Page queryRecords(Page<PodFavorite> page, PodFavorite record) {
        if (null == record){
            record = new PodFavorite();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            PodFavoriteExample example = new PodFavoriteExample();
            PodFavoriteExample.Criteria criteria = example.createCriteria();

            example.setPage(page);

            if (null != record.getUserId()){
                criteria.andUserIdEqualTo(record.getUserId());
            }
            if (null != record.getPodcastId()){
                criteria.andPodcastIdEqualTo(record.getPodcastId());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            
            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                example.setOrderByClause(buffer.toString());
            } else {
                // 默认按收藏时间倒序排序
                example.setOrderByClause("favorited_at DESC");
            }
            
            long count = page.getTotalRecord();
            if (count<=0){
                count = podFavoriteMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<PodFavorite> list;
            if (count > 0){
                list = podFavoriteMapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }

            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<PodFavorite> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<PodFavorite> result = new ExecuteResult<>();
        try {
            PodFavorite record = podFavoriteMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new PodFavorite();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<PodFavorite> addRecord(PodFavorite record) {
        ExecuteResult<PodFavorite> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }

            // 设置默认值
            if (null == record.getFavoritedAt()) {
                record.setFavoritedAt(new Date());
            }

            int saveFlag = podFavoriteMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<PodFavorite> updateRecord(PodFavorite record) {
        ExecuteResult<PodFavorite> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = podFavoriteMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = podFavoriteMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(PodFavorite record) {
        if (null == record) {
            return "收藏数据不能为空";
        }
        if (null == record.getUserEmail()) {
            return "用户email不能为空";
        }
        if (null == record.getPodcastId()) {
            return "播客ID不能为空";
        }
        return null;
    }

    @Override
    public String checkUpdateRecord(PodFavorite record) {
        if (null == record) {
            return "收藏数据不能为空";
        }
        if (null == record.getFavoriteId()) {
            return "收藏ID不能为空";
        }
        return null;
    }

    @Override
    public Page queryUserFavorites(Page<PodFavorite> page, Long userId) {
        if (null == userId){
            page.setErrorMessage("用户ID不能为空");
            return page;
        }
        
        PodFavorite record = new PodFavorite();
        record.setUserId(userId);
        
        // 按收藏时间倒序排序
        OrderObj orderObj = new OrderObj("favorited_at", InterfaceContant.OrderBy.DESC);
        record.addOrder(orderObj);
        
        return this.queryRecords(page, record);
    }
    
    @Override
    public Page queryUserFavoritesByEmail(Page<PodFavorite> page, String userEmail) {
        if (StringUtils.isBlank(userEmail)) {
            page.setErrorMessage("用户邮箱不能为空");
            return page;
        }
        
        PodFavorite record = new PodFavorite();
        record.setUserEmail(userEmail);
        
        // 按收藏时间倒序排序
        OrderObj orderObj = new OrderObj("favorited_at", InterfaceContant.OrderBy.DESC);
        record.addOrder(orderObj);
        
        return this.queryRecords(page, record);
    }
    
    @Override
    public Page<PodFavoriteWithPodcast> queryFavoritesWithPodcastByEmail(Page<PodFavoriteWithPodcast> page, String userEmail) {
        if (StringUtils.isBlank(userEmail)) {
            page.setErrorMessage("用户邮箱不能为空");
            return page;
        }
        
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("userEmail", userEmail);
            
            // 设置分页参数
            if (page == null) {
                page = new Page<>();
            }
            
            // 查询总数
            long count = podFavoriteCustomMapper.countFavoritesWithPodcast(params);
            page.setTotalRecord(count);
            
            if (count > 0) {
                // 查询分页数据
                List<PodFavoriteWithPodcast> list = podFavoriteCustomMapper.queryFavoritesWithPodcast(page, params);
                page.setRecords(list);
            } else {
                page.setRecords(new ArrayList<>());
            }
            
            return page;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询用户收藏的播客列表失败，userEmail={}", userEmail, e);
            page.setErrorMessage("查询失败：" + e.getMessage());
            return page;
        }
    }

    @Override
    public boolean checkUserFavorite(Long userId, Long podcastId) {
        if (null == userId || null == podcastId) {
            return false;
        }
        
        try {
            PodFavoriteExample example = new PodFavoriteExample();
            PodFavoriteExample.Criteria criteria = example.createCriteria();
            criteria.andUserIdEqualTo(userId);
            criteria.andPodcastIdEqualTo(podcastId);
            
            return podFavoriteMapper.countByExample(example) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("检查用户收藏关系失败，userId={}，podcastId={}", userId, podcastId, e);
        }
        
        return false;
    }
    
    @Override
    public boolean checkUserFavorite(String userEmail, Long podcastId) {
        if (StringUtils.isBlank(userEmail) || null == podcastId) {
            return false;
        }
        
        try {
            PodFavoriteExample example = new PodFavoriteExample();
            PodFavoriteExample.Criteria criteria = example.createCriteria();
            criteria.andUserEmailEqualTo(userEmail);
            criteria.andPodcastIdEqualTo(podcastId);
            
            return podFavoriteMapper.countByExample(example) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("检查用户收藏关系失败，userEmail={}，podcastId={}", userEmail, podcastId, e);
        }
        
        return false;
    }

    @Override
    public ExecuteResult<PodFavorite> addFavorite(Long userId, String userEmail, Long podcastId) {
        ExecuteResult<PodFavorite> result = new ExecuteResult<>();
        
        // 检查参数
        if (null == userId || null == podcastId) {
            result.addErrorMessage("用户ID和播客ID不能为空");
            return result;
        }
        
        // 检查是否已收藏
        if (this.checkUserFavorite(userId, podcastId)) {
            result.addErrorMessage("该播客已经收藏过了");
            return result;
        }
        
        // 创建收藏记录
        PodFavorite favorite = new PodFavorite();
        favorite.setUserId(userId);
        favorite.setUserEmail(userEmail);
        favorite.setPodcastId(podcastId);
        favorite.setFavoritedAt(new Date());
        
        return this.addRecord(favorite);
    }

    @Override
    public ExecuteResult<String> cancelFavorite(Long userId, Long podcastId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        
        // 检查参数
        if (null == userId || null == podcastId) {
            result.addErrorMessage("用户ID和播客ID不能为空");
            return result;
        }
        
        try {
            // 查找收藏记录
            PodFavoriteExample example = new PodFavoriteExample();
            PodFavoriteExample.Criteria criteria = example.createCriteria();
            criteria.andUserIdEqualTo(userId);
            criteria.andPodcastIdEqualTo(podcastId);
            
            List<PodFavorite> favorites = podFavoriteMapper.selectByExample(example);
            if (favorites == null || favorites.isEmpty()) {
                result.addErrorMessage("未找到收藏记录");
                return result;
            }
            
            // 删除收藏记录
            int count = podFavoriteMapper.deleteByExample(example);
            if (count > 0) {
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            } else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        
        return result;
    }
    
    @Override
    public ExecuteResult<String> cancelFavoriteByEmail(String userEmail, Long podcastId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        
        // 检查参数
        if (StringUtils.isBlank(userEmail) || null == podcastId) {
            result.addErrorMessage("用户邮箱和播客ID不能为空");
            return result;
        }
        
        try {
            // 查找收藏记录
            PodFavoriteExample example = new PodFavoriteExample();
            PodFavoriteExample.Criteria criteria = example.createCriteria();
            criteria.andUserEmailEqualTo(userEmail);
            criteria.andPodcastIdEqualTo(podcastId);
            
            List<PodFavorite> favorites = podFavoriteMapper.selectByExample(example);
            if (favorites == null || favorites.isEmpty()) {
                result.addErrorMessage("未找到收藏记录");
                return result;
            }
            
            // 删除收藏记录
            int count = podFavoriteMapper.deleteByExample(example);
            if (count > 0) {
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            } else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        
        return result;
    }
    
    @Override
    public ExecuteResult<PodFavorite> addFavoriteByEmail(String userEmail, Long podcastId) {
        ExecuteResult<PodFavorite> result = new ExecuteResult<>();
        
        // 检查参数
        if (StringUtils.isBlank(userEmail) || null == podcastId) {
            result.addErrorMessage("用户邮箱和播客ID不能为空");
            return result;
        }
        
        try {
            // 检查是否已收藏
            PodFavoriteExample example = new PodFavoriteExample();
            PodFavoriteExample.Criteria criteria = example.createCriteria();
            criteria.andUserEmailEqualTo(userEmail);
            criteria.andPodcastIdEqualTo(podcastId);
            
            long count = podFavoriteMapper.countByExample(example);
            if (count > 0) {
                // 已经收藏过，直接返回已存在的记录
                List<PodFavorite> favorites = podFavoriteMapper.selectByExample(example);
                if (favorites != null && !favorites.isEmpty()) {
                    result.setResult(favorites.get(0));
                    result.setSuccessMessage("该播客已经收藏过了");
                    return result;
                }
            }
            
            // 创建收藏记录
            PodFavorite favorite = new PodFavorite();
            favorite.setUserEmail(userEmail);
            favorite.setPodcastId(podcastId);
            favorite.setFavoritedAt(new Date());
            
            // 保存收藏记录
            return this.addRecord(favorite);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
            return result;
        }
    }
}
