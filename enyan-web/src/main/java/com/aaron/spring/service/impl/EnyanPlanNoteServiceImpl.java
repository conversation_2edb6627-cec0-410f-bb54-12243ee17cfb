package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanPlanNoteMapper;
import com.aaron.spring.mapper.custom.EnyanPlanNoteCustomMapper;
import com.aaron.spring.model.EnyanPlanNote;
import com.aaron.spring.model.EnyanPlanNoteExample;
import com.aaron.spring.service.EnyanPlanNoteService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  11/13/20
 * @Modified By:
 */
@Service
public class EnyanPlanNoteServiceImpl implements EnyanPlanNoteService {

    @Autowired
    protected EnyanPlanNoteMapper enyanPlanNoteMapper;

    @Autowired
    protected EnyanPlanNoteCustomMapper enyanPlanNoteCustomMapper;

    @Override
    public Page queryRecords(Page<EnyanPlanNote> page, EnyanPlanNote record) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanPlanNote> queryRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanPlanNote> addRecord(EnyanPlanNote record) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanPlanNote> updateRecord(EnyanPlanNote record) {
        return null;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public String checkSaveRecord(EnyanPlanNote record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanPlanNote record) {
        return null;
    }

    @Override
    public Page findSpiritPlanNotesGTUpdateTime(Page<EnyanPlanNote> page, EnyanPlanNote record) {
        if (null == record){
            record = new EnyanPlanNote();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanPlanNoteExample example = new EnyanPlanNoteExample();
            EnyanPlanNoteExample.Criteria criteria = example.createCriteria();
            example.setPage(page);
            example.setOrderByClause("update_time asc");

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            criteria.andUpdateTimeGreaterThan(record.getUpdateTime());

            List<EnyanPlanNote> list = enyanPlanNoteMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }

        return page;
    }

    @Override
    public int updateSyncPlanNote(List<EnyanPlanNote> list) {
        int result = 0;
        try {

            for (EnyanPlanNote planNote: list){
                /*
                try {
                    result = enyanPlanNoteMapper.insert(planNote) + result;
                }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）
                    result = enyanPlanNoteCustomMapper.updateSyncPlanNote(planNote) + result;
                }*/
                long count = enyanPlanNoteCustomMapper.countByPlanNote(planNote);
                if (count > 0){
                    result = enyanPlanNoteCustomMapper.updateSyncPlanNote(planNote) + result;
                }else{
                    try {
                        result = enyanPlanNoteMapper.insert(planNote) + result;
                    }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）
                    }
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
            //page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> revokeUser(String email, String revokedEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = enyanPlanNoteCustomMapper.deleteAllByEmail(email);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }
}
