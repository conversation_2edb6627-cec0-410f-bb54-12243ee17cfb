package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanUserBookmarksMapper;
import com.aaron.spring.mapper.EnyanUserHighlightsMapper;
import com.aaron.spring.mapper.custom.EnyanUserBookmarksCustomMapper;
import com.aaron.spring.model.EnyanUserBookmarks;
import com.aaron.spring.model.EnyanUserBookmarksExample;
import com.aaron.spring.model.EnyanUserHighlights;
import com.aaron.spring.model.EnyanUserHighlightsExample;
import com.aaron.spring.service.EnyanUserBookmarkService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON> @Date: Created in  2019-09-04
 * @Modified By:
 */
@Service
public class EnyanUserBookmarkServiceImpl extends BaseService<EnyanUserBookmarks, EnyanUserBookmarksExample> implements EnyanUserBookmarkService {

    @Resource
    private EnyanUserBookmarksMapper enyanUserBookmarksMapper;

    @Resource
    private EnyanUserBookmarksCustomMapper enyanUserBookmarksCustomMapper;

    @Override
    public List<EnyanUserBookmarks> findEnyanUserBookmarksList(EnyanUserBookmarks record) {
        if (null == record){
            record = new EnyanUserBookmarks();
        }
        try {
            EnyanUserBookmarksExample example = new EnyanUserBookmarksExample();
            EnyanUserBookmarksExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            criteria.andUpdateTimeGreaterThanOrEqualTo(record.getUpdateTime());

            List<EnyanUserBookmarks> list = enyanUserBookmarksMapper.selectByExample(example);

            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Page findUserBookmarksGTUpdateTime(Page<EnyanUserBookmarks> page, EnyanUserBookmarks record) {
        if (null == record){
            record = new EnyanUserBookmarks();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanUserBookmarksExample example = new EnyanUserBookmarksExample();
            EnyanUserBookmarksExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            example.setOrderByClause("update_time asc");
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            criteria.andUpdateTimeGreaterThan(record.getUpdateTime());

            List<EnyanUserBookmarks> list = enyanUserBookmarksMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanUserBookmarks> queryRecordByPrimaryKey(String pkId) {
        ExecuteResult<EnyanUserBookmarks> result = new ExecuteResult<>();
        try {
            EnyanUserBookmarks record= enyanUserBookmarksMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanUserBookmarks();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<Boolean> addBookmarks(List<EnyanUserBookmarks> list) {
        ExecuteResult<Boolean> result = new ExecuteResult<>();
        for (EnyanUserBookmarks tmp : list){
            if (StringUtils.isBlank(tmp.getUserEmail())){
                continue;
            }
            EnyanUserBookmarks bookmark = enyanUserBookmarksMapper.selectByPrimaryKey(tmp.getBookmarkId());
            if (null == bookmark){
                enyanUserBookmarksCustomMapper.add(tmp);
            }else if (tmp.getIsDeleted() != null && 1 == tmp.getIsDeleted()){
                if (!tmp.getUserEmail().equals(bookmark.getUserEmail())){
                    continue;
                }
                EnyanUserBookmarks newBookmark = new EnyanUserBookmarks();
                newBookmark.setIsDeleted(1);
                newBookmark.setBookmarkId(tmp.getBookmarkId());
                newBookmark.setUpdateTime(tmp.getUpdateTime());
                enyanUserBookmarksMapper.updateByPrimaryKeySelective(newBookmark);
            }
        }
        return result;
    }

    @Override
    public Page queryRecords(Page<EnyanUserBookmarks> page, EnyanUserBookmarks record) {
        if (null == record){
            record = new EnyanUserBookmarks();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanUserBookmarksExample example = new EnyanUserBookmarksExample();
            EnyanUserBookmarksExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanUserBookmarksMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanUserBookmarks> list = enyanUserBookmarksMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanUserBookmarks> queryRecordByPrimaryKey(Long pkId) {
        return null;
    }
}
