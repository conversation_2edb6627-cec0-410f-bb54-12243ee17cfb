package com.aaron.spring.service;

import com.aaron.spring.model.EnyanBalance;
import com.aaron.spring.model.EnyanBalanceExample;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/6/1
 * @Modified By:
 */
public interface EnyanBalanceService extends IService<EnyanBalance,EnyanBalanceExample>{
    /**
     *
     *
     * @param yearMonth yyyyMM
     * @Date: 2018/6/4
     */
    void createEnyanBalanceInfo(int yearMonth);

    List<EnyanBalance> findBalanceByIds(List<Long> balanceIdList,Long publisherId);
}
