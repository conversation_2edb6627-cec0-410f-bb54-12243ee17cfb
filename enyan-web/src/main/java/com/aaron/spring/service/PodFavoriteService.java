package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodFavorite;
import com.aaron.spring.model.PodFavoriteWithPodcast;
import com.aaron.spring.model.PodFavoriteExample;
import com.aaron.util.ExecuteResult;

/**
 * @Author: <PERSON>
 * @Description: 播客收藏服务接口
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
public interface PodFavoriteService extends IService<PodFavorite, PodFavoriteExample> {
    
    /**
     * <p>根据用户ID查询用户收藏的播客列表</p>
     * @param page 分页参数
     * @param userId 用户ID
     * @return com.aaron.mybatis.dao.pojo.Page
     * @since : 2025/5/13
     **/
    Page queryUserFavorites(Page<PodFavorite> page, Long userId);
    
    /**
     * <p>根据用户邮箱查询用户收藏的播客列表</p>
     * @param page 分页参数
     * @param userEmail 用户邮箱
     * @return com.aaron.mybatis.dao.pojo.Page
     * @since : 2025/5/20
     **/
    Page queryUserFavoritesByEmail(Page<PodFavorite> page, String userEmail);
    
    /**
     * <p>根据用户邮箱查询用户收藏的播客列表（包含播客信息）</p>
     * @param page 分页参数
     * @param userEmail 用户邮箱
     * @return com.aaron.mybatis.dao.pojo.Page 包含播客信息的收藏列表
     * @since : 2025/5/20
     **/
    Page<PodFavoriteWithPodcast> queryFavoritesWithPodcastByEmail(Page<PodFavoriteWithPodcast> page, String userEmail);
    
    /**
     * <p>检查用户是否已收藏播客</p>
     * @param userId 用户ID
     * @param podcastId 播客ID
     * @return boolean
     * @since : 2025/5/13
     **/
    boolean checkUserFavorite(Long userId, Long podcastId);
    
    /**
     * <p>通过邮箱检查用户是否已收藏播客</p>
     * @param userEmail 用户邮箱
     * @param podcastId 播客ID
     * @return boolean
     * @since : 2025/5/21
     **/
    boolean checkUserFavorite(String userEmail, Long podcastId);
    
    /**
     * <p>添加播客收藏</p>
     * @param userId 用户ID
     * @param userEmail 用户邮箱
     * @param podcastId 播客ID
     * @return ExecuteResult<PodFavorite>
     * @since : 2025/5/13
     **/
    ExecuteResult<PodFavorite> addFavorite(Long userId, String userEmail, Long podcastId);
    
    /**
     * <p>取消播客收藏</p>
     * @param userId 用户ID
     * @param podcastId 播客ID
     * @return ExecuteResult<String>
     * @since : 2025/5/13
     **/
    ExecuteResult<String> cancelFavorite(Long userId, Long podcastId);
    
    /**
     * <p>通过邮箱取消播客收藏</p>
     * @param userEmail 用户邮箱
     * @param podcastId 播客ID
     * @return ExecuteResult<String>
     * @since : 2025/5/21
     **/
    ExecuteResult<String> cancelFavoriteByEmail(String userEmail, Long podcastId);
    
    /**
     * <p>通过邮箱添加播客收藏</p>
     * @param userEmail 用户邮箱
     * @param podcastId 播客ID
     * @return ExecuteResult<PodFavorite>
     * @since : 2025/5/21
     **/
    ExecuteResult<PodFavorite> addFavoriteByEmail(String userEmail, Long podcastId);
}
