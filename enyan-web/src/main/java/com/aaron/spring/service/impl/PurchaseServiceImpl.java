package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.LicenseStatusMapper;
import com.aaron.spring.mapper.PurchaseMapper;
import com.aaron.spring.model.AuthUser;
import com.aaron.spring.model.LicenseStatus;
import com.aaron.spring.model.Purchase;
import com.aaron.spring.model.PurchaseExample;
import com.aaron.spring.service.PurchaseService;
import com.aaron.util.ExecuteResult;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-09
 * @Modified By:
 */
@Service
public class PurchaseServiceImpl  extends BaseService<Purchase, PurchaseExample> implements PurchaseService {
    @Resource
    protected PurchaseMapper mapper;

    @Resource
    private JdbcTemplate eBookJdbcTemplate;

    @Resource
    protected LicenseStatusMapper licenseStatusMapper;

    private static final String insertLicense = "INSERT INTO license (id, user_id, provider, issued, rights_print, rights_copy,rights_start, rights_end, content_fk, lsd_status) VALUES (?, ?, 'https://ebook.endao.co', ? , 10, 5000, ?,?,?, 201)";

    private static final String selectLicense = "select * from license where user_id = ? and content_fk = ?";
    @Override
    public Page queryRecords(Page<Purchase> page, Purchase record) {
        return null;
    }

    @Override
    public ExecuteResult<Purchase> queryRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public ExecuteResult<Purchase> addRecord(Purchase record) {
        ExecuteResult<Purchase> result = new ExecuteResult<>();
        try {
            //校验保存对象　
//            String checkMsg = this.checkSaveRecord(record);
//            if (StringUtils.isNotBlank(checkMsg)){
//                result.addErrorMessage("保存校验失败："+ checkMsg);
//                return result;
//            }
            int saveFlag=0;
            try {
                saveFlag = mapper.insert(record);
                if (saveFlag>0){
                    result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                    result.setResult(record);
                }else {
                    result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
                }
            } catch (DuplicateKeyException e) {
                //result.addErrorMessage("合同名称不能重复");
                /*
                if (saveFlag>0){
                    result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                    result.setResult(record);
                }else {
                    result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
                }*/
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }


    /**
     * <p>插入license信息</p>
     * @param id
     * @param userId
     * @param contentFk
     * @return void
     * @since : 2021/8/30
     **/
    @Override
    public void insertLicense(String id, String userId, Date start, Date end, String contentFk){
        Date date = new Date();
        //"INSERT INTO license (id, user_id, provider, issued, rights_print, rights_copy, content_fk, lsd_status) VALUES (?, ?, 'https://ebook.endao.co', ? , 10, 5000, ?, 201)";
        int saveFlag = eBookJdbcTemplate.update(insertLicense,((PreparedStatement ps)->{
            ps.setString(1,id);//id
            ps.setString(2,userId);//user_id
            ps.setTimestamp(3,new Timestamp(date.getTime()));//issued
            ps.setTimestamp(4,new Timestamp(start.getTime()));//rightStart
            ps.setTimestamp(5,new Timestamp(end.getTime()));//rightEnd
            ps.setString(6,contentFk);//, content_fk

        }));

        LicenseStatus licenseStatus = new LicenseStatus();
        licenseStatus.setLicenseRef(id);
        licenseStatus.setStatus(1);//默认是1，有设备下载后就是2
        licenseStatus.setLicenseUpdated(date);
        licenseStatus.setStatusUpdated(date);
        licenseStatus.setDeviceCount(0);
        licenseStatus.setPotentialRightsEnd(end);

        licenseStatusMapper.insert(licenseStatus);
    }

    @Override
    public List<String> selectLicenseByUserIdAndBookContent(String userId, String contentPK) {
        final List<String> listUser = new ArrayList();

        eBookJdbcTemplate.query(selectLicense, ((ResultSet resultSet)->{
            String id = resultSet.getString("id");

            listUser.add(id);
        }), userId,contentPK);

        return listUser;
    }
}
