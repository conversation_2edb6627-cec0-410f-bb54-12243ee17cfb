package com.aaron.spring.service;

import com.aaron.spring.model.EnyanCategory;
import com.aaron.spring.model.EnyanCategoryExample;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/2
 * @Modified By:
 */
public interface EnyanCategoryService extends IService<EnyanCategory,EnyanCategoryExample>{
    /**
     * 初始化 Categories
     * */
    void initCategories();
    /**
     * 获取 Categories
     * */
    List<EnyanCategory> findCategories(EnyanCategory record);
}
