package com.aaron.spring.service;

import com.aaron.spring.model.EnyanDailyWords;
import com.aaron.spring.model.EnyanDiscount;
import com.aaron.spring.model.EnyanDiscountExample;
import com.aaron.spring.model.EnyanPublisher;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/17
 * @Modified By:
 */
public interface EnyanDiscountService extends IService<EnyanDiscount,EnyanDiscountExample>{
    /**
     * 初始化 Discounts
     * */
    void initDiscounts();
    /**
     * 获取 Discounts
     * */
    List<EnyanDiscount> findDiscounts(EnyanDiscount enyanDiscount);

    ExecuteResult<EnyanDiscount> updateRecordOnly(EnyanDiscount record);
}
