package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.NameAndValue;
import com.aaron.data.DataInterface;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.EnyanPublisherMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.EnyanPublisherService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 *
 * @Author: Aaron <PERSON>
 * @Date: Created in  2017/11/2
 * @Modified By:
 */
@Service
public class EnyanPublisherServiceImpl extends BaseService<EnyanPublisher,EnyanPublisherExample> implements EnyanPublisherService{

    @Resource
    protected EnyanPublisherMapper enyanPublisherMapper;

    @Resource
    private DataInterface dataInterface;

    @Override
    public Page queryRecords(Page<EnyanPublisher> page, EnyanPublisher record) {
        if (null == record){
            record = new EnyanPublisher();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanPublisherExample example = new EnyanPublisherExample();
            EnyanPublisherExample.Criteria criteria = example.createCriteria();

            example.setPage(page);

            if (StringUtils.isNotBlank(record.getPublisherName())){
                criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanPublisherMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanPublisher> list;
            if (count > 0){
                list = enyanPublisherMapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }

            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanPublisher> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanPublisher> result = new ExecuteResult<>();
        try {
            EnyanPublisher publisher = dataInterface.getPublisherByID(pkId);
            if (null != publisher){
                result.setResult(publisher);
                return result;
            }
            EnyanPublisher record = enyanPublisherMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanPublisher();
            }
            if (StringUtils.isNotBlank(record.getMiscConfig())){
                PublisherInfo publisherInfo = JSONObject.parseObject(record.getMiscConfig(), PublisherInfo.class);
                record.setPublisherInfo(publisherInfo);
            }
            result.setResult(record);
            dataInterface.savePublisher(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanPublisher> addRecord(EnyanPublisher record) {
        ExecuteResult<EnyanPublisher> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanPublisherMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
                this.initPublishers();
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanPublisher> updateRecord(EnyanPublisher record) {
        ExecuteResult<EnyanPublisher> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanPublisherMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
                this.initPublishers();
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
            dataInterface.delPublisher(record.getPublisherId());
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = enyanPublisherMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
                this.initPublishers();
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
            dataInterface.delPublisher(pkId);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanPublisher record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (StringUtils.isBlank(record.getPublisherName())){
            sb.append("出版商不可以为空");
            return sb.toString();
        }

        if (sb.length()>0){
            return sb.toString();
        }
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanPublisher record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (record.getPublisherId()==0){
            sb.append("主键信息不可以为空");
            return sb.toString();
        }
        return null;
    }
    @Override
    public void initPublishers() {
        List<EnyanPublisher> list = this.findPublishers(null);

        List<NameAndValue> valueAndNameList = new ArrayList<>();

        for (EnyanPublisher publisher:list){
            NameAndValue valueAndName = new NameAndValue();
            valueAndName.setValue(String.valueOf(publisher.getPublisherId()));
            valueAndName.setName(publisher.getPublisherName());
            valueAndName.setOther(publisher.getPublisherNameTc());
            valueAndName.setThird(publisher.getPublisherNameEn());

            valueAndNameList.add(valueAndName);
        }
        Constant.publishersList = valueAndNameList;
    }

    @Override
    public List<EnyanPublisher> findPublishers(EnyanPublisher record) {
        if (null == record){
            record = new EnyanPublisher();
        }
        List<EnyanPublisher> list = null;
        try {
            EnyanPublisherExample example = new EnyanPublisherExample();
            EnyanPublisherExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(record.getPublisherName())){
                criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
            }

            list = enyanPublisherMapper.selectByExample(example);


        } catch (Exception e) {
            e.printStackTrace();
//            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return list;
    }

    @Override
    public List<EnyanPublisher> findPublishersReally(EnyanPublisher enyanPublisher) {
        if (null == enyanPublisher){
            enyanPublisher = new EnyanPublisher();
        }
        List<EnyanPublisher> list = null;
        try {
            EnyanPublisherExample example = new EnyanPublisherExample();
            EnyanPublisherExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(enyanPublisher.getPublisherName())){
                criteria.andPublisherNameLike(enyanPublisher.getPublisherName());
            }

            list = enyanPublisherMapper.selectByExample(example);


        } catch (Exception e) {
            e.printStackTrace();
//            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return list;
    }

    @Override
    public String getPublisherNameByID(Long publisherId) {
        if (null != publisherId){
            String publisherIdStr = String.valueOf(publisherId);
            List<NameAndValue> publisherList = Constant.publishersList;
            for (NameAndValue valueAndName:publisherList){
                if (valueAndName.getValue().equals(publisherIdStr)){
                    return valueAndName.getName();
                }
            }
        }

        // All we have is a specified literal text.
        return "";
    }
}
