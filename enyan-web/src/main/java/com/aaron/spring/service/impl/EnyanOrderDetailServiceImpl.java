package com.aaron.spring.service.impl;

import com.aaron.a4j.util.AaronDateUtils;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.Money;
import com.aaron.common.OrderObj;
import com.aaron.drm.model.*;
import com.aaron.drm.util.DRMUtil;
import com.aaron.http.HttpCredentials;
import com.aaron.http.HttpMethod;
import com.aaron.http.HttpProtocolHandler;
import com.aaron.http.HttpResult;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.AaronJF;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.mapper.*;
import com.aaron.spring.mapper.custom.EnyanOrderCustomMapper;
import com.aaron.spring.mapper.custom.EnyanOrderDetailCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.model.User;
import com.aaron.spring.service.*;
import com.aaron.spring.common.OrderUtil;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCallback;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2018/4/23
 * @Modified By:
 */
@Service
public class EnyanOrderDetailServiceImpl implements EnyanOrderDetailService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private JdbcTemplate eBookJdbcTemplate;

    @Resource
    private EnyanOrderMapper enyanOrderMapper;

    @Resource
    private EnyanOrderDetailMapper enyanOrderDetailMapper;

    @Resource
    private EnyanOrderCustomMapper enyanOrderCustomMapper;

    @Resource
    private EnyanOrderDetailCustomMapper enyanOrderDetailCustomMapper;

    @Resource
    private EnyanPayRateService enyanPayRateService;

    @Resource
    private EnyanPublisherService enyanPublisherService;

    //@Resource
    //private PurchaseService purchaseService;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private AuthUserService authUserService;

    @Resource
    private EnyanRedeemCodeMapper enyanRedeemCodeMapper;

    @Resource
    private EnyanBookBuyMapper enyanBookBuyMapper;

    @Resource
    protected PurchaseMapper purchaseMapper;

    @Resource
    protected LicenseStatusMapper licenseStatusMapper;

    @Resource
    protected PublicationMapper publicationMapper;

    private Map<String,EnyanPayRate> rateMap = new HashMap<>();

    private static final BigDecimal PAY_PERCENT_COST = new BigDecimal("95.6");//扣除的支付费用 money*(1-4.4%)-2.35

    private static final BigDecimal PAY_REGULAR_COST = new BigDecimal("10.35");//扣除的支付费用 2.35+8(下载费用)

    private static final String insertLicense = "INSERT INTO license (id, user_id, provider, issued, rights_print, rights_copy, content_fk, lsd_status) VALUES (?, ?, 'https://ebook.endao.co', ? , 10, 5000, ?, 201)";

    @Override
    public void splitOrders(Date endTime) {
        try {
            //EnyanOrder record = new EnyanOrder();

            EnyanOrderExample example = new EnyanOrderExample();
            EnyanOrderExample.Criteria criteria = example.createCriteria();

            criteria.andPurchasedAtLessThan(endTime);
            criteria.andIsCountedEqualTo(Constant.BYTE_VALUE_0);
            criteria.andIsPaidEqualTo(Constant.BYTE_VALUE_1);
            criteria.andIsValidEqualTo(Constant.BYTE_VALUE_1);
            criteria.andIsDeletedEqualTo(0);

            List<EnyanOrder> list = enyanOrderMapper.selectByExampleWithBLOBs(example);

            //Date currentDate = new Date();
//            if (!list.isEmpty()){
//                for (EnyanOrder enyanOrder:list){
//
//                }
//            }

            for (EnyanOrder enyanOrder:list){
                this.splitOrder(enyanOrder);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        rateMap.clear();//清空map
    }

    @Override
    public void splitOrder(EnyanOrder enyanOrder){
        this.splitOrder(enyanOrder,true);
    }

    @Override
    public void updateOrderDetailFee() {
        try {
            //EnyanOrder record = new EnyanOrder();
            EnyanOrderExample example = new EnyanOrderExample();
            EnyanOrderExample.Criteria criteria = example.createCriteria();

            criteria.andIsPaidEqualTo(Constant.BYTE_VALUE_1);
            criteria.andIsValidEqualTo(Constant.BYTE_VALUE_1);

            List<EnyanOrder> list = enyanOrderMapper.selectByExampleWithBLOBs(example);

            for (EnyanOrder enyanOrder:list){
                if (StringUtils.isNotBlank(enyanOrder.getOrderDetail())){
                    OrderDetailInfo orderDetailInfo = JSON.parseObject(enyanOrder.getOrderDetail(),OrderDetailInfo.class);
                    orderDetailInfo.resetFromJson();
                    enyanOrder.setOrderDetailInfo(orderDetailInfo);
                }

                if (StringUtils.isNotBlank(enyanOrder.getPayInfo())){
                    OrderPayInfo orderPayInfo = JSON.parseObject(enyanOrder.getPayInfo(),OrderPayInfo.class);
                    enyanOrder.setOrderPayInfo(orderPayInfo);
                }

                BigDecimal payFee = this.getPayFee(enyanOrder);
                List<BigDecimal> everyPayFeeList = this.getEveryPayFeeList(enyanOrder.getOrderDetailInfo().getProductInfoList(),enyanOrder);//产品的支付费用列表
                for (int i = 0; i < enyanOrder.getOrderDetailInfo().getProductInfoList().size(); i++) {
                    ProductInfo productInfo = enyanOrder.getOrderDetailInfo().getProductInfoList().get(i);
                    EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(productInfo.getCode()).getResult();
                    if (null == enyanBook){
                        continue;
                    }

                    EnyanOrderDetail orderDetail = new EnyanOrderDetail();
                    orderDetail.setBookId(productInfo.getCode());
                    orderDetail.setOrderNum(enyanOrder.getOrderNum());

                    orderDetail.setSalesModel(enyanBook.getSalesModel());
                    orderDetail.setOrderType(enyanOrder.getOrderType());

                    orderDetail.setPayFee(everyPayFeeList.get(i)); //支付时的费用
                    orderDetail.setVendorPercent(enyanBook.getVendorPercent()); //版税费率

                    EnyanOrderDetail orderDetailNew = this.getCalcOrder(productInfo,orderDetail);
                    orderDetail.setPriceFixed(orderDetailNew.getPriceFixed());
                    orderDetail.setPriceSelling(orderDetailNew.getPriceSelling());
                    orderDetail.setIncomeTotal(orderDetailNew.getIncomeTotal());
                    orderDetail.setIncomeReal(orderDetailNew.getIncomeReal());
                    orderDetail.setIncomeVendor(orderDetailNew.getIncomeVendor());
                    orderDetail.setIncomePlat(orderDetailNew.getIncomePlat());
                    orderDetail.setNetSales(orderDetailNew.getNetSales());

                    enyanOrderDetailCustomMapper.updateByOrderAndBookKeySelective(orderDetail);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public ExecuteResult<String> revokeUser(String email, String revokedEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = enyanOrderDetailCustomMapper.revokeUser(email, revokedEmail);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    private void splitOrder(EnyanOrder enyanOrder, Boolean updateOrderCount){
        if (StringUtils.isNotBlank(enyanOrder.getOrderDetail())){
            OrderDetailInfo orderDetailInfo = JSON.parseObject(enyanOrder.getOrderDetail(),OrderDetailInfo.class);
            orderDetailInfo.resetFromJson();
            enyanOrder.setOrderDetailInfo(orderDetailInfo);
        }

        if (StringUtils.isNotBlank(enyanOrder.getPayInfo())){
            OrderPayInfo orderPayInfo = JSON.parseObject(enyanOrder.getPayInfo(),OrderPayInfo.class);
            enyanOrder.setOrderPayInfo(orderPayInfo);
        }

        String dateStr = DateFormatUtils.format(enyanOrder.getPurchasedAt(),"yyyyMMdd");
        int dateInt = Integer.parseInt(dateStr);

        //String rateValue = this.getRateValue(dateInt,enyanOrder.getPurchasedAt());
        /*String rateValue = "";
        BigDecimal payFee = this.getPayFee(enyanOrder);
        BigDecimal avgPayFee = this.getAvgPayFee(enyanOrder,payFee);
        List<BigDecimal> allPastPayFee = new ArrayList<>();*/

        List<BigDecimal> payFeeList = this.getEveryPayFeeList(enyanOrder.getOrderDetailInfo().getProductInfoList(),enyanOrder);
        for (int i = 0; i < enyanOrder.getOrderDetailInfo().getProductInfoList().size(); i++) {
            ProductInfo productInfo = enyanOrder.getOrderDetailInfo().getProductInfoList().get(i);
            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(productInfo.getCode()).getResult();
            if (null == enyanBook){
                continue;
            }

            if (EBookConstant.BookType.EBOOK_SET == enyanBook.getBookType()){//电子书套装
                this.splitEBookSetToDB(enyanOrder,enyanBook,dateInt);
            }else {//电子书单本
                this.splitEBookSingleToDB(enyanOrder,enyanBook,productInfo,payFeeList,dateInt,i);
            }

            /*BigDecimal everyFee = this.getEveryPayFee(enyanOrder.getOrderDetailInfo().getProductInfoList(),enyanOrder.getOrderPayInfo().getCharge().getPayType(), payFee, avgPayFee, i, allPastPayFee);
            allPastPayFee.add(everyFee);
        //}
        //for (ProductInfo productInfo : enyanOrder.getOrderDetailInfo().getProductInfoList()){
            EnyanOrderDetail orderDetail = new EnyanOrderDetail();
            orderDetail.setBookId(productInfo.getCode());
            orderDetail.setBookTitle(productInfo.getName());
            orderDetail.setOrderCurrency(enyanOrder.getOrderCurrency());
            orderDetail.setOrderNum(enyanOrder.getOrderNum());

            orderDetail.setSalesModel(enyanBook.getSalesModel());
            orderDetail.setPayCountry(enyanOrder.getOrderPayInfo().getCharge().getCountry());//支付的国家
            orderDetail.setPayType(enyanOrder.getOrderPayInfo().getCharge().getPayType());
            orderDetail.setOrderType(enyanOrder.getOrderType());

            orderDetail.setPayFee(everyFee); //支付时的费用
            orderDetail.setVendorPercent(enyanBook.getVendorPercent()); //版税费率

            EnyanOrderDetail orderDetailNew = this.getCalcOrder(productInfo,orderDetail);
            orderDetail.setPriceFixed(orderDetailNew.getPriceFixed());
            orderDetail.setPriceSelling(orderDetailNew.getPriceSelling());
            orderDetail.setIncomeTotal(orderDetailNew.getIncomeTotal());
            orderDetail.setIncomeReal(orderDetailNew.getIncomeReal());
            orderDetail.setIncomeVendor(orderDetailNew.getIncomeVendor());
            orderDetail.setIncomePlat(orderDetailNew.getIncomePlat());
            orderDetail.setNetSales(orderDetailNew.getNetSales());

            orderDetail.setPurchasedAt(enyanOrder.getPurchasedAt());

            orderDetail.setQuantity(1);
            orderDetail.setPurchasedDay(dateInt);
            orderDetail.setUserEmail(enyanOrder.getUserEmail());
            orderDetail.setUserId(enyanOrder.getUserId());
            orderDetail.setIsCounted(EBookContant.BalanceStatus.NOT);
            orderDetail.setPublisherId(productInfo.getPublisherId());
            orderDetail.setBookPubCode(productInfo.getBookPubCode());
            orderDetail.setBookEsin(productInfo.getBookEsin());
            orderDetail.setRateValue(rateValue);


            //新购买的书籍，后边直接insert数据了，所以不需要再更新DRM Info的信息
            this.addDrmInfoAndPurchaseInfoToOrderDetail(orderDetail,false);
                    *//*if (null == productInfo.getEbookFormat()){
                        orderDetail.setEbookFormat(Constant.BYTE_VALUE_1);
                    }else {
                        orderDetail.setEbookFormat(productInfo.getEbookFormat());
                    }*//*
            try {
                enyanOrderDetailMapper.insert(orderDetail);
            }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

            }*/
        }
        if (updateOrderCount){
            enyanOrderCustomMapper.updateOrderCounted(enyanOrder.getOrderId());
        }
    }
    /**
     * <p>分离电子书的套装到数据库</p>
     * @param enyanOrder
     * @param enyanBookSet
     * @param payFeeList
     * @param dateInt
     * @return: void
     * @since : 2020-07-21
     */
    private void splitEBookSetToDB(EnyanOrder enyanOrder, EnyanBook enyanBookSet, Integer dateInt){
        List<ProductInfo> productInfoList = this.getProductListByBookSet(enyanBookSet,enyanOrder.getOrderTotal());
        List<BigDecimal> payFeeList = this.getEveryPayFeeList(productInfoList,enyanOrder);
        for (int i = 0; i < productInfoList.size(); i++) {
            ProductInfo productInfo = productInfoList.get(i);
            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(productInfo.getCode()).getResult();
            if (null == enyanBook){
                continue;
            }
            this.splitEBookSingleToDB(enyanOrder,enyanBook,productInfo,payFeeList,dateInt,i);
        }
    }

    /**
     * <p>分离电子书的单本到数据库</p>
     * @param enyanOrder
     * @param enyanBook
     * @param productInfo
     * @param payFeeList
     * @param dateInt
     * @param index
     * @return: void
     * @since : 2020-07-21
     */
    private void splitEBookSingleToDB(EnyanOrder enyanOrder, EnyanBook enyanBook, ProductInfo productInfo, List<BigDecimal> payFeeList, Integer dateInt, Integer index){
        //}
        //for (ProductInfo productInfo : enyanOrder.getOrderDetailInfo().getProductInfoList()){
        EnyanOrderDetail orderDetail = new EnyanOrderDetail();
        orderDetail.setBookId(productInfo.getCode());
        orderDetail.setBookTitle(productInfo.getName());
        orderDetail.setOrderCurrency(enyanOrder.getOrderCurrency());
        orderDetail.setOrderNum(enyanOrder.getOrderNum());

        orderDetail.setSalesModel(enyanBook.getSalesModel());
        orderDetail.setPayCountry(enyanOrder.getOrderPayInfo().getCharge().getCountry());//支付的国家
        orderDetail.setPayType(enyanOrder.getOrderPayInfo().getCharge().getPayType());
        orderDetail.setOrderType(enyanOrder.getOrderType());

        orderDetail.setPayFee(payFeeList.get(index)); //支付时的费用
        orderDetail.setVendorPercent(enyanBook.getVendorPercent()); //版税费率

        EnyanOrderDetail orderDetailNew = this.getCalcOrder(productInfo,orderDetail);
        orderDetail.setPriceFixed(orderDetailNew.getPriceFixed());
        orderDetail.setPriceSelling(orderDetailNew.getPriceSelling());
        orderDetail.setIncomeTotal(orderDetailNew.getIncomeTotal());
        orderDetail.setIncomeReal(orderDetailNew.getIncomeReal());
        orderDetail.setIncomeVendor(orderDetailNew.getIncomeVendor());
        orderDetail.setIncomePlat(orderDetailNew.getIncomePlat());
        orderDetail.setNetSales(orderDetailNew.getNetSales());

        orderDetail.setPurchasedAt(enyanOrder.getPurchasedAt());

        orderDetail.setQuantity(1);
        orderDetail.setPurchasedDay(dateInt);
        orderDetail.setUserEmail(enyanOrder.getUserEmail());
        orderDetail.setUserId(enyanOrder.getUserId());
        orderDetail.setIsCounted(EBookConstant.BalanceStatus.NOT);
        orderDetail.setPublisherId(productInfo.getPublisherId());
        orderDetail.setBookPubCode(productInfo.getBookPubCode());
        orderDetail.setBookEsin(productInfo.getBookEsin());
        orderDetail.setRateValue("");
        orderDetail.setIsDeleted(0);


        //新购买的书籍，后边直接insert数据了，所以不需要再更新DRM Info的信息
        this.addDrmInfoAndPurchaseInfoToOrderDetail(orderDetail,false);
                    /*if (null == productInfo.getEbookFormat()){
                        orderDetail.setEbookFormat(Constant.BYTE_VALUE_1);
                    }else {
                        orderDetail.setEbookFormat(productInfo.getEbookFormat());
                    }*/
        try {
            enyanOrderDetailMapper.insert(orderDetail);
        }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

        }
    }


    /**
     * <p>根据产品信息分离订单明细</p>
     * @param enyanBook
     * @return: void
     * @since : 2020-07-20
     */
    private void splitOrderByBookSet(EnyanBook enyanBook, EnyanOrder enyanOrder){

    }
    /**
     * <p>根据套装书重新生成ProductInfoList，主要是为了重新生成每本书优惠后新的价格
     * 套装书里的新价格，是按照 书的原价在总价中的比例来计算 2020-09-08
     * </p>
     * @param enyanBook
     * @param totalRealNewFee 真实新的最终价格
     * @return: java.util.List<com.aaron.spring.model.ProductInfo>
     * @since : 2020-07-20
     */
    private List<ProductInfo> getProductListByBookSet(EnyanBook enyanBook, BigDecimal totalRealNewFee){
        List<ProductInfo> productInfoList = new ArrayList<>();
        if (EBookConstant.BookType.EBOOK_SET != enyanBook.getBookType()){
            return productInfoList;
        }
        /*
        if (!StringUtils.isEmpty(enyanBook.getSetInfo())){
            BookSetInfo bookSetInfo = JSONObject.parseObject(enyanBook.getSetInfo(), BookSetInfo.class);
            if (null == bookSetInfo.getBooksToSetList() || bookSetInfo.getBooksToSetList().isEmpty()){
                return productInfoList;
            }
            List<Long> bookIds = new ArrayList<>();
            for (EnyanBook tmp:bookSetInfo.getBooksToSetList()){
                bookIds.add(tmp.getBookId());
            }
            List<EnyanBook> bookList = this.enyanBookService.findBookByIds(bookIds);
            BigDecimal totalRealOldFee = new BigDecimal("0");//单本书的旧的原定价总数
            BigDecimal pastFee = new BigDecimal("0");//已经累计的价格
            for (EnyanBook tmp: bookList){
                ProductInfo productInfo = new ProductInfo(tmp);
                totalRealOldFee = totalRealOldFee.add(productInfo.getPriceHkd());
                productInfoList.add(productInfo);
            }

            for (int i = 0; i < productInfoList.size(); i++) {
                ProductInfo productInfo = productInfoList.get(i);
                BigDecimal newPrice = productInfo.getPriceHkd().multiply(totalRealNewFee).divide(totalRealOldFee, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
                if (i == (productInfoList.size() - 1)){//最后一个直接使用减法
                    productInfo.setPriceHKDDiscount(totalRealNewFee.subtract(pastFee));
                }else{
                    pastFee = pastFee.add(newPrice);
                    productInfo.setPriceHKDDiscount(newPrice);
                }
            }
        }*/
        return productInfoList;
    }

    @Override
    public EnyanOrder saveOrderFromRedeem(EnyanOrder enyanOrder, EnyanRedeemCode enyanRedeemCodeToUpdate) {
        enyanOrder.setIsDeleted(0);
        int flag = this.enyanOrderMapper.insert(enyanOrder);
        if (flag > 0){
            //this.splitOrder(enyanOrder,false);//兑换码直接就split订单了，所以不需要再更新Count，兑换码兑换的订单不需要放在明细里
            enyanRedeemCodeToUpdate.getRedeemCodeNoteInfo().setOrderIdToRedeem(enyanOrder.getOrderId());
            enyanRedeemCodeToUpdate.getRedeemCodeNoteInfo().setOrderNumToRedeem(enyanOrder.getOrderNum());
            enyanRedeemCodeToUpdate.setNote(JSON.toJSONString(enyanRedeemCodeToUpdate.getRedeemCodeNoteInfo()));
            this.enyanRedeemCodeMapper.updateByPrimaryKeySelective(enyanRedeemCodeToUpdate);
        }
        return enyanOrder;
    }


    @Override
    public Page findRecordsByOrder(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanOrderDetailExample example = new EnyanOrderDetailExample();
            EnyanOrderDetailExample.Criteria criteria = example.createCriteria();

            example.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            if (null != record.getUserId()){
                criteria.andUserIdEqualTo(record.getUserId());
            }
            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (StringUtils.isNotBlank(record.getOrderNum())){
                criteria.andOrderNumEqualTo(record.getOrderNum());
            }

            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andPurchasedDayGreaterThanOrEqualTo(Integer.parseInt(record.getStartDate()));
            }

            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andPurchasedDayLessThanOrEqualTo(Integer.parseInt(record.getEndDate()));
            }

            if (StringUtils.isNotBlank(record.getBookTitle())){
                String searchText = record.getBookTitle();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andBookTitleRegexp(searchText);
            }
            criteria.andIsDeletedEqualTo(0);

            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                example.setOrderByClause(buffer.toString());
            }
            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanOrderDetailMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanOrderDetail> list;
            if (count > 0){
                list = enyanOrderDetailMapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanOrderDetail> findRecordsByOrder(EnyanOrderDetail record) {
        try {
            EnyanOrderDetailExample example = new EnyanOrderDetailExample();
            EnyanOrderDetailExample.Criteria criteria = example.createCriteria();
//            example.setOrderByClause("purchased_at desc");

            if (null != record.getUserId()){
                criteria.andUserIdEqualTo(record.getUserId());
            }
            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }
            if (StringUtils.isNotBlank(record.getOrderNum())){
                criteria.andOrderNumEqualTo(record.getOrderNum());
            }

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }

            if (null != record.getBookId()){
                criteria.andBookIdEqualTo(record.getBookId());
            }

            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andPurchasedDayGreaterThanOrEqualTo(Integer.parseInt(record.getStartDate()));
            }

            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andPurchasedDayLessThanOrEqualTo(Integer.parseInt(record.getEndDate()));
            }

            if (null != record.getOrderType()){
                criteria.andOrderTypeEqualTo(record.getOrderType());
            }

            if (StringUtils.isNotBlank(record.getBookTitle())){
                String searchText = record.getBookTitle();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andBookTitleRegexp(searchText);
            }

            criteria.andIsDeletedEqualTo(0);

            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                example.setOrderByClause(buffer.toString());
            }

            List<EnyanOrderDetail> list = enyanOrderDetailMapper.selectByExample(example);

           return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public Page queryRecordsByBook(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            if (StringUtils.isNotBlank(record.getBookTitle())){
                String searchText = record.getBookTitle();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                record.setBookTitle(searchText);
            }else {
                record.setBookTitle(null);
            }
            record.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanOrderDetailCustomMapper.countByBook(record);
                page.setTotalRecord(count);
            }
            List<EnyanOrderDetail> list;
            if (count > 0){
                list = enyanOrderDetailCustomMapper.queryOrderDetailByBook(record);
            }else {
                list = new ArrayList<>();
            }

            for (EnyanOrderDetail enyanOrderDetail:list){
                BigDecimal grossProfit = enyanOrderDetail.getIncomeTotal().subtract(enyanOrderDetail.getPayFee())
                        .subtract(enyanOrderDetail.getIncomeVendor());
                enyanOrderDetail.setGrossProfit(grossProfit);
                if (enyanOrderDetail.getIncomeTotal().doubleValue() > 0){
                    BigDecimal grossProfitMargin = grossProfit.divide(enyanOrderDetail.getIncomeTotal(), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
                    enyanOrderDetail.setGrossProfitMargin(grossProfitMargin);
                }else {
                    enyanOrderDetail.setGrossProfitMargin(new BigDecimal("0"));
                }
            }
            //Date currentDate = new Date();
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanOrderDetail> queryRecordsByBook(EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        try {
            if (StringUtils.isNotBlank(record.getBookTitle())){
                String searchText = record.getBookTitle();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                record.setBookTitle(searchText);
            }else {
                record.setBookTitle(null);
            }
            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByBook(record);
            for (EnyanOrderDetail enyanOrderDetail:list){
                BigDecimal grossProfit = enyanOrderDetail.getIncomeTotal().subtract(enyanOrderDetail.getPayFee())
                        .subtract(enyanOrderDetail.getIncomeVendor());
                enyanOrderDetail.setGrossProfit(grossProfit);
                if (enyanOrderDetail.getIncomeTotal().doubleValue() > 0){
                    BigDecimal grossProfitMargin = grossProfit.divide(enyanOrderDetail.getIncomeTotal(), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
                    enyanOrderDetail.setGrossProfitMargin(grossProfitMargin);
                }else {
                    enyanOrderDetail.setGrossProfitMargin(new BigDecimal("0"));
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public Page queryRecordsByVendor(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {

            record.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanOrderDetailCustomMapper.countByVendor(record);
                page.setTotalRecord(count);
            }
            List<EnyanOrderDetail> list;
            if (count > 0){
                list = enyanOrderDetailCustomMapper.queryOrderDetailByVendor(record);
            }else {
                list = new ArrayList<>();
            }

            //Date currentDate = new Date();
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanOrderDetail> queryRecordsByVendor(EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        try {
            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByVendor(record);

            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public Page searchRecordsByUser(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanOrderDetailExample example = new EnyanOrderDetailExample();
            EnyanOrderDetailExample.Criteria criteria = example.createCriteria();

            example.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            if (null != record.getUserId()){
                criteria.andUserIdEqualTo(record.getUserId());
            }
            if (null != record.getBookId()){
                criteria.andBookIdEqualTo(record.getBookId());
            }
            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (StringUtils.isNotBlank(record.getOrderNum())){
                criteria.andOrderNumEqualTo(record.getOrderNum());
            }

            if (StringUtils.isNotBlank(record.getBookTitle())){
                String searchText = record.getBookTitle();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andBookTitleRegexp(searchText);
            }

            criteria.andIsDeletedEqualTo(0);

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanOrderDetailMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanOrderDetail> list;
            if (count > 0){
                list = enyanOrderDetailMapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }

            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public Page searchRecordsByUserAndBookAndAuthorList(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        if (StringUtils.isBlank(record.getBookTitle()) || StringUtils.isBlank(record.getUserEmail())){
            page.setRecords(new ArrayList<>());
            return page;
        }
        try {
            EnyanOrderDetailExample example = new EnyanOrderDetailExample();
            EnyanOrderDetailExample.Criteria criteria = example.createCriteria();

            example.setPage(page);

            record.setPage(page);

            String searchText = record.getBookTitle();
            String searchTextSc = AaronJF.f2j(searchText);
            String searchTextTc = AaronJF.j2f(searchText);
            searchText = searchTextSc+"|"+searchTextTc;
            record.setBookTitle(searchText);

            long count = page.getTotalRecord();
            if (count<=0){
                Long returnCount = enyanOrderDetailCustomMapper.countRecordsByUserAndBookAndAuthorList(record);
                if (null == returnCount){
                    count = 0L;
                }
                page.setTotalRecord(count);
            }
            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.searchRecordsByUserAndBookAndAuthorList(record);

            //Date currentDate = new Date();
            if (!list.isEmpty()){
                page.setRecords(list);
                page.setTotalRecord(count);
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanOrderDetail> searchRecordsByUserList(EnyanOrderDetail record) {
        try {
            EnyanOrderDetailExample example = new EnyanOrderDetailExample();
            EnyanOrderDetailExample.Criteria criteria = example.createCriteria();


            if (null != record.getUserId()){
                criteria.andUserIdEqualTo(record.getUserId());
            }
            if (null != record.getBookId()){
                criteria.andBookIdEqualTo(record.getBookId());
            }
            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (StringUtils.isNotBlank(record.getOrderNum())){
                criteria.andOrderNumEqualTo(record.getOrderNum());
            }

            if (StringUtils.isNotBlank(record.getBookTitle())){
                String searchText = record.getBookTitle();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andBookTitleRegexp(searchText);
            }

            criteria.andIsDeletedEqualTo(0);

            List<EnyanOrderDetail> list = enyanOrderDetailMapper.selectByExample(example);

            return list;
        } catch (Exception e) {
            e.printStackTrace();
            //page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return null;
    }

    @Override
    public Page searchRecordsByUserAndBook(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        return null;
    }

    @Override
    public Page queryOrderCountsByDay(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {

            record.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByDay(record);

            if (!list.isEmpty()){
                page.setRecords(list);
                page.setPageSize(list.size());
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanOrderDetail> queryOrderCountsByDay(EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        try {
            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByDay(record);

            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public Page queryOrderCountsByWeek(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {

            record.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByWeek(record);

            if (!list.isEmpty()){
                for (int i = 0; i < list.size(); i++) {
                    EnyanOrderDetail orderDetail = list.get(i);
                    int[] weekBetween = AaronDateUtils.getWeekInfo(orderDetail.getPurchasedDayStart());
                    orderDetail.setPurchasedDayStart(weekBetween[0]);
                    orderDetail.setPurchasedDayEnd(weekBetween[1]);
                }
                int startDate = Integer.parseInt(record.getStartDate());
                int endDate = Integer.parseInt(record.getEndDate());
                EnyanOrderDetail orderDetailStart = list.get(0);
                EnyanOrderDetail orderDetailEnd = list.get(list.size()-1);
                if (startDate>orderDetailStart.getPurchasedDayStart()){
                    orderDetailStart.setPurchasedDayStart(Integer.parseInt(record.getStartDate()));
                }
                if (endDate<orderDetailEnd.getPurchasedDayEnd()){
                    orderDetailEnd.setPurchasedDayEnd(Integer.parseInt(record.getEndDate()));
                }
                page.setRecords(list);
                page.setPageSize(list.size());
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanOrderDetail> queryOrderCountsByWeek(EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        try {

            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByWeek(record);

            if (!list.isEmpty()){
                for (int i = 0; i < list.size(); i++) {
                    EnyanOrderDetail orderDetail = list.get(i);
                    int[] weekBetween = AaronDateUtils.getWeekInfo(orderDetail.getPurchasedDayStart());
                    orderDetail.setPurchasedDayStart(weekBetween[0]);
                    orderDetail.setPurchasedDayEnd(weekBetween[1]);
                }
                int startDate = Integer.parseInt(record.getStartDate());
                int endDate = Integer.parseInt(record.getEndDate());
                EnyanOrderDetail orderDetailStart = list.get(0);
                EnyanOrderDetail orderDetailEnd = list.get(list.size()-1);
                if (startDate>orderDetailStart.getPurchasedDayStart()){
                    orderDetailStart.setPurchasedDayStart(Integer.parseInt(record.getStartDate()));
                }
                if (endDate<orderDetailEnd.getPurchasedDayEnd()){
                    orderDetailEnd.setPurchasedDayEnd(Integer.parseInt(record.getEndDate()));
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public Page queryOrderCountsByMonth(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {

            record.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByMonth(record);

            if (!list.isEmpty()){
                for (int i = 0; i < list.size(); i++) {
                    EnyanOrderDetail orderDetail = list.get(i);
                    orderDetail.setPurchasedDayEnd(orderDetail.getPurchasedDayStart());
                }
                page.setRecords(list);
                page.setPageSize(list.size());
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanOrderDetail> queryOrderCountsByMonth(EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        try {
            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByMonth(record);

            if (!list.isEmpty()){
                for (int i = 0; i < list.size(); i++) {
                    EnyanOrderDetail orderDetail = list.get(i);
                    orderDetail.setPurchasedDayEnd(orderDetail.getPurchasedDayStart());
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public Page queryOrderCountsByYear(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {

            record.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByYear(record);

            if (!list.isEmpty()){
                for (int i = 0; i < list.size(); i++) {
                    EnyanOrderDetail orderDetail = list.get(i);
                    orderDetail.setPurchasedDayEnd(orderDetail.getPurchasedDayStart());
                }
                page.setRecords(list);
                page.setPageSize(list.size());
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanOrderDetail> queryOrderCountsByYear(EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        try {
            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByYear(record);

            if (!list.isEmpty()){
                for (int i = 0; i < list.size(); i++) {
                    EnyanOrderDetail orderDetail = list.get(i);
                    orderDetail.setPurchasedDayEnd(orderDetail.getPurchasedDayStart());
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public Page queryOrderDetailBySpecialMonth(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }
        if (null == page){
            page = new Page<>();
        }
        try {

            record.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanOrderDetailCustomMapper.countBySpecialMonth(record);
                page.setTotalRecord(count);
            }

            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailBySpecialMonth(record);

            if (!list.isEmpty()){
                page.setRecords(list);
                page.setPageSize(list.size());
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public long countOrderDetailByOrder(EnyanOrderDetail record) {
        if (null == record){
            record = new EnyanOrderDetail();
        }

        try {
            EnyanOrderDetailExample example = new EnyanOrderDetailExample();
            EnyanOrderDetailExample.Criteria criteria = example.createCriteria();

            //example.setPage(page);
            //example.setOrderByClause("purchased_at desc");

            if (null != record.getUserId()){
                criteria.andUserIdEqualTo(record.getUserId());
            }
            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (StringUtils.isNotBlank(record.getOrderNum())){
                criteria.andOrderNumEqualTo(record.getOrderNum());
            }

            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andPurchasedDayGreaterThanOrEqualTo(Integer.parseInt(record.getStartDate()));
            }

            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andPurchasedDayLessThanOrEqualTo(Integer.parseInt(record.getEndDate()));
            }
            criteria.andIsDeletedEqualTo(0);
            long count = 0;
            if (count<=0){
                count = enyanOrderDetailMapper.countByExample(example);
                return count;
            }

        } catch (Exception e) {
            e.printStackTrace();
            //page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return 0;
    }
    /**
     *
     *
     * @param enyanOrderDetail
     * @param shouldUpdate 更新现有的OrderDetail的DRM信息（只用于reset）
     * @Date: 2019-11-14
     */
    private void addDrmInfoAndPurchaseInfoToOrderDetail(EnyanOrderDetail enyanOrderDetail, boolean shouldUpdate){
        try {
            if (StringUtils.isNotBlank(enyanOrderDetail.getDrminfo())){//已经有drm信息，直接跳过
                return;
            }
            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(enyanOrderDetail.getBookId()).getResult();
            if (enyanBook == null){
                return;
            }
            if (StringUtils.isBlank(enyanBook.getBookDrmRef())){//暂时还没有关联DRM，则直接跳过
                return;
            }
            //User user = authUserService.getLcpUserById(enyanOrderDetail.getUserId().intValue()).getResult();
            User user = authUserService.getLcpUserByEmail(enyanOrderDetail.getUserEmail()).getResult();
            if (null == user){
                user = new User();
                user.setEmail(enyanOrderDetail.getUserEmail().toLowerCase());
                user.setHint(DRMUtil.DEFAULT_HINT);
                user.setName(enyanOrderDetail.getUserEmail().toLowerCase());
                user.setPassword(DRMUtil.getDefaultHintPasswd(enyanOrderDetail.getUserEmail().toLowerCase()));
                user.setUuid(DRMUtil.getRandomUUID());
                authUserService.addLcpUser(user);
            }

            DrmInfo drmInfo = new DrmInfo();
            LcpInfo lcpInfo = new LcpInfo();

            drmInfo.setLcpInfo(lcpInfo);
            Purchase purchase = new Purchase();
            purchase.setEndDate(new Date());
            purchase.setStartDate(new Date());
            purchase.setStatus("ok");
            purchase.setPublicationId(Integer.parseInt(enyanBook.getBookDrmRef()));
            purchase.setTransactionDate(enyanOrderDetail.getPurchasedAt());
            purchase.setType("BUY");
            purchase.setUserId(user.getId());//LCP的UserId
            purchase.setUuid(UUID.randomUUID().toString());


            try {
                purchaseMapper.insert(purchase);
                lcpInfo.setPurchseId(purchase.getId());
                lcpInfo.setUserId(user.getId());//LCP的UserId
                lcpInfo.setPublicationId(purchase.getPublicationId());
                //lcpInfo.setLicenseUuid(purchase.getLicenseUuid());
                enyanOrderDetail.setDrminfo(JSONObject.toJSONString(drmInfo));
                if (shouldUpdate){//更新现有的OrderDetail的DRM信息（只用于reset）
                    enyanOrderDetailMapper.updateByPrimaryKey(enyanOrderDetail);
                }
            }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

            }
        }catch (Exception e) {
            e.printStackTrace();
            //page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

    }

    @Override
    public void resetOrderDetailAndPurchaseInfo() {
        try {
            List<EnyanOrderDetail> list = this.findAllOrderDetailList(null);
            for (EnyanOrderDetail enyanOrderDetail : list){
                this.addDrmInfoAndPurchaseInfoToOrderDetail(enyanOrderDetail,true);
            }
        } catch (Exception e) {
            e.printStackTrace();
//            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

    }

    @Override
    public void resetOrderDetailAndPurchaseInfoByEmail(String email){
        //User user = authUserService.getLcpUserById(enyanOrderDetail.getUserId().intValue()).getResult();
        User user = authUserService.getLcpUserByEmail(email).getResult();
        if (null == user){
            user = new User();
            user.setEmail(email.toLowerCase());
            user.setHint(DRMUtil.DEFAULT_HINT);
            user.setName(email.toLowerCase());
            user.setPassword(DRMUtil.getDefaultHintPasswd(email.toLowerCase()));
            user.setUuid(DRMUtil.getRandomUUID());
            authUserService.addLcpUser(user);
        }

        EnyanOrderDetail detail = new EnyanOrderDetail();
        detail.setUserEmail(email);
        List<EnyanOrderDetail> list = this.findAllOrderDetailList(detail);
        for (EnyanOrderDetail enyanOrderDetail : list){
            try {
                EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(enyanOrderDetail.getBookId()).getResult();
                if (enyanBook == null){
                    return;
                }
                if (StringUtils.isBlank(enyanBook.getBookDrmRef())){//暂时还没有关联DRM，则直接跳过
                    return;
                }

                DrmInfo drmInfo = new DrmInfo();
                LcpInfo lcpInfo = new LcpInfo();

                drmInfo.setLcpInfo(lcpInfo);
                Purchase purchase = new Purchase();
                purchase.setEndDate(new Date());
                purchase.setStartDate(new Date());
                purchase.setStatus("ok");
                purchase.setPublicationId(Integer.parseInt(enyanBook.getBookDrmRef()));
                purchase.setTransactionDate(enyanOrderDetail.getPurchasedAt());
                purchase.setType("BUY");
                purchase.setUserId(user.getId());//LCP的UserId
                purchase.setUuid(UUID.randomUUID().toString());

                EnyanBookBuy bookBuy = new EnyanBookBuy();
                bookBuy.setBookId(enyanBook.getBookId());
                bookBuy.setBookTitle(enyanBook.getBookTitle());
                bookBuy.setIsDeleted(0);
                bookBuy.setOrderNum(enyanOrderDetail.getOrderNum());
                bookBuy.setPurchasedAt(enyanOrderDetail.getPurchasedAt());
                bookBuy.setPurchasedDay(enyanOrderDetail.getPurchasedDay());
                bookBuy.setUserEmail(enyanOrderDetail.getUserEmail());
                bookBuy.setUserId(enyanOrderDetail.getUserId());

                Publication publication = publicationMapper.selectByPrimaryKey(purchase.getPublicationId());
                try {
                    purchaseMapper.insert(purchase);
                }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

                }
                String licenseId = UUID.randomUUID().toString();
                lcpInfo.setPurchseId(purchase.getId());
                lcpInfo.setUserId(user.getId());//LCP的UserId
                lcpInfo.setPublicationId(purchase.getPublicationId());
                lcpInfo.setLicenseUuid(licenseId);
                if (null != publication){
                    this.insertLicense(licenseId, user.getUuid(), publication.getUuid());
                }

                try {
                    bookBuy.setDrminfo(JSONObject.toJSONString(drmInfo));
                    enyanBookBuyMapper.insert(bookBuy);
                }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

                }
            }catch (Exception e) {
                e.printStackTrace();
                //page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
            }
        }
    }

    @Override
    public void resetOrderDetailAndCountry() {
        try {
            //EnyanOrder record = new EnyanOrder();
            EnyanOrderExample example = new EnyanOrderExample();
            EnyanOrderExample.Criteria criteria = example.createCriteria();

            criteria.andIsPaidEqualTo(Constant.BYTE_VALUE_1);
            criteria.andIsValidEqualTo(Constant.BYTE_VALUE_1);
            criteria.andIsDeletedEqualTo(0);

            List<EnyanOrder> list = enyanOrderMapper.selectByExampleWithBLOBs(example);

            for (EnyanOrder enyanOrder:list){
                if (StringUtils.isNotBlank(enyanOrder.getPayInfo())){
                    OrderPayInfo orderPayInfo = JSON.parseObject(enyanOrder.getPayInfo(),OrderPayInfo.class);
                    enyanOrder.setOrderPayInfo(orderPayInfo);

                    if (StringUtils.isNotBlank(orderPayInfo.getCharge().getCountry())){
                        EnyanOrderDetailExample queryExample = new EnyanOrderDetailExample();
                        EnyanOrderDetailExample.Criteria queryCriteria = queryExample.createCriteria();
                        queryCriteria.andOrderNumEqualTo(enyanOrder.getOrderNum());

                        EnyanOrderDetail orderDetail = new EnyanOrderDetail();
                        orderDetail.setPayCountry(orderPayInfo.getCharge().getCountry());

                        enyanOrderDetailMapper.updateByExampleSelective(orderDetail,queryExample);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
//            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
    }


    @Override
    public List<EnyanOrderDetail> findAllOrderDetailList(EnyanOrderDetail enyanOrderDetail) {
        if (null == enyanOrderDetail){
            enyanOrderDetail = new EnyanOrderDetail();
        }
        List<EnyanOrderDetail> list = null;
        try {
            EnyanOrderDetailExample example = new EnyanOrderDetailExample();
            EnyanOrderDetailExample.Criteria criteria = example.createCriteria();
            if (null != enyanOrderDetail.getBookId()){
                criteria.andBookIdEqualTo(enyanOrderDetail.getBookId());
            }
            if (null != enyanOrderDetail.getUserEmail()){
                criteria.andUserEmailEqualTo(enyanOrderDetail.getUserEmail());
            }
            if (StringUtils.isNotBlank(enyanOrderDetail.getStartDate())){
                criteria.andPurchasedDayGreaterThanOrEqualTo(Integer.parseInt(enyanOrderDetail.getStartDate()));
            }

            if (StringUtils.isNotBlank(enyanOrderDetail.getEndDate())){
                criteria.andPurchasedDayLessThanOrEqualTo(Integer.parseInt(enyanOrderDetail.getEndDate()));
            }

            if (StringUtils.isNotBlank(enyanOrderDetail.getBookTitle())){
                String searchText = enyanOrderDetail.getBookTitle();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andBookTitleRegexp(searchText);
            }
            criteria.andIsDeletedEqualTo(0);
            if (null != enyanOrderDetail.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < enyanOrderDetail.getOrderObjList().size(); i++) {
                    OrderObj orderObj = enyanOrderDetail.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                example.setOrderByClause(buffer.toString());
            }
            list = enyanOrderDetailMapper.selectByExample(example);


        } catch (Exception e) {
            e.printStackTrace();
//            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return list;
    }

    @Override
    public Licenses getLicenseByPurchaseId(int purchaseId) {
        String url = DRMUtil.getApiLicenseByPurchaseId(purchaseId);
        //下载模板
        //File file = FileUtils.toFile(new URL(url));

        HttpResult httpResult = HttpProtocolHandler.execute(new HashMap<>(),url, HttpMethod.GET);
        //logger.debug(httpResult.getStringResult());

        if (!httpResult.isSuccess()){
            return null;
        }
        try {
            Licenses licenses = JSONObject.parseObject(httpResult.getStringResult(),Licenses.class);
            return licenses;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getLicenseStringByLicenseId(String licenseId) {
        HttpCredentials httpCredentials = new HttpCredentials();
        httpCredentials.setAuthName(DRMUtil.AUTH_LCP_NAME);
        httpCredentials.setAuthPasswd(DRMUtil.AUTH_LCP_PASSWD);

        String url = DRMUtil.getApiLicenseByLicenseId(licenseId);
        //url = "http://127.0.0.1:8989/licenses/b850e414-3468-462f-a0e5-d19f61bc443b";
        //下载模板
        //File file = FileUtils.toFile(new URL(url));

        com.aaron.drm.model.User user = new com.aaron.drm.model.User();
        User u = authUserService.getLcpUserByLicenseId(licenseId);
        if (null == u || StringUtils.isBlank(u.getEmail())){
            return null;
        }
        Encryption encryption = new Encryption();
        UserKey userKey = new UserKey();

        user.setEmail(u.getEmail());
        //user.setId("111");

        userKey.setTextHint(u.getHint());
        userKey.setHexValue(u.getPassword());
        userKey.setValue(u.getPassword());

        encryption.setUserKey(userKey);

        String jsonString = DRMUtil.getJsonForPublicationByLicenseID(user,encryption);


        //String jsonString = "{\"user\":{\"email\":\"<EMAIL>\",\"encrypted\":[\"email\"]},\"encryption\":{\"user_key\":{\"text_hint\":\"123\",\"hex_value\":\"a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3\"}}}";
        HttpResult httpResult = HttpProtocolHandler.execute(null,url, HttpMethod.POST,httpCredentials,jsonString);
        //logger.debug(httpResult.getStringResult());
        try {
            if (httpResult.isSuccess()){
                return httpResult.getStringResult();
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public byte[] downloadLcpFiles(DrmInfo drmInfo) {
        String url = DRMUtil.getApiDownloadEpubByLicense(drmInfo.getLcpInfo().getLicenseUuid());
        HttpCredentials httpCredentials = new HttpCredentials();
        httpCredentials.setAuthName(DRMUtil.AUTH_LCP_NAME);
        httpCredentials.setAuthPasswd(DRMUtil.AUTH_LCP_PASSWD);

        User lcpUser = authUserService.getLcpUserById(drmInfo.getLcpInfo().getUserId()).getResult();

        if (null == lcpUser){
            return "error005".getBytes();
        }

        com.aaron.drm.model.User user = new com.aaron.drm.model.User();
        Encryption encryption = new Encryption();
        UserKey userKey = new UserKey();

        user.setEmail(lcpUser.getEmail());
        //user.setId("111");

        userKey.setTextHint(lcpUser.getHint());
        userKey.setHexValue(lcpUser.getPassword());
        userKey.setValue(lcpUser.getPassword());

        encryption.setUserKey(userKey);

        String jsonString = DRMUtil.getJsonForPublicationByLicenseID(user,encryption);

        //String jsonString = "{\"user\":{\"email\":\"<EMAIL>\",\"encrypted\":[\"email\"]},\"encryption\":{\"user_key\":{\"text_hint\":\"123\",\"hex_value\":\"a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3\"}}}";
        HttpResult result = HttpProtocolHandler.execute(null,url, HttpMethod.POST,httpCredentials,jsonString);
        //System.out.println(result.getStringResult());

        return result.getByteResult();
    }

    @Override
    public List<EnyanOrderDetail> findBookIDAndNameByEmail(String email) {
        return enyanOrderDetailCustomMapper.findBookIDAndNameByEmail(email);
    }

    @Override
    public List<EnyanOrderDetail> findBookIDAndNameAndEmailByBookID(Long bookId) {
        return enyanOrderDetailCustomMapper.findBookIDAndNameAndEmailByBookID(bookId);
    }

    @Override
    public List<EnyanOrderDetail> getBookIDAndNameByEmailAndBookId(String email, Long bookId) {
        return enyanOrderDetailCustomMapper.getBookIDAndNameByEmailAndBookId(email,bookId);
    }

    public void test() throws Exception{
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager(eBookJdbcTemplate.getDataSource());
        TransactionStatus status = transactionManager.getTransaction(def);


        //String sql = "insert into TEST(C1,C2,C3) values(?,?,? )" ;
        String sql = "" ;
        /*
        ps = connection.prepareStatement(baseSQL.toString(), ResultSet.TYPE_FORWARD_ONLY,
                ResultSet.CONCUR_READ_ONLY);//采用的是流数据接收方式，每次只从服务器接收部份数据，直到所有数据处理完毕
            ps.setFetchSize(Integer.MIN_VALUE);//每次从服务器取fetch_size条数据。
            ps.setFetchDirection(ResultSet.FETCH_REVERSE);//设置合适的值
            rs = ps.executeQuery();
        * */

        try{
            eBookJdbcTemplate.execute(sql, (new PreparedStatementCallback() {
                public Object doInPreparedStatement(PreparedStatement ps) throws SQLException {
                    for (int i = 0; i < 100; i++) {
                        ps.setString(1, "bbbb" + i);
                        ps.setString(2, "iiii" + i);
                        ps.setInt(3, 2);
                        ps.addBatch();
                        if(i % 10 == 0){
                            ps.executeBatch();
                        }
                    }
                    ps.executeBatch(); // 数据不会持久化到数据库中
                    return null ;
                }
            }));


        }catch (Exception e){
            transactionManager.rollback(status); // 所有的数据都会rollback
            throw e;
        }
        transactionManager.commit(status); // 持久化所有数据
    }

    @Override
    public Page queryRecords(Page<EnyanOrderDetail> page, EnyanOrderDetail record) {
        return findRecordsByOrder(page,record);
    }

    @Override
    public ExecuteResult<EnyanOrderDetail> queryRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanOrderDetail> addRecord(EnyanOrderDetail record) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanOrderDetail> updateRecord(EnyanOrderDetail record) {
        ExecuteResult<EnyanOrderDetail> result = new ExecuteResult<>();
        try {
            int saveFlag = enyanOrderDetailMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public String checkSaveRecord(EnyanOrderDetail record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanOrderDetail record) {
        return null;
    }

    private String getRateValue(int rateDate,Date purchaseDate){
        EnyanPayRate enyanPayRate = enyanPayRateService.getRateByRateDate(rateDate);
        if (null == enyanPayRate){
            logger.error("rateDate error:{}",rateDate);
            return "";
        }
        String rateTimeStr = DateFormatUtils.format(purchaseDate,"HHmmss");
        int rateTime = Integer.parseInt(rateTimeStr);

        if (rateTime>=enyanPayRate.getRateTime()){
            return enyanPayRate.getRateValue();
        }
        Date yesterday = DateUtils.addDays(purchaseDate,-1);
        String rateDateYesterdayStr = DateFormatUtils.format(yesterday,"HHmmss");
        int rateDateYesterday = Integer.parseInt(rateDateYesterdayStr);
        enyanPayRate = enyanPayRateService.getRateByRateDate(rateDateYesterday);
        if (null == enyanPayRate){
            logger.error("rateDate error:{}",rateDate);
            return "";
        }
        return enyanPayRate.getRateValue();
    }
    /**
     *
     * 获取计算的OrderDetail
     * @param productInfo
     * @Date: 2018/6/11
     */
    private EnyanOrderDetail getCalcOrder(ProductInfo productInfo, EnyanOrderDetail inputOrderDetail){
        return OrderUtil.getCalcOrder(productInfo,inputOrderDetail);
    }
    /**
     *
     * 根据支付类型，获取支付费用
     * 信用卡：香港本地卡是2.9%+2.35，其他全部是4.4%+2.35的手续费
     * 支付宝：*3%
     * label.pay.1=支付宝
     * label.pay.2=信用卡
     * label.pay.3=免费
     * label.pay.4=兑换码
     * label.pay.21=信用卡(非香港)
     * label.pay.22=信用卡(香港)
     * @param enyanOrder
     * @Date: 2020-04-09
     */
    private BigDecimal getPayFee(EnyanOrder enyanOrder){
        return OrderUtil.getPayFee(enyanOrder);
    }
    /**
     *
     * 平均的支付费用
     * @param productSize
     * @Date: 2020-04-09
     */
    private BigDecimal getAvgPayFee(int productSize, BigDecimal payFee){
        if (productSize == 0){
            return new BigDecimal("0");
        }

        return payFee.divide(new BigDecimal(productSize), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
    }
    /**
     *
     * 每个产品的支付费用分摊
     * 支付宝：销售价格 * 3%，如果最后一个则直接进行减法
     * 信用卡：按比例分配到每本书，如果最后一个则直接进行减法 (2020.9.9)
     * @Date: 2020-09-09
     */
    private BigDecimal getEveryPayFeeNew(List<ProductInfo> productInfoList, int payType, BigDecimal payFee, BigDecimal totalFee, int index, List<BigDecimal> allPastFee){
        if (null == productInfoList ||productInfoList.size() == 0){
            return new BigDecimal("0");
        }
        int size = productInfoList.size();
        if (index == (size - 1)){//最后一个，直接进行减法
            BigDecimal pastFee = new BigDecimal("0");
            for (BigDecimal fee:allPastFee){
                pastFee = pastFee.add(fee);
            }
            return payFee.subtract(pastFee);
        }

        //int payType = enyanOrder.getOrderPayInfo().getCharge().getPayType();
        ProductInfo productInfo = productInfoList.get(index);
        BigDecimal priceSell = productInfo.getRealPriceHKD(); //销售的价格
        if (productInfo.isDiscountAnyIsValid()){
            //priceSell = productInfo.getPriceHKDDiscount();
        }
        if (1 == payType){ //支付宝单独处理
            return priceSell.multiply(new BigDecimal("3"))
                    .divide(new BigDecimal("100"), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
        }
        BigDecimal newPayFee = OrderUtil.getNewValuePart(priceSell,totalFee,payFee);
        return newPayFee;
    }

    /**
     * <p>获取每一本书的支付费用的List</p>
     * @param productInfoList
     * @param enyanOrder
     * @return: java.util.List<java.math.BigDecimal>
     * @since : 2020-07-21
     */
    private List<BigDecimal> getEveryPayFeeList(List<ProductInfo> productInfoList, EnyanOrder enyanOrder){
        BigDecimal payFee = this.getPayFee(enyanOrder);
        BigDecimal avgPayFee = this.getAvgPayFee(productInfoList.size(),payFee);

        List<BigDecimal> payFeeList = new ArrayList<>();
        for (int i = 0; i < productInfoList.size(); i++) {
            BigDecimal everyPayFee = this.getEveryPayFeeNew(productInfoList,enyanOrder.getOrderPayInfo().getCharge().getPayType(),payFee,enyanOrder.getOrderTotal(),i,payFeeList);
            payFeeList.add(everyPayFee);
        }
        return payFeeList;
    }

    private void insertLicense(String id, String userId, String contentFk){
        //"INSERT INTO license (id, user_id, provider, issued, rights_print, rights_copy, content_fk, lsd_status) VALUES (?, ?, 'https://ebook.endao.co', ? , 10, 5000, ?, 201)";
        Date date = new Date();
        //"INSERT INTO license (id, user_id, provider, issued, rights_print, rights_copy, content_fk, lsd_status) VALUES (?, ?, 'https://ebook.endao.co', ? , 10, 5000, ?, 201)";
        int saveFlag = eBookJdbcTemplate.update(insertLicense,((PreparedStatement ps)->{
            ps.setString(1,id);//id
            ps.setString(2,userId);//user_id
            ps.setTimestamp(3,new Timestamp(date.getTime()));//issued
            ps.setString(4,contentFk);//, content_fk

        }));

        LicenseStatus licenseStatus = new LicenseStatus();
        licenseStatus.setLicenseRef(id);
        licenseStatus.setStatus(1);//默认是1，有设备下载后就是2
        licenseStatus.setLicenseUpdated(date);
        licenseStatus.setStatusUpdated(date);
        licenseStatus.setDeviceCount(0);
        licenseStatus.setPotentialRightsEnd(date);

        licenseStatusMapper.insert(licenseStatus);
    }

    public static  void main(String[] args){
        int totalFee = 165;
        int productSize = 4;
        EnyanOrder enyanOrder = new EnyanOrder();
        OrderDetailInfo orderDetailInfo = new OrderDetailInfo();
        OrderPayInfo orderPayInfo = new OrderPayInfo();
        List<BigDecimal> allPastPayFee = new ArrayList<>();

        List<ProductInfo> list = new ArrayList<>();
        for (int i = 0; i < productSize; i++) {
            list.add(new ProductInfo());
        }
        orderDetailInfo.setProductInfoList(list);

        enyanOrder.setOrderDetailInfo(orderDetailInfo);
        enyanOrder.setOrderTotal(new BigDecimal(totalFee));

        PayDetail payDetail = new PayDetail();
        payDetail.setPayType(EBookConstant.PayType.ALI_PAY_HK);
        orderPayInfo.setCharge(payDetail);
        enyanOrder.setOrderPayInfo(orderPayInfo);

        EnyanOrderDetailServiceImpl impl = new EnyanOrderDetailServiceImpl();
        BigDecimal payFee = impl.getPayFee(enyanOrder);
        BigDecimal avgPayFee = impl.getAvgPayFee(productSize,payFee);
        //BigDecimal everyFee = impl.getEveryPayFee(enyanOrder.getOrderDetailInfo().getProductInfoList(),enyanOrder.getOrderPayInfo().getCharge().getPayType(),payFee,avgPayFee,(productSize-1),allPastPayFee);

        System.out.println("totalFee:"+totalFee);
        System.out.println("totalFee calc:"+avgPayFee.multiply(new BigDecimal(productSize)));
        System.out.println("payFee:"+payFee);
        System.out.println("avgPayFee:"+avgPayFee);
        //System.out.println("currentFee:"+everyFee);
    }
}
