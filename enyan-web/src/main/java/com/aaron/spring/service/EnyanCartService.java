package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.*;
import com.aaron.util.ExecuteResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/28
 * @Modified By:
 */
public interface EnyanCartService extends IService<EnyanCart, EnyanCartExample>{
   /**
    * <p></p>
    * @param email
    * @param bookId
    * @return com.aaron.util.ExecuteResult<java.lang.String>
    * @since : 2021/4/28
    **/
   ExecuteResult<String> deleteByEmailAndBookId(String email, Long bookId);

   ExecuteResult<String> deleteByExample(EnyanCart record);
   /**
    *
    *  查询购物车的书籍
    * @param page
    * @param enyanCart
    * @Date: 2018/2/5
    */
   Page queryCarts(Page<EnyanBook> page, EnyanCart enyanCart);

   List<EnyanBook> searchAllByEmail(String email);

   int deleteCartsAllByEmail(String email);

   int deleteCarts(String email, List<Long> ids);

   long countOfCartByEmail(String email);

   /**
    * <p>获取是否有某本书</p>
    * @param email
    * @param bookId
    * @return long
    * @since : 2021/5/8
    **/
   long countOfCartByEmailAndBookId(String email, Long bookId);

   /**
    * <p>注销用户</p>
    * @param email
    * @param revokedEmail
    * @return com.aaron.util.ExecuteResult<java.lang.String>
    * @since : 2022/8/26
    **/
   ExecuteResult<String> revokeUser(String email, String revokedEmail);
}
