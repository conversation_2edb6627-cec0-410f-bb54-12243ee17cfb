package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.Money;
import com.aaron.drm.model.DrmInfo;
import com.aaron.drm.model.LcpInfo;
import com.aaron.drm.util.DRMUtil;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.mapper.*;
import com.aaron.spring.mapper.custom.EnyanBookBuyCustomMapper;
import com.aaron.spring.mapper.custom.EnyanOrderCustomMapper;
import com.aaron.spring.mapper.custom.EnyanPlanCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.AuthUserService;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanCouponService;
import com.aaron.spring.service.EnyanOrderService;
import com.aaron.spring.common.OrderUtil;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.*;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/11/2
 * @Modified By:
 */
@Service
public class EnyanOrderServiceImpl implements EnyanOrderService{

    public static final String SELECT_ORDER_BY_BOOKID_EMAIL = "select * from enyan_order where user_email = '${email}' and is_paid = 1 and JSON_CONTAINS(order_detail->'$.productInfoList[*].code', '${bookId}', '$');";

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanOrderMapper enyanOrderMapper;

    @Resource
    private EnyanOrderCustomMapper enyanOrderCustomMapper;

    @Autowired
    protected EnyanPlanCustomMapper enyanPlanCustomMapper;

    @Resource
    private EnyanRedeemCodeMapper enyanRedeemCodeMapper;

    @Resource
    private AuthUserService authUserService;

    @Resource
    private EnyanOrderDetailMapper enyanOrderDetailMapper;

    @Resource
    protected PurchaseMapper purchaseMapper;

    @Resource
    private EnyanBookBuyMapper enyanBookBuyMapper;

    @Resource
    private JdbcTemplate eBookJdbcTemplate;

    @Resource
    private EnyanBookBuyCustomMapper enyanBookBuyCustomMapper;

    @Resource
    protected PublicationMapper publicationMapper;

    //@Resource
    //protected EnyanCouponCustomMapper enyanCouponCustomMapper;

    @Resource
    private EnyanCouponService enyanCouponService;

    @Resource
    protected LicenseStatusMapper licenseStatusMapper;

    private static final String insertLicense = "INSERT INTO license (id, user_id, provider, issued, rights_print, rights_copy, content_fk, lsd_status) VALUES (?, ?, 'https://ebook.endao.co', ? , 10, 5000, ?, 201)";

    /**
     * <p>根据条件分页查询订单记录</p>
     * <p>根据传入的订单条件和分页信息，查询匹配的订单记录</p>
     * @param page 分页信息
     * @param record 包含查询条件的订单对象
     * @return: com.aaron.mybatis.dao.pojo.Page<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public Page queryRecords(Page<EnyanOrder> page, EnyanOrder record) {
        if (null == record){
            record = new EnyanOrder();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanOrderExample example = new EnyanOrderExample();
            EnyanOrderExample.Criteria criteria = example.createCriteria();

            example.setPage(page);
            example.setOrderByClause("purchased_at desc");

            if (null != record.getIsPaid()){
                criteria.andIsPaidEqualTo(record.getIsPaid());
            }
            if (null != record.getIsValid()){
                criteria.andIsValidEqualTo(record.getIsValid());
            }
            if (null != record.getOrderType()){
                criteria.andOrderTypeEqualTo(record.getOrderType());
            }
            if (null != record.getUserId()){
                criteria.andUserIdEqualTo(record.getUserId());
            }
            if (StringUtils.isNotBlank(record.getOrderNum())){
                criteria.andOrderNumEqualTo(record.getOrderNum());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andPurchasedAtGreaterThanOrEqualTo(DateUtils.parseDate(record.getStartDate()+"000000","yyyyMMddHHmmss"));
            }
            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andPurchasedAtLessThanOrEqualTo(DateUtils.parseDate(record.getEndDate()+"235959","yyyyMMddHHmmss"));
            }
            criteria.andIsDeletedEqualTo(0);
            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanOrderMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanOrder> list;
            if (count > 0){
                list = enyanOrderCustomMapper.selectByExampleWithBLOBs(example); //自定义了分页
            }else {
                list = new ArrayList<>();
            }

            Date currentDate = new Date();
            if (!list.isEmpty()){
                for (EnyanOrder enyanOrder:list){
                    if (StringUtils.isNotBlank(enyanOrder.getOrderDetail())){
                        OrderDetailInfo orderDetailInfo = JSON.parseObject(enyanOrder.getOrderDetail(),OrderDetailInfo.class);
                        orderDetailInfo.resetFromJson();
                        enyanOrder.setOrderDetailInfo(orderDetailInfo);
                    }
                    if (StringUtils.isNotBlank(enyanOrder.getOrderTitle())){
                        OrderTitleInfo orderTitleInfo = JSON.parseObject(enyanOrder.getOrderTitle(),OrderTitleInfo.class);
                        enyanOrder.setOrderTitleInfo(orderTitleInfo);
                    }
                    if (StringUtils.isNotBlank(enyanOrder.getPayInfo())){
                        OrderPayInfo orderPayInfo = JSON.parseObject(enyanOrder.getPayInfo(),OrderPayInfo.class);
                        enyanOrder.setOrderPayInfo(orderPayInfo);
                    }
                    if (Constant.BYTE_VALUE_1.equals(enyanOrder.getIsValid())
                            && !Constant.BYTE_VALUE_1.equals(enyanOrder.getIsPaid())){//未付款
                        if (currentDate.compareTo(enyanOrder.getExpiredAt()) == 1){
                            enyanOrder.setIsValid(Constant.BYTE_VALUE_0);
                        }
                    }
                }
            }
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    /**
     * <p>根据主键查询订单记录</p>
     * <p>根据传入的主键值，查询匹配的订单记录</p>
     * @param pkId 主键值
     * @return: com.aaron.util.ExecuteResult<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public ExecuteResult<EnyanOrder> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanOrder> result = new ExecuteResult<>();
        try {
            EnyanOrder record = enyanOrderMapper.selectByPrimaryKey(pkId);
            if (null == record){
                //throw new BusinessException(Integer.valueOf("103"),"订单不存在！");
                //record = new EnyanOrder();
                return result;
            }
            if (StringUtils.isNotBlank(record.getOrderDetail())){
                OrderDetailInfo orderDetailInfo = JSON.parseObject(record.getOrderDetail(),OrderDetailInfo.class);
                orderDetailInfo.resetFromJson();
                record.setOrderDetailInfo(orderDetailInfo);
            }
            if (StringUtils.isNotBlank(record.getOrderTitle())){
                OrderTitleInfo orderTitleInfo = JSON.parseObject(record.getOrderTitle(),OrderTitleInfo.class);
                record.setOrderTitleInfo(orderTitleInfo);
            }
            if (StringUtils.isNotBlank(record.getPayInfo())){
                OrderPayInfo orderPayInfo = JSON.parseObject(record.getPayInfo(),OrderPayInfo.class);
                record.setOrderPayInfo(orderPayInfo);
            }
            Date currentDate = new Date();
            if (Constant.BYTE_VALUE_1.equals(record.getIsValid())
                    && !Constant.BYTE_VALUE_1.equals(record.getIsPaid())){//未付款
                if (currentDate.compareTo(record.getExpiredAt()) == 1){
                    record.setIsValid(Constant.BYTE_VALUE_0);
                }
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    /**
     * <p>添加订单记录</p>
     * <p>将传入的订单信息保存到数据库</p>
     * @param record 包含订单信息的对象
     * @return: com.aaron.util.ExecuteResult<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public ExecuteResult<EnyanOrder> addRecord(EnyanOrder record) {
        ExecuteResult<EnyanOrder> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            if (null != record.getOrderDetailInfo()){
                record.setOrderDetail(JSON.toJSONString(record.getOrderDetailInfo()));
            }
            if (null != record.getOrderTitleInfo()){
                record.setOrderTitle(JSON.toJSONString(record.getOrderTitleInfo()));
            }
            if (null != record.getOrderPayInfo()){
                record.setPayInfo(JSON.toJSONString(record.getOrderPayInfo()));
            }
            record.setIsDeleted(0);

            int saveFlag = enyanOrderMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    /**
     * <p>更新订单记录</p>
     * <p>根据传入的订单信息更新数据库中的记录</p>
     * @param record 包含订单信息的对象
     * @return: com.aaron.util.ExecuteResult<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public ExecuteResult<EnyanOrder> updateRecord(EnyanOrder record) {
        ExecuteResult<EnyanOrder> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            if (null != record.getOrderDetailInfo()){
                record.setOrderDetail(JSON.toJSONString(record.getOrderDetailInfo()));
            }
            if (null != record.getOrderTitleInfo()){
                record.setOrderTitle(JSON.toJSONString(record.getOrderTitleInfo()));
            }
            if (null != record.getOrderPayInfo()){
                record.setPayInfo(JSON.toJSONString(record.getOrderPayInfo()));
            }
            int saveFlag = enyanOrderMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    /**
     * <p>根据主键删除订单记录</p>
     * <p>根据传入的主键值，删除匹配的订单记录</p>
     * @param pkId 主键值
     * @return: com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2020-07-21
     */
    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = enyanOrderCustomMapper.updateOrderDeletedByBookId(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    /**
     * <p>检查保存订单记录的条件</p>
     * <p>检查传入的订单对象是否满足保存条件</p>
     * @param record 包含订单信息的对象
     * @return: java.lang.String
     * @since : 2020-07-21
     */
    @Override
    public String checkSaveRecord(EnyanOrder record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }

        if (sb.length()>0){
            return sb.toString();
        }
        return null;
    }

    /**
     * <p>检查更新订单记录的条件</p>
     * <p>检查传入的订单对象是否满足更新条件</p>
     * @param record 包含订单信息的对象
     * @return: java.lang.String
     * @since : 2020-07-21
     */
    @Override
    public String checkUpdateRecord(EnyanOrder record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (record.getOrderId()==0){
            sb.append("主键信息不可以为空");
            return sb.toString();
        }
        return null;
    }

    /**
     * <p>根据条件查询订单记录</p>
     * <p>根据传入的订单条件，查询匹配的订单记录</p>
     * @param record 包含查询条件的订单对象
     * @return: java.util.List<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public List<EnyanOrder> findRecordsByOrder(EnyanOrder record) {
        EnyanOrderExample example = new EnyanOrderExample();
        EnyanOrderExample.Criteria criteria = example.createCriteria();

        if (null != record.getIsPaid()){
            criteria.andIsPaidEqualTo(record.getIsPaid());
        }
        if (null != record.getIsValid()){
            criteria.andIsValidEqualTo(record.getIsValid());
        }
        if (StringUtils.isNotBlank(record.getOrderNum())){
            criteria.andOrderNumEqualTo(record.getOrderNum());
        }
        if (StringUtils.isNotBlank(record.getUserEmail())){
            criteria.andUserEmailEqualTo(record.getUserEmail());
        }
        if (StringUtils.isNotBlank(record.getOrderBookHash())){
            criteria.andOrderBookHashEqualTo(record.getOrderBookHash());
        }
        if (null != record.getOrderType()){
            criteria.andOrderTypeEqualTo(record.getOrderType());
        }
        criteria.andIsDeletedEqualTo(0);
        return enyanOrderMapper.selectByExample(example);
    }

    /**
     * <p>根据订单号查询订单记录（包含BLOB字段）</p>
     * <p>根据传入的订单号，查询匹配的订单记录，并解析BLOB字段内容</p>
     * @param orderNum 订单号
     * @return: java.util.List<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public List<EnyanOrder> findRecordsBlobByOrderNum(String orderNum) {
        EnyanOrderExample example = new EnyanOrderExample();
        EnyanOrderExample.Criteria criteria = example.createCriteria();

        if (StringUtils.isBlank(orderNum)){
            return List.of();
        }
        criteria.andOrderNumEqualTo(orderNum);
        criteria.andIsDeletedEqualTo(0);
        List<EnyanOrder> list = enyanOrderMapper.selectByExampleWithBLOBs(example);
        for (EnyanOrder record : list){
            if (StringUtils.isNotBlank(record.getOrderDetail())){
                OrderDetailInfo orderDetailInfo = JSON.parseObject(record.getOrderDetail(),OrderDetailInfo.class);
                orderDetailInfo.resetFromJson();
                record.setOrderDetailInfo(orderDetailInfo);
            }
            if (StringUtils.isNotBlank(record.getOrderTitle())){
                OrderTitleInfo orderTitleInfo = JSON.parseObject(record.getOrderTitle(),OrderTitleInfo.class);
                record.setOrderTitleInfo(orderTitleInfo);
            }
            if (StringUtils.isNotBlank(record.getPayInfo())){
                OrderPayInfo orderPayInfo = JSON.parseObject(record.getPayInfo(),OrderPayInfo.class);
                record.setOrderPayInfo(orderPayInfo);
            }
        }
        return list;
    }

    /**
     * <p>删除订单记录</p>
     * <p>根据传入的订单信息，删除匹配的订单记录</p>
     * @param order 包含订单信息的对象
     * @return: com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2020-07-21
     */
    @Override
    public ExecuteResult<String> deleteOrder(EnyanOrder order) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            EnyanOrderExample example = new EnyanOrderExample();
            EnyanOrderExample.Criteria criteria = example.createCriteria();
            if (order.getOrderId()>0){
                criteria.andOrderIdEqualTo(order.getOrderId());
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
                return result;
            }
            criteria.andIsValidEqualTo(order.getIsValid());

            int deleteFlag = enyanOrderCustomMapper.updateOrderDeletedByBookId(order.getOrderId());
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    /**
     * <p>更新订单过期状态</p>
     * <p>更新数据库中所有过期的订单状态</p>
     * @return: com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2020-07-21
     */
    @Override
    public ExecuteResult<String> updateOrderExpired() {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int updateFlag = enyanOrderCustomMapper.updateOrderExpired();
            if (updateFlag>=0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    /**
     * <p>撤销用户订单</p>
     * <p>根据传入的用户邮箱和撤销邮箱，更新数据库中的订单状态</p>
     * @param email 用户邮箱
     * @param revokedEmail 撤销邮箱
     * @return: com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2020-07-21
     */
    @Override
    public ExecuteResult<String> revokeUser(String email, String revokedEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = enyanOrderCustomMapper.revokeUser(email, revokedEmail);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    /**
     * <p>根据书籍ID和用户邮箱查询订单记录</p>
     * <p>根据传入的书籍ID和用户邮箱，查询匹配的订单记录</p>
     * @param bookId 书籍ID
     * @param email 用户邮箱
     * @return: java.util.List<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public List<EnyanOrder> selectByBookId(Long bookId, String email) {
        /*
        String bookCondition = "JSON_CONTAINS(order_detail->'$.productInfoList[*].code', '"+bookId+"', '$')";
        return enyanOrderCustomMapper.selectByBookId(bookCondition,email);*/

        Map valuesMap = new HashMap();
        valuesMap.put("email", email);
        valuesMap.put("bookId", bookId.toString());
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        /* 方法一：
        List<EnyanOrder> list = new ArrayList<>();
        List<LinkedHashMap<String,Object>> items = enyanOrderCustomMapper.getItems(sub.replace(SELECT_ORDER_BY_BOOKID_EMAIL));
        for (LinkedHashMap<String,Object> item: items){
            EnyanOrder enyanOrder = new EnyanOrder();
            enyanOrder.setIsPaid(new Byte(item.get("is_paid").toString()));
            enyanOrder.setOrderId(Long.parseLong(item.get("order_id").toString()));
            enyanOrder.setOrderNum(item.get("order_num").toString());
            list.add(enyanOrder);
        }
        return list;*/
        return enyanOrderCustomMapper.findRecordList(sub.replace(SELECT_ORDER_BY_BOOKID_EMAIL));

        //return enyanOrderCustomMapper.selectByBookIdAndEmail(bookId,email);
    }

    /**
     * <p>根据兑换码和用户邮箱查询订单记录</p>
     * <p>根据传入的兑换码和用户邮箱，查询匹配的订单记录</p>
     * @param couponCode 兑换码
     * @param email 用户邮箱
     * @return: java.util.List<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public List<EnyanOrder> selectByCouponCodeAndEmail(String couponCode, String email) {
        return enyanOrderCustomMapper.selectByCouponCodeAndEmail(couponCode,email);
    }

    /**
     * <p>根据兑换码和用户邮箱查询订单数量</p>
     * <p>根据传入的兑换码和用户邮箱，查询匹配的订单数量</p>
     * @param couponCode 兑换码
     * @param email 用户邮箱
     * @return: long
     * @since : 2020-07-21
     */
    @Override
    public long selectCountByCouponCodeAndEmail(String couponCode, String email) {
        return enyanOrderCustomMapper.selectCountByCouponCodeAndEmail(couponCode, email);
    }



    /**
     * <p>更新灵修计划的购买状态</p>
     * <p>兑换码购买订单，不需要更新灵修计划的购买</p>
     * @param order 包含订单信息的对象
     * @return: int 更新的记录数
     * @since : 2021/3/24
     */
    private int updatePlanHasBuy(EnyanOrder order){
        String[] ids = new String[order.getOrderDetailInfo().getProductInfoList().size()];
        for (int i = 0; i < order.getOrderDetailInfo().getProductInfoList().size(); i++) {
            ids[i] = order.getOrderDetailInfo().getProductInfoList().get(i).getCode().toString();
        }

        return enyanPlanCustomMapper.updatePlanHasBuyByEmailAndIds(order.getUserEmail(),System.currentTimeMillis(),ids);
    }

    /**
     * <p>保存兑换码购买订单</p>
     * <p>根据传入的订单信息和书籍列表，保存兑换码购买订单</p>
     * @param enyanOrder 包含订单信息的对象
     * @param bookList 书籍列表
     * @param redeemCodeToUpdate 需要更新的兑换码信息
     * @return: void
     * @since : 2020-07-21
     */
    @Override
    public void saveOrderRedeem(EnyanOrder enyanOrder, List<EnyanBook> bookList, EnyanRedeemCode redeemCodeToUpdate) {
        enyanOrder.getOrderDetailInfo().resetFromJson();
        enyanOrder.setIsDeleted(0);

        User user = authUserService.getLcpUserByEmail(enyanOrder.getUserEmail()).getResult();
        if (null == user){
            user = new User();
            user.setEmail(enyanOrder.getUserEmail().toLowerCase());
            user.setHint(DRMUtil.DEFAULT_HINT);
            user.setName(enyanOrder.getUserEmail().toLowerCase());
            user.setPassword(DRMUtil.getDefaultHintPasswd(enyanOrder.getUserEmail().toLowerCase()));
            user.setUuid(DRMUtil.getRandomUUID());
            authUserService.addLcpUser(user);
        }

        int flag = this.enyanOrderMapper.insert(enyanOrder);
        if (flag > 0){
            //this.splitOrder(enyanOrder,false);//兑换码直接就split订单了，所以不需要再更新Count，兑换码兑换的订单不需要放在明细里
            redeemCodeToUpdate.getRedeemCodeNoteInfo().setOrderIdToRedeem(enyanOrder.getOrderId());
            redeemCodeToUpdate.getRedeemCodeNoteInfo().setOrderNumToRedeem(enyanOrder.getOrderNum());
            redeemCodeToUpdate.setNote(JSON.toJSONString(redeemCodeToUpdate.getRedeemCodeNoteInfo()));
            this.enyanRedeemCodeMapper.updateByPrimaryKeySelective(redeemCodeToUpdate);
        }
        String dateStr = DateFormatUtils.format(enyanOrder.getPurchasedAt(),"yyyyMMdd");
        int dateInt = Integer.parseInt(dateStr);
        for (EnyanBook enyanBook:bookList){
            EnyanBookBuy bookBuy = new EnyanBookBuy();
            bookBuy.setBookId(enyanBook.getBookId());
            bookBuy.setBookTitle(enyanBook.getBookTitle());
            bookBuy.setIsDeleted(0);
            bookBuy.setOrderNum(enyanOrder.getOrderNum());
            bookBuy.setPurchasedAt(enyanOrder.getPurchasedAt());
            bookBuy.setPurchasedDay(dateInt);
            bookBuy.setUserEmail(enyanOrder.getUserEmail());
            bookBuy.setUserId(enyanOrder.getUserId());

            //新购买的书籍，方法内部直接insert数据了，所以不需要再更新DRM Info的信息
            this.addDrmInfoAndPurchaseInfoToBookBuy(bookBuy,user,false);
            try {
                enyanBookBuyMapper.insert(bookBuy);
            }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

            }
        }

        this.updatePlanHasBuy(enyanOrder);
    }

    /**
     * <p>保存已支付订单</p>
     * <p>根据传入的订单信息，保存已支付订单</p>
     * @param order 包含订单信息的对象
     * @return: void
     * @since : 2020-07-21
     */
    @Override
    public void saveOrderHasPay(EnyanOrder order) {
        order.getOrderDetailInfo().resetFromJson();

        User user = authUserService.getLcpUserByEmail(order.getUserEmail()).getResult();
        if (null == user){
            user = new User();
            user.setEmail(order.getUserEmail().toLowerCase());
            user.setHint(DRMUtil.DEFAULT_HINT);
            user.setName(order.getUserEmail().toLowerCase());
            user.setPassword(DRMUtil.getDefaultHintPasswd(order.getUserEmail().toLowerCase()));
            user.setUuid(DRMUtil.getRandomUUID());
            authUserService.addLcpUser(user);
        }

        List<Long> productIdList = new ArrayList<>(order.getOrderDetailInfo().getProductInfoList().size());
        for (ProductInfo productInfo:order.getOrderDetailInfo().getProductInfoList()){
            productIdList.add(productInfo.getCode());
        }
        List<EnyanBook> list = enyanBookService.findBookByIds(productIdList);
        Map<Long,EnyanBook> bookMap = new HashMap<>();
        for (EnyanBook book:list){
            bookMap.put(book.getBookId(), book);
        }
        order.setBookMap(bookMap);

        EnyanOrder newOrder = new EnyanOrder();
        newOrder.setOrderId(order.getOrderId());
        newOrder.setOrderPayInfo(order.getOrderPayInfo());
        newOrder.setPayInfo(JSON.toJSONString(order.getOrderPayInfo()));
        newOrder.setIsPaid(Constant.BYTE_VALUE_1);
        enyanOrderMapper.updateByPrimaryKeySelective(newOrder);//更新订单的支付成功信息

        if (null != order.getOrderDetailInfo() && StringUtils.isNotBlank(order.getOrderDetailInfo().getCouponCode())){//使用了兑换码
//            enyanCouponCustomMapper.updateBookDiscountByCouponCode(order.getOrderDetailInfo().getCouponCode());
            enyanCouponService.updateCouponCount(order.getOrderDetailInfo().getCouponCode());
        }

        if (EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY == order.getOrderType()
                || EBookConstant.OrderType.ORDER_EBOOK_SET_BUY == order.getOrderType()
                    || EBookConstant.OrderType.ORDER_FROM_RENT_BUY == order.getOrderType()){//单本书、套装书及先租后买转换购买订单
            this.splitOrder(order,user ,false);
            this.updatePlanHasBuy(order);
        }else if (EBookConstant.OrderType.ORDER_REDEEM_BUY == order.getOrderType()){//购买兑换码订单，不需要放到 读书信息里
            this.splitOrderRedeem(order, false);
        }else if (EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE == order.getOrderType()){//兑换码兑换订单，不需要生成销售明细
            //saveOrderRedeem 处理
        }


    }

    /**
     * <p>更新订单为已支付状态</p>
     * <p>根据传入的订单ID，更新数据库中的订单状态为已支付</p>
     * @param order 包含订单信息的对象
     * @return: void
     * @since : 2020-07-21
     */
    @Override
    public void updateOrderToPaid(EnyanOrder order) {
        enyanOrderCustomMapper.updateOrderToPaid(order.getOrderId());
    }

    /**
     * <p>保存license信息</p>
     * <p>根据传入的书籍购买信息和DRM信息，保存license信息</p>
     * @param bookBuy 书籍购买信息
     * @param drmInfo DRM信息
     * @return: java.lang.String
     * @since : 2020-07-21
     */
    @Override
    public String saveLicensesByDrmInfo(EnyanBookBuy bookBuy,DrmInfo drmInfo) {
        if (null == drmInfo || null == drmInfo.getLcpInfo()){
            return null;
        }
        User user = authUserService.getLcpUserByEmail(bookBuy.getUserEmail()).getResult();
        if (null == user){
            user = new User();
            user.setEmail(bookBuy.getUserEmail().toLowerCase());
            user.setHint(DRMUtil.DEFAULT_HINT);
            user.setName(bookBuy.getUserEmail().toLowerCase());
            user.setPassword(DRMUtil.getDefaultHintPasswd(bookBuy.getUserEmail().toLowerCase()));
            user.setUuid(DRMUtil.getRandomUUID());
            authUserService.addLcpUser(user);
        }
        LcpInfo lcpInfo = drmInfo.getLcpInfo();
        Publication publication = publicationMapper.selectByPrimaryKey(drmInfo.getLcpInfo().getPublicationId());
        try {
            String licenseId = UUID.randomUUID().toString();

//            purchaseMapper.insert(purchase);
//            lcpInfo.setPurchseId(purchase.getId());
//            lcpInfo.setUserId(user.getId());//LCP的UserId
//            lcpInfo.setPublicationId(purchase.getPublicationId());
//            lcpInfo.setLicenseUuid(licenseId);
            this.insertLicense(licenseId, user.getUuid(), publication.getUuid());
            lcpInfo.setLicenseUuid(licenseId);

            EnyanBookBuy newBookBuy = new EnyanBookBuy();
            newBookBuy.setBookBuyId(bookBuy.getBookBuyId());
            newBookBuy.setDrminfo(JSONObject.toJSONString(drmInfo));
            enyanBookBuyMapper.updateByPrimaryKeySelective(newBookBuy);
            return licenseId;
        }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

        }
        return null;
    }

    /**
     * <p>将订单拆分为多个订单明细记录</p>
     * <p>根据订单中包含的产品信息，将订单拆分为多个订单明细记录并保存到数据库</p>
     * @param enyanOrder 订单信息
     * @param user 用户信息
     * @param updateOrderCount 是否更新订单计数
     * @return: void
     * @since : 2020-07-21
     */
    private void splitOrder(EnyanOrder enyanOrder, User user, Boolean updateOrderCount){
        /*
        if (StringUtils.isNotBlank(enyanOrder.getOrderDetail())){
            OrderDetailInfo orderDetailInfo = JSON.parseObject(enyanOrder.getOrderDetail(),OrderDetailInfo.class);
            orderDetailInfo.resetFromJson();
            enyanOrder.setOrderDetailInfo(orderDetailInfo);
        }

        if (StringUtils.isNotBlank(enyanOrder.getPayInfo())){
            OrderPayInfo orderPayInfo = JSON.parseObject(enyanOrder.getPayInfo(),OrderPayInfo.class);
            enyanOrder.setOrderPayInfo(orderPayInfo);
        }*/

        String dateStr = DateFormatUtils.format(enyanOrder.getPurchasedAt(),"yyyyMMdd");
        int dateInt = Integer.parseInt(dateStr);

        //String rateValue = this.getRateValue(dateInt,enyanOrder.getPurchasedAt());
        /*String rateValue = "";
        BigDecimal payFee = this.getPayFee(enyanOrder);
        BigDecimal avgPayFee = this.getAvgPayFee(enyanOrder,payFee);
        List<BigDecimal> allPastPayFee = new ArrayList<>();*/

        List<BigDecimal> payFeeList = this.getEveryPayFeeList(enyanOrder.getOrderDetailInfo().getProductInfoList(),enyanOrder);
        for (int i = 0; i < enyanOrder.getOrderDetailInfo().getProductInfoList().size(); i++) {
            ProductInfo productInfo = enyanOrder.getOrderDetailInfo().getProductInfoList().get(i);
            EnyanBook enyanBook = enyanOrder.getBookMap().get(productInfo.getCode());
            if (null == enyanBook){
                continue;
            }

            if (EBookConstant.BookType.EBOOK_SET == enyanBook.getBookType()){//电子书套装
                this.splitEBookSetToDB(enyanOrder,enyanBook,user,dateInt);
            }else {//电子书单本
                this.splitEBookSingleToDB(enyanOrder,enyanBook,user,productInfo,payFeeList,dateInt,i);
            }


        }
        if (updateOrderCount){
            enyanOrderCustomMapper.updateOrderCounted(enyanOrder.getOrderId());
        }
    }

    /**
     * <p>处理购买兑换码的订单</p>
     * <p>根据订单信息生成相应的兑换码，并将订单明细保存到数据库</p>
     * @param enyanOrder 订单信息
     * @param updateOrderCount 是否更新订单计数
     * @return: void
     * @since : 2021/3/24
     */
    private void splitOrderRedeem(EnyanOrder enyanOrder, boolean updateOrderCount) {
        /* 可以有多个书籍 20250110
        if (enyanOrder.getOrderDetailInfo().getProductInfoList().size() != 1){//兑换码应该只有一个商品
            return;
        }*/
        if (enyanOrder.getOrderTitleInfo().getBookType() != EBookConstant.BookType.EBOOK_SINGLE){
            this.splitOrderRedeemSetOrMenu(enyanOrder,updateOrderCount);
            return;
        }
        
        String dateStr = DateFormatUtils.format(enyanOrder.getPurchasedAt(),"yyyyMMdd");
        int dateInt = Integer.parseInt(dateStr);

        //String rateValue = this.getRateValue(dateInt,enyanOrder.getPurchasedAt());
        String rateValue = "";
        BigDecimal payFee = OrderUtil.getPayFee(enyanOrder);
        BigDecimal avgPayFee = this.getAvgPayFeeRedeem(enyanOrder,payFee);
        String currentDate = DateFormatUtils.format(new Date(),"yyyyMMddHHmmss");
        
        
        ProductInfo productInfo = enyanOrder.getOrderDetailInfo().getProductInfoList().get(0);
        EnyanBook enyanBook = enyanOrder.getBookMap().get(productInfo.getCode());
        if (null == enyanBook){
            return;
        }
        EnyanBook newBook = new EnyanBook();
        newBook.setBookId(enyanBook.getBookId());
        newBook.setBookTitle(enyanBook.getBookTitle());

        List<EnyanBook> newBookList = new ArrayList<>();//兑换码的List
        newBookList.add(newBook);

        EnyanOrderDetail buyOrderDetail = null;

        for (int i = 0; i < productInfo.getQuantity().intValue(); i++) {//每一本书有一个兑换码

            //}
            //for (ProductInfo productInfo : enyanOrder.getOrderDetailInfo().getProductInfoList()){
            EnyanOrderDetail orderDetail = new EnyanOrderDetail();
            orderDetail.setBookId(productInfo.getCode());
            orderDetail.setBookTitle(productInfo.getName());
            orderDetail.setOrderCurrency(enyanOrder.getOrderCurrency());
            orderDetail.setOrderNum(enyanOrder.getOrderNum());

            orderDetail.setSalesModel(enyanBook.getSalesModel());
            orderDetail.setOrderType(enyanOrder.getOrderType());
            orderDetail.setPayCountry(enyanOrder.getOrderPayInfo().getCharge().getCountry());//支付的国家
            orderDetail.setPayType(enyanOrder.getOrderPayInfo().getCharge().getPayType());

            orderDetail.setPayFee(avgPayFee); //支付时的费用
            orderDetail.setVendorPercent(enyanBook.getVendorPercent()); //版税费率

            EnyanOrderDetail orderDetailNew = OrderUtil.getCalcOrder(productInfo,orderDetail);
            orderDetail.setPriceFixed(orderDetailNew.getPriceFixed());
            orderDetail.setPriceSelling(orderDetailNew.getPriceSelling());
            orderDetail.setIncomeTotal(orderDetailNew.getIncomeTotal());
            orderDetail.setIncomeReal(orderDetailNew.getIncomeReal());
            orderDetail.setIncomeVendor(orderDetailNew.getIncomeVendor());
            orderDetail.setIncomePlat(orderDetailNew.getIncomePlat());
            orderDetail.setNetSales(orderDetailNew.getNetSales());

            orderDetail.setPurchasedAt(enyanOrder.getPurchasedAt());

            orderDetail.setQuantity(1);
            orderDetail.setPurchasedDay(dateInt);
            orderDetail.setUserEmail(enyanOrder.getUserEmail());
            orderDetail.setUserId(enyanOrder.getUserId());
            orderDetail.setIsCounted(EBookConstant.BalanceStatus.NOT);
            orderDetail.setPublisherId(productInfo.getPublisherId());
            orderDetail.setBookPubCode(productInfo.getBookPubCode());
            orderDetail.setBookEsin(productInfo.getBookEsin());
            orderDetail.setRateValue(rateValue);

            orderDetail.setOrderFrom(enyanOrder.getOrderFrom());

            if (null == buyOrderDetail){
                buyOrderDetail = new EnyanOrderDetail();
                this.buyOrderDetailRedeem(orderDetail,buyOrderDetail,productInfo.getQuantity());
            }



//            EnyanRedeemCode redeemCode = new EnyanRedeemCode();
//            redeemCode.setCode("B-"+ UUID.randomUUID().toString());
//            redeemCode.setStatus(EBookContant.RedeemStatus.DEFAULT);
//            redeemCode.setType(EBookContant.RedeemType.SPECIAL);

            RedeemCodeNoteInfo redeemCodeNoteInfo = new RedeemCodeNoteInfo();
            redeemCodeNoteInfo.setSource(EBookConstant.RedeemSource.BUY_DESCRIPTION);
            redeemCodeNoteInfo.setType(EBookConstant.RedeemType.SPECIAL);

            redeemCodeNoteInfo.setOtherThings("用户购买兑换码");
            redeemCodeNoteInfo.setBooksToRedeemList(newBookList);
            redeemCodeNoteInfo.setSourceOrderId(enyanOrder.getOrderId()+"");
            redeemCodeNoteInfo.setEnyanOrderDetail(orderDetail);
            redeemCodeNoteInfo.setBookNameDescription(enyanOrder.getOrderTitleInfo().getProductDescription());

            EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
            enyanRedeemCode.setCode("B-"+ UUID.randomUUID().toString());
            enyanRedeemCode.setCreateTime(currentDate);
            enyanRedeemCode.setStatus(EBookConstant.RedeemStatus.DEFAULT);
            enyanRedeemCode.setType(EBookConstant.RedeemType.SPECIAL);
            enyanRedeemCode.setNote(JSON.toJSONString(redeemCodeNoteInfo));
            enyanRedeemCode.setUserEmail(enyanOrder.getUserEmail());
            try {
                enyanRedeemCodeMapper.insert(enyanRedeemCode);
            }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

            }
        }
        buyOrderDetail.setIsDeleted(0);
        enyanOrderDetailMapper.insert(buyOrderDetail);
        if (updateOrderCount){
            //enyanOrderCustomMapper.updateOrderCounted(enyanOrder.getOrderId());
        }
    }

    /**
     * <p>生成书系或书单的兑换码购买功能</p>
     * <p>只需要生成一个兑换码，用于兑换整个书系或书单</p>
     * @param enyanOrder 订单信息
     * @param updateOrderCount 是否更新订单计数
     * @return void
     * @since : 2025-01-20
     **/
    private void splitOrderRedeemSetOrMenu(EnyanOrder enyanOrder, boolean updateOrderCount) {
        String dateStr = DateFormatUtils.format(enyanOrder.getPurchasedAt(),"yyyyMMdd");
        int dateInt = Integer.parseInt(dateStr);

        //String rateValue = this.getRateValue(dateInt,enyanOrder.getPurchasedAt());
        String rateValue = "";
        BigDecimal payFee = OrderUtil.getPayFee(enyanOrder);
        //BigDecimal avgPayFee = this.getAvgPayFeeRedeem(enyanOrder,payFee);
        String currentDate = DateFormatUtils.format(new Date(),"yyyyMMddHHmmss");

        List<EnyanBook> newBookList = new ArrayList<>();//兑换码的List
        int redeemQuantity = 1;
        for (ProductInfo productInfo : enyanOrder.getOrderDetailInfo().getProductInfoList()){
            EnyanBook enyanBook = enyanOrder.getBookMap().get(productInfo.getCode());
            if (null == enyanBook){
                return;
            }
            EnyanBook newBook = new EnyanBook();
            newBook.setBookId(enyanBook.getBookId());
            newBook.setBookTitle(enyanBook.getBookTitle());
            newBook.setAuthor(enyanBook.getAuthor());

            newBookList.add(newBook);

            EnyanOrderDetail buyOrderDetail = null;
            EnyanOrderDetail orderDetail = new EnyanOrderDetail();
            orderDetail.setBookId(productInfo.getCode());
            orderDetail.setBookTitle(productInfo.getName());
            orderDetail.setOrderCurrency(enyanOrder.getOrderCurrency());
            orderDetail.setOrderNum(enyanOrder.getOrderNum());

            orderDetail.setSalesModel(enyanBook.getSalesModel());
            orderDetail.setOrderType(enyanOrder.getOrderType());
            orderDetail.setPayCountry(enyanOrder.getOrderPayInfo().getCharge().getCountry());//支付的国家
            orderDetail.setPayType(enyanOrder.getOrderPayInfo().getCharge().getPayType());

            //BigDecimal currentFee = productInfo.getRealPriceHKD().multiply(productInfo.getQuantity());//当前产品的销售额
            BigDecimal currentPayFee = getPayFee(enyanOrder.getOrderTotal(), productInfo.getRealPriceHKD(), payFee);//当前单件产品应有的支付费用
            orderDetail.setPayFee(currentPayFee); //支付时的费用
            orderDetail.setVendorPercent(enyanBook.getVendorPercent()); //版税费率

            EnyanOrderDetail orderDetailNew = OrderUtil.getCalcOrder(productInfo,orderDetail);
            orderDetail.setPriceFixed(orderDetailNew.getPriceFixed());
            orderDetail.setPriceSelling(orderDetailNew.getPriceSelling());
            orderDetail.setIncomeTotal(orderDetailNew.getIncomeTotal());
            orderDetail.setIncomeReal(orderDetailNew.getIncomeReal());
            orderDetail.setIncomeVendor(orderDetailNew.getIncomeVendor());
            orderDetail.setIncomePlat(orderDetailNew.getIncomePlat());
            orderDetail.setNetSales(orderDetailNew.getNetSales());

            orderDetail.setPurchasedAt(enyanOrder.getPurchasedAt());

            orderDetail.setQuantity(1);
            orderDetail.setPurchasedDay(dateInt);
            orderDetail.setUserEmail(enyanOrder.getUserEmail());
            orderDetail.setUserId(enyanOrder.getUserId());
            orderDetail.setIsCounted(EBookConstant.BalanceStatus.NOT);
            orderDetail.setPublisherId(productInfo.getPublisherId());
            orderDetail.setBookPubCode(productInfo.getBookPubCode());
            orderDetail.setBookEsin(productInfo.getBookEsin());
            orderDetail.setRateValue(rateValue);

            orderDetail.setOrderFrom(enyanOrder.getOrderFrom());

            if (null == buyOrderDetail){
                buyOrderDetail = new EnyanOrderDetail();
                this.buyOrderDetailRedeem(orderDetail,buyOrderDetail,productInfo.getQuantity());
            }



//            EnyanRedeemCode redeemCode = new EnyanRedeemCode();
//            redeemCode.setCode("B-"+ UUID.randomUUID().toString());
//            redeemCode.setStatus(EBookContant.RedeemStatus.DEFAULT);
//            redeemCode.setType(EBookContant.RedeemType.SPECIAL);
            buyOrderDetail.setIsDeleted(0);
            enyanOrderDetailMapper.insert(buyOrderDetail);
            redeemQuantity = productInfo.getQuantity().intValue();
        }

        for (int i = 0; i < redeemQuantity; i++) {
            RedeemCodeNoteInfo redeemCodeNoteInfo = new RedeemCodeNoteInfo();
            redeemCodeNoteInfo.setSource(EBookConstant.RedeemSource.BUY_DESCRIPTION);
            redeemCodeNoteInfo.setType(EBookConstant.RedeemType.ALL);

            redeemCodeNoteInfo.setOtherThings("用户购买兑换码");
            redeemCodeNoteInfo.setBooksToRedeemList(newBookList);
            redeemCodeNoteInfo.setSourceOrderId(enyanOrder.getOrderId()+"");
            //redeemCodeNoteInfo.setEnyanOrderDetail(orderDetail); 套装书的兑换码暂时取消这个值
            redeemCodeNoteInfo.setBookNameDescription(enyanOrder.getOrderTitleInfo().getProductDescription());

            EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
            enyanRedeemCode.setCode("B-"+ UUID.randomUUID().toString());
            enyanRedeemCode.setCreateTime(currentDate);
            enyanRedeemCode.setStatus(EBookConstant.RedeemStatus.DEFAULT);
            enyanRedeemCode.setType(EBookConstant.RedeemType.ALL);
            enyanRedeemCode.setNote(JSON.toJSONString(redeemCodeNoteInfo));
            enyanRedeemCode.setUserEmail(enyanOrder.getUserEmail());
            try {
                enyanRedeemCodeMapper.insert(enyanRedeemCode);
            }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

            }
        }
    }

    /**
     * <p>分离电子书的套装到数据库</p>
     * <p>将电子书套装拆分为单本书，并分别保存到数据库中</p>
     * @param enyanOrder 订单信息
     * @param enyanBookSet 电子书套装信息
     * @param user 用户信息
     * @param dateInt 日期整数值
     * @return: void
     * @since : 2020-07-21
     */
    private void splitEBookSetToDB(EnyanOrder enyanOrder, EnyanBook enyanBookSet, User user, Integer dateInt){
        List<ProductInfo> productInfoList = this.getProductListByBookSet(enyanBookSet,enyanOrder.getOrderTotal());
        List<BigDecimal> payFeeList = this.getEveryPayFeeList(productInfoList,enyanOrder);
        for (int i = 0; i < productInfoList.size(); i++) {
            ProductInfo productInfo = productInfoList.get(i);
            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(productInfo.getCode()).getResult();
            if (null == enyanBook){
                continue;
            }
            this.splitEBookSingleToDB(enyanOrder,enyanBook,user,productInfo,payFeeList,dateInt,i);
        }
    }

    /**
     * <p>分离电子书的单本到数据库</p>
     * <p>将单本电子书信息保存到订单明细和购买记录表中</p>
     * @param enyanOrder 订单信息
     * @param enyanBook 电子书信息
     * @param user 用户信息
     * @param productInfo 产品信息
     * @param payFeeList 支付费用列表
     * @param dateInt 日期整数值
     * @param index 产品在列表中的索引
     * @return: void
     * @since : 2020-07-21
     */
    private void splitEBookSingleToDB(EnyanOrder enyanOrder, EnyanBook enyanBook, User user, ProductInfo productInfo, List<BigDecimal> payFeeList, Integer dateInt, Integer index){
        //}
        //for (ProductInfo productInfo : enyanOrder.getOrderDetailInfo().getProductInfoList()){
        EnyanOrderDetail orderDetail = new EnyanOrderDetail();
        orderDetail.setBookId(productInfo.getCode());
        orderDetail.setBookTitle(productInfo.getName());
        orderDetail.setOrderCurrency(enyanOrder.getOrderCurrency());
        orderDetail.setOrderNum(enyanOrder.getOrderNum());

        orderDetail.setSalesModel(enyanBook.getSalesModel());
        orderDetail.setPayCountry(enyanOrder.getOrderPayInfo().getCharge().getCountry());//支付的国家
        orderDetail.setPayType(enyanOrder.getOrderPayInfo().getCharge().getPayType());
        orderDetail.setOrderType(enyanOrder.getOrderType());

        orderDetail.setPayFee(payFeeList.get(index)); //支付时的费用
        orderDetail.setVendorPercent(enyanBook.getVendorPercent()); //版税费率

        EnyanOrderDetail orderDetailNew = this.getCalcOrder(productInfo,orderDetail);
        orderDetail.setPriceFixed(orderDetailNew.getPriceFixed());
        orderDetail.setPriceSelling(orderDetailNew.getPriceSelling());
        orderDetail.setIncomeTotal(orderDetailNew.getIncomeTotal());
        orderDetail.setIncomeReal(orderDetailNew.getIncomeReal());
        orderDetail.setIncomeVendor(orderDetailNew.getIncomeVendor());
        orderDetail.setIncomePlat(orderDetailNew.getIncomePlat());
        orderDetail.setNetSales(orderDetailNew.getNetSales());

        orderDetail.setPurchasedAt(enyanOrder.getPurchasedAt());

        orderDetail.setQuantity(1);
        orderDetail.setPurchasedDay(dateInt);
        orderDetail.setUserEmail(enyanOrder.getUserEmail());
        orderDetail.setUserId(enyanOrder.getUserId());
        orderDetail.setIsCounted(EBookConstant.BalanceStatus.NOT);
        orderDetail.setPublisherId(productInfo.getPublisherId());
        orderDetail.setBookPubCode(productInfo.getBookPubCode());
        orderDetail.setBookEsin(productInfo.getBookEsin());
        orderDetail.setRateValue("");

        orderDetail.setOrderFrom(enyanOrder.getOrderFrom());
        orderDetail.setIsDeleted(0);

        EnyanBookBuy bookBuy = new EnyanBookBuy();
        bookBuy.setBookId(enyanBook.getBookId());
        bookBuy.setBookTitle(enyanBook.getBookTitle());
        bookBuy.setIsDeleted(0);
        bookBuy.setOrderNum(enyanOrder.getOrderNum());
        bookBuy.setPurchasedAt(enyanOrder.getPurchasedAt());
        bookBuy.setPurchasedDay(dateInt);
        bookBuy.setUserEmail(enyanOrder.getUserEmail());
        bookBuy.setUserId(enyanOrder.getUserId());

        //新购买的书籍，后边直接insert数据了，所以不需要再更新DRM Info的信息
        this.addDrmInfoAndPurchaseInfoToBookBuy(bookBuy,user,false);
                    /*if (null == productInfo.getEbookFormat()){
                        orderDetail.setEbookFormat(Constant.BYTE_VALUE_1);
                    }else {
                        orderDetail.setEbookFormat(productInfo.getEbookFormat());
                    }*/
        try {

            enyanOrderDetailMapper.insert(orderDetail);
            enyanBookBuyMapper.insert(bookBuy);
        }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

        }
    }

    /**
     * <p>为购买记录添加DRM信息和购买信息</p>
     * <p>根据用户和书籍信息，为购买记录添加DRM保护相关信息</p>
     * @param bookBuy 书籍购买记录
     * @param user 用户信息
     * @param shouldUpdate 是否需要更新现有的OrderDetail的DRM信息（只用于reset）
     * @return: void
     * @since : 2019-11-14
     */
    private void addDrmInfoAndPurchaseInfoToBookBuy(EnyanBookBuy bookBuy, User user, boolean shouldUpdate){
        try {
            if (Constant.IS_LOCAL || Constant.IS_TEST){//非正式环境不需要注册DRM
                return;
            }
            if (StringUtils.isNotBlank(bookBuy.getDrminfo())){//已经有drm信息，直接跳过
                return;
            }
            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookBuy.getBookId()).getResult();
            if (enyanBook == null){
                return;
            }
            if (StringUtils.isBlank(enyanBook.getBookDrmRef())){//暂时还没有关联DRM，则直接跳过
                return;
            }
            Date currentDate = new Date();
            /*
            //User user = authUserService.getLcpUserById(enyanOrderDetail.getUserId().intValue()).getResult();
            User user = authUserService.getLcpUserByEmail(bookBuy.getUserEmail()).getResult();
            if (null == user){
                user = new User();
                user.setEmail(bookBuy.getUserEmail().toLowerCase());
                user.setHint(DRMUtil.DEFAULT_HINT);
                user.setName(bookBuy.getUserEmail().toLowerCase());
                user.setPassword(DRMUtil.getDefaultHintPasswd(bookBuy.getUserEmail().toLowerCase()));
                user.setUuid(DRMUtil.getRandomUUID());
                authUserService.addLcpUser(user);
            }*/

            DrmInfo drmInfo = new DrmInfo();
            LcpInfo lcpInfo = new LcpInfo();

            drmInfo.setLcpInfo(lcpInfo);
            Purchase purchase = new Purchase();
            purchase.setEndDate(currentDate);
            purchase.setStartDate(currentDate);
            purchase.setStatus("ok");
            purchase.setPublicationId(Integer.parseInt(enyanBook.getBookDrmRef()));
            purchase.setTransactionDate(bookBuy.getPurchasedAt());
            purchase.setType("BUY");
            purchase.setUserId(user.getId());//LCP的UserId
            purchase.setUuid(UUID.randomUUID().toString());


            Publication publication = publicationMapper.selectByPrimaryKey(purchase.getPublicationId());
            try {
                purchaseMapper.insert(purchase);
            }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

            }
            String licenseId = UUID.randomUUID().toString();
            lcpInfo.setPurchseId(purchase.getId());
            lcpInfo.setUserId(user.getId());//LCP的UserId
            lcpInfo.setPublicationId(purchase.getPublicationId());
            lcpInfo.setLicenseUuid(licenseId);
            if (null != publication){
                this.insertLicense(licenseId, user.getUuid(), publication.getUuid());
            }

            //lcpInfo.setLicenseUuid(purchase.getLicenseUuid());
            bookBuy.setDrminfo(JSONObject.toJSONString(drmInfo));
            if (shouldUpdate){//更新现有的OrderDetail的DRM信息（只用于reset）
                //enyanOrderDetailMapper.updateByPrimaryKey(enyanOrderDetail);
            }
        }catch (Exception e) {
            System.out.println("addDrmInfoAndPurchaseInfoToBookBuy:"+bookBuy.getBookBuyId());
            e.printStackTrace();
            //page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

    }

    /**
     * <p>根据套装书重新生成ProductInfoList</p>
     * <p>主要是为了重新生成每本书优惠后新的价格，套装书里的新价格是按照书的原价在总价中的比例来计算</p>
     * @param enyanBook 电子书信息
     * @param totalRealNewFee 真实新的最终价格
     * @return: java.util.List<com.aaron.spring.model.ProductInfo>
     * @since : 2020-07-20
     */
    private List<ProductInfo> getProductListByBookSet(EnyanBook enyanBook, BigDecimal totalRealNewFee){
        List<ProductInfo> productInfoList = new ArrayList<>();
        if (EBookConstant.BookType.EBOOK_SET != enyanBook.getBookType()){
            return productInfoList;
        }
        /*
        if (!StringUtils.isEmpty(enyanBook.getSetInfo())){
            BookSetInfo bookSetInfo = JSONObject.parseObject(enyanBook.getSetInfo(), BookSetInfo.class);
            if (null == bookSetInfo.getBooksToSetList() || bookSetInfo.getBooksToSetList().isEmpty()){
                return productInfoList;
            }
            List<Long> bookIds = new ArrayList<>();
            for (EnyanBook tmp:bookSetInfo.getBooksToSetList()){
                bookIds.add(tmp.getBookId());
            }
            List<EnyanBook> bookList = this.enyanBookService.findBookByIds(bookIds);
            BigDecimal totalRealOldFee = new BigDecimal("0");//单本书的旧的原定价总数
            BigDecimal pastFee = new BigDecimal("0");//已经累计的价格
            for (EnyanBook tmp: bookList){
                ProductInfo productInfo = new ProductInfo(tmp);
                totalRealOldFee = totalRealOldFee.add(productInfo.getPriceHkd());
                productInfoList.add(productInfo);
            }

            for (int i = 0; i < productInfoList.size(); i++) {
                ProductInfo productInfo = productInfoList.get(i);
                BigDecimal newPrice = productInfo.getPriceHkd().multiply(totalRealNewFee).divide(totalRealOldFee, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
                if (i == (productInfoList.size() - 1)){//最后一个直接使用减法
                    productInfo.setPriceHKDDiscount(totalRealNewFee.subtract(pastFee));
                }else{
                    pastFee = pastFee.add(newPrice);
                    productInfo.setPriceHKDDiscount(newPrice);
                }
            }
        }*/
        return productInfoList;
    }

    /**
     * <p>根据条件查询订单记录（包含BLOB字段）</p>
     * <p>根据传入的订单条件，查询匹配的订单记录，并解析BLOB字段内容</p>
     * @param record 包含查询条件的订单对象
     * @return: java.util.List<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-21
     */
    @Override
    public List<EnyanOrder> findRecordsWithBLOBsByOrder(EnyanOrder record) {
        EnyanOrderExample example = new EnyanOrderExample();
        EnyanOrderExample.Criteria criteria = example.createCriteria();

        if (null != record.getIsPaid()){
            criteria.andIsPaidEqualTo(record.getIsPaid());
        }
        if (null != record.getIsValid()){
            criteria.andIsValidEqualTo(record.getIsValid());
        }
        if (StringUtils.isNotBlank(record.getOrderNum())){
            criteria.andOrderNumEqualTo(record.getOrderNum());
        }
        if (null != record.getOrderType()){
            criteria.andOrderTypeEqualTo(record.getOrderType());
        }

        try {
            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andPurchasedAtGreaterThanOrEqualTo(DateUtils.parseDate(record.getStartDate()+"000000","yyyyMMddHHmmss"));
            }
            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andPurchasedAtLessThanOrEqualTo(DateUtils.parseDate(record.getEndDate()+"235959","yyyyMMddHHmmss"));
            }
            List<EnyanOrder> list = enyanOrderMapper.selectByExampleWithBLOBs(example);
            if (!list.isEmpty()){
                for (EnyanOrder enyanOrder:list){
                    if (StringUtils.isNotBlank(enyanOrder.getOrderDetail())){
                        OrderDetailInfo orderDetailInfo = JSON.parseObject(enyanOrder.getOrderDetail(),OrderDetailInfo.class);
                        orderDetailInfo.resetFromJson();
                        enyanOrder.setOrderDetailInfo(orderDetailInfo);
                    }
                    if (StringUtils.isNotBlank(enyanOrder.getOrderTitle())){
                        OrderTitleInfo orderTitleInfo = JSON.parseObject(enyanOrder.getOrderTitle(),OrderTitleInfo.class);
                        enyanOrder.setOrderTitleInfo(orderTitleInfo);
                    }
                    if (StringUtils.isNotBlank(enyanOrder.getPayInfo())){
                        OrderPayInfo orderPayInfo = JSON.parseObject(enyanOrder.getPayInfo(),OrderPayInfo.class);
                        enyanOrder.setOrderPayInfo(orderPayInfo);
                    }
                }
            }
            return list;
        }catch (Exception e){

        }
        return Lists.newArrayList();
    }

    /**
     * <p>获取计算后的订单明细</p>
     * <p>根据产品信息和输入的订单明细，计算相关的价格和收入信息</p>
     * @param productInfo 产品信息
     * @param inputOrderDetail 输入的订单明细
     * @return: com.aaron.spring.model.EnyanOrderDetail
     * @since : 2018/6/11
     */
    private EnyanOrderDetail getCalcOrder(ProductInfo productInfo, EnyanOrderDetail inputOrderDetail){
        return OrderUtil.getCalcOrder(productInfo,inputOrderDetail);
    }

    /**
     * <p>根据支付类型获取支付费用</p>
     * <p>不同支付方式有不同的手续费计算方式：
     * 信用卡：香港本地卡是2.9%+2.35，其他全部是4.4%+2.35的手续费
     * 支付宝：3%
     * label.pay.1=支付宝
     * label.pay.2=信用卡
     * label.pay.3=免费
     * label.pay.4=兑换码
     * label.pay.21=信用卡(非香港)
     * label.pay.22=信用卡(香港)</p>
     * @param enyanOrder 订单信息
     * @return: java.math.BigDecimal
     * @since : 2020-04-09
     */
    private BigDecimal getPayFee(EnyanOrder enyanOrder){
        return OrderUtil.getPayFee(enyanOrder);
    }

    /**
     * <p>计算平均的支付费用</p>
     * <p>将总支付费用按产品数量平均分配</p>
     * @param productSize 产品数量
     * @param payFee 支付费用总额
     * @return: java.math.BigDecimal
     * @since : 2020-04-09
     */
    private BigDecimal getAvgPayFee(int productSize, BigDecimal payFee){
        if (productSize == 0){
            return new BigDecimal("0");
        }

        return payFee.divide(new BigDecimal(productSize), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
    }

    /**
     * <p>获取每一本书的支付费用列表</p>
     * <p>根据产品列表和订单信息，计算每本书应分摊的支付费用</p>
     * @param productInfoList 产品信息列表
     * @param enyanOrder 订单信息
     * @return: java.util.List<java.math.BigDecimal>
     * @since : 2020-07-21
     */
    private List<BigDecimal> getEveryPayFeeList(List<ProductInfo> productInfoList, EnyanOrder enyanOrder){
        BigDecimal payFee = this.getPayFee(enyanOrder);
//        BigDecimal avgPayFee = this.getAvgPayFee(productInfoList.size(),payFee);

        List<BigDecimal> payFeeList = new ArrayList<>();
        for (int i = 0; i < productInfoList.size(); i++) {
            BigDecimal everyPayFee = this.getEveryPayFeeNew(productInfoList,enyanOrder.getOrderPayInfo().getCharge().getPayType(),payFee,enyanOrder.getOrderTotal(),i,payFeeList);
            payFeeList.add(everyPayFee);
        }
        return payFeeList;
    }

    /**
     * <p>计算每个产品的支付费用分摊</p>
     * <p>根据支付类型不同采用不同的计算方式：
     * 支付宝：销售价格 * 3%，如果最后一个则直接进行减法
     * 信用卡：按比例分配到每本书，如果最后一个则直接进行减法</p>
     * @param productInfoList 产品信息列表
     * @param payType 支付类型
     * @param payFee 支付费用总额
     * @param totalFee 订单总金额
     * @param index 当前产品索引
     * @param allPastFee 已计算的费用列表
     * @return: java.math.BigDecimal
     * @since : 2020-09-09
     */
    private BigDecimal getEveryPayFeeNew(List<ProductInfo> productInfoList, int payType, BigDecimal payFee, BigDecimal totalFee, int index, List<BigDecimal> allPastFee){
        if (null == productInfoList ||productInfoList.size() == 0){
            return new BigDecimal("0");
        }
        if (EBookConstant.PayType.DIRECT_PAY == payType){//直接支付没有支付费用 2022.05.25
            return new BigDecimal("0");
        }
        int size = productInfoList.size();
        if (index == (size - 1)){//最后一个，直接进行减法
            BigDecimal pastFee = new BigDecimal("0");
            for (BigDecimal fee:allPastFee){
                pastFee = pastFee.add(fee);
            }
            return payFee.subtract(pastFee);
        }

        //int payType = enyanOrder.getOrderPayInfo().getCharge().getPayType();
        ProductInfo productInfo = productInfoList.get(index);
        BigDecimal priceSell = productInfo.getRealPriceHKD(); //销售的价格
        if (productInfo.isDiscountAnyIsValid()){
            //priceSell = productInfo.getPriceHKDDiscount();
        }
        if (EBookConstant.PayType.ALI_PAY == payType){ //支付宝单独处理
            return priceSell.multiply(new BigDecimal("3"))
                    .divide(new BigDecimal("100"), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
        }
        if (EBookConstant.PayType.ALI_PAY_HK == payType || EBookConstant.PayType.DIRECT_PAY_ALIPAY == payType){ //支付宝HK单独处理
            return priceSell.multiply(new BigDecimal("1.7"))
                           .divide(new BigDecimal("100"), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
        }
        BigDecimal newPayFee = OrderUtil.getNewValuePart(priceSell,totalFee,payFee);
        return newPayFee;
    }

    /**
     * <p>计算兑换码订单的平均支付费用</p>
     * <p>将支付费用按照产品数量平均分配,每次购买，productList 只有一个产品</p>
     * @param enyanOrder 订单信息
     * @param payFee 支付费用总额
     * @return: java.math.BigDecimal
     * @since : 2020-04-09
     */
    public static BigDecimal getAvgPayFeeRedeem(EnyanOrder enyanOrder, BigDecimal payFee){
        if (null == enyanOrder.getOrderDetailInfo().getProductInfoList()
                ||enyanOrder.getOrderDetailInfo().getProductInfoList().size() != 1){
            return new BigDecimal("0");
        }
        ProductInfo productInfo = enyanOrder.getOrderDetailInfo().getProductInfoList().get(0);
        return payFee.divide(productInfo.getQuantity(), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
    }

    /**
     * <p>根据当前费用的占比获取手续费</p>
     * <p>按照当前费用在总费用中的比例计算应分摊的手续费</p>
     * @param totalFee 总费用
     * @param currentFee 当前费用
     * @param payFee 支付手续费总额
     * @return java.math.BigDecimal
     * @since : 2025-01-21
     **/
    public static BigDecimal getPayFee(BigDecimal totalFee, BigDecimal currentFee, BigDecimal payFee){
        if (totalFee.floatValue() > 0){

        }else {
            return Constant.VALUE_0;
        }
       return payFee.multiply(currentFee).divide(totalFee, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
    }

    /**
     * <p>将购买兑换码的信息添加到销售明细</p>
     * <p>根据订单明细和购买数量，创建兑换码购买的销售明细记录，count值为实际购买的数值</p>
     * @param orderDetail 订单明细信息
     * @param buyOrderDetail 购买订单明细（输出参数）
     * @param countBigDecimal 购买数量
     * @return: void
     * @since : 2021/3/22
     */
    private void buyOrderDetailRedeem(EnyanOrderDetail orderDetail, EnyanOrderDetail buyOrderDetail,BigDecimal countBigDecimal){
        //BigDecimal countBigDecimal = new BigDecimal(String.valueOf(count));

        buyOrderDetail.setBookId(orderDetail.getBookId());
        buyOrderDetail.setBookTitle(orderDetail.getBookTitle());
        buyOrderDetail.setOrderCurrency(orderDetail.getOrderCurrency());
        buyOrderDetail.setOrderNum(orderDetail.getOrderNum());

        buyOrderDetail.setSalesModel(orderDetail.getSalesModel());
        buyOrderDetail.setOrderType(orderDetail.getOrderType());
        buyOrderDetail.setPayCountry(orderDetail.getPayCountry());//支付的国家
        buyOrderDetail.setPayType(orderDetail.getPayType());

        buyOrderDetail.setPayFee(orderDetail.getPayFee().multiply(countBigDecimal)); //支付时的费用
        buyOrderDetail.setVendorPercent(orderDetail.getVendorPercent()); //版税费率
        buyOrderDetail.setPriceFixed(orderDetail.getPriceFixed());
        buyOrderDetail.setPriceSelling(orderDetail.getPriceSelling());

        buyOrderDetail.setIncomeTotal(orderDetail.getIncomeTotal().multiply(countBigDecimal));
        buyOrderDetail.setIncomeReal(orderDetail.getIncomeReal().multiply(countBigDecimal));
        buyOrderDetail.setIncomeVendor(orderDetail.getIncomeVendor().multiply(countBigDecimal));
        buyOrderDetail.setIncomePlat(orderDetail.getIncomePlat().multiply(countBigDecimal));
        buyOrderDetail.setNetSales(orderDetail.getNetSales().multiply(countBigDecimal));

        buyOrderDetail.setPurchasedAt(orderDetail.getPurchasedAt());

        buyOrderDetail.setQuantity(countBigDecimal.intValue());
        buyOrderDetail.setPurchasedDay(orderDetail.getPurchasedDay());
        buyOrderDetail.setUserEmail(orderDetail.getUserEmail());
        buyOrderDetail.setUserId(orderDetail.getUserId());
        buyOrderDetail.setIsCounted(EBookConstant.BalanceStatus.NOT);
        buyOrderDetail.setPublisherId(orderDetail.getPublisherId());
        buyOrderDetail.setBookPubCode(orderDetail.getBookPubCode());
        buyOrderDetail.setBookEsin(orderDetail.getBookEsin());
        buyOrderDetail.setRateValue(orderDetail.getRateValue());

        buyOrderDetail.setOrderFrom(orderDetail.getOrderFrom());
    }

    /**
     * <p>插入license信息</p>
     * <p>为电子书DRM保护创建license记录和license状态记录</p>
     * @param id license唯一标识
     * @param userId 用户ID
     * @param contentFk 内容外键
     * @return void
     * @since : 2021/8/30
     **/
    private void insertLicense(String id, String userId, String contentFk){
        Date date = new Date();
        //"INSERT INTO license (id, user_id, provider, issued, rights_print, rights_copy, content_fk, lsd_status) VALUES (?, ?, 'https://ebook.endao.co', ? , 10, 5000, ?, 201)";
        int saveFlag = eBookJdbcTemplate.update(insertLicense,((PreparedStatement ps)->{
            ps.setString(1,id);//id
            ps.setString(2,userId);//user_id
            ps.setTimestamp(3,new Timestamp(date.getTime()));//issued
            ps.setString(4,contentFk);//, content_fk

        }));

        LicenseStatus licenseStatus = new LicenseStatus();
        licenseStatus.setLicenseRef(id);
        licenseStatus.setStatus(1);//默认是1，有设备下载后就是2
        licenseStatus.setLicenseUpdated(date);
        licenseStatus.setStatusUpdated(date);
        licenseStatus.setDeviceCount(0);
        licenseStatus.setPotentialRightsEnd(date);

        licenseStatusMapper.insert(licenseStatus);
    }
}
