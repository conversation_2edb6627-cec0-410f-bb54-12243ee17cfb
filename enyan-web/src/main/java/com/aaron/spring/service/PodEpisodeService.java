package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodEpisode;
import com.aaron.spring.model.PodEpisodeExample;

/**
 * @Author: <PERSON>
 * @Description: 播客单集服务接口
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
public interface PodEpisodeService extends IService<PodEpisode, PodEpisodeExample> {
    /**
     * <p>按播客ID查询单集列表</p>
     * @param page  分页参数
     * @param record  播客
     * @return  com.aaron.mybatis.dao.pojo.Page
     * @since  : 2025/5/13
     * @implNote 这个方法是重写的，所有的参数都需要new一个对象
     *      例如：page=new Page<>();  record = new PodEpisode();
     *      为了避免在其他地方修改了对象的值
     *      详细信息可以查看 {@link IService} 接口
     **/
    Page queryEpisodesByRestObj(Page<PodEpisode> page, PodEpisode record);

    /**
     * <p>增加单集播放量</p>
     * @param id 单集ID
     * @return int
     * @since : 2025/5/13
     **/
    int updateEpisodeListenCountById(Long id);

    /**
     * <p>增加单集喜欢量</p>
     * @param id 单集ID
     * @return int
     * @since : 2025/5/13
     **/
    int updateEpisodeLikeCountById(Long id);
    
    /**
     * <p>根据播客ID获取封面图片URL</p>
     * @param podcastId 播客ID
     * @return 封面图片URL
     * @since : 2025/5/17
     **/
    String getPodcastCoverImageUrl(Long podcastId);
    
    /**
     * <p>根据播客ID获取详情页长条图片URL</p>
     * @param podcastId 播客ID
     * @return 详情页长条图片URL
     * @since : 2025/6/3
     **/
    String getPodcastCoverImageUrl2(Long podcastId);
    
    /**
     * <p>记录单集开始播放</p>
     * @param episodeId 单集ID
     * @param email 用户邮箱
     * @return 是否成功
     * @since : 2025/5/22
     **/
    boolean recordEpisodePlayStart(Long episodeId, String email);
    
    /**
     * <p>记录单集播放完成</p>
     * @param episodeId 单集ID
     * @param email 用户邮箱
     * @return 是否成功
     * @since : 2025/5/22
     **/
    boolean recordEpisodePlayStop(Long episodeId, String email);
    

}
