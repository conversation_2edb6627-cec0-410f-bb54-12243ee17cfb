package com.aaron.spring.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/8/3
 * @Modified By:
 */
@Configuration
public class InitSystemConfig {
	@Value("${ssl.mysql.trustStore}")
	private String trustStore;

	@Value("${ssl.mysql.trustStorePassword}")
	private String trustStorePassword;

	@PostConstruct
	public void init(){
		//设置服务器证书 需要单独处理azure mysql的证书访问
//		System.setProperty("javax.net.ssl.trustStore", trustStore);
//		System.setProperty("javax.net.ssl.trustStorePassword",trustStorePassword);
	}
}
