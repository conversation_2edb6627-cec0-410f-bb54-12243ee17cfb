package com.aaron.spring.service.page;

import java.util.Map;

/** 
 * 封装查询参数和查询条件 
 *  
 * <AUTHOR> 
 *   
 */  
public class Query {  
    private Map<String, Object> queryParams;  
    private Pages pages;  
  
    public Map<String, Object> getQueryParams() {  
        return queryParams;  
    }  
  
    public void setQueryParams(Map<String, Object> queryParams) {  
        this.queryParams = queryParams;  
    }

	public Pages getPages() {
		return pages;
	}

	public void setPages(Pages pages) {
		this.pages = pages;
	}

	
}
