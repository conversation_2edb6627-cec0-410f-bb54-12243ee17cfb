package com.aaron.spring.service;

import com.aaron.drm.model.DrmInfo;
import com.aaron.spring.model.*;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/2
 * @Modified By:
 */
public interface EnyanOrderService extends IService<EnyanOrder,EnyanOrderExample>{
    List<EnyanOrder> findRecordsWithBLOBsByOrder(EnyanOrder order);

    List<EnyanOrder> findRecordsByOrder(EnyanOrder order);

    List<EnyanOrder> findRecordsBlobByOrderNum(String orderNum);

    ExecuteResult<String> deleteOrder(EnyanOrder order) ;

    ExecuteResult<String> updateOrderExpired();


    /**
     * <p>注销用户</p>
     * @param email
     * @param revokedEmail
     * @return com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2022/8/26
     **/
    ExecuteResult<String> revokeUser(String email, String revokedEmail);

    /**
     * <p><code>email</code> 已经购买 <code>bookId</code> 的订单</p>
     * @param bookId
     * @param email
     * @return: java.util.List<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-22
     */
    List<EnyanOrder> selectByBookId(Long bookId, String email);
    /**
     * <p>查询用户是否使用了优惠码</p>
     * @param couponCode
     * @param email
     * @return: java.util.List<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-27
     */
    List<EnyanOrder> selectByCouponCodeAndEmail(String couponCode, String email);

    /**
     * <p>查询用户使用了优惠码个数</p>
     * @param couponCode
     * @param email
     * @return long
     * @since : 2021/9/9
     **/
    long selectCountByCouponCodeAndEmail(String couponCode, String email);

    /***
     * <p>兑换码兑换书籍，保存订单信息并更新兑换码信息</p>
     *  * @param order
     * @param redeemCodeToUpdate
     * @return: void
     * @since : 2021/3/23
     */
    void saveOrderRedeem(EnyanOrder order, List<EnyanBook> bookList, EnyanRedeemCode redeemCodeToUpdate);
    /**
     * <p>已经支付成功的订单，进行更新及相应处理</p>
     *  * @param order
     * @return: void
     * @since : 2021/3/23
     */
    void saveOrderHasPay(EnyanOrder order);

    /**
     * <p>只是修改订单状态为已支付（因为有时候订单处理周期比较长）</p>
     * @param order
     * @return void
     * @since : 2022/1/28
     **/
    void updateOrderToPaid(EnyanOrder order);
    /**
     * <p></p>
     * @param drmInfo
     * @return com.aaron.drm.model.Licenses
     * @since : 2021/12/20
     **/
    String saveLicensesByDrmInfo(EnyanBookBuy bookBuy, DrmInfo drmInfo);
}
