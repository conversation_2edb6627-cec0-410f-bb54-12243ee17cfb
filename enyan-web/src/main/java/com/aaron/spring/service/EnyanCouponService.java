package com.aaron.spring.service;

import com.aaron.spring.model.EnyanCoupon;
import com.aaron.spring.model.EnyanCouponExample;
import com.aaron.spring.model.EnyanRefund;
import com.aaron.spring.model.EnyanRefundExample;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/3/25
 * @Modified By:
 */
public interface EnyanCouponService extends IService<EnyanCoupon, EnyanCouponExample>{
	/**
	 * <p>根据code获取优惠码信息</p>
	 * @param code
	 * @return com.aaron.spring.model.EnyanCoupon
	 * @since : 2021/9/7
	 **/
	EnyanCoupon getCouponByCode(String code);

	/**
	 * <p>更新优惠码的计数</p>
	 * @param code
	 * @return void
	 * @since : 2021/9/7
	 **/
	void updateCouponCount(String code);
}
