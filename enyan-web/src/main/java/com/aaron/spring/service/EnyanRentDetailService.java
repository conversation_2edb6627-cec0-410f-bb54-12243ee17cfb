package com.aaron.spring.service;

import com.aaron.spring.model.EnyanRent;
import com.aaron.spring.model.EnyanRentDetail;
import com.aaron.spring.model.EnyanRentDetailExample;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
public interface EnyanRentDetailService extends IService<EnyanRentDetail, EnyanRentDetailExample>{
	/**
	 * <p>根据条件查询数据列表</p>
	 * @param record
	 * @return java.util.List<com.aaron.spring.model.EnyanRentDetail>
	 * @since : 2022/11/4
	 **/
	List<EnyanRentDetail> findRecords(EnyanRentDetail record);

	/**
	 * <p>获取今天的订单数（已经支付）</p>
	 * @param orderNum
	 * @return long
	 * @since : 2022/12/15
	 **/
	long countOfTodayOrder(String orderNum);
}
