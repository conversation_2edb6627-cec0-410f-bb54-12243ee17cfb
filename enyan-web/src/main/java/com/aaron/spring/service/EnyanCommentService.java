package com.aaron.spring.service;

import com.aaron.spring.model.EnyanComment;
import com.aaron.spring.model.EnyanCommentExample;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
public interface EnyanCommentService extends IService<EnyanComment, EnyanCommentExample>{

	/**
	 * <p>增加喜欢量</p>
	 * @param id
	 * @return int
	 * @since : 2023/4/21
	 **/
	int updateCommentLikeCountById(Long id);

	/**
	 * <p>删除评论，如果是子评论还需要减父评论的计数</p>
	 * @param id
	 * @param email
	 * @return int
	 * @since : 2023/4/23
	 **/
	int updateCommentToDeletedById(Long id, String email, Long parentId);
}
