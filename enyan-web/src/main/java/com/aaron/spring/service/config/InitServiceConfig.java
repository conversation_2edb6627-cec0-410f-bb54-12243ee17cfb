package com.aaron.spring.service.config;

import com.aaron.spring.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/16
 * @Modified By:
 */
@Component
public class InitServiceConfig {
    private final Logger logger = LoggerFactory.getLogger(InitServiceConfig.class);
    @Resource
    private EnyanCategoryService enyanCategoryService;

    @Resource
    private EnyanPublisherService enyanPublisherService;

    @Resource
    private EnyanDiscountService enyanDiscountService;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanConfigService enyanConfigService;

    @Resource
    private EnyanBannerService enyanBannerService;

    @Resource
    private EnyanReadingService enyanReadingService;

    @Resource
    private EnyanDailyWordsService enyanDailyWordsService;

    @Resource
    private EnyanRentService enyanRentService;

    @PostConstruct
    public void init(){
        //logger.info("------ InitServiceConfig init -------");
        try {
            enyanCategoryService.initCategories();
            enyanPublisherService.initPublishers();
            enyanDiscountService.initDiscounts();
            enyanBookService.initIndexAllRecommended();
            enyanConfigService.initConfigs();
            enyanBookService.initBookNameAndValue(false);
            enyanBannerService.initBanners();
            enyanReadingService.initReadings();
            enyanDailyWordsService.initDailywords();
            enyanRentService.initRentsInfo();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @PreDestroy
    public void  destroy(){
        logger.info("I'm  destroy method  using  @PreDestroy.....");
    }
}
