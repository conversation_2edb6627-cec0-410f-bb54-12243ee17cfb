package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.data.repository.mongo.HighlightRepository;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanReaderHighlightsMapper;
import com.aaron.spring.mapper.custom.EnyanReaderHighlightsCustomMapper;
import com.aaron.spring.model.EnyanReaderHighlights;
import com.aaron.spring.model.EnyanReaderHighlightsExample;
import com.aaron.spring.service.EnyanReaderHighlightService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.convert.QueryByExamplePredicateBuilder;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2020-05-07
 * @Modified By:
 */
@Service
public class EnyanReaderHighlightServiceImpl extends BaseService<EnyanReaderHighlights, EnyanReaderHighlightsExample> implements EnyanReaderHighlightService {
    @Resource
    private EnyanReaderHighlightsMapper enyanReaderHighlightsMapper;

    @Resource
    private EnyanReaderHighlightsCustomMapper enyanReaderHighlightsCustomMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    HighlightRepository highlightRepository;

    @Override
    public List<EnyanReaderHighlights> findEnyanReaderHighlightsList(EnyanReaderHighlights record) {
        if (null == record){
            record = new EnyanReaderHighlights();
        }
        /*
        try {
            EnyanReaderHighlightsExample example = new EnyanReaderHighlightsExample();
            EnyanReaderHighlightsExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            criteria.andUpdateTimeGreaterThanOrEqualTo(record.getUpdateTime());

            List<EnyanReaderHighlights> list = enyanReaderHighlightsMapper.selectByExample(example);

            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }*/
        Query query = getQuery(record);
//        query.skip(skip);
//        query.limit(limit);
        return mongoTemplate.find(query, EnyanReaderHighlights.class);
    }

    @Override
    public Page findReaderHighlightsGTUpdateTime(Page<EnyanReaderHighlights> page, EnyanReaderHighlights record) {
        if (null == record){
            record = new EnyanReaderHighlights();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            /*
            EnyanReaderHighlightsExample example = new EnyanReaderHighlightsExample();
            EnyanReaderHighlightsExample.Criteria criteria = example.createCriteria();
            example.setPage(page);
            example.setOrderByClause("update_time asc");

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            criteria.andUpdateTimeGreaterThan(record.getUpdateTime());

            List<EnyanReaderHighlights> list = enyanReaderHighlightsMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }*/

            /*
            Query query = getQuery(record);
//        query.skip(skip);
            query.limit(page.getPageSize());
            Sort.TypedSort<EnyanReaderHighlights> highlightsTypedSort = Sort.sort(EnyanReaderHighlights.class);
            Sort sort = highlightsTypedSort.by(EnyanReaderHighlights::getUpdateTime).ascending();
            ExampleMatcher matcher = ExampleMatcher.matching()
                    .withMatcher("userEmail", match -> match.exact().ignoreCase());
            Example<EnyanReaderHighlights> example = Example.of(record,matcher);
            */
            Pageable pageableRequest = PageRequest.of(0, page.getPageSize(),Sort.by(Sort.Direction.ASC, "updateTime"));
            org.springframework.data.domain.Page<EnyanReaderHighlights> pageRecord = highlightRepository.findByUserEmailAndUpdateTimeGreaterThan(record.getUserEmail(), record.getUpdateTime(), pageableRequest);
            List<EnyanReaderHighlights> list = pageRecord.getContent();
            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }

        return page;
    }

    @Override
    public Page findReaderHighlightOnlyGTUpdateTime(Page<EnyanReaderHighlights> page, EnyanReaderHighlights record) {
        if (null == record){
            record = new EnyanReaderHighlights();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            Pageable pageableRequest = PageRequest.of(0, page.getPageSize(),Sort.by(Sort.Direction.ASC, "updateTime"));
            org.springframework.data.domain.Page<EnyanReaderHighlights> pageRecord = highlightRepository.findByUserEmailAndTypeAndUpdateTimeGreaterThan(record.getUserEmail(), 0,record.getUpdateTime(), pageableRequest);
            List<EnyanReaderHighlights> list = pageRecord.getContent();
            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }

        return page;
    }

    @Override
    public Page findReaderBookmarkOnlyGTUpdateTime(Page<EnyanReaderHighlights> page, EnyanReaderHighlights record) {
        if (null == record){
            record = new EnyanReaderHighlights();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            Pageable pageableRequest = PageRequest.of(0, page.getPageSize(),Sort.by(Sort.Direction.ASC, "updateTime"));
            org.springframework.data.domain.Page<EnyanReaderHighlights> pageRecord = highlightRepository.findByUserEmailAndTypeAndUpdateTimeGreaterThan(record.getUserEmail(), 1,record.getUpdateTime(), pageableRequest);
            List<EnyanReaderHighlights> list = pageRecord.getContent();
            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }

        return page;
    }

    @Override
    public Page findReaderHighlights(Page<EnyanReaderHighlights> page, Long bookId, Integer type) {
        if (null == page){
            page = new Page<>();
        }
        try {
            Pageable pageableRequest = PageRequest.of(page.getCurrentPage()-1, page.getPageSize(),Sort.by(Sort.Direction.DESC, "updateTime"));
            org.springframework.data.domain.Page<EnyanReaderHighlights> pageRecord = highlightRepository.findAllByBookIdAndType(bookId,type,pageableRequest);
            List<EnyanReaderHighlights> list = pageRecord.getContent();
            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
            page.setTotalRecord(pageRecord.getTotalElements());
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }

        return page;
    }

    @Override
    public Page queryRecords(Page<EnyanReaderHighlights> page, EnyanReaderHighlights record) {

        if (null == page){
            page = new Page<>();
        }
        try {
            Pageable pageableRequest = PageRequest.of(page.getCurrentPage(), page.getPageSize(),Sort.by(Sort.Direction.DESC, "updateTime"));
            org.springframework.data.domain.Page<EnyanReaderHighlights> pageRecord = highlightRepository.findAllByType(1, pageableRequest);
            List<EnyanReaderHighlights> list = pageRecord.getContent();
            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanReaderHighlights> queryRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanReaderHighlights> queryRecordByPrimaryKey(String pkId) {
        ExecuteResult<EnyanReaderHighlights> result = new ExecuteResult<>();
        try {
            EnyanReaderHighlights record= enyanReaderHighlightsMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanReaderHighlights();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> addHighlights(List<EnyanReaderHighlights> list) {
        ExecuteResult<String> result = new ExecuteResult<>();
//        highlightRepository.saveAll(list);
        try {
            for (EnyanReaderHighlights highlights : list){
                if (StringUtils.isBlank(highlights.getUserEmail())){
                    continue;
                }
                /*
                EnyanReaderHighlights highlight = enyanReaderHighlightsMapper.selectByPrimaryKey(tmp.getId());
                if (null == highlight){
                    try {
                        enyanReaderHighlightsCustomMapper.add(tmp);
                    }catch (DuplicateKeyException e){//因已添加索引

                    }
                }else {
                    if (!tmp.getUserEmail().equals(highlight.getUserEmail())){
                        continue;
                    }
                    EnyanReaderHighlights newHighlight = new EnyanReaderHighlights();
                    newHighlight.setIsDeleted(tmp.getIsDeleted());
                    //newHighlight.setHighlightId(tmp.getHighlightId());
                    newHighlight.setUpdateTime(tmp.getUpdateTime());
                    newHighlight.setId(tmp.getId());
                    newHighlight.setAnnotation(tmp.getAnnotation());
                    //newHighlight.setAnnotationMarkStyle(tmp.getAnnotationMarkStyle());
                    newHighlight.setColor(tmp.getColor());
                    enyanReaderHighlightsMapper.updateByPrimaryKeySelective(newHighlight);
                }*/
                /*
                EnyanReaderHighlights queryObj = new EnyanReaderHighlights();
                queryObj.setId(highlights.getId());
                Query query = this.getQuery(queryObj);
                Update update = new Update();
                update.set("id",highlights.getId());
                update.set("highlightId",highlights.getHighlightId());
                update.set("userEmail",highlights.getUserEmail());
                update.set("bookId",highlights.getBookId());
                update.set("publicationId",highlights.getPublicationId());
                update.set("resourceIndex",highlights.getResourceIndex());
                update.set("resourceHref",highlights.getResourceHref());
                update.set("resourceType",highlights.getResourceType());
                update.set("resourceTitle",highlights.getResourceTitle());
                update.set("location",highlights.getLocation());
                update.set("locatorText",highlights.getLocatorText());
                update.set("color",highlights.getColor());
                update.set("annotation",highlights.getAnnotation());
                update.set("type",highlights.getType());
                update.set("creationDate",highlights.getCreationDate());
                update.set("isDeleted", highlights.getIsDeleted());
                update.set("updateTime", highlights.getUpdateTime());
                mongoTemplate.upsert(query, update, EnyanReaderHighlights.class);*/
                mongoTemplate.save(highlights);
            }
        }catch (Exception e){
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> addHighlight(EnyanReaderHighlights highlights) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            if (StringUtils.isBlank(highlights.getUserEmail())){
                return result;
            }
            /*
//            EnyanReaderHighlights highlight = enyanReaderHighlightsMapper.selectByPrimaryKey(highlights.getId());
            long count = enyanReaderHighlightsCustomMapper.countOfHighlightById(highlights.getId());
            if (count > 0){
                EnyanReaderHighlights newHighlight = new EnyanReaderHighlights();
                newHighlight.setIsDeleted(highlights.getIsDeleted());
                //newHighlight.setHighlightId(tmp.getHighlightId());
                newHighlight.setUpdateTime(highlights.getUpdateTime());
                newHighlight.setId(highlights.getId());
                newHighlight.setAnnotation(highlights.getAnnotation());
                //newHighlight.setAnnotationMarkStyle(tmp.getAnnotationMarkStyle());
                newHighlight.setColor(highlights.getColor());
                enyanReaderHighlightsMapper.updateByPrimaryKeySelective(newHighlight);
            }else {
                try {
                    enyanReaderHighlightsCustomMapper.add(highlights);
                }catch (DuplicateKeyException e){//因已添加索引

                }
            }

            */
            /*
            EnyanReaderHighlights queryObj = new EnyanReaderHighlights();
            queryObj.setId(highlights.getId());
            Query query = this.getQuery(queryObj);
            Update update = new Update();
            update.set("id",highlights.getId());
            update.set("highlightId",highlights.getHighlightId());
            update.set("userEmail",highlights.getUserEmail());
            update.set("bookId",highlights.getBookId());
            update.set("publicationId",highlights.getPublicationId());
            update.set("resourceIndex",highlights.getResourceIndex());
            update.set("resourceHref",highlights.getResourceHref());
            update.set("resourceType",highlights.getResourceType());
            update.set("resourceTitle",highlights.getResourceTitle());
            update.set("location",highlights.getLocation());
            update.set("locatorText",highlights.getLocatorText());
            update.set("color",highlights.getColor());
            update.set("annotation",highlights.getAnnotation());
            update.set("type",highlights.getType());
            update.set("creationDate",highlights.getCreationDate());
            update.set("isDeleted", highlights.getIsDeleted());
            update.set("updateTime", highlights.getUpdateTime());
            mongoTemplate.upsert(query, update, EnyanReaderHighlights.class);*/
            mongoTemplate.save(highlights);
        }catch (Exception e){
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public Boolean addHighlightOnly(EnyanReaderHighlights enyanReaderHighlights) {
        mongoTemplate.insert(enyanReaderHighlights);
        return true;
    }

    @Override
    public Boolean addHighlightsOnly(List<EnyanReaderHighlights> highlightsList) {
        mongoTemplate.insertAll(highlightsList);
        return true;
    }

    @Override
    public int updateHighlightHref(long bookId, String hrefOld, String hrefNew, long now) {
        return enyanReaderHighlightsCustomMapper.updateHighlightHref(bookId, hrefOld, hrefNew, now);
    }

    @Override
    public long count() {
        return highlightRepository.count();
    }

    @Override
    public long countInDB() {
        EnyanReaderHighlightsExample example = new EnyanReaderHighlightsExample();
        return enyanReaderHighlightsMapper.countByExample(example);
    }

    @Override
    public ExecuteResult<String> revokeUser(String email, String revokedEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            //EnyanReaderHighlights highlights = new EnyanReaderHighlights();
            //highlights.setUserEmail(email);
            //highlightRepository.delete(highlights);
            mongoTemplate.remove(Query.query(Criteria.where("userEmail").is(email)), EnyanReaderHighlights.class);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    private Query getQuery(EnyanReaderHighlights criteriaObj) {
        if (criteriaObj == null) {
            criteriaObj = new EnyanReaderHighlights();
        }
        Query query = new Query();
        if (criteriaObj.getId() != null) {
            Criteria criteria = Criteria.where("id").is(criteriaObj.getId());
            query.addCriteria(criteria);
        }
        if (criteriaObj.getUserEmail() != null) {
            Criteria criteria = Criteria.where("userEmail").is(criteriaObj.getUserEmail());
            query.addCriteria(criteria);
        }
        if (criteriaObj.getUpdateTime() != null) {
            Criteria criteria = Criteria.where("updateTime").gte(criteriaObj.getUpdateTime());
            query.addCriteria(criteria);
        }
        /*
        if (criteriaObj.getAge() > 0) {
            Criteria criteria = Criteria.where("age").gt(criteriaObj.getAge());
            query.addCriteria(criteria);
        }
        if (criteriaObj.getName() != null) {
            Criteria criteria = Criteria.where("name").regex("^" + criteriaObj.getName());
            query.addCriteria(criteria);
        }*/

        return query;
    }
    /*
    public Specification<EnyanReaderHighlights> getSpecFromDatesAndExample(
            LocalDateTime from, LocalDateTime to, Example<EnyanReaderHighlights> example) {

        return (Specification<EnyanReaderHighlights>) (root, query, builder) -> {
            final List<Predicate> predicates = new ArrayList<>();

            if (from != null) {
                predicates.add(builder.greaterThan(root.get("dateField"), from));
            }
            if (to != null) {
                predicates.add(builder.lessThan(root.get("dateField"), to));
            }
            predicates.add(QueryByExamplePredicateBuilder.getPredicate(root, builder, example));

            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        }
    }*/
}
