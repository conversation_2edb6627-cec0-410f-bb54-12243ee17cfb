package com.aaron.spring.service;

import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.model.EnyanPublisherExample;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/2
 * @Modified By:
 */
public interface EnyanPublisherService extends IService<EnyanPublisher,EnyanPublisherExample>{
    /**
     * 初始化 Publishers
     * */
    void initPublishers();
    /**
     * 获取 Publishers
     * */
    List<EnyanPublisher> findPublishers(EnyanPublisher enyanPublisher);

    /**
     *
     * 根据查询项获取数据
     * @param enyanPublisher
     * @Date: 2017/12/27
     */
    List<EnyanPublisher> findPublishersReally(EnyanPublisher enyanPublisher);

    String getPublisherNameByID(Long publisherId);
}
