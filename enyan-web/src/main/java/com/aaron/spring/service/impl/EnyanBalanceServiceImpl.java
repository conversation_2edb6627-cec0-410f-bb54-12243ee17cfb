package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.mapper.EnyanBalanceMapper;
import com.aaron.spring.mapper.custom.EnyanBalanceCustomMapper;
import com.aaron.spring.mapper.custom.EnyanOrderDetailCustomMapper;
import com.aaron.spring.model.EnyanBalance;
import com.aaron.spring.model.EnyanBalanceExample;
import com.aaron.spring.model.EnyanOrderDetail;
import com.aaron.spring.service.EnyanBalanceService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/6/4
 * @Modified By:
 */
@Service
public class EnyanBalanceServiceImpl implements EnyanBalanceService{

    @Resource
    private EnyanOrderDetailCustomMapper enyanOrderDetailCustomMapper;

    @Resource
    private EnyanBalanceMapper enyanBalanceMapper;

    @Resource
    private EnyanBalanceCustomMapper enyanBalanceCustomMapper;

    @Override
    public Page queryRecords(Page<EnyanBalance> page, EnyanBalance record) {

        if (null == record){
            record = new EnyanBalance();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanBalanceExample example = new EnyanBalanceExample();
            EnyanBalanceExample.Criteria criteria = example.createCriteria();

            record.setPage(page);
            example.setOrderByClause("purchased_month desc");

            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }

            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andPurchasedMonthGreaterThanOrEqualTo(Integer.parseInt(record.getStartDate()));
            }

            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andPurchasedMonthLessThanOrEqualTo(Integer.parseInt(record.getEndDate()));
            }

            List<EnyanBalance> list = enyanBalanceMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanBalance> queryRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanBalance> addRecord(EnyanBalance record) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanBalance> updateRecord(EnyanBalance record) {
        return null;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public String checkSaveRecord(EnyanBalance record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanBalance record) {
        return null;
    }

    @Override
    public void createEnyanBalanceInfo(int yearMonth) {
        try {
            EnyanOrderDetail record = new EnyanOrderDetail();
            record.setStartDate(yearMonth+"00");
            record.setEndDate(yearMonth+"31");
            List<EnyanOrderDetail> list = enyanOrderDetailCustomMapper.queryOrderDetailByMonth(record);

            if (!list.isEmpty()){
                for (int i = 0; i < list.size(); i++) {
                    EnyanOrderDetail orderDetail = list.get(i);
                    orderDetail.setPurchasedDayEnd(orderDetail.getPurchasedDayStart());

                    EnyanBalance balance = new EnyanBalance();
                    balance.setIncomePlat(orderDetail.getIncomePlat());
                    balance.setIncomeReal(orderDetail.getIncomeReal());
                    balance.setIncomeTotal(orderDetail.getIncomeTotal());
                    balance.setIncomeVendor(orderDetail.getIncomeVendor());
                    balance.setIsCounted(EBookConstant.BalanceStatus.NOT);
                    balance.setOrderType(0);
                    balance.setPriceFixed(orderDetail.getPriceFixed());
                    balance.setPriceSelling(orderDetail.getPriceSelling());
                    balance.setPublisherId(orderDetail.getPublisherId());
                    balance.setPurchasedMonth(yearMonth);
                    balance.setQuantity(orderDetail.getQuantity());

                    try {
                        enyanBalanceMapper.insert(balance);
                    }catch (DuplicateKeyException ex){

                    }
                }
            }else {
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<EnyanBalance> findBalanceByIds(List<Long> balanceIdList, Long publisherId) {
        if (balanceIdList.isEmpty()){
            return new ArrayList<>();
        }
        return enyanBalanceCustomMapper.findBalanceByIds(balanceIdList, publisherId);
    }

}
