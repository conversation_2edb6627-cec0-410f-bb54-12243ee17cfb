package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodUserEpisodeInteraction;
import com.aaron.spring.model.PodUserEpisodeInteractionExample;
import com.aaron.util.ExecuteResult;

/**
 * @Author: <PERSON>
 * @Description: 用户播客单集交互服务接口
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
public interface PodUserEpisodeInteractionService extends IService<PodUserEpisodeInteraction, PodUserEpisodeInteractionExample> {
    
    /**
     * <p>记录用户的播放进度</p>
     * @param userId 用户ID
     * @param episodeId 单集ID
     * @param progressSeconds 进度（秒）
     * @return ExecuteResult<PodUserEpisodeInteraction>
     * @since : 2025/5/13
     **/
    ExecuteResult<PodUserEpisodeInteraction> recordPlayProgress(Long userId, Long episodeId, Integer progressSeconds);
    
    /**
     * <p>获取用户的播放历史</p>
     * @param page 分页参数
     * @param userId 用户ID
     * @return com.aaron.mybatis.dao.pojo.Page
     * @since : 2025/5/13
     **/
    Page queryUserPlayHistory(Page<PodUserEpisodeInteraction> page, Long userId);
    
    /**
     * <p>获取用户播放进度</p>
     * @param userId 用户ID
     * @param episodeId 单集ID
     * @return ExecuteResult<PodUserEpisodeInteraction>
     * @since : 2025/5/13
     **/
    ExecuteResult<PodUserEpisodeInteraction> getUserPlayProgress(Long userId, Long episodeId);
}
