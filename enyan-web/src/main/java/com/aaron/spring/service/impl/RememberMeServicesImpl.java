package com.aaron.spring.service.impl;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.rememberme.PersistentRememberMeToken;
import org.springframework.security.web.authentication.rememberme.PersistentTokenBasedRememberMeServices;
import org.springframework.security.web.authentication.rememberme.PersistentTokenRepository;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 *
 * @Author: Aaron <PERSON>
 * @Date: Created in  2018/3/23
 * @Modified By:
 */
//@Service("rememberMeServices")
public class RememberMeServicesImpl extends PersistentTokenBasedRememberMeServices {
    public RememberMeServicesImpl(String key, UserDetailsService userDetailsService, PersistentTokenRepository tokenRepository) {
        super(key, userDetailsService, tokenRepository);
    }
    protected void onLoginSuccess(HttpServletRequest request,
                                  HttpServletResponse response, Authentication successfulAuthentication) {
        logger.debug("----------------RememberMeServicesImpl----------------");
        super.onLoginSuccess(request,response,successfulAuthentication);
    }
}
