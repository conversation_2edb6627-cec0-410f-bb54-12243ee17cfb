package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanWish;
import com.aaron.spring.model.EnyanWishExample;
import com.aaron.util.ExecuteResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/2/3
 * @Modified By:
 */
public interface EnyanWishService extends IService<EnyanWish,EnyanWishExample>{

    ExecuteResult<String> deleteByExample(EnyanWish record);
    /**
     *
     *  查询收藏的书籍
     * @param page
     * @param enyanBook
     * @Date: 2018/2/5
     */
    Page queryWishes(Page<EnyanBook> page, EnyanWish enyanWish);
    /**
     *
     *  删除收藏信息
     * @param bookId
     * @param userId
     * @Date: 2018/2/3
     */
//    ExecuteResult<String> deleteWish(Long bookId, Long userId) ;

    /**
     * <p>获取所有的userId，存储与bookId上</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2021/4/28
     **/
    List<EnyanBook> findDistinctUserIdAsBookIdList();

    /**
     * <p>根据userId更新email</p>
     * @param toEmail
     * @param userId
     * @return int
     * @since : 2021/4/28
     **/
    int updateEmailById(String toEmail, Long userId);

    /**
     * <p>删除收藏信息</p>
     * @param email
     * @param bookId
     * @return com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2021/5/7
     **/
    int deleteByEmailAndBookId(String email, Long bookId);

    /**
     * <p>批量删除收藏</p>
     * @param email
     * @param bookIds
     * @return int
     * @since : 2021/5/11
     **/
    int deleteByEmailAndBookIdList(String email, List<Long> bookIds);

    /**
     * <p>获取收藏的总数</p>
     * @param email
     * @return long
     * @since : 2021/5/8
     **/
    long countOfWishByEmail(String email);

    /**
     * <p>获取是否有某本书</p>
     * @param email
     * @param bookId
     * @return long
     * @since : 2021/5/8
     **/
    long countOfWishByEmailAndBookId(String email, Long bookId);

    /**
     * <p>注销用户</p>
     * @param email
     * @param revokedEmail
     * @return com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2022/8/26
     **/
    ExecuteResult<String> revokeUser(String email, String revokedEmail);
}
