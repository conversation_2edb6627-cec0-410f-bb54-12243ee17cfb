package com.aaron.spring.service;

import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.EnyanRefund;
import com.aaron.spring.model.EnyanRefundExample;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/3/25
 * @Modified By:
 */
public interface EnyanRefundService extends IService<EnyanRefund, EnyanRefundExample>{
    /**
     * <p>根据record的信息查询数据</p>
     *  * @param record
     * @return: java.util.List<com.aaron.spring.model.EnyanRefund>
     * @since : 2021/4/1
     */
    List<EnyanRefund> findRecordByRefund(EnyanRefund record);
}
