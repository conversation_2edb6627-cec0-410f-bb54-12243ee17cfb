package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.data.DataInterface;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanCouponMapper;
import com.aaron.spring.mapper.custom.EnyanCouponCustomMapper;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanCoupon;
import com.aaron.spring.model.EnyanCouponExample;
import com.aaron.spring.service.EnyanCouponService;
import com.aaron.util.DateUtil;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/8/31
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanCouponServiceImpl implements EnyanCouponService {
    @Resource
    private EnyanCouponMapper enyanCouponMapper;

    @Resource
    private EnyanCouponCustomMapper enyanCouponCustomMapper;

    @Resource
    private DataInterface dataInterface;

    @Override
    public Page queryRecords(Page<EnyanCoupon> page, EnyanCoupon record) {
        if (null == record){
            record = new EnyanCoupon();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanCouponExample example = new EnyanCouponExample();
            EnyanCouponExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            if (StringUtils.isNotBlank(record.getCouponName())){
                criteria.andCouponNameRegexp(record.getCouponName());
            }
            criteria.andIsDeletedEqualTo(0);

            example.setOrderByClause("create_at desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanCouponMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanCoupon> list = enyanCouponMapper.selectByExample(example);
            if (!list.isEmpty()){
                page.setRecords(list);
                page.setTotalRecord(count);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanCoupon> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanCoupon> result = new ExecuteResult<>();
        try {
            EnyanCoupon record = enyanCouponMapper.selectByPrimaryKey(pkId);
            if (null == record){
                return result;
            }
            if (StringUtils.isNotBlank(record.getBookString())){
                HashSet<Long> set = JSON.parseObject(record.getBookString(), new TypeReference<HashSet<Long>>(){});
                record.setBookSet(set);
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanCoupon> addRecord(EnyanCoupon record) {
        ExecuteResult<EnyanCoupon> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanCouponMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanCoupon> updateRecord(EnyanCoupon record) {
        ExecuteResult<EnyanCoupon> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            dataInterface.delCoupon(record.getCouponCode());
            record.setCouponCode(null);
            int saveFlag = enyanCouponMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            EnyanCoupon record = new EnyanCoupon();
            record.setDataId(pkId);
            int deleteFlag = enyanCouponMapper.updateByPrimaryKeySelective(record);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanCoupon record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanCoupon record) {
        return null;
    }

    public List<EnyanCoupon> findRecordByRefund(EnyanCoupon record) {
        if (null == record){
            record = new EnyanCoupon();
        }
        try {
            EnyanCouponExample example = new EnyanCouponExample();
            EnyanCouponExample.Criteria criteria = example.createCriteria();

            criteria.andIsDeletedEqualTo(0);
            example.setOrderByClause("create_at desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            List<EnyanCoupon> list = enyanCouponMapper.selectByExample(example);
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public EnyanCoupon getCouponByCode(String code) {
        EnyanCoupon coupon = dataInterface.getCouponByCode(code);
        if (null != coupon){
            return coupon;
        }
        List<EnyanCoupon> list = enyanCouponCustomMapper.getCouponRecordByCode(code);
        if (list.isEmpty()){
            return null;
        }
        coupon = list.get(0);
        if (StringUtils.isNotBlank(coupon.getBookString())){
            HashSet<Long> set = JSON.parseObject(coupon.getBookString(), new TypeReference<HashSet<Long>>(){});
            coupon.setBookSet(set);
        }
        dataInterface.saveCoupon(coupon);
        return coupon;
    }

    @Override
    public void updateCouponCount(String code) {
        EnyanCoupon coupon = dataInterface.getCouponByCode(code);
        if (null != coupon){
            coupon.setBuyCount(coupon.getBuyCount()+1);
            dataInterface.saveCoupon(coupon);
        }
        enyanCouponCustomMapper.updateCouponBuyCountByCode(code);
    }
}
