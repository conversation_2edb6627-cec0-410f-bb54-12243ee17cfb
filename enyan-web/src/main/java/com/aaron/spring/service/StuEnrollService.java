package com.aaron.spring.service;

import com.aaron.spring.model.EnyanFeedback;
import com.aaron.spring.model.EnyanFeedbackExample;
import com.aaron.spring.model.StuEnroll;
import com.aaron.spring.model.StuEnrollExample;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
public interface StuEnrollService extends IService<StuEnroll, StuEnrollExample>{
	/**
	 * <p>获取当前营期数据</p>
	 * @param
	 * @return java.util.List<com.aaron.spring.model.StuEnroll>
	 * @since : 2023/5/10
	 **/
	List<StuEnroll> findRecordsInCurrentTerms();

	StuEnroll getRecordByEmail(String email);

	StuEnroll getRecordByEnrollCode(String enrollCode);
	/**
	 * <p></p>
	 * @param enroll
	 * @param enrollAt
	 * @return void
	 * @since : 2023/5/11
	 **/
	void updateRecordToEnrollByEmail(StuEnroll enroll, Integer status, Date enrollAt);


	void updateRecordToEnrollByEnrollCode(StuEnroll enroll, Integer status, Date enrollAt);

	/**
	 * <p></p>
	 * @return void
	 * @since : 2023/5/11
	 **/
	void updateRecordToCheckinByEmail(StuEnroll enroll, Integer type, Integer status, Long time);


	void updateRecordToCheckinByEnrollCode(StuEnroll enroll, Integer type, Integer status, Long time);
}
