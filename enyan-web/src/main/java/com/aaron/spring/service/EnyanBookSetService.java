package com.aaron.spring.service;

import com.aaron.spring.model.*;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
public interface EnyanBookSetService extends IService<EnyanBookSet, EnyanBookSetExample>{
	/**
	 * <p>重置缓存</p>
	 * @param dataId
	 * @return void
	 * @since : 2024-09-14
	 **/
	void resetCache(Long dataId);

	/**
	 * <p>根据是否首页来获取书系书籍</p>
	 * @param index
	 * @return java.util.List<com.aaron.spring.model.EnyanBookSet>
	 * @since : 2024-10-05
	 **/
	List<EnyanBookSet> findBookSetByIndex(Integer index);
}
