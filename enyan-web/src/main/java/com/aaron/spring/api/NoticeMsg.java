package com.aaron.spring.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2024-02-25
 * @Modified By:
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NoticeMsg implements Serializable {
	@Serial
	private static final long serialVersionUID = -3982296571490183266L;
	private Long msgId;
	private String text;
	private Integer shouldShow;
	private Long createAt;
	private Long expiredAt;

	public Long getMsgId() {
		return msgId;
	}

	public void setMsgId(Long msgId) {
		this.msgId = msgId;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public Integer getShouldShow() {
		return shouldShow;
	}

	public void setShouldShow(Integer shouldShow) {
		this.shouldShow = shouldShow;
	}

	public Long getCreateAt() {
		return createAt;
	}

	public void setCreateAt(Long createAt) {
		this.createAt = createAt;
	}

	public Long getExpiredAt() {
		return expiredAt;
	}

	public void setExpiredAt(Long expiredAt) {
		this.expiredAt = expiredAt;
	}
}
