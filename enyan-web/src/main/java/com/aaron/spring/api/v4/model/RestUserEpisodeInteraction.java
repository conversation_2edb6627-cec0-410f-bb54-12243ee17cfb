package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.PodUserEpisodeInteraction;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: <PERSON>
 * @Description: 用户播客单集交互前端展示模型
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Data
@EqualsAndHashCode(callSuper = true) // 已明确指定包含父类字段
@NoArgsConstructor
public class RestUserEpisodeInteraction extends RestBaseDTO {
    private static final long serialVersionUID = 3745404191930344347L;

    private Long interactionId;
    private Long userId;
    private String userEmail;
    private Long episodeId;
    private Integer isLiked; //是否点赞(当前不用了，需要变为匿名点赞)
    private Integer playbackProgressSeconds;
    private Integer cumulativePlaybackSeconds;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastPlayedAt;
    
    private Integer isCompleted;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date downloadedAt;
    
    // 关联信息
    private String episodeTitle;
    private String podcastTitle;
    private String audioFileUrl;
    private Integer durationSeconds;
    private String durationFormatted;
    private Long podcastId;
    private String coverImageUrl;
    
    /**
     * 从PodUserEpisodeInteraction对象初始化RestUserEpisodeInteraction对象
     * @param interaction PodUserEpisodeInteraction对象
     */
    public void initFrom(PodUserEpisodeInteraction interaction) {
        if (interaction != null) {
            this.interactionId = interaction.getInteractionId();
            this.userId = interaction.getUserId();
            this.userEmail = interaction.getUserEmail();
            this.episodeId = interaction.getEpisodeId();
            this.isLiked = interaction.getIsLiked();
            this.playbackProgressSeconds = interaction.getPlaybackProgressSeconds();
            this.cumulativePlaybackSeconds = interaction.getCumulativePlaybackSeconds();
            this.lastPlayedAt = interaction.getLastPlayedAt();
            this.isCompleted = interaction.getIsCompleted();
            this.downloadedAt = interaction.getDownloadedAt();
        }
    }
}
