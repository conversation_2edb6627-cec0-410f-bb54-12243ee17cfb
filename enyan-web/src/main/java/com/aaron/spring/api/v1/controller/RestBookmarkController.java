package com.aaron.spring.api.v1.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v1.model.RestBook;
import com.aaron.spring.api.v1.model.RestBookmark;
import com.aaron.spring.api.v1.model.RestUser;
import com.aaron.spring.model.EnyanOrderDetail;
import com.aaron.spring.model.EnyanUserBookmarks;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanUserBookmarkService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.hwpf.usermodel.Bookmark;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2019-09-04
 * @Modified By:
 */
@RestController("RestBookmarkControllerV1")
@RequestMapping("/api/v1/bookmark")
public class RestBookmarkController extends RestBaseController {
    private final Log logger = LogFactory.getLog(RestBookmarkController.class);
    private static final int PAGE_SIZE = 30;
    @Resource
    private EnyanUserBookmarkService enyanUserBookmarkService;

    @RequestMapping(value = "/sync",method = RequestMethod.POST)
    public PageResult<RestBookmark> bookmarks(@RequestBody RestBookmark restBookmark){
        if (StringUtils.isBlank(restBookmark.getEmail()) || null == restBookmark.getUpdateTime()){
            PageResult<RestBookmark> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }
        Page<EnyanUserBookmarks> searchPage = new Page<>();
        searchPage.setCurrentPage(1);
        searchPage.setPageSize(PAGE_SIZE);

        EnyanUserBookmarks enyanUserBookmarks = new EnyanUserBookmarks();
        enyanUserBookmarks.setUserEmail(restBookmark.getEmail());
        enyanUserBookmarks.setUpdateTime(restBookmark.getUpdateTime());

        Page<EnyanUserBookmarks> bookmarksPage = enyanUserBookmarkService.findUserBookmarksGTUpdateTime(searchPage, enyanUserBookmarks);

        PageResult<RestBookmark> page = new PageResult<>(1,-1,searchPage.getPageSize());
        for (EnyanUserBookmarks obj :bookmarksPage.getRecords()){
            RestBookmark bookmark = new RestBookmark();
            bookmark.initFrom(obj);
            page.getResult().add(bookmark);
        }
        return page;
    }

    @RequestMapping(value = "/upload",method = RequestMethod.POST)
    public ExecuteResult<Long> upload(@RequestBody RestBookmark restBookmark){
        ExecuteResult<Long> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restBookmark.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (restBookmark.getList() == null || restBookmark.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
            return result;
        }
        //System.out.printf("upload list:"+restBookmark);

        Long updateTime = System.currentTimeMillis();
        List<EnyanUserBookmarks> list = new ArrayList<>();
        boolean canNotUpload = false; //不能正常上传
        for (RestBookmark tmp : restBookmark.getList()){
            if (StringUtils.isNotBlank(restBookmark.getChapterName()) && restBookmark.getChapterName().length() > 100){
                canNotUpload = true;
                break;
            }

            EnyanUserBookmarks bookmark = new EnyanUserBookmarks();
            bookmark.setUpdateTime(updateTime);
            bookmark.setUserEmail(restBookmark.getEmail());
            bookmark.setBookId(tmp.getBookId());
            bookmark.setChapterName(tmp.getChapterName());
            bookmark.setBookmarkId(tmp.getBookmarkId());
            bookmark.setCreateTime(tmp.getCreateTime());
            bookmark.setCurrentPage(tmp.getCurrentPage());
            bookmark.setPageChapter(tmp.getPageChapter());
            bookmark.setTotalPage(tmp.getTotalPage());
            bookmark.setUserId(-1L);
            bookmark.setIsDeleted(tmp.getIsDeleted());
            list.add(bookmark);
            updateTime += 1;
        }
        if (canNotUpload){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        enyanUserBookmarkService.addBookmarks(list);
        result.setResult(updateTime);
        result.setSuccessMessage(String.valueOf(updateTime));
        return result;
    }
}
