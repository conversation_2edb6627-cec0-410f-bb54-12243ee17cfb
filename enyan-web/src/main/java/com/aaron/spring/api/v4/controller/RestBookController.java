package com.aaron.spring.api.v4.controller;

import com.aaron.a4j.util.ResultUtils;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.drm.model.DrmInfo;
import com.aaron.drm.model.Licenses;
import com.aaron.exception.DownloadException;
import com.aaron.exception.RestException;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestBook;
import com.aaron.spring.api.RestList;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookBuy;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.EnyanBookBuyService;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanOrderService;
import com.aaron.spring.service.LogService;
import com.aaron.util.ExecuteResult;
import com.aaron.util.FilePropertiesUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 *  BusinessException 捕获异常 2XX
 * @Author: Aaron Hao
 * @Date: Created in  2018/8/29
 * @Modified By:
 */
@Slf4j
@RestController("RestBookControllerV4")
@RequestMapping(path = {"/api/v4/book","/front/v4/book"})
public class RestBookController extends RestBaseController {
    //private static final String epubBaseDir = FilePropertiesUtil.props.getProperty("epubBaseDir");//
    //@Resource
    //private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private EnyanBookBuyService enyanBookBuyService;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanOrderService enyanOrderService;

    @Resource
    private LogService logService;

    @RequestMapping(value = "/books",method = RequestMethod.POST)
    public ExecuteResult<List<RestBook>> books(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<List<RestBook>> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        List<RestBook> bookList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            RestBook book = new RestBook();
            book.setBookId(i+1L);
            book.setName("书籍所以没关系"+(15+i));
            book.setAuthor("author");
            book.setDateStr("2008-1-"+(i+1));
            book.setBookLanguage((i%2 == 0)?"sc":"tc");
            book.setImgUrl("imgUrl");
            book.setFileName("markTC"+(15+i)+".epub");
            bookList.add(book);
        }
        result.setResult(bookList);
        return result;
    }

    @RequestMapping(value = "/show",method = RequestMethod.POST)
    public ExecuteResult<String> show(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        result.setResult("展示书籍="+restObj.getName());
        return result;
    }

    @RequestMapping(value = "/booksPage",method = RequestMethod.POST)
    public PageResult<RestBook> booksPage(@RequestBody RestBook restBook, HttpServletRequest request){
        if (StringUtils.isBlank(restBook.getEmail()) || null == restBook.getPage()){
            PageResult<RestBook> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        EnyanBookBuy paramBookBuy = new EnyanBookBuy();
        paramBookBuy.setUserEmail(restBook.getEmail());
        paramBookBuy.setIsDeleted(0);

        Page<EnyanBookBuy> searchPage = new Page<>();
        searchPage.setCurrentPage(restBook.getPage());
        /*if (!Constant.IS_PRODUCT){
            searchPage.setPageSize(1);
        }*/

        Page<EnyanBookBuy> bookBuyPage = enyanBookBuyService.queryRecordsByOrder(searchPage,paramBookBuy);
        PageResult<RestBook> page = new PageResult<>(bookBuyPage.getCurrentPage(),bookBuyPage.getTotalRecord(),bookBuyPage.getPageSize());

        for (EnyanBookBuy enyanBookBuy:bookBuyPage.getRecords()){
            ExecuteResult<EnyanBook> bookExecuteResult = enyanBookService.queryRecordByPrimaryKey(enyanBookBuy.getBookId());
            EnyanBook enyanBook = bookExecuteResult.getResult();
            if (null == enyanBook){
                continue;
            }
            RestBook book = new RestBook();
            book.initFrom(enyanBook);
            book.setGroupName(enyanBookBuy.getGroupName());
            book.setReadLocation(enyanBookBuy.getReadInfo());
            book.setDateStr(DateFormatUtils.format(enyanBookBuy.getPurchasedAt(), Constant.DATE_FORMAT_PATTERN));
            page.getResult().add(book);
        }
        return page;
    }

    @RequestMapping(value = "/booksPageIds",method = RequestMethod.POST)
    public PageResult<RestBook> booksPageIds(@RequestBody RestList<Long> restList, HttpServletRequest request){
        if (StringUtils.isBlank(restList.getEmail())){
            PageResult<RestBook> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        if (null == restList.getList()|| restList.getList().isEmpty()){
            PageResult<RestBook> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        List<EnyanBook> bookList = enyanBookService.findBookByIds(restList.getList());
        List<EnyanBook> newBookList = new ArrayList<>();
        for (Long bookId:restList.getList()){
            if (bookId < 0){//导入的书籍
                EnyanBook importBook = new EnyanBook();
                importBook.setBookId(bookId);
                newBookList.add(importBook);
                continue;
            }
            EnyanBook book =  this.getBookById(bookId,bookList);
            if (null != book){
                newBookList.add(book);
            }
        }
        PageResult<RestBook> page = new PageResult<>();
        for (EnyanBook enyanBook:newBookList){
            RestBook book = new RestBook();
            book.setBookId(enyanBook.getBookId());
            if (book.getBookId() < 0){//导入的书籍
                book.setName("");
                book.setAuthor("");
                //book.setDateStr(DateFormatUtils.format(enyanOrderDetail.getPurchasedAt(), Constant.DATE_FORMAT_PATTERN));
                book.setBookLanguage("sc");
                book.setImgUrl("");
                book.setFileName("");
                book.setIsbn("");
                book.setSize("");
                book.setVersion("");
                book.setSalesModel(0);
            }else{
                book.initFrom(enyanBook);
            }

            page.getResult().add(book);
        }
        return page;
    }

    @RequestMapping(value = "/search",method = RequestMethod.POST)
    public ExecuteResult<List<RestBook>> search(@RequestBody RestBook restBook){
        ExecuteResult<List<RestBook>> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restBook.getName()) || StringUtils.isBlank(restBook.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        List<RestBook> bookList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            RestBook book = new RestBook();
            book.setBookId(i+1L);
            book.setName("书籍所以没关系"+(15+i));
            book.setAuthor("作者");
            book.setDateStr("2008-1-"+(i+1));
            book.setBookLanguage((i%2 == 0)?"sc":"tc");
            book.setImgUrl("imgUrl");
            book.setFileName("markTC"+(15+i)+".epub");
            bookList.add(book);
        }
        result.setResult(bookList);
        return result;
    }

    @RequestMapping(value = "/searchPage",method = RequestMethod.POST)
    public PageResult<RestBook> searchPage(@RequestBody RestBook restBook, HttpServletRequest request){
        restBook.initHeaderValue(request);
        if (StringUtils.isBlank(restBook.getName()) || StringUtils.isBlank(restBook.getEmail())){
            PageResult<RestBook> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        EnyanBookBuy paramBookBuy = new EnyanBookBuy();
        paramBookBuy.setUserEmail(restBook.getEmail());
        paramBookBuy.setBookTitle(restBook.getName());
        paramBookBuy.setIsDeleted(0);

        Page<EnyanBookBuy> searchPage = new Page<>();
        searchPage.setCurrentPage(restBook.getPage());
        if (!Constant.IS_PRODUCT){
            searchPage.setPageSize(1);
        }

        Page<EnyanBookBuy> bookBuyPage = enyanBookBuyService.searchRecordsByUserAndBookAndAuthorList(searchPage,paramBookBuy);

        PageResult<RestBook> page = new PageResult<>(bookBuyPage.getCurrentPage(),bookBuyPage.getTotalRecord(),bookBuyPage.getPageSize());
        for (EnyanBookBuy enyanBookBuy:bookBuyPage.getRecords()){
            ExecuteResult<EnyanBook> bookExecuteResult = enyanBookService.queryRecordByPrimaryKey(enyanBookBuy.getBookId());
            EnyanBook enyanBook = bookExecuteResult.getResult();

            RestBook book = new RestBook();
            book.initFrom(enyanBook);
            book.setGroupName(enyanBookBuy.getGroupName());
            book.setReadLocation(enyanBookBuy.getReadInfo());
            book.setDateStr(DateFormatUtils.format(enyanBookBuy.getPurchasedAt(), Constant.DATE_FORMAT_PATTERN));
            page.getResult().add(book);
        }
        return page;
    }

    @RequestMapping(value = "/uploadRead",method = RequestMethod.POST)
    public ExecuteResult<RestBook> uploadRead(@RequestBody RestBook restObj, HttpServletRequest request){
        ExecuteResult<RestBook> result = new ExecuteResult<>();
        restObj.initHeaderValue(request);
        if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getBookId() || restObj.getBookId() < 0){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (StringUtils.isBlank(restObj.getReadLocation()) && StringUtils.isBlank(restObj.getGroupName())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (restObj.getReadLocation().length() > 500){
            restObj.setReadLocation(null);
            restObj.setGroupName(null);
            result.setResult(restObj);
            logService.sendTimeLog( "email="+restObj.getEmail()+",bookId="+restObj.getBookId()+",device="+restObj.getDeviceID()+",阅读位置过长="+restObj.getReadLocation());
            //throw new RestException(233, ReturnInfo.ERROR_PARAM_INVALID, "email="+restObj.getEmail()+",bookId="+restObj.getBookId()+",device="+restObj.getDeviceID()+",阅读位置过长="+restObj.getReadLocation());
            return result;
        }
        Long currentTime = System.currentTimeMillis();

        EnyanBookBuy bookBuy = new EnyanBookBuy();
        bookBuy.setUserEmail(restObj.getEmail());
        bookBuy.setBookId(restObj.getBookId());
        if (StringUtils.isNotBlank(restObj.getReadLocation()) && StringUtils.isNotBlank(restObj.getGroupName())){//既有进度，又有分组
            bookBuy.setReadInfo(restObj.getReadLocation());
            bookBuy.setReadTime(currentTime);

            bookBuy.setGroupName(restObj.getGroupName());
            bookBuy.setGroupNameTime(currentTime);

            restObj.setReadTime(currentTime);
            restObj.setGroupNameTime(currentTime);
            enyanBookBuyService.updateReadInfoAndGroupNameByEmailAndBookId(bookBuy);
        }else if (StringUtils.isNotBlank(restObj.getReadLocation())){//只有进度
            bookBuy.setReadTime(currentTime);
            bookBuy.setReadInfo(restObj.getReadLocation());

            restObj.setReadTime(currentTime);
            enyanBookBuyService.updateReadInfoByEmailAndBookId(bookBuy);
        }else if (StringUtils.isNotBlank(restObj.getGroupName())){//只有分组
            bookBuy.setGroupName(restObj.getGroupName());
            bookBuy.setGroupNameTime(currentTime);

            restObj.setGroupNameTime(currentTime);
            enyanBookBuyService.updateGroupNameByEmailAndBookId(bookBuy);
        }

        restObj.setReadLocation(null);
        restObj.setGroupName(null);
        result.setResult(restObj);
        return result;
    }

    @RequestMapping(value = "/uploadShelfGroupName",method = RequestMethod.POST)
    public ExecuteResult<RestBook> uploadShelfGroupName(@RequestBody RestBook restObj, HttpServletRequest request){
        ExecuteResult<RestBook> result = new ExecuteResult<>();
        restObj.initHeaderValue(request);
        if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getList()
                    || restObj.getList().isEmpty() == true ){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (StringUtils.isBlank(restObj.getGroupName())){
            restObj.setGroupName("");
        }
        Long currentTime = System.currentTimeMillis();

        EnyanBookBuy bookBuy = new EnyanBookBuy();
        bookBuy.setUserEmail(restObj.getEmail());
//        bookBuy.setBookId(restObj.getBookId());
        bookBuy.setGroupName(restObj.getGroupName());
        bookBuy.setGroupNameTime(currentTime);

        restObj.setGroupNameTime(currentTime);
        enyanBookBuyService.updateGroupNameByEmailAndBookIds(bookBuy, restObj.getList());

        restObj.setReadLocation(null);
        restObj.setGroupName(null);
        restObj.setList(null);
        result.setResult(restObj);
        return result;
    }

    @RequestMapping(value = "/download")
    public ResponseEntity<byte[]> downloadEpub(@RequestBody RestBook restBook, HttpServletRequest request){
        ResponseEntity<byte[]> entity = null;
        //System.out.println("downloadEpub");
        HttpHeaders headers = new HttpHeaders();

        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
        headers.add("Accept-Ranges","bytes");

       /* if (0 < restBook.getBookId()){
            throw new DownloadException(Integer.valueOf(200), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID>0，book="+restBook);
        }*/

        if (restBook.getBookId() == null || StringUtils.isBlank(restBook.getEmail()) || 0 > restBook.getBookId()){
            throw new DownloadException(Integer.valueOf(201), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID或email为空，book="+restBook);
        }
        String email = this.getRestEmail(request);
        if (StringUtils.isBlank(email) || !email.equals(restBook.getEmail())){//与header里传的email不同
            throw new DownloadException(Integer.valueOf(2011), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID或email为空，book="+restBook);
        }
        ExecuteResult<EnyanBook> bookExecuteResult = enyanBookService.queryRecordByPrimaryKey(restBook.getBookId());
        EnyanBook enyanBook = bookExecuteResult.getResult();
        if (enyanBook.getSalesModel() == 1){//预售中
            throw new DownloadException(Integer.valueOf(202), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍预售中，book="+restBook);
        }

        List<EnyanBookBuy> list = enyanBookBuyService.findBookBuyListByEmailAndBookId(restBook.getEmail(),restBook.getBookId());
        if (null == list || list.isEmpty()){
            throw new DownloadException(Integer.valueOf(203), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍不存在，book="+restBook);
        }
///TODO 需要 分段try catch，方便排查问题

        EnyanBookBuy enyanBookBuy = list.get(0);
        if (StringUtils.isBlank(enyanBookBuy.getDrminfo())){
            throw new DownloadException(Integer.valueOf(204), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "dInfo不存在，book="+restBook);
        }

        DrmInfo drmInfo = JSONObject.parseObject(enyanBookBuy.getDrminfo(),DrmInfo.class);
        if (null == drmInfo.getLcpInfo() || StringUtils.isBlank(drmInfo.getLcpInfo().getLicenseUuid())){
            //下载 licenseID 并重新更新数据 TODO 这里经常会报错，需要升级新的功能，改换成直接数据库操作
            /*
            Licenses licenses = enyanBookBuyService.getLicenseByPurchaseId(drmInfo.getLcpInfo().getPurchseId());
            if (null == licenses){
                throw new DownloadException(Integer.valueOf(205), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"license 为空,book:"+restBook);
            }
            drmInfo.getLcpInfo().setLicenseUuid(licenses.getID());
            EnyanBookBuy newOrderDetail = new EnyanBookBuy();
            newOrderDetail.setDrminfo(JSONObject.toJSONString(drmInfo));
            newOrderDetail.setBookBuyId(enyanBookBuy.getBookBuyId());
            enyanBookBuyService.updateRecord(newOrderDetail);*/
            String license = enyanOrderService.saveLicensesByDrmInfo(enyanBookBuy, drmInfo);
            if (StringUtils.isBlank(license)){
                throw new DownloadException(Integer.valueOf(205), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"license 为空,book:"+restBook);
            }
        }

        //根据 licenseID 下载书籍
        byte[] fileByte = enyanBookBuyService.downloadLcpFiles(drmInfo);
        if (null == fileByte){
            throw new DownloadException(Integer.valueOf(206), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"LCP 文件为空,book:"+restBook);
        }

        try {
            headers.setContentLength(fileByte.length);
            //headers.setLastModified(System.currentTimeMillis());
            //headers.setETag(enyanBook.getBookDrmRef());
            String fileName = this.getFileName(restBook);
            headers.setContentDispositionFormData("attachment", fileName);//告知浏览器以下载方式打开
            entity = new ResponseEntity<>(fileByte, headers, HttpStatus.OK);
            return entity;
        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
            log.error("下载的问题：Connection reset by peer - download");

            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
            entity = new ResponseEntity<>("error004".getBytes(), headers, HttpStatus.INTERNAL_SERVER_ERROR);//HttpStatus.SERVICE_UNAVAILABLE
            throw new DownloadException(Integer.valueOf(207), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"book:"+restBook+",msg="+e.getMessage());
        }finally {
            return entity;
        }
    }
    @RequestMapping(value = "/downloadTest")
    public ResponseEntity<byte[]> downloadEpubTest(@RequestBody RestBook restBook, HttpServletRequest request){
        ResponseEntity<byte[]> entity ;
        HttpHeaders headers = new HttpHeaders();

        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);//设置MIME类型
        headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
        //ResultUtils.getFailedResultData("下载失败");
        //"error004";
        entity = new ResponseEntity<>(ResultUtils.getFailedResultData("下载失败").toString().getBytes(), headers, HttpStatus.INTERNAL_SERVER_ERROR);//HttpStatus.SERVICE_UNAVAILABLE
        return entity;
    }

    @RequestMapping(value = "/download2")
    public ResponseEntity<byte[]> downloadEpub2(@RequestBody RestBook restBook, HttpServletRequest request, HttpServletResponse response, @RequestHeader(required = false) String range) throws IOException {
        String filePath = "/usr/local/tomcat/lib/ecj-4.12.jar";
        if (Constant.IS_PRODUCT){
            filePath = "/usr/local/tomcat9/lib/ecj-4.9.jar";
        }
        ResponseEntity<byte[]> entity = null;
        //System.out.println("downloadEpub");
        HttpHeaders headers = new HttpHeaders();

        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型

       /* if (0 < restBook.getBookId()){
            throw new DownloadException(Integer.valueOf(200), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID>0，book="+restBook);
        }*/

        if (restBook.getBookId() == null || StringUtils.isBlank(restBook.getEmail()) || 0 > restBook.getBookId()){
            throw new DownloadException(Integer.valueOf(201), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID或email为空，book="+restBook);
        }
        String email = this.getRestEmail(request);
        if (StringUtils.isBlank(email) || !email.equals(restBook.getEmail())){//与header里传的email不同
            throw new DownloadException(Integer.valueOf(2011), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID或email为空，book="+restBook);
        }
        ExecuteResult<EnyanBook> bookExecuteResult = enyanBookService.queryRecordByPrimaryKey(restBook.getBookId());
        EnyanBook enyanBook = bookExecuteResult.getResult();
        if (enyanBook.getSalesModel() == 1){//预售中
            throw new DownloadException(Integer.valueOf(202), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍预售中，book="+restBook);
        }

        EnyanBookBuy paramOrderDetail = new EnyanBookBuy();
        paramOrderDetail.setUserEmail(restBook.getEmail());
        paramOrderDetail.setBookId(restBook.getBookId());

        List<EnyanBookBuy> list = enyanBookBuyService.searchRecordsByUserList(paramOrderDetail);
        if (null == list || list.isEmpty()){
            throw new DownloadException(Integer.valueOf(203), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍不存在，book="+restBook);
        }
///TODO 需要 分段try catch，方便排查问题

        EnyanBookBuy bookBuy = list.get(0);
        if (StringUtils.isBlank(bookBuy.getDrminfo())){
            throw new DownloadException(Integer.valueOf(204), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "dInfo不存在，book="+restBook);
        }

        DrmInfo drmInfo = JSONObject.parseObject(bookBuy.getDrminfo(),DrmInfo.class);
        if (null == drmInfo.getLcpInfo() || StringUtils.isBlank(drmInfo.getLcpInfo().getLicenseUuid())){
            //下载 licenseID 并重新更新数据
            Licenses licenses = enyanBookBuyService.getLicenseByPurchaseId(drmInfo.getLcpInfo().getPurchseId());
            if (null == licenses){
                throw new DownloadException(Integer.valueOf(205), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"license 为空,book:"+restBook);
            }
            drmInfo.getLcpInfo().setLicenseUuid(licenses.getID());
            EnyanBookBuy newOrderDetail = new EnyanBookBuy();
            newOrderDetail.setDrminfo(JSONObject.toJSONString(drmInfo));
            newOrderDetail.setBookBuyId(bookBuy.getBookBuyId());
            enyanBookBuyService.updateRecord(newOrderDetail);
        }

        //根据 licenseID 下载书籍
        byte[] fileByte = FileUtils.readFileToByteArray(new File(filePath));
        if (null == fileByte){
            throw new DownloadException(Integer.valueOf(206), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"LCP 文件为空,book:"+restBook);
        }
        int fileLength = fileByte.length;// 记录文件大小,不要大于 Integer.MAX_VALUE（**********）2G
        int pastLength = 0;// 记录已下载文件大小
        int toLength = 0;// 记录客户端需要下载的字节段的最后一个字节偏移量（比如bytes=27000-39000，则这个值是为39000）
        int contentLength = 0;// 客户端请求的字节总量
        String rangeBytes = "";// 记录客户端传来的形如“bytes=27000-”或者“bytes=27000-39000”的内容

        if (StringUtils.isNotBlank(range)){
            log.debug("range:"+range);
        }
        if (request.getHeader("Range") != null) {// 客户端请求的下载的文件块的开始字节
            //response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
//            log.info("request.getHeader(\"Range\")="
//                    + request.getHeader("Range"));
            rangeBytes = request.getHeader("Range").replaceAll("bytes=", "");
            if (rangeBytes.indexOf('-') == rangeBytes.length() - 1) {// bytes=969998336-
                rangeBytes = rangeBytes.substring(0, rangeBytes.indexOf('-'));
                pastLength = Integer.parseInt(rangeBytes.trim());
                toLength = fileLength - 1;
            } else {// bytes=1275856879-1275877358
                String temp0 = rangeBytes.substring(0, rangeBytes.indexOf('-'));
                String temp2 = rangeBytes.substring(
                        rangeBytes.indexOf('-') + 1, rangeBytes.length());
                // bytes=1275856879-1275877358，从第 1275856879个字节开始下载
                pastLength = Integer.parseInt(temp0.trim());
                // bytes=1275856879-1275877358，到第 1275877358 个字节结束
                toLength = Integer.parseInt(temp2);
            }
        } else {// 从开始进行下载
            toLength = fileLength - 1;
        }
        // 客户端请求的是1275856879-1275877358 之间的字节
        contentLength = toLength - pastLength + 1;
        /*
        if (contentLength < Integer.MAX_VALUE) {
            headers.setContentLength(contentLength);
        } else {
            // Set the content-length as String to be able to use a long
            response.setHeader("content-length", "" + contentLength);
        }*/
        byte[] fileByteUse = ArrayUtils.subarray(fileByte,pastLength,toLength);
        //ArrayUtils.
        try {
            headers.setContentLength(contentLength);
            headers.add("Last-Modified", enyanBook.getOpensaleAt().toString());
            headers.add("ETag",
                    "W/\"" + fileLength + "-" + enyanBook.getBookId() + "\"");
            headers.add("Accept-Ranges","bytes");
            headers.add("Content-Range","bytes " + pastLength + "-"
                    + toLength + "/" + fileLength);
            //headers.setLastModified(System.currentTimeMillis());
            //headers.setETag(enyanBook.getBookDrmRef());
            String fileName = this.getFileName(restBook)+"aa";
            //headers.setContentDispositionFormData("attachment", fileName);//告知浏览器以下载方式打开
            response.setHeader("Content-Disposition", "inline;filename="+fileName);
            entity = new ResponseEntity<>(fileByteUse, headers, HttpStatus.PARTIAL_CONTENT);
            return entity;
        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
            log.error("下载的问题：Connection reset by peer - download");

            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
            entity = new ResponseEntity<>("error004".getBytes(), headers, HttpStatus.INTERNAL_SERVER_ERROR);//HttpStatus.SERVICE_UNAVAILABLE
            throw new DownloadException(Integer.valueOf(207), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"book:"+restBook+",msg="+e.getMessage());
        }finally {
            return entity;
        }
    }

    private String getFileName(RestBook restBook){
        //return "1.epub";
        return restBook.getBookId() + ".epub";
    }

    private EnyanBook getBookById(Long id, List<EnyanBook> bookList){
        //return bookList.stream().filter(book -> book.getBookId() == id).;

        for (EnyanBook enyanBook : bookList){
            if (enyanBook.getBookId().equals(id)){
                return enyanBook;
            }
        }
        return null;
    }
}