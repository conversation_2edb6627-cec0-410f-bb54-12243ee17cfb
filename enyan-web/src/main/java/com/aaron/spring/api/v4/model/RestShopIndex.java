package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBanner;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.CurrencyType;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanCategory;
import com.aaron.spring.model.ShopIndex;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/12
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestShopIndex extends RestBaseDTO {
    private static final long serialVersionUID = -2648658213836986366L;
    private List<RestBook> editorList;//推荐
    private List<RestShopCategory> shopTopCategories;//一些促销的分类（首页展示）
    /**
     * 需要在首页展示的分类(要过期)
     * */
    @Deprecated
    private List<RestShopCategory> shopCategories;//需要在首页展示的分类
    private List<RestShopCategory> shopTopMenu;//需要在首页展示的在套装书上方的书单书籍
    private List<RestShopCategory> shopMenu;//需要在首页展示套装书的书单书籍
    private List<RestShopCategory> shopSet;//需要在首页展示套装书的书系书籍
    private List<RestShopCategory> shopBottomMenu;//需要在首页展示的在套装书下方的书单书籍
    private List<RestBanner> topBanners;//最上边的banner
    private List<RestBanner> topBlogs;//读书会
    private List<RestBanner> shopBanners;//其他展示的banner（一次展示一张）

    //@JSONField(serialize = false)
    //@JsonIgnore
    //private boolean isInit = false; //是否已经被初始化，如果没有初始化则需要重新处理


    public void initWithShopIndex(ShopIndex shopIndex, CurrencyType currencyType) {
        editorList = new ArrayList<>();
        for (EnyanBook obj : shopIndex.getEditorList()){
            RestBook book = new RestBook();
            book.initFrom(obj, currencyType);
            editorList.add(book);
        }

        shopTopCategories = new ArrayList<>();
        for (EnyanCategory obj : shopIndex.getShopTopCategories()){
            RestShopCategory category = new RestShopCategory();
            category.initFrom(obj, currencyType);
            shopTopCategories.add(category);
        }

        shopTopMenu = new ArrayList<>();
        for (EnyanCategory obj : shopIndex.getShopTopMenu()){
            RestShopCategory category = new RestShopCategory();
            category.initFrom(obj, currencyType);
            shopTopMenu.add(category);
        }

        shopMenu = new ArrayList<>();
        for (EnyanCategory obj : shopIndex.getShopMenu()){
            RestShopCategory category = new RestShopCategory();
            category.initFrom(obj, currencyType);
            shopMenu.add(category);
        }

        shopSet = new ArrayList<>();
        for (EnyanCategory obj : shopIndex.getShopSet()){
            RestShopCategory category = new RestShopCategory();
            category.initFrom(obj, currencyType);
            shopSet.add(category);
        }

        shopBottomMenu = new ArrayList<>();
        for (EnyanCategory obj : shopIndex.getShopBottomMenu()){
            RestShopCategory category = new RestShopCategory();
            category.initFrom(obj, currencyType);
            shopBottomMenu.add(category);
        }

        topBanners = new ArrayList<>();
        this.setTopBanners(shopIndex.getTopBanners());

        topBlogs = new ArrayList<>();
        this.setTopBlogs(shopIndex.getTopBlogs());

        shopBanners = new ArrayList<>();
        this.setShopBanners(shopIndex.getShopBanners());
    }

    public List<RestBook> getEditorList() {
        return editorList;
    }

    public void setEditorList(List<RestBook> editorList) {
        this.editorList = editorList;
    }

    public List<RestShopCategory> getShopCategories() {
        return shopCategories;
    }

    public void setShopCategories(List<RestShopCategory> shopCategories) {
        this.shopCategories = shopCategories;
    }

    public List<RestShopCategory> getShopTopCategories() {
        return shopTopCategories;
    }

    public void setShopTopCategories(List<RestShopCategory> shopTopCategories) {
        this.shopTopCategories = shopTopCategories;
    }

    public List<RestBanner> getTopBanners() {
        return topBanners;
    }

    public void setTopBanners(List<RestBanner> topBanners) {
        this.topBanners = topBanners;
    }

    public List<RestShopCategory> getShopTopMenu() {
        return shopTopMenu;
    }

    public void setShopTopMenu(List<RestShopCategory> shopTopMenu) {
        this.shopTopMenu = shopTopMenu;
    }

    public List<RestShopCategory> getShopMenu() {
        return shopMenu;
    }

    public void setShopMenu(List<RestShopCategory> shopMenu) {
        this.shopMenu = shopMenu;
    }

    public List<RestShopCategory> getShopSet() {
        return shopSet;
    }

    public void setShopSet(List<RestShopCategory> shopSet) {
        this.shopSet = shopSet;
    }

    public List<RestBanner> getShopBanners() {
        return shopBanners;
    }

    public void setShopBanners(List<RestBanner> shopBanners) {
        this.shopBanners = shopBanners;
    }

    public List<RestShopCategory> getShopBottomMenu() {
        return shopBottomMenu;
    }

    public void setShopBottomMenu(List<RestShopCategory> shopBottomMenu) {
        this.shopBottomMenu = shopBottomMenu;
    }

    public List<RestBanner> getTopBlogs() {
        return topBlogs;
    }

    public void setTopBlogs(List<RestBanner> topBlogs) {
        this.topBlogs = topBlogs;
    }
}
