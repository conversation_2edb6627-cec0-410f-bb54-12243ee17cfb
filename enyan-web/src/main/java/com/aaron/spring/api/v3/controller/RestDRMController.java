package com.aaron.spring.api.v3.controller;

import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v1.model.RestBook;
import com.aaron.spring.service.EnyanOrderDetailService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-09-24
 * @Modified By:
 */
@RestController("RestDRMControllerV3")
@RequestMapping("/api/v3/dr")
public class RestDRMController  extends RestBaseController {
    private final Log logger = LogFactory.getLog(RestDRMController.class);
//    private static final int PAGE_SIZE = 30;
    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;


    @RequestMapping(value = "/licenses/{id}", method = RequestMethod.GET)
    public String licenses(@PathVariable("id")String licenseId){
        String returnJson = enyanOrderDetailService.getLicenseStringByLicenseId(licenseId);
        if (null == returnJson){
            return "invalid license";
        }
        return returnJson;
    }
}
