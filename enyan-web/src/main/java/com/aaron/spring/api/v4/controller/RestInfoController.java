package com.aaron.spring.api.v4.controller;

import co.elastic.clients.elasticsearch.watcher.ScheduleTriggerEvent;
import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.data.model.SearchBook;
import com.aaron.data.repository.elasticsearch.BookSearchRepository;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.RestConfig;
import com.aaron.spring.api.SearchModel;
import com.aaron.spring.model.CurrencyType;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.impl.EnyanBookServiceImpl;
import com.aaron.util.CookieUtil;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2023/3/21
 * @Modified By:
 */
@Slf4j
@RestController("RestInfoControllerV4")
@RequestMapping({"/api/v4/info","/front/v4/info"})
public class RestInfoController {

	@Resource
	private EnyanBookService enyanBookService;

	@LoginAnonymous
	@RequestMapping(value = "/search",method = RequestMethod.POST)
	public ExecuteResult<List<SearchModel>> changeCurrency(@RequestBody SearchModel restObj, HttpServletRequest request, HttpServletResponse response) {
		log.debug("search term:{}",restObj.getTerm());
		//restObj.initHeaderValue(request);
		ExecuteResult<List<SearchModel>> result = new ExecuteResult<>();
		List<SearchModel> list = new ArrayList<>();
		/*
		if (StringUtils.isBlank(restObj.getCurrency())){
			result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}*/
		String keyWord = restObj.getTerm();
		/*
		List<SearchBook> retList = bookSearchRepository.findDistinctByBookTitleOrOrAuthorOrBookDescription(keyWord,keyWord,keyWord);
		List<SearchBook> ret2List = bookSearchRepository.findDistinctTop10ByBookTitleOrOrAuthorOrBookDescription(keyWord,keyWord,keyWord);
		List<SearchBook> ret3List = bookSearchRepository.findByBookTitleOrOrAuthorOrBookDescription(keyWord,keyWord,keyWord);
		*/
		//List<SearchBook> retList = bookSearchRepository.findDistinctTop10ByBookTitleOrOrAuthorOrBookDescription(keyWord,keyWord,keyWord);
		SearchHits<SearchBook> retList = enyanBookService.searchBookByKeywordWithElasticSearch(keyWord);
		/*

		for (int i = 0; i < 10; i++) {
			SearchModel model = new SearchModel();
			model.setId((i+1)+"");
			model.setLabel("abcdefg"+i);
			model.setValue("abcdefg"+i);
			list.add(model);
		}
		for (SearchBook book :   retList) {
			SearchModel model = new SearchModel();
			model.setId(book.getBookId()+"");
			model.setLabel(book.getBookTitle());
			model.setValue(book.getBookTitle()+"");
			list.add(model);
		}*/

		for (SearchHit tmp :   retList) {
			SearchBook book = (SearchBook) tmp.getContent();
			SearchModel model = new SearchModel();
			model.setId(book.getBookId()+"");
			model.setLabel(book.getBookTitle());
			model.setValue(book.getBookTitle()+"");
			/*
			List<String> highlightList = (List)tmp.getHighlightFields().get("bookTitle");
			if (null != highlightList && highlightList.size() > 0){
				//model.setLabelHighlight(highlightList.get(0));
			}*/
			//model.setLabel("<b>圣</b><b>灵</b>的心意：<b>圣</b>经诠释导论(简)[古道译丛·<b>圣</b>经神学]");
			list.add(model);
		}

		log.debug("retList size:{}",retList.getTotalHits());
//		log.debug("ret2List size:{}",ret2List.size());
//		log.debug("ret3List size:{}",ret3List.size());
		result.setResult(list);
		return result;
	}

	@LoginAnonymous
	@RequestMapping(value = "/search2",method = RequestMethod.GET)
	public ExecuteResult<List<SearchModel>> search2(@RequestBody SearchModel restObj, HttpServletRequest request, HttpServletResponse response) {
		//restObj.initHeaderValue(request);
		ExecuteResult<List<SearchModel>> result = new ExecuteResult<>();
		List<SearchModel> list = new ArrayList<>();
		/*
		if (StringUtils.isBlank(restObj.getCurrency())){
			result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}*/
		for (int i = 0; i < 10; i++) {
			SearchModel model = new SearchModel();
			model.setId((i+1)+"");
			model.setLabel("abcdefg"+i);
			model.setValue("abcdefg"+i);
			list.add(model);
		}
		result.setResult(list);
		return result;
	}
}
