package com.aaron.spring.api.v1.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.EnyanUserBookmarks;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/8/24
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestBookmark extends RestBaseDTO{

    private static final long serialVersionUID = -65562568028606412L;
    private String bookmarkId;

    private Long userId;

    private String email;

    private Long bookId;

    private Long createTime;

    private String chapterName;

    private Integer pageChapter;

    private Integer currentPage;

    private Integer totalPage;

    private Integer isDeleted;

    private Long updateTime;

    private Integer pageProcess;

    private List<RestBookmark> list;

    public void initFrom(EnyanUserBookmarks obj){
        this.bookmarkId = obj.getBookmarkId();
        this.userId = obj.getUserId();
        this.email = obj.getUserEmail();
        this.bookId = obj.getBookId();
        this.createTime = obj.getCreateTime();
        this.chapterName = obj.getChapterName();
        this.pageChapter = obj.getPageChapter();
        this.currentPage = obj.getCurrentPage();
        this.totalPage = obj.getTotalPage();
        this.isDeleted = obj.getIsDeleted();
        this.updateTime = obj.getUpdateTime();
        this.pageProcess = obj.getPageProcess();
    }

    public String getBookmarkId() {
        return bookmarkId;
    }

    public void setBookmarkId(String bookmarkId) {
        this.bookmarkId = bookmarkId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String getEmail() {
        return email;
    }

    @Override
    public void setEmail(String email) {
        this.email = email;
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }


    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public Integer getPageChapter() {
        return pageChapter;
    }

    public void setPageChapter(Integer pageChapter) {
        this.pageChapter = pageChapter;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }


    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getPageProcess() {
        return pageProcess;
    }

    public void setPageProcess(Integer pageProcess) {
        this.pageProcess = pageProcess;
    }

    public List<RestBookmark> getList() {
        return list;
    }

    public void setList(List<RestBookmark> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return "RestBookmark{" +
                "bookmarkId='" + bookmarkId + '\'' +
                ", userId=" + userId +
                ", email='" + email + '\'' +
                ", bookId=" + bookId +
                ", createTime=" + createTime +
                ", chapterName='" + chapterName + '\'' +
                ", pageChapter=" + pageChapter +
                ", currentPage=" + currentPage +
                ", totalPage=" + totalPage +
                ", isDeleted=" + isDeleted +
                ", updateTime=" + updateTime +
                ", pageProcess=" + pageProcess +
                ", list=" + list +
                '}';
    }

    public static  void main(String[] args){
        printInit("com.aaron.spring.api.v1.model.RestBookmark","EnyanUserBookmarks");
    }
}
