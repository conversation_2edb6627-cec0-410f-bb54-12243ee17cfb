package com.aaron.spring.api.v4.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2024-01-14
 * @Modified By:
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestStuAnnotationAddress implements Serializable {
	@Serial
	private static final long serialVersionUID = -1311155796441673796L;

	/**
	 * <p>baidu标记点</p>
	 **/
	private List<RestStuAddress> baiduAnnotations = new ArrayList<>();

	/**
	 * <p>高德标记点</p>
	 **/
	private List<RestStuAddress> naviAnnotations = new ArrayList<>();

	/**
	 * <p>QQ标记点</p>
	 **/
	private List<RestStuAddress> qqAnnotations = new ArrayList<>();

	/**
	 * <p>Apple标记点</p>
	 **/
	private List<RestStuAddress> appleAnnotations = new ArrayList<>();

	/**
	 * <p>Google标记点</p>
	 **/
	private List<RestStuAddress> googleAnnotations = new ArrayList<>();

	/**
	 * <p>初始化大棠营期地址信息</p>
	 * @since : 2024-01-15
	 **/
	public void initDestDaTangAddress(){
		//保良局
		RestStuAddress baidu = new RestStuAddress();
		baidu.setLongitude(114.042463);
		baidu.setLatitude(22.414417);
		baiduAnnotations.add(baidu);

		//保良局
		RestStuAddress navi = new RestStuAddress();
		navi.setLongitude(114.03585);
		navi.setLatitude(22.40856);
		naviAnnotations.add(navi);

		//22.4109229,114.0307584
		RestStuAddress google = new RestStuAddress();
		google.setLongitude(114.0308282);
		google.setLatitude(22.4113382);
		googleAnnotations.add(google);

		RestStuAddress qq = new RestStuAddress();
		qq.setLongitude(114.035913);
		qq.setLatitude(22.4085);
		qqAnnotations.add(qq);

		/*
		* Apple：
		* 22.405449，113.989579 忠信堂
        * 113.989149,22.4054 林护堂
        * 22.408219137256097,113.98423052101111
		* */
		RestStuAddress apple = new RestStuAddress();
		apple.setLongitude(113.98423052101111);
		apple.setLatitude(22.408219137256097);
		appleAnnotations.add(apple);
	}

	/**
	 * <p>初始化岭南营期地址信息</p>
	 * @since : 2024-01-15
	 **/
	public void initDestLingNanAddress(){
		//岭南
		RestStuAddress baidu = new RestStuAddress();
		baidu.setLongitude(113.995599);
		baidu.setLatitude(22.411618);
		baiduAnnotations.add(baidu);

		//岭南
		RestStuAddress navi = new RestStuAddress();
		navi.setLongitude(113.989189);
		navi.setLatitude(22.405525);
		naviAnnotations.add(navi);

		//22.4109229,114.0307584
		RestStuAddress google = new RestStuAddress();
		google.setLongitude(113.98420393526264);
		google.setLatitude(22.40825810150094);
		googleAnnotations.add(google);

		RestStuAddress qq = new RestStuAddress();
		qq.setLongitude(114.035913);
		qq.setLatitude(22.4085);
		//qqAnnotations.add(qq);

		/*
		 * Apple：
		 * 22.405449，113.989579 忠信堂
		 * 113.989149,22.4054 林护堂
		 * */
		RestStuAddress apple = new RestStuAddress();
		apple.setLongitude(113.9877909776993);
		apple.setLatitude(22.406846253612432);
		appleAnnotations.add(apple);
	}

	/**
	 * <p>初始化大棠营期地址信息</p>
	 * @since : 2024-01-15
	 **/
	public void initDaTangAnnAddress(){
		//----------------百度---------------------//
		RestStuAddress baiduAnn1 = new RestStuAddress();
		baiduAnn1.setTitle("餐厅");
		baiduAnn1.setSubtitle("过来吃饭吧");
		baiduAnn1.setLongitude(114.044646);
		baiduAnn1.setLatitude(22.414831);
		baiduAnnotations.add(baiduAnn1);

		RestStuAddress baiduAnn2 = new RestStuAddress();
		baiduAnn2.setTitle("会堂/会议室");//会堂/宿舍/会议室
		//baiduAnn2.setSubtitle("过来吃饭吧");
		baiduAnn2.setLongitude(114.045454);
		baiduAnn2.setLatitude(22.413641);
		baiduAnnotations.add(baiduAnn2);

		//----------------高德---------------------//
		RestStuAddress naviAnn1 = new RestStuAddress();
		naviAnn1.setTitle("餐厅");
		naviAnn1.setSubtitle("过来吃饭吧");
		naviAnn1.setLongitude(114.03311);
		naviAnn1.setLatitude(22.4119);
		naviAnnotations.add(naviAnn1);

		RestStuAddress naviAnn2 = new RestStuAddress();
		naviAnn2.setTitle("会堂/会议室");
		//naviAnn2.setSubtitle("过来吃饭吧");
		naviAnn2.setLongitude(114.034365);
		naviAnn2.setLatitude(22.4105);
		naviAnnotations.add(naviAnn2);

		//----------------Google---------------------//
		RestStuAddress googleAnn1 = new RestStuAddress();
		googleAnn1.setTitle("餐厅");
		googleAnn1.setSubtitle("过来吃饭吧");
		googleAnn1.setLongitude(114.03302659964464);
		googleAnn1.setLatitude(22.41185382213535);
		googleAnnotations.add(googleAnn1);

		RestStuAddress googleAnn2 = new RestStuAddress();
		googleAnn2.setTitle("会堂/会议室");
		//googleAnn2.setSubtitle("过来吃饭吧");
		googleAnn2.setLongitude(114.03461983166625);
		googleAnn2.setLatitude(22.410623928077015);
		googleAnnotations.add(googleAnn2);

		//----------------QQ---------------------//
		RestStuAddress qqAnn1 = new RestStuAddress();
		qqAnn1.setTitle("餐厅");
		qqAnn1.setSubtitle("过来吃饭吧");
		qqAnn1.setLongitude(114.03822);
		qqAnn1.setLatitude(22.408947);
		qqAnnotations.add(qqAnn1);

		RestStuAddress qqAnn2 = new RestStuAddress();
		qqAnn2.setTitle("会堂/会议室");
		//qqAnn2.setSubtitle("过来吃饭吧");
		qqAnn2.setLongitude(114.039281);
		qqAnn2.setLatitude(22.407643);
		qqAnnotations.add(qqAnn2);

		//----------------Apple---------------------//

		//22.4118687446028,114.03311612311076
		RestStuAddress appleAnn1 = new RestStuAddress();
		appleAnn1.setTitle("餐厅");
		appleAnn1.setSubtitle("过来吃饭吧");
		appleAnn1.setLongitude(114.03311612311076);
		appleAnn1.setLatitude(22.4118687446028);
		appleAnnotations.add(appleAnn1);

		//22.410588413331467,114.03416693486447
		RestStuAddress appleAnn2 = new RestStuAddress();
		appleAnn2.setTitle("会堂/会议室");
		//appleAnn2.setSubtitle("过来吃饭吧");
		appleAnn2.setLongitude(114.03416693486447);
		appleAnn2.setLatitude(22.410588413331467);
		appleAnnotations.add(appleAnn2);
	}

	/**
	 * <p>初始化岭南营期地址信息</p>
	 * @since : 2024-01-15
	 **/
	public void initLingNanAnnAddress(){
		//----------------百度---------------------//
		RestStuAddress baiduAnn1 = new RestStuAddress();
		baiduAnn1.setTitle("餐厅");
		baiduAnn1.setSubtitle("过来吃饭吧");
		baiduAnn1.setLongitude(113.995038);
		baiduAnn1.setLatitude(22.414431);
		baiduAnnotations.add(baiduAnn1);

		RestStuAddress baiduAnn2 = new RestStuAddress();
		baiduAnn2.setTitle("課室MBG01");
		//baiduAnn2.setSubtitle("过来吃饭吧");
		baiduAnn2.setLongitude(113.994201);
		baiduAnn2.setLatitude(22.413131);
		baiduAnnotations.add(baiduAnn2);

		RestStuAddress baiduAnn3 = new RestStuAddress();
		baiduAnn3.setTitle("課室LKK303");
		//baiduAnn3.setSubtitle("过来吃饭吧");
		baiduAnn3.setLongitude(113.994969);
		baiduAnn3.setLatitude(22.413104);
		baiduAnnotations.add(baiduAnn3);

		RestStuAddress baidu = new RestStuAddress();
		baidu.setTitle("宿舍林護堂（Hall D）");
		//baidu.setSubtitle("过来吃饭吧");
		baidu.setLongitude(113.995599);
		baidu.setLatitude(22.411618);
		baiduAnnotations.add(baidu);

		//----------------高德---------------------//
		RestStuAddress naviAnn1 = new RestStuAddress();
		naviAnn1.setTitle("餐厅");
		naviAnn1.setSubtitle("过来吃饭吧");
		naviAnn1.setLongitude(113.988582);
		naviAnn1.setLatitude(22.408015);
		naviAnnotations.add(naviAnn1);

		RestStuAddress naviAnn2 = new RestStuAddress();
		naviAnn2.setTitle("課室MBG01");
		//naviAnn2.setSubtitle("过来吃饭吧");
		naviAnn2.setLongitude(113.987735);
		naviAnn2.setLatitude(22.406847);
		naviAnnotations.add(naviAnn2);

		RestStuAddress naviAnn3 = new RestStuAddress();
		naviAnn3.setTitle("課室LKK303");
		//naviAnn3.setSubtitle("过来吃饭吧");
		naviAnn3.setLongitude(113.988325);
		naviAnn3.setLatitude(22.406778);
		naviAnnotations.add(naviAnn3);

		RestStuAddress navi = new RestStuAddress();
		navi.setTitle("宿舍林護堂（Hall D）");
		//navi.setSubtitle("过来吃饭吧");
		navi.setLongitude(113.989189);
		navi.setLatitude(22.405525);
		naviAnnotations.add(navi);

		//----------------Google---------------------//
		RestStuAddress googleAnn1 = new RestStuAddress();
		googleAnn1.setTitle("餐厅");
		googleAnn1.setSubtitle("过来吃饭吧");
		googleAnn1.setLongitude(113.98368666702407);
		googleAnn1.setLatitude(22.41106081789709);
		googleAnnotations.add(googleAnn1);

		RestStuAddress googleAnn2 = new RestStuAddress();
		googleAnn2.setTitle("課室MBG01");
		//googleAnn2.setSubtitle("过来吃饭吧");
		googleAnn2.setLongitude(113.98273896067636);
		googleAnn2.setLatitude(22.409817904728797);
		googleAnnotations.add(googleAnn2);

		RestStuAddress googleAnn3 = new RestStuAddress();
		googleAnn3.setTitle("課室LKK303");
		//googleAnn3.setSubtitle("过来吃饭吧");
		googleAnn3.setLongitude(113.98352001665342);
		googleAnn3.setLatitude(22.40975556531248);
		googleAnnotations.add(googleAnn3);

		RestStuAddress google = new RestStuAddress();
		google.setTitle("宿舍林護堂（Hall D）");
		//google.setSubtitle("过来吃饭吧");
		google.setLongitude(113.98420393526264);
		google.setLatitude(22.40825810150094);
		googleAnnotations.add(google);

		//----------------QQ---------------------//
		RestStuAddress qqAnn1 = new RestStuAddress();
		qqAnn1.setTitle("餐厅");
		qqAnn1.setSubtitle("过来吃饭吧");
		qqAnn1.setLongitude(114.03822);
		qqAnn1.setLatitude(22.408947);
		//qqAnnotations.add(qqAnn1);

		RestStuAddress qqAnn2 = new RestStuAddress();
		qqAnn2.setTitle("会堂/宿舍/会议室");
		//qqAnn2.setSubtitle("过来吃饭吧");
		qqAnn2.setLongitude(114.039281);
		qqAnn2.setLatitude(22.407643);
		//qqAnnotations.add(qqAnn2);

		//----------------Apple---------------------//

		//22.4118687446028,114.03311612311076
		RestStuAddress appleAnn1 = new RestStuAddress();
		appleAnn1.setTitle("餐厅");
		appleAnn1.setSubtitle("过来吃饭吧");
		appleAnn1.setLongitude(113.98861212136008);
		appleAnn1.setLatitude(22.407989651672416);
		appleAnnotations.add(appleAnn1);

		//22.410588413331467,114.03416693486447
		RestStuAddress appleAnn2 = new RestStuAddress();
		appleAnn2.setTitle("課室MBG01");
		//appleAnn2.setSubtitle("过来吃饭吧");
		appleAnn2.setLongitude(113.9877909776993);
		appleAnn2.setLatitude(22.406846253612432);
		appleAnnotations.add(appleAnn2);


		RestStuAddress appleAnn3 = new RestStuAddress();
		appleAnn3.setTitle("課室LKK303");
		//appleAnn3.setSubtitle("过来吃饭吧");
		appleAnn3.setLongitude(113.98854729855044);
		appleAnn3.setLatitude(22.40677434726834);
		appleAnnotations.add(appleAnn3);

		RestStuAddress apple = new RestStuAddress();
		apple.setTitle("宿舍林護堂（Hall D）");
		//apple.setSubtitle("过来吃饭吧");
		apple.setLongitude(113.98921772699289);
		apple.setLatitude(22.40553609479478);
		appleAnnotations.add(apple);
	}

	public List<RestStuAddress> getBaiduAnnotations() {
		return baiduAnnotations;
	}

	public void setBaiduAnnotations(List<RestStuAddress> baiduAnnotations) {
		this.baiduAnnotations = baiduAnnotations;
	}

	public List<RestStuAddress> getNaviAnnotations() {
		return naviAnnotations;
	}

	public void setNaviAnnotations(List<RestStuAddress> naviAnnotations) {
		this.naviAnnotations = naviAnnotations;
	}

	public List<RestStuAddress> getQqAnnotations() {
		return qqAnnotations;
	}

	public void setQqAnnotations(List<RestStuAddress> qqAnnotations) {
		this.qqAnnotations = qqAnnotations;
	}

	public List<RestStuAddress> getAppleAnnotations() {
		return appleAnnotations;
	}

	public void setAppleAnnotations(List<RestStuAddress> appleAnnotations) {
		this.appleAnnotations = appleAnnotations;
	}

	public List<RestStuAddress> getGoogleAnnotations() {
		return googleAnnotations;
	}

	public void setGoogleAnnotations(List<RestStuAddress> googleAnnotations) {
		this.googleAnnotations = googleAnnotations;
	}
}
