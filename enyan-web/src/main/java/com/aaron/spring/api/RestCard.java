package com.aaron.spring.api;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/6/4
 * @Modified By:
 */
public class RestCard extends RestBaseDTO {
	private static final long serialVersionUID = -2939748931203457032L;
	private String number;
	private String exp_month;
	private String exp_year;
	private String cvc;

	/**
	 * alipay支付类型：0:大陆；1：HK
	 * */
	private Integer alipayType;

	private String orderNum;

	/**
	 * 订单类型：0:电子书；1：先租后买
	 * */
	private Integer orderType;//订单类型

	/**
	 * 订阅月份数
	 * */
	private Integer toRentMonths;

	/**
	 *用于支付的链接，根据mobile有不同的url（html）
	 */
	private String payUrl;//

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public String getExp_month() {
		return exp_month;
	}

	public void setExp_month(String exp_month) {
		this.exp_month = exp_month;
	}

	public String getExp_year() {
		return exp_year;
	}

	public void setExp_year(String exp_year) {
		this.exp_year = exp_year;
	}

	public String getCvc() {
		return cvc;
	}

	public void setCvc(String cvc) {
		this.cvc = cvc;
	}

	public Integer getAlipayType() {
		return alipayType;
	}

	public void setAlipayType(Integer alipayType) {
		this.alipayType = alipayType;
	}

	public String getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(String orderNum) {
		this.orderNum = orderNum;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public Integer getToRentMonths() {
		return toRentMonths;
	}

	public void setToRentMonths(Integer toRentMonths) {
		this.toRentMonths = toRentMonths;
	}

	public String getPayUrl() {
		return payUrl;
	}

	public void setPayUrl(String payUrl) {
		this.payUrl = payUrl;
	}

	@Override
	public String toString() {
		return "RestCard{" +
				       "number='" + number + '\'' +
				       ", exp_month='" + exp_month + '\'' +
				       ", exp_year='" + exp_year + '\'' +
				       ", cvc='" + cvc + '\'' +
				       '}';
	}
}
