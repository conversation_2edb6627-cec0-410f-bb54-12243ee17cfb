package com.aaron.spring.api.v3.controller;

import com.aaron.annotation.LoginAnonymous;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.RestObject;
import com.aaron.spring.api.v3.model.RestPlan;
import com.aaron.spring.api.v3.model.RestPlanNote;
import com.aaron.spring.api.v3.model.RestSpirit;
import com.aaron.spring.api.v3.model.RestSyncSpirit;
import com.aaron.spring.common.BitSetUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.WebUtil;
import com.aaron.spring.model.*;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.*;
import com.aaron.util.ExecuteResult;
import com.aaron.util.FilePropertiesUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;

/**
 *
 *  BusinessException 捕获异常 5XX
 * @Author: Aaron Hao
 * @Date: Created in  2020/10/28
 * @Modified By:
 */
@RestController("RestSpiritControllerV3")
@RequestMapping("/api/v3/spirit")
public class RestSpiritController extends RestBaseController {
    private final Log logger = LogFactory.getLog(RestSpiritController.class);
    public static final Long ABS_DAY_OFFSET = 24 * 60 * 60 * 1000L; //24小时
    public static final Long ONE_DAY = 24 * 60 * 60 * 1000L; //24小时
    private static final String ERROR_NAME = "error.txt";
    private static final String epubBaseDir = FilePropertiesUtil.props.getProperty("epubBaseDir");//
    private static String Book_Img1 = "https://ehome.endao.co/book_image/detail/1601437535784.png";
    private static String Book_Img2 = "https://ehome.endao.co/book_image/detail/1600848324877.png";
    private static String Spirit_List = "https://ehome.endao.co/statics/images/tmp/spiritList.png";
    private static String Spirit_Read = "https://ehome.endao.co/statics/images/tmp/spiritRead.xhtml";
    private static String Link_Base = "https://ehome.endao.co/statics/Styles/";
    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanSpiritService enyanSpiritService;

    @Resource
    private EnyanPlanService enyanPlanService;

    @Resource
    private EnyanPlanNoteService enyanPlanNoteService;

    @Resource
    private EnyanBookBuyService enyanBookBuyService;

    /**
     * <p>全部灵修</p>
     * @param restSpirit
     * @return: com.aaron.spring.pojo.PageResult<com.aaron.spring.api.v3.model.RestSpirit>
     * @since : 2020-10-28
     */
    @LoginAnonymous
    @RequestMapping(value = "/spiritsPage",method = RequestMethod.POST)
    public PageResult<RestSpirit> spirits(@RequestBody RestSpirit restSpirit, HttpServletRequest request){
        restSpirit.initHeaderValue(request);

        PageResult<RestSpirit> result = new PageResult<>();
        /*if (StringUtils.isBlank(rest.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }*/
        List<EnyanSpirit> spiritList = enyanSpiritService.findAllSpiritsOnShelf();
        for (EnyanSpirit tmp:spiritList){
            RestSpirit spirit = new RestSpirit();
            spirit.initFrom(tmp);
            result.getResult().add(spirit);
        }
    /*
        int bookNum =  10 ;
        for (int i = 0; i < 31; i++) {
            bookNum++;
            RestSpirit spirit = new RestSpirit();
            spirit.setBookId(bookNum + 0L);
            spirit.setName("365每日恩典"+bookNum);
            spirit.setAuthor("D.A.卡森");
            spirit.setAuthorDescription("馬克·瓊斯 (Mark Jones)，荷蘭萊頓大學博士，自2007年擔任加拿大溫哥華信心改革宗長老教會的牧師，同時任教於南非的自由州大學（the University of Free State）");
            spirit.setBookDescription("追隨基督的人或多或少都想過相似的問題：耶穌年少時幫爸爸約瑟做木工，會不會問這個或那個工具是什麼？基督這位永生神的兒子是否需要禱告？還是祂禱告只是為信徒立下榜樣？那麼，祂在世時，是憑信心還是憑眼見行事？");
            spirit.setBenefitImgUrl(Spirit_List);
            spirit.setBookImgUrl((i%2 == 0)?Book_Img1:Book_Img2);
            spirit.setDays((i%2 == 0)?365:(i+1));
            spirit.setCopyright("恩道电子书平台的所有电子书产品均受DRM版权保护，且内容提供方对其拥有独立产权，受到香港特别行政区法律的版权法令及国际版权法规保护。");
            spirit.setInfoImgUrl(Spirit_List);
            spirit.setToBuyImgUrl(Spirit_List);
            spirit.setDataBits("排除的时间-占位用可忽略");
            //spirit.setDateStr("2008-1-"+(i+1));
            //spirit.setLanguage((i%2 == 0)?"sc":"tc");
            result.getResult().add(spirit);
        }*/
        return result;
    }
    /**
     * <p>我的灵修下载</p>
     * @param rest
     * @return: com.aaron.spring.pojo.PageResult<com.aaron.spring.api.v3.model.RestSpirit>
     * @since : 2020-10-28
     */
    @RequestMapping(value = "/sync",method = RequestMethod.POST)
    public ExecuteResult<RestSyncSpirit> sync(@RequestBody RestSyncSpirit rest, HttpServletRequest request){
        rest.initHeaderValue(request);

        ExecuteResult<RestSyncSpirit> result = new ExecuteResult<>();
        if (StringUtils.isBlank(rest.getEmail())
                || null == rest.getUpdateTimeSpiritNote()
                || null == rest.getUpdateTimeSpiritPlan()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        Page<EnyanPlan> searchPageSpiritPlan = new Page<>();
        searchPageSpiritPlan.setCurrentPage(1);
        searchPageSpiritPlan.setPageSize(SYNC_PAGE_SIZE);

        EnyanPlan enyanPlan = new EnyanPlan();
        enyanPlan.setUserEmail(rest.getEmail());
        enyanPlan.setUpdateTime(rest.getUpdateTimeSpiritPlan());

        Page<EnyanPlan> plansPage = enyanPlanService.findSpiritPlansGTUpdateTime(searchPageSpiritPlan, enyanPlan);
        List<RestPlan> planList = new ArrayList<>();
        for (EnyanPlan plan:plansPage.getRecords()){
            RestPlan restPlan = new RestPlan();
            restPlan.initFrom(plan);
            planList.add(restPlan);
        }
        rest.setSpiritPlanList(planList);


        Page<EnyanPlanNote> searchPageSpiritPlanNote = new Page<>();
        searchPageSpiritPlanNote.setCurrentPage(1);
        searchPageSpiritPlanNote.setPageSize(SYNC_PAGE_SIZE);

        EnyanPlanNote enyanPlanNote = new EnyanPlanNote();
        enyanPlanNote.setUserEmail(rest.getEmail());
        enyanPlanNote.setUpdateTime(rest.getUpdateTimeSpiritNote());

        Page<EnyanPlanNote> planNotesPage = enyanPlanNoteService.findSpiritPlanNotesGTUpdateTime(searchPageSpiritPlanNote, enyanPlanNote);
        List<RestPlanNote> planNoteList = new ArrayList<>();
        for (EnyanPlanNote planNote:planNotesPage.getRecords()){
            RestPlanNote restPlanNote = new RestPlanNote();
            restPlanNote.initFrom(planNote);
            planNoteList.add(restPlanNote);
        }
        rest.setSpiritNoteList(planNoteList);

        result.setResult(rest);
        //PageResult<RestHighlight> page = new PageResult<>(1,-1,searchPage.getPageSize());

        /*
        int bookNum =  10 ;
        for (int i = 0; i < 10; i++) {
            bookNum++;
            RestSpirit spirit = new RestSpirit();
            spirit.setBookId(bookNum + 0L);
            spirit.setPlanId(UUID.randomUUID().toString());
            spirit.setName("书籍"+bookNum);
            spirit.setEmail("<EMAIL>");
            spirit.setStartFrom(1590940801000L + 24*60*60*1000*i );
            if (i%2==0){
                spirit.setFinished("////////////////////////////////////////////////////////////Hw\\u003d\\u003d");
            }else {
                spirit.setFinished("////////////////////////////////////////////////////////////Hw==");
            }
            spirit.setUpdateTime(1598975999000L+ 24*60*60*1000*i );
            spirit.setIsDeleted(0);

            //result.getResult().add(spirit);
        }*/
        return result;
    }
    /**
     * <p>灵修计划上传</p>
     * @param rest
     * @return: com.aaron.util.ExecuteResult<java.lang.Long>
     * @since : 2020-10-29
     */
    @RequestMapping(value = "/upload",method = RequestMethod.POST)
    public ExecuteResult<Long> upload(@RequestBody RestSpirit rest, HttpServletRequest request){
        rest.initHeaderValue(request);

        ExecuteResult<Long> result = new ExecuteResult<>();
        if (StringUtils.isBlank(rest.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (rest.getList() == null || rest.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
            return result;
        }
        Long updateTime = System.currentTimeMillis();
        result.setResult(0L);
        result.setSuccessMessage(updateTime.toString());
        return result;
    }

    /**
     * <p>灵修计划上传</p>
     * @param rest
     * @return: com.aaron.util.ExecuteResult<java.lang.Long>
     * @since : 2020-12-10
     */
    @RequestMapping(value = "/uploadSpiritPlan",method = RequestMethod.POST)
    public ExecuteResult<Long> uploadSpiritPlan(@RequestBody RestPlan rest, HttpServletRequest request){
        rest.initHeaderValue(request);

        ExecuteResult<Long> result = new ExecuteResult<>();
        if (StringUtils.isBlank(rest.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (rest.getList() == null || rest.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
            return result;
        }
        List<EnyanPlan> enyanPlanList = new ArrayList<>();
        Long updateTime = System.currentTimeMillis();
        for (int i = 0; i < rest.getList().size(); i++) {
            RestPlan restPlan = rest.getList().get(i);
            EnyanPlan enyanPlan = new EnyanPlan();
            enyanPlan.setUserEmail(rest.getEmail());
            enyanPlan.setBookId(restPlan.getBookId());
            enyanPlan.setIsDeleted(restPlan.getIsDeleted());
            enyanPlan.setUpdateTime(updateTime+i);
            if (StringUtils.isNotBlank(restPlan.getFinished())){
                enyanPlan.setFinishedBitSet(BitSetUtil.decode(restPlan.getFinished()).toByteArray());
            }

            enyanPlanList.add(enyanPlan);
        }
        enyanPlanService.updateSyncPlan(rest.getEmail(),enyanPlanList);
        result.setResult(0L);
        result.setSuccessMessage(updateTime.toString());
        return result;
    }

    @RequestMapping(value = "/uploadSpiritNote",method = RequestMethod.POST)
    public ExecuteResult<Long> uploadSpiritNote(@RequestBody RestPlanNote rest, HttpServletRequest request){
        rest.initHeaderValue(request);

        ExecuteResult<Long> result = new ExecuteResult<>();
        if (StringUtils.isBlank(rest.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (rest.getList() == null || rest.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
            return result;
        }
        List<EnyanPlanNote> enyanPlanNoteList = new ArrayList<>();
        Long updateTime = System.currentTimeMillis();
        for (int i = 0; i < rest.getList().size(); i++) {
            RestPlanNote restPlanNote = rest.getList().get(i);
            EnyanPlanNote enyanPlanNote = new EnyanPlanNote();
            enyanPlanNote.setUserEmail(rest.getEmail());
            enyanPlanNote.setBookId(restPlanNote.getBookId());
            enyanPlanNote.setDay(restPlanNote.getDay());
            enyanPlanNote.setDayName(restPlanNote.getDayName());
            enyanPlanNote.setNote(restPlanNote.getNote());
            enyanPlanNote.setCreateTime(restPlanNote.getCreateTime());
            enyanPlanNote.setUpdateTime(updateTime+i);
            enyanPlanNote.setIsDeleted(restPlanNote.getIsDeleted());

            enyanPlanNoteList.add(enyanPlanNote);
        }
        enyanPlanNoteService.updateSyncPlanNote(enyanPlanNoteList);
        result.setResult(updateTime);
        result.setSuccessMessage(updateTime.toString());
        return result;
    }

    @RequestMapping(value = "/addPlan",method = RequestMethod.POST)
    public ExecuteResult<RestPlan> addPlan(@RequestBody RestPlan rest, HttpServletRequest request){
        ExecuteResult<RestPlan> result = new ExecuteResult<>();
        rest.initHeaderValue(request);
        if (StringUtils.isBlank(rest.getEmail())|| null == rest.getBookId()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanSpirit spirit = new EnyanSpirit();
        spirit.setBookId(rest.getBookId());
        EnyanSpirit enyanSpirit = enyanSpiritService.getSpiritByBookId(rest.getBookId());
//        logger.debug("access email= "+request.getAttribute(Constant.ACCESS_EMAIL));
        //EnyanBook book = enyanBookService.queryRecordByPrimaryKey(rest.getBookId()).getResult();
        if (null == enyanSpirit){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanPlan plan = new EnyanPlan();

        List<EnyanBookBuy> buyList = enyanBookBuyService.getBookIDAndNameByEmailAndBookId(rest.getEmail(),rest.getBookId());
        if (null == buyList || buyList.isEmpty()){
            plan.setHasBuy(0);
        }else {
            plan.setHasBuy(1);
        }
        Long updateTime = System.currentTimeMillis();
        plan.setUpdateTime(updateTime);
        plan.setStartFrom(updateTime);
        plan.setName(enyanSpirit.getName());
        plan.setIsDeleted(0);
        plan.setBookId(rest.getBookId());
        plan.setUserEmail(rest.getEmail());

        enyanPlanService.addRecord(plan);
        rest.setUpdateTime(0L);
        rest.setDeviceID(null);
        rest.setStartFrom(updateTime);
        rest.setHasBuy(plan.getHasBuy());
        result.setResult(rest);
        //result.setSuccessMessage(updateTime.toString());
        return result;
    }

    @RequestMapping(value = "/removePlan",method = RequestMethod.POST)
    public ExecuteResult<RestPlan> removePlan(@RequestBody RestPlan rest, HttpServletRequest request){
        ExecuteResult<RestPlan> result = new ExecuteResult<>();
        rest.initHeaderValue(request);
        if (StringUtils.isBlank(rest.getEmail())|| null == rest.getBookId()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
//        logger.debug("access email= "+request.getAttribute(Constant.ACCESS_EMAIL));
        EnyanPlan plan = new EnyanPlan();

        Long updateTime = System.currentTimeMillis();
        plan.setUpdateTime(updateTime);
        plan.setStartFrom(updateTime);
        plan.setIsDeleted(1);
        plan.setBookId(rest.getBookId());
        plan.setUserEmail(rest.getEmail());

        enyanPlanService.removePlan(plan);
        rest.setDeviceID(null);
        result.setResult(rest);
        //result.setSuccessMessage(updateTime.toString());
        return result;
    }

    @RequestMapping(value = "/adjustPlan",method = RequestMethod.POST)
    public ExecuteResult<RestPlan> adjustPlan(@RequestBody RestPlan rest, HttpServletRequest request){
        ExecuteResult<RestPlan> result = new ExecuteResult<>();
        rest.initHeaderValue(request);
        if (StringUtils.isBlank(rest.getEmail()) || null == rest.getBookId() || null == rest.getStartFrom()
            || null == rest.getToDay() || null == rest.getCheckinType()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
//        logger.debug("access email= "+request.getAttribute(Constant.ACCESS_EMAIL));
        EnyanPlan plan = enyanPlanService.getFullPlanByEmailAndBookId(rest.getEmail(),rest.getBookId());
        if (null == plan || 1 != plan.getHasBuy()){//未购买
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        int today = rest.getToDay() - 1;//当天不能打卡
        plan.setStartFrom(rest.getStartFrom());
        if (2 == rest.getCheckinType()){//之前全部打卡
            BitSet bitSet = new BitSet();
            for (int i = 0; i < today; i++) {
                bitSet.set(i);
            }
            plan.setFinishedBitSet(bitSet.toByteArray());
        }else if (null != plan.getFinishedBitSet()){
            if (1 == rest.getCheckinType()){//保留原有
                BitSet setsDB = BitSetUtil.fromByteArray(plan.getFinishedBitSet());
                BitSet setsNew = new BitSet();
                for (int i = 0; i < setsDB.length(); i++) {
                    if (i >= today){
                        break;
                    }
                    if (setsDB.get(i)){
                        setsNew.set(i);
                    }
                }
                plan.setFinishedBitSet(setsNew.toByteArray());
            }
        }

        Long updateTime = System.currentTimeMillis();
        plan.setUpdateTime(updateTime);

        enyanPlanService.adjustPlan(plan);

        rest.setDeviceID(null);
        if (null != plan.getFinishedBitSet()){
            rest.setFinished(BitSetUtil.encode(plan.getFinishedBitSet()));
        }
        result.setResult(rest);
        //result.setSuccessMessage(updateTime.toString());
        return result;
    }


    /**
     * <p>阅读</p>
     * @param rest
     * @return: com.aaron.util.ExecuteResult<RestObject<String>>
     * @since : 2020-11-19
     */
    @RequestMapping(value = "/read",method = RequestMethod.POST)
    public ExecuteResult<RestObject<String>> readObject(@RequestBody RestSpirit rest, HttpServletRequest request){
        ExecuteResult<RestObject<String>> result = new ExecuteResult<>();
        RestObject<String> object = new RestObject<>();
        if (StringUtils.isBlank(rest.getEmail()) || null == rest.getCurrentDay()|| null == rest.getBookId()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        String path = request.getContextPath();
        String scheme = request.getScheme();
        if (!Constant.IS_LOCAL){
            scheme = "https";
        }
        String basePath = WebUtil.getBasePath();

        object.setObj(basePath+"spiritRead/v3/read/getPage1?day="+ rest.getCurrentDay()+"&bookId="+ rest.getBookId());
        result.setResult(object);
        return result;
    }

    @RequestMapping(value = "/listPlan",method = RequestMethod.POST)
    public ExecuteResult<List<RestPlan>> listPlan(@RequestBody RestPlan rest, HttpServletRequest request){
        ExecuteResult<List<RestPlan>> result = new ExecuteResult<>();
        rest.initHeaderValue(request);
        if (StringUtils.isBlank(rest.getEmail())|| null == rest.getBookId()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
//        logger.debug("access email= "+request.getAttribute(Constant.ACCESS_EMAIL));
        EnyanPlan plan = new EnyanPlan();

//        Long updateTime = System.currentTimeMillis();
//        plan.setUpdateTime(updateTime);
//        plan.setStartFrom(updateTime);
//        plan.setIsDeleted(1);
//        plan.setBookId(rest.getBookId());
        plan.setUserEmail(rest.getEmail());

        List<EnyanPlan> list = enyanPlanService.queryRecords(null,plan).getRecords();
        List<RestPlan> restPlanList = new ArrayList<>();
        for (EnyanPlan enyanPlan:list){
            RestPlan restPlan = new RestPlan();
            restPlan.initFrom(enyanPlan);
            restPlanList.add(restPlan);

            if (null != enyanPlan.getFinishedBitSet()){
                BitSet finshedBitSet = BitSetUtil.fromByteArray(enyanPlan.getFinishedBitSet());
                System.out.println("finished:"+finshedBitSet);
            }
        }
        rest.setDeviceID(null);
        result.setResult(restPlanList);
        //result.setSuccessMessage(updateTime.toString());
        return result;
    }
}
