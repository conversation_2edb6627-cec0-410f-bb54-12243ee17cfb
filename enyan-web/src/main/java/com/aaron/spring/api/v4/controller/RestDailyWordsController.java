package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestBlog;
import com.aaron.spring.api.v4.model.RestDailyWords;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBlog;
import com.aaron.spring.model.EnyanDailyWords;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.EnyanDailyWordsService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
@Slf4j
@RestController("RestDailyWordsControllerV4")
@RequestMapping("/api/v4/dailyWords")
public class RestDailyWordsController extends RestBaseController {

	@Resource
	private EnyanDailyWordsService enyanDailyWordsService;

	@LoginAnonymous
	@RequestMapping(value = "/page",method = RequestMethod.POST)
	public PageResult<RestDailyWords> page(@RequestBody RestDailyWords restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		PageResult<RestDailyWords> pageResult = new PageResult<>();
		if (null == restObj.getPage()){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}
		if (restObj.getPage() == 1){//第一页直接使用缓存即可
			pageResult.setResult(Constant.DEFAULT_REST_CONFIG.getDailyWords());
			pageResult.setTotalRecord(Constant.DEFAULT_REST_CONFIG.getCountOfDailyWords());
			return pageResult;
		}

		Page<EnyanDailyWords> page = new Page();
		page.setCurrentPage(restObj.getPage());
		page.setPageSize(pageResult.getPageSize());

		EnyanDailyWords searchObj = new EnyanDailyWords();
		searchObj.setPage(page);

		String todayString = DateFormatUtils.format(new Date(), "yyyyMMdd");
		Integer today = Integer.parseInt(todayString);
		searchObj.setDataAt(today);

        OrderObj orderObj = new OrderObj("data_at",InterfaceContant.OrderBy.DESC);
		searchObj.addOrder(orderObj);
		page = enyanDailyWordsService.queryRecords(searchObj.getPage(),searchObj);
		/*
		for (EnyanDailyWords obj : page.getRecords()){
			RestDailyWords tmp = new RestDailyWords();
			tmp.initFrom(obj);
			pageResult.getResult().add(tmp);
		}*/
		for (int i = page.getRecords().size() - 1; i >= 0; i--) {
			EnyanDailyWords obj = page.getRecords().get(i);
			RestDailyWords tmp = new RestDailyWords();
			tmp.initFrom(obj);
			pageResult.getResult().add(tmp);
		}

		pageResult.setCurrentPage(page.getCurrentPage());
		pageResult.setTotalRecord(page.getTotalRecord());
		return pageResult;
	}

	@LoginAnonymous
	@RequestMapping(value = "/like",method = RequestMethod.POST)
	public ExecuteResult<RestDailyWords> like(@RequestBody RestDailyWords restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		ExecuteResult<RestDailyWords> result = new ExecuteResult<>();
		if (null == restObj.getDataId()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		enyanDailyWordsService.updateRecordToLikeById(restObj.getDataId());
		return result;
	}

	@LoginAnonymous
	@RequestMapping(value = "/today")
	public String today( HttpServletRequest request){
		if (Constant.DEFAULT_REST_CONFIG.getDailyWords().isEmpty()){
			return "【诗篇23篇:1节】（大卫的诗。）耶和华是我的牧者，我必不至缺乏。";
		}
		//https://ebook.endao.co/api/v4/dailyWords/today
		return Constant.DEFAULT_REST_CONFIG.getDailyWords().get(0).showMsg();
	}
}
