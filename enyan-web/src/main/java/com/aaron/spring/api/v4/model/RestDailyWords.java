package com.aaron.spring.api.v4.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.EnyanDailyWords;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/7/19
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestDailyWords extends RestBaseDTO {
	private static final long serialVersionUID = -1014254861651350127L;
	private Long dataId;

	private String dataContent;

	private Long bookId;

	private String bookTitle;

	private String bookAuthor;

	private String dataImgUrl;

	private Integer likeCount;

	private Integer dataAt;

	public void initFrom(EnyanDailyWords obj){
		this.dataId = obj.getDataId();
		this.dataContent = obj.getDataContent();
		this.bookId = obj.getBookId();
		this.bookTitle = obj.getBookTitle();
		this.bookAuthor = obj.getBookAuthor();
		this.dataImgUrl = obj.getDataImgUrl();
		this.likeCount = obj.getLikeCount();
		this.dataAt = obj.getDataAt();
	}

	public Long getDataId() {
		return dataId;
	}

	public void setDataId(Long dataId) {
		this.dataId = dataId;
	}

	public String getDataContent() {
		return dataContent;
	}

	public void setDataContent(String dataContent) {
		this.dataContent = dataContent;
	}

	public Long getBookId() {
		return bookId;
	}

	public void setBookId(Long bookId) {
		this.bookId = bookId;
	}

	public String getBookTitle() {
		return bookTitle;
	}

	public void setBookTitle(String bookTitle) {
		this.bookTitle = bookTitle;
	}

	public String getBookAuthor() {
		return bookAuthor;
	}

	public void setBookAuthor(String bookAuthor) {
		this.bookAuthor = bookAuthor;
	}

	public String getDataImgUrl() {
		return dataImgUrl;
	}

	public void setDataImgUrl(String dataImgUrl) {
		this.dataImgUrl = dataImgUrl;
	}

	public Integer getLikeCount() {
		return likeCount;
	}

	public void setLikeCount(Integer likeCount) {
		this.likeCount = likeCount;
	}

	public Integer getDataAt() {
		return dataAt;
	}

	public void setDataAt(Integer dataAt) {
		this.dataAt = dataAt;
	}

	@Override
	public String toString() {
		return "RestDailyWords{" +
				       "dataId=" + dataId +
				       ", dataContent='" + dataContent + '\'' +
				       ", bookId=" + bookId +
				       ", bookTitle='" + bookTitle + '\'' +
				       ", bookAuthor='" + bookAuthor + '\'' +
				       ", dataImgUrl='" + dataImgUrl + '\'' +
				       ", likeCount=" + likeCount +
				       ", dataAt=" + dataAt +
				       '}';
	}

	public String showMsg() {
		return dataContent +
				       ", 出自书籍：" + bookTitle  +
				       ", 作者为：" + bookAuthor ;
	}

	public static void main(String[] args) {
		printInit("com.aaron.spring.api.v4.model.RestDailyWords","EnyanDailyWords");
	}
}
