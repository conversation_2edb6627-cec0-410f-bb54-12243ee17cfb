package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.GeoIPRequired;
import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.CreditInfo;
import com.aaron.common.OrderObj;
import com.aaron.drm.model.LcpInfo;
import com.aaron.exception.DownloadException;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.RestCard;
import com.aaron.spring.api.RestConfig;
import com.aaron.spring.api.v4.model.*;
import com.aaron.spring.common.*;
import com.aaron.spring.controller.ShopController;
import com.aaron.spring.model.*;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.*;
import com.aaron.util.ExecuteResult;
import com.aaron.util.UserUtils;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.config.AlipayConfig;
import com.alipay.util.AlipaySubmit;
import com.stripe.config.StripeConfig;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.net.RequestOptions;
import com.stripe.util.StripeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.aaron.spring.api.RestBaseController.ReturnInfo.*;

/**
 * BusinessException 捕获异常 6XX
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2022/10/24
 * @Modified By:
 */
@Slf4j
@RestController("RestRentControllerV4")
@RequestMapping(path = {"/api/v4/rent","/front/v4/rent"})
public class RestRentController extends RestBaseController{
	@Resource
	private EnyanBookService enyanBookService;

	@Resource
	private EnyanBookBuyService enyanBookBuyService;

	@Resource
	private EnyanRentService enyanRentService;

	@Resource
	private EnyanRentDetailService enyanRentDetailService;

	@Resource
	private EnyanOrderService enyanOrderService;

	@Resource
	private AuthUserService authUserService;

	@Resource
	private ShopController shopController;

	@Resource
	private SendEmailService sendEmailService;

	@LoginAnonymous
	@RequestMapping(value = "/baseInfo",method = RequestMethod.POST)
	public ExecuteResult<JSONObject> baseInfo(@RequestBody RestRent restObj, HttpServletRequest request){
		log.debug("list");
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		ExecuteResult<JSONObject> result = new ExecuteResult<>();
		JSONObject object = new JSONObject();
		try {
			RestConfig config = (RestConfig) Constant.DEFAULT_REST_CONFIG.clone();
			List<RestRent> autoList = config.getToBuyRentsAuto();
			List<RestRent> manualList = config.getToBuyRentsManual();

			for (RestRent tmp : autoList) {
				tmp.setFeeByCurrency(currencyType);
			}
			for (RestRent tmp : manualList) {
				tmp.setFeeByCurrency(currencyType);
			}
			object.put("autoList", autoList);
			object.put("manualList", manualList);

			String email = "";
			String name = "";
			String isLogin = "0";
			if (UserUtils.isAnonymous() == false){
				email = UserUtils.getCurrentLoginUser().getEmail();
				isLogin = "1";
				name = UserUtils.getCurrentLoginUser().getNickName();
			}
			Object sessionData = request.getSession().getAttribute(EBookConstant.Cart.DATA);
			String cartNum = "0";
			if (null != sessionData){
				Map<String,String> data = (Map<String, String>) sessionData;
				if (null != data.get("quantity")){
					cartNum = data.get("quantity");
				}
			}
			JSONObject user = new JSONObject();
			user.put("isLogin", isLogin);//是否已经登陆
			user.put("name", name);
			user.put("email", email);//email
			user.put("cart", cartNum);//购物车个数

			object.put("user", user);
		} catch (CloneNotSupportedException e) {
			throw new RuntimeException(e);
		}
		result.setResult(object);
		return result;
	}
	/**
	 * <p>我的订阅列表</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<java.util.List<com.aaron.spring.api.v4.model.RestRent>>
	 * @since : 2022/10/31
	 **/
	@RequestMapping(value = "/list",method = RequestMethod.POST)
	public PageResult<RestRent> list(@RequestBody RestRent restObj, HttpServletRequest request){
		log.debug("list");
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		PageResult<RestRent> pageResult = new PageResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getPage()){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}
		Page<EnyanRent> page = new Page();
		page.setCurrentPage(restObj.getPage());
		page.setPageSize(pageResult.getPageSize());

		EnyanRent searchObj = new EnyanRent();
		searchObj.setUserEmail(restObj.getEmail());
		searchObj.setIsPaid(restObj.getIsPaid());
		searchObj.setPage(page);

		OrderObj orderObj = new OrderObj("create_at", InterfaceContant.OrderBy.DESC);
		searchObj.addOrder(orderObj); //暂时屏蔽
		page = enyanRentService.queryRecords(searchObj.getPage(),searchObj);
		/*
		for (EnyanDailyWords obj : page.getRecords()){
			RestDailyWords tmp = new RestDailyWords();
			tmp.initFrom(obj);
			pageResult.getResult().add(tmp);
		}*/
		for (EnyanRent obj : page.getRecords()){
			RestRent tmp = new RestRent();
			tmp.initFrom(obj,currencyType);
			pageResult.getResult().add(tmp);
		}

		pageResult.setCurrentPage(page.getCurrentPage());
		pageResult.setTotalRecord(page.getTotalRecord());
		return pageResult;
	}


	@RequestMapping(value = "/add",method = RequestMethod.POST)
	public ExecuteResult<RestRent> add(@RequestBody RestRent restObj, HttpServletRequest request) throws Exception {
		log.debug("add");
		restObj.initHeaderValue(request);

		ExecuteResult<RestRent> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getRentType()
				    || null == restObj.getRentLang() || null == restObj.getIsAuto()
				    || null == restObj.getToRentMonths()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		/*
		if (1 == restObj.getIsAuto()){
			if (StringUtils.isBlank(restObj.getCreditInfo())){//自动续费必须要有信用卡信息
				result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
				return result;
			}
		}*/

		Date current = new Date();
		EnyanRent queryObject = new EnyanRent();
		queryObject.setRentType(restObj.getRentType());
		queryObject.setRentLang(restObj.getRentLang());
		queryObject.setUserEmail(restObj.getEmail());

		List<EnyanRent> searchList = enyanRentService.findRecords(queryObject);
		if (null != searchList && searchList.size() > 0){
			for (EnyanRent rent:searchList){
				if (rent.getIsValid() == 1){//有现存订单
					result.addErrorMessage(ReturnInfo.ERROR_RENT_ORDER_EXIST);
					return result;
				}
				if (rent.getIsPaid() !=1){//有待支付的订单
					Date orderExpiredAt = DateUtils.addHours(rent.getBeginAt(), 2);
					if (current.after(orderExpiredAt)){//应该要超时了
						continue;
					}
					result.addErrorMessage(ReturnInfo.ERROR_RENT_ORDER_NOT_PAY);
					return result;
				}

			}
		}

		EnyanRent obj = new EnyanRent();

		Date date = new Date();
		obj.setCreateAt(date);
		obj.setExpiredAt(date);//未付款的时候，过期时间就是当前时间，付款成功后需要更新这个数据，直接添加月份数即可
		obj.setBeginAt(date);//创建时间，后续不会发生改变

		obj.setOrderNum(OrderUtil.getOrderIdFromRent());
		obj.setIsAuto(restObj.getIsAuto());
		obj.setIsPaid(0);
		obj.setIsValid(0);
		obj.setRentLang(restObj.getRentLang());
		obj.setRentStatus(EBookConstant.RentStatus.Default);
		obj.setRentType(restObj.getRentType());
		obj.setUserEmail(restObj.getEmail());
		obj.setLeaveBuy(0);
		obj.setTotalMonths(0);
		obj.setOrderFrom(BookUtil.getOrderFrom(restObj.getDeviceFrom()));

		obj.setPublisherId(1L);
		obj.setIsDeleted(0);

		BigDecimal price = BookUtil.getRentPrice(restObj.getRentType(), restObj.getIsAuto());
		obj.setRentPrice(price);
		obj.setTotalFee(new BigDecimal("0"));

		enyanRentService.addRecord(obj);

		restObj.setCreateAt(date.getTime());
		restObj.setOrderNum(obj.getOrderNum());
		result.setResult(restObj);
		return result;
	}

	@RequestMapping(value = "/detail",method = RequestMethod.POST)
	public ExecuteResult<List<RestRentDetail>> detail(@RequestBody RestRent restObj, HttpServletRequest request){
		log.debug("detail");
		restObj.initHeaderValue(request);

		ExecuteResult<List<RestRentDetail>> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getOrderNum())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanRentDetail queryObject = new EnyanRentDetail();
		queryObject.setUserEmail(restObj.getEmail());
		queryObject.setOrderNum(restObj.getOrderNum());
		List<EnyanRentDetail> queryList = enyanRentDetailService.findRecords(queryObject);
		List<RestRentDetail> list = new ArrayList<>();

		for (EnyanRentDetail tmp :
				queryList) {
			RestRentDetail obj = new RestRentDetail();
			obj.initFrom(tmp);
			list.add(obj);
		}

		result.setResult(list);
		return result;
	}

	/**
	 * <p>可以下载的书籍</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.spring.pojo.PageResult<com.aaron.spring.api.v4.model.RestBook>
	 * @since : 2022/12/14
	 **/
	@RequestMapping(value = "/rentRead",method = RequestMethod.POST)
	public PageResult<RestBook> rentRead(@RequestBody RestRent restObj, HttpServletRequest request){
		log.debug("rentRead");
		restObj.initHeaderValue(request);

		PageResult<RestBook> pageResult = new PageResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getOrderNum()) || null == restObj.getPage()){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}
		EnyanRent queryObject = new EnyanRent();
		queryObject.setUserEmail(restObj.getEmail());
		queryObject.setOrderNum(restObj.getOrderNum());
		List<EnyanRent> queryList = enyanRentService.findRecords(queryObject);
		if (queryList.isEmpty() == true){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}
		EnyanRent currentRent = queryList.get(0);
		if (currentRent.getIsValid() == 0){
			pageResult.addErrorMessage(ReturnInfo.ERROR_RENT_EXPIRED);
			return pageResult;
		}
		List<Long> bookIdList = BookUtil.getBooksInRentType(currentRent.getRentType(), currentRent.getRentLang());
		pageResult.setTotalRecord(bookIdList.size());
		pageResult.setCurrentPage(restObj.getPage());
		if (restObj.getPage() > pageResult.getTotalPage()){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}
		int firstIndex = (pageResult.getCurrentPage() - 1) * pageResult.getPageSize();
		int toIndex = (pageResult.getCurrentPage()) * pageResult.getPageSize();
		if (toIndex > bookIdList.size()){
			toIndex = bookIdList.size();
		}
		List<Long> newIdList = bookIdList.subList(firstIndex, toIndex);
		List<EnyanBook> bookList = enyanBookService.findBookByIds(newIdList);
		Collections.sort(bookList, (o1, o2) -> {
			int io1 = bookIdList.indexOf(o1.getBookId().longValue());
			int io2 = bookIdList.indexOf(o2.getBookId().longValue());
			return (io1 == -1 || io2 == -1)?(io2-io1):(io1-io2);
		});
		for (EnyanBook enyanBook:bookList){
			if (null == enyanBook){
				continue;
			}
			RestBook book = new RestBook();
			book.initFrom(enyanBook);
			//book.setGroupName(enyanBookBuy.getGroupName());
			//book.setReadLocation(enyanBookBuy.getReadInfo());
			//book.setDateStr(DateFormatUtils.format(enyanBookBuy.getPurchasedAt(), Constant.DATE_FORMAT_PATTERN));
			pageResult.getResult().add(book);
		}
		return pageResult;
	}

	@GeoIPRequired
	@RequestMapping(value = "/leave",method = RequestMethod.POST)
	public ExecuteResult<RestOrder<RestBook>> leave(@RequestBody RestRent restObj, HttpServletRequest request) throws Exception {
		log.debug("leave");
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		ExecuteResult<RestOrder<RestBook>> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getDataId() || null == restObj.getLeaveType()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		if (restObj.getLeaveType() == 2){//是部分购买
			if (null == restObj.getList() || restObj.getList().isEmpty() == true){
				result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
				result.addErrorMessage("E1");
				return result;
			}
		}

		EnyanRent rent = enyanRentService.queryRecordBaseInfoByPrimaryKey(restObj.getDataId());
		if (null == rent || rent.getUserEmail().equals(restObj.getEmail()) == false){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage("E2");
			return result;
		}
		if (rent.getIsPaid() != 1 || rent.getLeaveBuy() == 1){//本身就没有付款或者已经使用过优惠
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage("E3");
			return result;
		}

		if (restObj.getLeaveType() == 0){//直接退出
			EnyanRent update = new EnyanRent();
			update.setLeaveAt(new Date());
			update.setRentStatus(EBookConstant.RentStatus.leave);//退订
			update.setRentId(rent.getRentId());
			enyanRentService.updateRecord(update);
			return result;
		}
		Date current = new Date();
		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}
		HashSet<Long> notBuySet = new HashSet<>();
		//List<RestBook> notBuyList = new ArrayList<>();
		List<Long> rentBookIdList = BookUtil.getBooksInRentType(rent.getRentType(), rent.getRentLang());
		for (Long bookId:rentBookIdList){
			if (booksHaveBuy.contains(bookId.longValue()) == false){
				notBuySet.add(bookId);
			}
		}

		for (Long bookId: restObj.getList()){
			if (notBuySet.contains(bookId.longValue()) == false){//有不在 未购里面的书籍，直接报错
				result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
				result.addErrorMessage("E4");
				return result;
			}
		}
		List<EnyanBook> mustBuyList ;
		if (restObj.getLeaveType() == 1){//全部购买
			mustBuyList = enyanBookService.findBookByIds(notBuySet.stream().collect(Collectors.toList()));
		}else {//部分购买
			mustBuyList = enyanBookService.findBookByIds(restObj.getList());
		}
		BigDecimal discount = new BigDecimal("1");
		Byte isSingleValid = Constant.BYTE_VALUE_0;
		if (restObj.getLeaveType() == 1){//如果是全部购买
			discount = BookUtil.getRentDiscount(rent.getBeginAt());
			isSingleValid = Constant.BYTE_VALUE_1;
		}
		CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restObj.getLang());
		for (EnyanBook enyanBook:mustBuyList){
			enyanBook.setDiscountSingleIsValid(isSingleValid);
			enyanBook.setPriceHKDDiscount(enyanBook.getPriceHkd().multiply(discount).setScale(Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

			enyanBook.setDiscountId(null);
			enyanBook.setDiscountIsValid(Constant.BYTE_VALUE_0);

			enyanBook.resetByArea(restObj.getArea());

			ProductInfo productInfo = new ProductInfo(enyanBook);
			cartInfo.addProduct(productInfo,1);
		}

		OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

		OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
		EnyanOrder order = new EnyanOrder();
		order.setOrderNum(OrderUtil.getOrderIdFromRentToBuy());
		order.setOrderTitleInfo(titleInfo);
		order.setOrderDetailInfo(detailInfo);

		order.setIsValid(Constant.BYTE_VALUE_1);
		order.setIsPaid(Constant.BYTE_VALUE_0);
		order.setIsCounted(Constant.BYTE_VALUE_0);

		order.setOrderType(EBookConstant.OrderType.ORDER_FROM_RENT_BUY);

		order.setUserId(-1L);
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
		order.setUserEmail(restObj.getEmail());
		order.setPurchasedAt(current);
		order.setExpiredAt(DateUtils.addHours(order.getPurchasedAt(), 2));

		order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
		order.setOrderTotal(detailInfo.getAmountHkd());
		order.setOrderFrom(EBookConstant.OrderFrom.WEB);

		EnyanCoupon enyanCoupon = new EnyanCoupon();
		enyanCoupon.initWithRent(rent);
		if (order.getOrderTotal().doubleValue() > 0){
			if (StringUtils.isNotBlank(enyanCoupon.getCouponCode())){
				if (enyanCoupon.getCouponValue() != order.getOrderDetailInfo().getAmountCoupon().intValue()){
					order.getOrderDetailInfo().setCouponCode(enyanCoupon.getCouponCode().toUpperCase());//设置优惠码，用于记录一个人只能使用一次
					order.getOrderDetailInfo().setAmountCoupon(new BigDecimal(enyanCoupon.getCouponValue()+""));//设置优惠金额
					order.getOrderDetailInfo().resetProductListByCoupon(enyanCoupon);
					/*
					if (order.getOrderDetailInfo().getTotalFeeBeforeCoupon().compareTo(new BigDecimal(enyanCoupon.getMinLimitValue())) == -1){//小于最小满减限额
						result.addErrorMessage(ReturnInfo.ERROR_COUPON_LIMIT);
						return result;
					}*/
					order.setOrderTotal(new BigDecimal(order.getOrderDetailInfo().getAmountHkd()+""));
				}
			}
		}else{//免费的订单
			OrderPayInfo orderPayInfo = new OrderPayInfo();
			orderPayInfo.addFree();
			order.setOrderPayInfo(orderPayInfo);
			order.setIsPaid(Constant.BYTE_VALUE_1);
		}

		enyanOrderService.addRecord(order);

		RestOrder restOrder = new RestOrder();
		restOrder.initFrom(order,currencyType);
		restOrder.setOrderId(order.getOrderId());
		result.setResult(restOrder);


		EnyanRent update = new EnyanRent();
		if (rent.getRentStatus() != EBookConstant.RentStatus.leave
				    && rent.getRentStatus() != EBookConstant.RentStatus.expired){//还没有设置退订状态
			update.setLeaveAt(new Date());
			update.setRentStatus(EBookConstant.RentStatus.leave);//退订
		}
		update.setRentId(rent.getRentId());
		update.setLeaveBuy(1);
		update.setLeaveBuyAt(current);
		update.setToOrderNum(order.getOrderNum());
		enyanRentService.updateRecord(update);

		if (order.getOrderTotal().doubleValue() > 0){

		}else {//免费的，直接支付成功
			OrderPayInfo orderPayInfo = new OrderPayInfo();
			orderPayInfo.addFree();
			order.setOrderPayInfo(orderPayInfo);
			order.setIsPaid(Constant.BYTE_VALUE_1);
			new Thread(new Runnable(){
				@Override
				public void run() {
					shopController.paySuccess(order, order.getOrderPayInfo(), request);
				}
			}
			).start();
		}

		return result;
	}

	/**
	 * <p>购买全套前的数据信息</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestCart>
	 * @since : 2023/1/5
	 **/
	@GeoIPRequired
	@RequestMapping(value = "/toBuyCBCS",method = RequestMethod.POST)
	public ExecuteResult<RestCart> buyCBCSToBuy(@RequestBody RestRent restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		ExecuteResult<RestCart> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getRentLang() || null == restObj.getRentType()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		List<EnyanBook> list = enyanBookService.findBookByIds(BookUtil.getBooksInRentType(restObj.getRentType(), restObj.getRentLang()));
		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}
		BigDecimal discount = new BigDecimal("0.55");//购买全套55折
		List<RestBook> buyList = new ArrayList<>();
		List<RestBook> notBuyList = new ArrayList<>();
		for (EnyanBook enyanBook:list){
			enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_1);
			enyanBook.setPriceHKDDiscount(enyanBook.getPriceHkd().multiply(discount).setScale(Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

			enyanBook.resetByArea(restObj.getArea());

			RestBook tmp = new RestBook();
			tmp.initFrom(enyanBook,currencyType);
			if (null == tmp.getPrice()){//如果没有设置折扣信息，则原价为折扣价
				tmp.setPrice(tmp.getPriceDiscount());
			}
			//tmp.setPriceDiscount(tmp.getPrice().multiply(discount));
			if(booksHaveBuy.contains(enyanBook.getBookId().longValue())){//需要使用long
				buyList.add(tmp);
			}else {
				notBuyList.add(tmp);
			}
		}

		RestCart restCart = new RestCart();
		restCart.setBuyList(buyList);
		restCart.setNotBuyList(notBuyList);
		result.setResult(restCart);
		return result;
	}

	/**
	 * <p>直接购买整套书籍</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestOrder<com.aaron.spring.api.v4.model.RestBook>>
	 * @since : 2023/1/5
	 **/
	@RequestMapping(value = "/buyCBCS",method = RequestMethod.POST)
	public ExecuteResult<RestOrder<RestBook>> buyCBCS(@RequestBody RestRent restObj, HttpServletRequest request) throws Exception {
		log.debug("leave");
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		ExecuteResult<RestOrder<RestBook>> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getRentLang() || null == restObj.getRentType()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}
		HashSet<Long> notBuySet = new HashSet<>();
		//List<RestBook> notBuyList = new ArrayList<>();
		List<Long> rentBookIdList = BookUtil.getBooksInRentType(restObj.getRentType(), restObj.getRentLang());
		for (Long bookId:rentBookIdList){
			if (booksHaveBuy.contains(bookId.longValue()) == false){
				notBuySet.add(bookId);
			}
		}

		List<EnyanBook> mustBuyList = enyanBookService.findBookByIds(notBuySet.stream().collect(Collectors.toList()));//全部购买

		BigDecimal discount = new BigDecimal("0.55");//全部购买
		Byte isSingleValid = Constant.BYTE_VALUE_1;
		CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restObj.getLang());
		for (EnyanBook enyanBook:mustBuyList){
			enyanBook.setDiscountSingleIsValid(isSingleValid);
			enyanBook.setPriceHKDDiscount(enyanBook.getPriceHkd().multiply(discount).setScale(Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

			enyanBook.setDiscountId(null);
			enyanBook.setDiscountIsValid(Constant.BYTE_VALUE_0);

			enyanBook.resetByArea(restObj.getArea());

			ProductInfo productInfo = new ProductInfo(enyanBook);
			cartInfo.addProduct(productInfo,1);
		}

		OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

		OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
		EnyanOrder order = new EnyanOrder();
		order.setOrderNum(OrderUtil.getOrderId());
		order.setOrderTitleInfo(titleInfo);
		order.setOrderDetailInfo(detailInfo);

		order.setIsValid(Constant.BYTE_VALUE_1);
		order.setIsPaid(Constant.BYTE_VALUE_0);
		order.setIsCounted(Constant.BYTE_VALUE_0);

		order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);

		order.setUserId(-1L);
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
		order.setUserEmail(restObj.getEmail());
		order.setPurchasedAt(new Date());
		order.setExpiredAt(DateUtils.addHours(order.getPurchasedAt(), 2));

		order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
		order.setOrderTotal(detailInfo.getAmountHkd());
		order.setOrderFrom(EBookConstant.OrderFrom.WEB);

		/*
		EnyanCoupon enyanCoupon = new EnyanCoupon();
		enyanCoupon.initWithRent(rent);
		if (order.getOrderTotal().doubleValue() > 0){
			if (StringUtils.isNotBlank(enyanCoupon.getCouponCode())){
				if (enyanCoupon.getCouponValue() != order.getOrderDetailInfo().getAmountCoupon().intValue()){
					order.getOrderDetailInfo().setCouponCode(enyanCoupon.getCouponCode().toUpperCase());//设置优惠码，用于记录一个人只能使用一次
					order.getOrderDetailInfo().setAmountCoupon(new BigDecimal(enyanCoupon.getCouponValue()+""));//设置优惠金额
					order.getOrderDetailInfo().resetProductListByCoupon(enyanCoupon);

					order.setOrderTotal(new BigDecimal(order.getOrderDetailInfo().getAmountHkd()+""));
				}
			}
		}else{
			OrderPayInfo orderPayInfo = new OrderPayInfo();
			orderPayInfo.addFree();
			order.setOrderPayInfo(orderPayInfo);
			order.setIsPaid(Constant.BYTE_VALUE_1);
		}*/

		enyanOrderService.addRecord(order);

		RestOrder restOrder = new RestOrder();
		restOrder.initFrom(order,currencyType);
		restOrder.setOrderId(order.getOrderId());
		result.setResult(restOrder);

		return result;
	}

	@RequestMapping(value = "/del",method = RequestMethod.POST)
	public ExecuteResult<RestRent> del(@RequestBody RestRent restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		ExecuteResult<RestRent> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getDataId()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanRent queryObj = enyanRentService.queryRecordBaseInfoByPrimaryKey(restObj.getDataId());
		if (null == queryObj || queryObj.getUserEmail().equals(restObj.getEmail()) == false){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage("E1");
			return result;
		}
		if (queryObj.getIsPaid() == 1){//已经支付的订单不可以删除
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage("E2");
			return result;
		}

		EnyanRent update = new EnyanRent();
		update.setRentId(restObj.getDataId());
		update.setIsDeleted(1);//直接删除
		enyanRentService.updateRecord(update);

		return result;
	}

	/**
	 * <p>退订要进行购买时的书籍信息</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestCart>
	 * @since : 2022/11/1
	 **/
	@GeoIPRequired
	@RequestMapping(value = "/toBuy",method = RequestMethod.POST)
	public ExecuteResult<RestCart> toBuy(@RequestBody RestRent restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		ExecuteResult<RestCart> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getOrderNum())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		EnyanRent queryObject = new EnyanRent();//根据orderNum获取Rent数据
		queryObject.setOrderNum(restObj.getOrderNum());
		queryObject.setUserEmail(restObj.getEmail());

		List<EnyanRent> queryList = enyanRentService.findRecords(queryObject);
		if (queryList.isEmpty() == true){//需要校验email数据
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage("E1");
			return result;
		}

		queryObject = queryList.get(0);

		List<EnyanBook> list = enyanBookService.findBookByIds(BookUtil.getBooksInRentType(queryObject.getRentType(), queryObject.getRentLang()));
		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}
		BigDecimal discount = BookUtil.getRentDiscount(queryObject.getBeginAt());
		List<RestBook> buyList = new ArrayList<>();
		List<RestBook> notBuyList = new ArrayList<>();
		for (EnyanBook enyanBook:list){
			enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_1);
			enyanBook.setPriceHKDDiscount(enyanBook.getPriceHkd().multiply(discount).setScale(Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

			enyanBook.resetByArea(restObj.getArea());

			RestBook tmp = new RestBook();
			tmp.initFrom(enyanBook,currencyType);
			if (null == tmp.getPrice()){//如果没有设置折扣信息，则原价为折扣价
				tmp.setPrice(tmp.getPriceDiscount());
			}
			//tmp.setPriceDiscount(tmp.getPrice().multiply(discount));
			if(booksHaveBuy.contains(enyanBook.getBookId().longValue())){//需要使用long
				buyList.add(tmp);
			}else {
				notBuyList.add(tmp);
			}
		}

		RestCart restCart = new RestCart();
        restCart.setBuyList(buyList);
		restCart.setNotBuyList(notBuyList);
		result.setResult(restCart);
		return result;
	}

	@RequestMapping(value = "/checkOrderPay",method = RequestMethod.POST)
	public ExecuteResult<RestCard> payCheckOrderHasPay(@RequestBody RestCard restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		//CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
		ExecuteResult<RestCard> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getOrderNum())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		long count = enyanRentDetailService.countOfTodayOrder(restObj.getOrderNum());
		if (count > 0){
			result.setResult(restObj);
			return result;
		}
		result.addErrorMessage(ERROR_PAY_FAIL);
		return result;
	}

	@RequestMapping(value = "/payAlipay",method = RequestMethod.POST)
	public ExecuteResult<RestCard> payAlipay(@RequestBody RestCard restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		//CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
		ExecuteResult<RestCard> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getOrderNum()) || null == restObj.getAlipayType()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}


		EnyanRent queryObj = new EnyanRent();
		queryObj.setOrderNum(restObj.getOrderNum());
		queryObj.setUserEmail(restObj.getEmail());
		List<EnyanRent> queryList = enyanRentService.findRecords(queryObj);
		if (queryList.isEmpty() == true){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage("E1");
			return result;
		}
		EnyanRent rent = queryList.get(0);
		boolean isHK = (restObj.getAlipayType() == 1);
		boolean isMobile = false;
		AaronAlipay alipay = new AaronAlipay();
		alipay.initFrom(rent,restObj.getToRentMonths(),isHK,isMobile);

		restObj.setPayUrl(alipay.getPayUrl());
		log.debug("payUrl:{}",alipay.getPayUrl());
		//restObj.setPayUrl("https://ebookstore.endao.co/rent/payCode.png");
		result.setResult(restObj);
		return result;
	}

	@RequestMapping(value = "/payCredit",method = RequestMethod.POST)
	public ExecuteResult<RestCard> payCredit(@RequestBody RestCard restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		//CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
		ExecuteResult<RestCard> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getOrderNum())
					|| null == restObj.getToRentMonths()
				    || StringUtils.isBlank(restObj.getNumber()) || StringUtils.isBlank(restObj.getExp_month())
				    || StringUtils.isBlank(restObj.getExp_year())|| StringUtils.isBlank(restObj.getCvc())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		EnyanRent queryObj = new EnyanRent();
		queryObj.setOrderNum(restObj.getOrderNum());
		queryObj.setUserEmail(restObj.getEmail());
		List<EnyanRent> queryList = enyanRentService.findRecordsWithBlob(queryObj);
		if (queryList.isEmpty() == true){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage(ERROR_E1);
			return result;
		}
		EnyanRent rent = queryList.get(0);

		CreditInfo creditInfo = new CreditInfo();
		creditInfo.setPayCvc(restObj.getCvc());
		creditInfo.setPayExpireMonth(restObj.getExp_month());
		creditInfo.setPayExpireYear(restObj.getExp_year());
		creditInfo.setPayNumber(restObj.getNumber());

		boolean paySuccess = false;
		boolean isTestPay = false;

		OrderPayInfo payInfo = new OrderPayInfo();
		//TODO:后续写成正式的代码
		if (Constant.IS_TEST || Constant.IS_LOCAL){
			if ("test".equals(restObj.getRemark())){
				if (Constant.TEST_ACCOUNT.contains(restObj.getEmail()) || Constant.TEST_ACCOUNT_PAY.contains(restObj.getEmail())){
					paySuccess = true;
					isTestPay = true;
					payInfo.addCreditTestWithFee(BookUtil.getRentFee(rent.getRentPrice(),restObj.getToRentMonths()));
				}
			}
		}else{
			String token = StripeUtil.getToken(creditInfo, rent.getUserEmail());
			if (org.springframework.util.StringUtils.hasLength(token) == false){
				result.addErrorMessage(ERROR_PAY_FAIL);
				result.addErrorMessage(ERROR_E2);
				return result;
			}
			RequestOptions requestOptions = (new RequestOptions.RequestOptionsBuilder()).setApiKey(StripeConfig.SECRET_KEY).build();
			Map<String, Object> chargeMap = new HashMap<>();
			//付款金额，必填

			BigDecimal fee = BookUtil.getRentFee(rent.getRentPrice(),restObj.getToRentMonths()).multiply(new BigDecimal("100"));
			int total_fee = fee.intValue();

			//chargeMap.put("amount", 400);//100分
			chargeMap.put("amount", total_fee);//100分
			chargeMap.put("currency", "hkd");//cny usd
			chargeMap.put("source", token); // obtained via Stripe.js
			chargeMap.put("description",rent.getOrderNum());
			try {
				Charge charge = Charge.create(chargeMap, requestOptions);
				//Card card = (Card) charge.getSource();
				//System.out.println("country:"+card.getAccount()+card.getCustomer()+card.getCountry());
				//result.setResult("/shop/scr");
				//result.setSuccessMessage(this.getMessage("pay.success",request));

				payInfo.addStripe(charge,true);
				paySuccess = true;
			} catch (StripeException e) {
				e.printStackTrace();
				//result.addErrorMessage("支付失败");
				//return result;
			}
		}

		if (paySuccess){
			rent.getRentInfo().setCreditInfo(creditInfo);
			rent.getRentInfo().encryptCredit();
			new Thread(new Runnable(){
					@Override
					public void run() {
						shopController.payRentSuccess(rent, payInfo, restObj.getLang(), restObj.getToRentMonths());
					}
				}
			).start();
		}else {
			result.addErrorMessage(ERROR_PAY_FAIL);
		}

		result.setResult(restObj);
		return result;
	}

	@RequestMapping(value = "/download")
	public ResponseEntity<byte[]> downloadEpub(@RequestBody RestRent restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ResponseEntity<byte[]> entity = null;
		//System.out.println("downloadEpub");
		HttpHeaders headers = new HttpHeaders();

		headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
		headers.add("Accept-Ranges","bytes");

       /* if (0 < restBook.getBookId()){
            throw new DownloadException(Integer.valueOf(200), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID>0，book="+restBook);
        }*/

		if (restObj.getBookId() == null || StringUtils.isBlank(restObj.getEmail()) || 0 > restObj.getBookId() || null == restObj.getDataId()){
			throw new DownloadException(Integer.valueOf(601), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID或email为空，book="+ restObj);
		}
		/*
		String email = this.getRestEmail(request);
		if (StringUtils.isBlank(email) || !email.equals(restBook.getEmail())){//与header里传的email不同
			throw new DownloadException(Integer.valueOf(2011), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID或email为空，book="+restBook);
		}*/
		/*
		EnyanRent queryObj = new EnyanRent();
		queryObj.setOrderNum(restObj.getOrderNum());
		queryObj.setUserEmail(restObj.getEmail());
		List<EnyanRent> queryList = enyanRentService.findRecords(queryObj);
		if (queryList.isEmpty() == true){
			throw new DownloadException(Integer.valueOf(6011), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "租赁不存在，book="+ restObj);
		}*/
		EnyanRent rent = enyanRentService.queryRecordByPrimaryKey(restObj.getDataId()).getResult();
		if (null == rent || rent.getIsValid() != 1){
			throw new DownloadException(Integer.valueOf(6012), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "租赁已无效，book="+ restObj);
		}
		//书籍是否存在
		List<Long> bookIds = BookUtil.getBooksInRentType(rent.getRentType(),rent.getRentLang());
		if (bookIds.contains(restObj.getBookId().longValue()) == false){
			throw new DownloadException(Integer.valueOf(603), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍不存在，book="+ restObj);
		}
		//校验license
		RentInfo rentInfo = rent.getRentInfo();
		if (null == rentInfo || null == rentInfo.getLcpInfoMap() || StringUtils.isBlank(rent.getBaseLicense()) == true){
			throw new DownloadException(Integer.valueOf(604), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍license不存在，book="+ restObj);
		}
		Map<String, LcpInfo> lcpInfoMap = rentInfo.getLcpInfoMap();
		LcpInfo lcpInfo = lcpInfoMap.get(restObj.getBookId()+"");
		if (null == lcpInfo){
			throw new DownloadException(Integer.valueOf(605), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍license不存在，book="+ restObj);
		}
		//获取Lcp
		lcpInfo.setUserId(rentInfo.getUserId());
		//lcpInfo.setLicenseUuid(rentInfo.getBaseLicense()+"_"+restObj.getBookId());
		lcpInfo.setLicenseUuid(BookUtil.getRentLicenseByBaseLicense(rent.getBaseLicense(),restObj.getBookId()));
		//根据lcp返回文件流
		/*
		ExecuteResult<EnyanBook> bookExecuteResult = enyanBookService.queryRecordByPrimaryKey(restObj.getBookId());
		EnyanBook enyanBook = bookExecuteResult.getResult();
		if (enyanBook.getSalesModel() == 1){//预售中
			throw new DownloadException(Integer.valueOf(602), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍预售中，book="+ restObj);
		}

		List<EnyanBookBuy> list = enyanBookBuyService.findBookBuyListByEmailAndBookId(restObj.getEmail(), restObj.getBookId());
		if (null == list || list.isEmpty()){
			throw new DownloadException(Integer.valueOf(203), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍不存在，book="+ restObj);
		}
///TODO 需要 分段try catch，方便排查问题

		EnyanBookBuy enyanBookBuy = list.get(0);
		if (StringUtils.isBlank(enyanBookBuy.getDrminfo())){
			throw new DownloadException(Integer.valueOf(204), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "dInfo不存在，book="+ restObj);
		}

		DrmInfo drmInfo = JSONObject.parseObject(enyanBookBuy.getDrminfo(),DrmInfo.class);
		if (null == drmInfo.getLcpInfo() || StringUtils.isBlank(drmInfo.getLcpInfo().getLicenseUuid())){
			String license = enyanOrderService.saveLicensesByDrmInfo(enyanBookBuy, drmInfo);
			if (StringUtils.isBlank(license)){
				throw new DownloadException(Integer.valueOf(205), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"license 为空,book:"+ restObj);
			}
		}*/

		//根据 licenseID 下载书籍
		byte[] fileByte = enyanBookBuyService.downloadLcpFiles(lcpInfo);
		if (null == fileByte){
			throw new DownloadException(Integer.valueOf(606), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"LCP 文件为空,book:"+ restObj);
		}

		try {
			headers.setContentLength(fileByte.length);
			//headers.setLastModified(System.currentTimeMillis());
			//headers.setETag(enyanBook.getBookDrmRef());
			//String fileName = this.getFileName(restObj);
			String fileName = restObj.getBookId() + ".epub";
			headers.setContentDispositionFormData("attachment", fileName);//告知浏览器以下载方式打开
			entity = new ResponseEntity<>(fileByte, headers, HttpStatus.OK);
			return entity;
		}catch (Exception e){
			log.error(e.getMessage());
			e.printStackTrace();
			log.error("下载的问题：Connection reset by peer - download");

			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
			headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
			entity = new ResponseEntity<>("error004".getBytes(), headers, HttpStatus.INTERNAL_SERVER_ERROR);//HttpStatus.SERVICE_UNAVAILABLE
			throw new DownloadException(Integer.valueOf(607), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"book:"+ restObj +",msg="+e.getMessage());
		}finally {
			return entity;
		}
	}

	/**
	 * <p>生成购买订单</p>
	 * @param restList
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestOrder<com.aaron.spring.api.v4.model.RestBook>>
	 * @since : 2022/11/1
	 **/
	@GeoIPRequired
	@RequestMapping(value = "/buyOrderSubmit",method = RequestMethod.POST)
	public ExecuteResult<RestOrder<RestBook>> buyOrderSubmit(@RequestBody RestOrder<Long> restList, HttpServletRequest request){
		restList.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restList.getCurrency());

		ExecuteResult<RestOrder<RestBook>> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restList.getEmail()) || restList.getList().isEmpty() == true
				    || StringUtils.isBlank(restList.getOrderNum()) || null == restList.getOrderType()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		//orderType: 0:退订；1：部分购买；2：全部购买

		if ("0".equals(restList.getOrderType())){//直接退订
			return result;
		}

		EnyanRent queryObject = new EnyanRent();//根据orderNum获取Rent数据
		queryObject.setOrderNum(restList.getOrderNum());
		queryObject.setUserEmail(restList.getEmail());
		List<EnyanRent> queryList = enyanRentService.findRecords(queryObject);
		if (queryList.isEmpty() == true){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		queryObject = queryList.get(0);

		List<EnyanBook> list = enyanBookService.findBookByIds(BookUtil.getBooksInRentType(queryObject.getRentType(), queryObject.getRentLang()));
		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(queryObject.getUserEmail());

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}

		//List<RestBook> buyList = new ArrayList<>();
		List<EnyanBook> notBuyList = new ArrayList<>();
		for (EnyanBook enyanBook:list){
			if(booksHaveBuy.contains(enyanBook.getBookId().longValue())){//需要使用long
				//buyList.add(tmp);
			}else {//重制价格为原价
				enyanBook.resetByArea(restList.getArea());
				enyanBook.setPriceHKDDiscount(enyanBook.getPrice());
				enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_0);
				enyanBook.setDiscountId(null);
				enyanBook.setDiscountIsValid(Constant.BYTE_VALUE_0);
				notBuyList.add(enyanBook);
			}
		}
		if ("1".equals(restList.getOrderType())){//部分购买
			for (Long bookId: restList.getList()){
				if (notBuyList.contains(bookId.longValue()) == false){//有非套餐的书籍或已经购买的书籍
					result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
					result.addErrorMessage("E2");
					return result;
				}
			}
		}else{//全部购买 notBuyList

		}
		List<Long> newBookIdList = notBuyList.stream().map(data->data.getBookId()).collect(Collectors.toList());
		if (newBookIdList.isEmpty()){//没有可以购买的书籍
			result.addErrorMessage(ReturnInfo.ERROR_BOOK_BUY_NONE);
			return result;
		}
		boolean hasAddOrder;
		EnyanOrder order = new EnyanOrder();
		Date current = new Date();

		Collections.sort(newBookIdList, (o1, o2) -> o1.compareTo(o2));
		String orderBookHash = newBookIdList.hashCode()+"";

		EnyanOrder queryOrder = new EnyanOrder();
		queryOrder.setOrderBookHash(orderBookHash);
		queryOrder.setUserEmail(restList.getEmail());
		queryOrder.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);
		List<EnyanOrder> orderList = enyanOrderService.findRecordsByOrder(queryOrder);
		if (orderList == null || orderList.isEmpty()){//有时候会出现订单重复的问题，如果订单已经存在，则不需要保存
			hasAddOrder = false;
		}else {
			EnyanOrder orderDb = orderList.get(0);
            /*
            if (Constant.BYTE_VALUE_1.equals(orderDb.getIsPaid())
                        || Constant.BYTE_VALUE_1.equals(orderDb.getIsValid())){//有可能有人会重复刷订单，对已经购买的订单，后退刷新订单信息
                result.addErrorMessage(ReturnInfo.ERROR_ORDER_EXIST);
                return result;
            }*/
			if (Constant.BYTE_VALUE_1.equals(orderDb.getIsPaid())){//有可能有人会重复刷订单，对已经购买的订单，后退刷新订单信息
				result.addErrorMessage(ReturnInfo.ERROR_ORDER_EXIST);
				return result;
			}
			hasAddOrder = true;
			order.setOrderId(orderDb.getOrderId());
			order.setOrderNum(orderDb.getOrderNum());
		}

		List<EnyanBook> bookList = notBuyList;
		for (EnyanBook book:bookList){
			if (EBookConstant.BookType.EBOOK_SET == book.getBookType()
					    || !Constant.BYTE_VALUE_1.equals(book.getShelfStatus())){
				result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
				return result;
			}
		}
        /*
        List<EnyanBook> newBookList = new ArrayList<>();
        for (Long bookId:newBookIdList){
            EnyanBook book =  this.getBookById(bookId,bookList);
            if (null != book){
                newBookList.add(book);
            }
        }*/

		AuthUser authUser = authUserService.getUserByEmail(restList.getEmail()).getResult();
		if (null == authUser){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restList.getLang());
		for (EnyanBook book:bookList){

			ProductInfo productInfo = new ProductInfo(book);
			cartInfo.addProduct(productInfo, 1);
		}

		OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);
		OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
		try {
			if (hasAddOrder == false){//已经添加的订单，不需要重新生成订单号
				order.setOrderNum(OrderUtil.getOrderIdFromRentToBuy());
			}
		}catch (Exception e){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		order.setOrderBookHash(orderBookHash);

		order.setOrderTitleInfo(titleInfo);
		order.setOrderDetailInfo(detailInfo);

		order.setIsValid(Constant.BYTE_VALUE_1);
		order.setIsPaid(Constant.BYTE_VALUE_0);
		order.setIsCounted(Constant.BYTE_VALUE_0);

		order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);//只购买普通的单本书

		order.setUserId(authUser.getUserId());
//        order.setUserName(authUser.getEmail());
		order.setUserEmail(authUser.getEmail());
		order.setPurchasedAt(current);
		order.setExpiredAt(DateUtils.addHours(current,2));

		order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
		order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountHkd())));

		order.setOrderFrom(EBookConstant.OrderFrom.APP);
        /*
        EnyanOrder queryOrder = new EnyanOrder();
        queryOrder.setOrderNum(order.getOrderNum());
        queryOrder.setUserEmail(order.getUserEmail());
        List<EnyanOrder> orderList = enyanOrderService.selectByExample(queryOrder);
        if (orderList == null || orderList.isEmpty()){//有时候会出现订单重复的问题，如果订单已经存在，则不需要保存
            ExecuteResult<EnyanOrder> resultOrder = enyanOrderService.addRecord(order);
            if (!resultOrder.isSuccess()){
                log.error(result.getErrorMessageString());
            }
        }else {
            EnyanOrder orderDb = orderList.get(0);
            if (Constant.BYTE_VALUE_1.equals(orderDb.getIsPaid())){//有可能有人会重复刷订单，对已经购买的订单，后退刷新订单信息
                result.addErrorMessage(ReturnInfo.ERROR_ORDER_INVALID);
                return result;
            }
        }*/
		EnyanCoupon enyanCoupon = new EnyanCoupon();
		enyanCoupon.initWithRent(queryObject);
		if (order.getOrderTotal().doubleValue() > 0){
			if (StringUtils.isNotBlank(enyanCoupon.getCouponCode())){
				if (enyanCoupon.getCouponValue() != order.getOrderDetailInfo().getAmountCoupon().intValue()){
					order.getOrderDetailInfo().setCouponCode(enyanCoupon.getCouponCode().toUpperCase());//设置优惠码，用于记录一个人只能使用一次
					order.getOrderDetailInfo().setAmountCoupon(new BigDecimal(enyanCoupon.getCouponValue()+""));//设置优惠金额
					order.getOrderDetailInfo().resetProductListByCoupon(enyanCoupon);
					/*
					if (order.getOrderDetailInfo().getTotalFeeBeforeCoupon().compareTo(new BigDecimal(enyanCoupon.getMinLimitValue())) == -1){//小于最小满减限额
						result.addErrorMessage(ReturnInfo.ERROR_COUPON_LIMIT);
						return result;
					}*/
					order.setOrderTotal(new BigDecimal(order.getOrderDetailInfo().getAmountHkd()+""));
				}
			}
		}else{//免费书订单
			OrderPayInfo orderPayInfo = new OrderPayInfo();
			orderPayInfo.addFree();
			order.setOrderPayInfo(orderPayInfo);
			order.setIsPaid(Constant.BYTE_VALUE_1);
		}
		if (hasAddOrder) {
			enyanOrderService.updateRecord(order);
		}else {
			enyanOrderService.addRecord(order);
		}

		if (order.getOrderTotal().doubleValue() > 0){

		}else {//免费书直接分拆订单(没有免费书)
			OrderPayInfo orderPayInfo = new OrderPayInfo();
			orderPayInfo.addFree();
			order.setOrderPayInfo(orderPayInfo);
			order.setIsPaid(Constant.BYTE_VALUE_1);
			new Thread(new Runnable(){
				@Override
				public void run() {
					shopController.paySuccess(order, order.getOrderPayInfo(), request);
				}
			}
			).start();
		}

//        if (null != restList.getFromCart() && restList.getFromCart() == 1){//从购物车来的订单，直接清除购物车的数据
//
//        }
		//enyanCartService.deleteCarts(restList.getEmail(), restList.getList());
		//CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restList.getCurrency());
		RestOrder restOrder = new RestOrder();
		restOrder.initFrom(order,currencyType);
		result.setResult(restOrder);
		return result;
	}

	@RequestMapping(value = "/alipayW",method = RequestMethod.POST)
	public ExecuteResult<RestRent> alipayWeb(@RequestBody RestRent restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		ExecuteResult<RestRent> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getOrderNum())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		boolean isMobile = false;
		EnyanRent queryObject = new EnyanRent();//根据orderNum获取Rent数据
		queryObject.setRentType(2);
		queryObject.setRentLang(1);
		queryObject.setUserEmail(restObj.getEmail());

		if (queryObject.getUserEmail().equals(restObj.getEmail()) == false){//需要校验email数据
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage("E1");
			return result;
		}
		EnyanOrder order = new EnyanOrder();
		OrderDetailInfo orderDetailInfo = new OrderDetailInfo();

		order.setOrderDetailInfo(orderDetailInfo);

		CartDiscountInfo cartDiscountInfo = new CartDiscountInfo();
		ProductInfo productInfo = new ProductInfo(queryObject);
		cartDiscountInfo.addProduct(productInfo, 1);

		orderDetailInfo.getCartDiscountInfoList().add(cartDiscountInfo);

		String out_trade_no = order.getOrderNum();

		//订单名称，必填
		String subject = out_trade_no;//"恩道電子書 -  # "+out_trade_no;

		//付款金额，必填
		String total_fee = String.valueOf(order.getOrderTotal());

		//商品描述，可空 2019.2.14 添加json body
		//String body = this.getAlipayGoodsBody(order);

		//商品描述，可空 2019.5.17 添加json body
		String tradeInformation = ShopController.getAlipayGoodsTradeInformation(order);

		//币种，不可空
		//String currency = "USD";

		//把请求参数打包成数组
		Map<String, String> sParaTemp = new HashMap<>();
		sParaTemp.put("service", AlipayConfig.service);
		//注意：必传，PC端是NEW_OVERSEAS_SELLER，移动端是NEW_WAP_OVERSEAS_SELLER
		//		Remarks:Mandatory.For PC: NEW_OVERSEAS_SELLER ;FOR WAP and APP: NEW_WAP_OVERSEAS_SELLER
		sParaTemp.put("product_code", AlipayConfig.product_code);//PC与WAP一起使用
		if (isMobile == true){
			sParaTemp.put("service", AlipayConfig.service_mobile);
			//sParaTemp.put("product_code", "NEW_WAP_OVERSEAS_SELLER"); //WAP
		}
		sParaTemp.put("partner", AlipayConfig.partner);
		sParaTemp.put("_input_charset", AlipayConfig.input_charset);
		sParaTemp.put("notify_url", AlipayConfig.notify_url);
		sParaTemp.put("return_url", AlipayConfig.return_url);
		sParaTemp.put("refer_url", AlipayConfig.refer_url);
		sParaTemp.put("out_trade_no", out_trade_no);
		sParaTemp.put("subject", subject);
		//sParaTemp.put("total_fee", total_fee);
		//sParaTemp.put("rmb_fee", total_fee);

        /*
        if (order.getOrderCurrency().equals(Constant.BYTE_VALUE_1){//美元
            sParaTemp.put("total_fee", total_fee);
        }else {
            //sParaTemp.put("rmb_fee", total_fee);
            //sParaTemp.put("rmb_fee", "0.1");//RMB
            sParaTemp.put("total_fee", "0.1");
        }*/
		sParaTemp.put("total_fee", total_fee);
		//sParaTemp.put("total_fee", "0.1");//HKD

		//sParaTemp.put("body", body);
		sParaTemp.put("trade_information", tradeInformation);
		sParaTemp.put("currency", AlipayConfig.currency);
		//其他业务参数根据在线开发文档，添加参数
		//如sParaTemp.put("参数名","参数值");

		//https://global.alipay.com/docs/ac/hkapi/create_forex_trade_wap
		//https://global.alipay.com/docs/ac/website_hk/ux
		if ("HK".equals(restObj.getPayType())){
			sParaTemp.put("payment_inst", AlipayConfig.payment_inst_HK);//ALIPAYHK ALIPAYCN
		}else {
			//sParaTemp.put("payment_inst", AlipayConfig.payment_inst_CN);//ALIPAYHK ALIPAYCN
		}

		sParaTemp.put("qr_pay_mode", "4");
		sParaTemp.put("qrcode_width", "200");
		//sParaTemp.put("split_fund_info", "[{\"transIn\":\"2088441611946675\",\"amount\":\""+total_fee+"\",\"currency\":\"HKD\",\"desc\":\"电子书\"}]");
		//sParaTemp.put("app_pay","Y");//启用此参数可唤起钱包APP支付。

		//建立请求
		/*
		if (isMobile == true){
			String sHtmlText = AlipaySubmit.buildRequest(sParaTemp,"get","确认");
			modelMap.addAttribute("result",sHtmlText);
			return "/showResult";
		}*/

		String toUrl = AlipaySubmit.buildRequestMethod(sParaTemp);

		restObj.setRemark(toUrl);
		//restCart.setBuyList(buyList);
		//restCart.setNotBuyList(notBuyList);
		result.setResult(restObj);
		return result;
	}

	@RequestMapping(value = "/testPay",method = RequestMethod.POST)
	public ExecuteResult<RestRent> testPay(@RequestBody RestRent restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<RestRent> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getOrderNum()) || null == restObj.getToRentMonths()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		if (Constant.IS_PRODUCT){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		if (!Constant.TEST_ACCOUNT.contains(restObj.getEmail()) && !Constant.TEST_ACCOUNT_PAY.contains(restObj.getEmail())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		boolean ret = this.testDoPay(restObj, request);
		if (ret == false){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		return result;
	}

	@RequestMapping(value = "/testInit",method = RequestMethod.POST)
	public ExecuteResult<RestRent> testInit(@RequestBody RestRent restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<RestRent> result = new ExecuteResult<>();
		//update enyan_rent set order_num = concat(order_num,rent_id);

		if (Constant.IS_PRODUCT){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		if (!Constant.TEST_ACCOUNT.contains(restObj.getEmail()) && !Constant.TEST_ACCOUNT_PAY.contains(restObj.getEmail())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		enyanRentService.resetDateToNull();
		List<EnyanRent> list = enyanRentService.findRecords(new EnyanRent());
		Date current = new Date();
		Date beginAt = null;
		Date expiredAt = null;
		Date leaveAt = null;
		int maxSize = list.size() > 40 ? 40 : list.size();
		for (int i = 0; i < maxSize; i++) {
			EnyanRent rent = list.get(i);
			EnyanRent input = new EnyanRent();
			input.setRentId(rent.getRentId());
			input.setRentType(1);
			input.setRentLang(1);
			input.setIsValid(EBookConstant.RentValid.ON);
			input.setRentStatus(EBookConstant.RentStatus.ON);
			input.setIsAuto(0);
			input.setTotalMonths(3);
			input.setLeaveBuy(0);

			switch (i){
				case 0://测试订单1："月度订阅 正常订阅中" 订阅时间：T1（至多可订阅36个月，期满后系统将自动退订）  到期时间：T2
					beginAt = DateUtils.addDays(current, -10);
					expiredAt = DateUtils.addMonths(beginAt, 1);
					leaveAt = null;
					break;
				case 1://测试订单2："月度订阅 到期未续订，宽限期内" "订阅时间：T1（至多可订阅36个月，期满后系统将自动退订） 到期时间：T2 订阅已到期，您在北京时间 T4  24时前仍可续订，否则将自动退订"
					beginAt = DateUtils.addMonths(current, -1);
					beginAt = DateUtils.addDays(beginAt, -2);
					expiredAt = DateUtils.addMonths(beginAt, 1);
					leaveAt = expiredAt;//没有宽限期概念了，所以直接退订

					input.setIsValid(EBookConstant.RentValid.Default);
					input.setRentStatus(EBookConstant.RentStatus.expired);
					break;
				case 2://测试订单5："月度订阅 过了宽限期，可优惠购书"
					beginAt = DateUtils.addMonths(current, -1);
					beginAt = DateUtils.addDays(beginAt, -5);
					expiredAt = DateUtils.addMonths(beginAt, 1);
					leaveAt = expiredAt;//DateUtils.addDays(expiredAt, 4);
					input.setIsValid(EBookConstant.RentValid.Default);
					input.setRentStatus(EBookConstant.RentStatus.expired);
					break;
				case 3://测试订单3："连续包月 正常订阅中"
					beginAt = DateUtils.addDays(current, -5);
					expiredAt = DateUtils.addMonths(beginAt, 1);//测试数据有问题，这个地方应该有到期时间
					leaveAt = null;
					input.setIsAuto(1);
					break;
				case 4://测试订单4："连续包月 手动退订，可优惠购书 订阅未到期"
					beginAt = DateUtils.addDays(current, -20);
					expiredAt = DateUtils.addMonths(beginAt, 1);
					leaveAt = DateUtils.addDays(current, -8);
					input.setIsAuto(1);
					input.setRentStatus(EBookConstant.RentStatus.leave);
					break;
				case 5://测试订单6："连续包月 手动退订，可优惠购书 订阅已到期"
					beginAt = DateUtils.addDays(current, -40);
					expiredAt = DateUtils.addMonths(beginAt, 1);
					leaveAt = DateUtils.addDays(current, -15);
					input.setIsAuto(1);
					input.setIsValid(EBookConstant.RentValid.Default);
					input.setRentStatus(EBookConstant.RentStatus.leave);
					break;
				case 6://测试订单7："月度订阅 手动退订，优惠购书已过期 订阅未到期"
					beginAt = DateUtils.addDays(current, -50);
					expiredAt = DateUtils.addMonths(beginAt, 2);
					leaveAt = DateUtils.addDays(current, -35);
					input.setIsAuto(0);
					input.setTotalMonths(2);
					input.setRentStatus(EBookConstant.RentStatus.leave);
					break;
				case 7://测试订单8："月度订阅 手动退订，优惠购书已使用 订阅未到期"
					beginAt = DateUtils.addDays(current, -25);
					expiredAt = DateUtils.addMonths(beginAt, 1);
					leaveAt = DateUtils.addDays(beginAt, 5);
					input.setIsAuto(0);
					input.setTotalMonths(2);
					input.setLeaveBuy(1);
					input.setLeaveBuyAt(current);
					input.setRentStatus(EBookConstant.RentStatus.leave);
					break;
				case 8://测试订单9："连续包月 付费到第36个月，正常订阅中"
					beginAt = DateUtils.addMonths(current, -36);
					beginAt = DateUtils.addDays(beginAt, 10);
					expiredAt = DateUtils.addMonths(beginAt, 36);
					leaveAt = null;
					input.setIsAuto(1);
					input.setTotalMonths(36);
					//input.setRentStatus(EBookConstant.RentStatus.leave);
					break;
				case 9://测试订单10："月度订阅 实际订阅满36个月，系统已自动退订"
					beginAt = DateUtils.addMonths(current, -36);
					beginAt = DateUtils.addDays(beginAt, -10);
					expiredAt = DateUtils.addMonths(beginAt, 36);
					leaveAt = DateUtils.addDays(expiredAt, 1);;
					input.setIsAuto(0);
					input.setTotalMonths(36);
					input.setIsValid(EBookConstant.RentValid.Default);
					input.setRentStatus(EBookConstant.RentStatus.expired);
					input.setIsValid(EBookConstant.RentValid.Default);
					break;
				case 10://测试订单11："月度订阅 手动退订，优惠购书已过期 订阅已到期"
					beginAt = DateUtils.addDays(current, -50);
					expiredAt = DateUtils.addMonths(beginAt, 1);
					leaveAt = DateUtils.addDays(current, -35);;
					input.setIsAuto(0);
					input.setTotalMonths(1);
					input.setIsValid(EBookConstant.RentValid.Default);
					input.setRentStatus(EBookConstant.RentStatus.leave);
					break;
				case 20://0101：手动付款用户将在到期前5日提醒付款
					expiredAt = DateUtils.addDays(current, 5);
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.ON, EBookConstant.RentStatus.ON,0,3,beginAt,expiredAt,leaveAt);
					break;
				case 21://0102：连续包月订阅用户将在自动扣费前5天收到邮件提醒
					expiredAt = DateUtils.addDays(current, 5);
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.ON, EBookConstant.RentStatus.ON,1,3,beginAt,expiredAt,leaveAt);
					break;
				case 22://0103：如果已经订阅36个月，到期前5天将收到0103
					expiredAt = DateUtils.addDays(current, 5);
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.ON, EBookConstant.RentStatus.ON,1,36,beginAt,expiredAt,leaveAt);
					break;
				case 23://0201：到期后1日，提醒仍有3日付款宽限期
					expiredAt = DateUtils.addDays(current, -1);
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.ON, EBookConstant.RentStatus.ON,1,3,beginAt,expiredAt,null);
					break;
				case 24://0202：结束前一天扣款，自动扣款日扣款失败时提醒可以手动续订
					expiredAt = DateUtils.addDays(current, 1);
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.ON, EBookConstant.RentStatus.ON,1,3,beginAt,expiredAt,null);
					break;
				case 25://0301【先租后买已自动退订，请尽快使用购书优惠】-月度订阅。 手动订阅
					leaveAt = DateUtils.addDays(current, -1);//昨天
					expiredAt = DateUtils.addDays(leaveAt, -3);//3天宽限期
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.Default, EBookConstant.RentStatus.leave,0,3,beginAt,expiredAt,leaveAt);
					break;
				case 26://0302【先租后买已自动退订，请尽快使用购书优惠】-连续包月自动扣费失败。 自动订阅失败
					leaveAt = DateUtils.addDays(current, -1);//昨天
					expiredAt = DateUtils.addDays(leaveAt, -3);//3天宽限期
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.Default, EBookConstant.RentStatus.leave,1,3,beginAt,expiredAt,leaveAt);
					break;
				case 27://06：订满36个月，系统自动退订成功时
					expiredAt = DateUtils.addDays(current, -1);
					leaveAt = expiredAt;//订满的时间,结束时间就是退订时间
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.Default, EBookConstant.RentStatus.expired,1,36,beginAt,expiredAt,leaveAt);
					break;
				case 28://07：手动/自动退订后，购书优惠到期前7天收到07
					expiredAt = DateUtils.addDays(current, 6);//提前7-1天
					expiredAt = DateUtils.addMonths(expiredAt, -1);//宽限1个月
					leaveAt = expiredAt;
					//this.initTestRent(rent.getRentId(), 1, 1, EBookConstant.RentValid.Default, EBookConstant.RentStatus.expired,1,36,beginAt,expiredAt,leaveAt);
					break;
				case 29://1 进入宽限期
					beginAt = DateUtils.addYears(current, -1);
					expiredAt = current;
					leaveAt = null;

					input.setRentStatus(EBookConstant.RentStatus.ON);
					input.setRentType(EBookConstant.RentTypeCode.CBCS_NT);
					input.setTotalFee(new BigDecimal("360"));
					input.setIsAuto(0);
					input.setIsValid(1);

					break;
				case 30://2 宽限期结束，自动退订
					beginAt = DateUtils.addYears(current, -1);
					beginAt = DateUtils.addDays(beginAt, -3);
					expiredAt = DateUtils.addDays(current, -3);//
					leaveAt = DateUtils.addDays(current, -2);

					input.setRentStatus(EBookConstant.RentStatus.expired);
					input.setRentType(EBookConstant.RentTypeCode.CBCS_OT);
					input.setTotalFee(new BigDecimal("480"));
					input.setIsAuto(0);
					input.setIsValid(0);
					break;
				case 31://3 优惠购书未使用且到期失效，套装到期
					beginAt = DateUtils.addMonths(current, -25);
					beginAt = DateUtils.addDays(beginAt, -1);
					expiredAt = DateUtils.addMonths(beginAt, 24);//
					leaveAt = DateUtils.addMonths(current, -1);

					input.setRentStatus(EBookConstant.RentStatus.expired);
					input.setRentType(EBookConstant.RentTypeCode.CBCS_All);
					input.setTotalFee(new BigDecimal("1560"));
					input.setLeaveBuy(0);
					input.setIsAuto(0);
					input.setIsValid(0);
					break;
				case 32://4 优惠购书未使用且到期失效，套装未到期
					beginAt = DateUtils.addMonths(current, -11);
					expiredAt = DateUtils.addMonths(beginAt, 15);//
					leaveAt = DateUtils.addMonths(current, -1);

					input.setRentStatus(EBookConstant.RentStatus.leave);
					input.setRentType(EBookConstant.RentTypeCode.CBCS_NT);
					input.setTotalFee(new BigDecimal("450"));
					input.setLeaveBuy(0);
					input.setIsAuto(0);
					input.setIsValid(1);
					break;
				case 33://5 自动续费后
					beginAt = DateUtils.addMonths(current, -11);
					beginAt = DateUtils.addDays(beginAt, 1);
					expiredAt = DateUtils.addMonths(beginAt, 11);//
					//leaveAt = DateUtils.addMonths(current, -1);

					input.setRentStatus(EBookConstant.RentStatus.ON);
					input.setRentType(EBookConstant.RentTypeCode.CBCS_OT);
					input.setTotalFee(new BigDecimal("440"));
					input.setLeaveBuy(0);
					input.setIsAuto(1);
					input.setIsValid(1);
					break;
				case 34://6 自动续费，进入第36个月
					beginAt = DateUtils.addMonths(current, -35);
					beginAt = DateUtils.addDays(beginAt, 1);
					expiredAt = DateUtils.addMonths(beginAt, 35);//
					//leaveAt = DateUtils.addMonths(current, -1);

					input.setRentStatus(EBookConstant.RentStatus.ON);
					input.setRentType(EBookConstant.RentTypeCode.CBCS_All);
					input.setTotalFee(new BigDecimal("2275"));
					input.setLeaveBuy(0);
					input.setIsAuto(1);
					input.setIsValid(1);
					break;
				case 35://7 第36个月结束后，自动退订
					beginAt = DateUtils.addMonths(current, -36);
					expiredAt = current;//
					//leaveAt = DateUtils.addMonths(current, -1);

					input.setRentStatus(EBookConstant.RentStatus.ON);
					input.setRentType(EBookConstant.RentTypeCode.CBCS_NT);
					input.setTotalFee(new BigDecimal("1080"));
					input.setTotalMonths(36);
					input.setLeaveBuy(0);
					input.setIsAuto(0);
					input.setIsValid(1);
					break;
			}

			input.setBeginAt(beginAt);
			input.setExpiredAt(expiredAt);
			input.setLeaveAt(leaveAt);
			this.initTestRent(input);
		}
		return result;
	}

	private void initTestRent(EnyanRent inputRent){
		EnyanRent update = new EnyanRent();
		update.setRentId(inputRent.getRentId());
		update.setRentType(inputRent.getRentType());
		update.setRentLang(inputRent.getRentLang());
		update.setTotalMonths(inputRent.getTotalMonths());
		update.setTotalFee(inputRent.getTotalFee());
		update.setIsAuto(inputRent.getIsAuto());
		update.setIsValid(inputRent.getIsValid());
		update.setRentStatus(inputRent.getRentStatus());
		if (inputRent.getIsValid() != 0){//如果不是默认状态，就是已经支付过
			update.setIsPaid(1);
		}
		if (null == inputRent.getBeginAt()){
			if (null != inputRent.getExpiredAt() && null != inputRent.getTotalMonths()){
				update.setBeginAt(DateUtils.addMonths(inputRent.getExpiredAt(), -inputRent.getTotalMonths()));
			}
		}else {
			update.setBeginAt(inputRent.getBeginAt());
		}
		update.setExpiredAt(inputRent.getExpiredAt());
		update.setLeaveAt(inputRent.getLeaveAt());
		update.setIsDeleted(0);
		update.setLeaveBuy(inputRent.getLeaveBuy());
		enyanRentService.updateRecord(update);
	}

	/**
	 * <p>初始化信息</p>
	 * @param rentId
	 * @param rentType
	 * @param rentLang
	 * @param expiredAt
	 * @return void
	 * @since : 2022/11/18
	 **/
	private void initTestRent(long rentId, int rentType, int rentLang, int isValid,int rentStatus, int isAuto, int totalMonths,Date beginAt,Date expiredAt, Date leaveAt){
		EnyanRent update = new EnyanRent();
		update.setRentId(rentId);
		update.setRentType(rentType);
		update.setRentLang(rentLang);
		update.setTotalMonths(totalMonths);
		update.setIsAuto(isAuto);
		update.setIsValid(isValid);
		update.setRentStatus(rentStatus);
		if (isValid != 0){//如果不是默认状态，就是已经支付过
			update.setIsPaid(1);
		}
		if (null == beginAt){
			update.setBeginAt(DateUtils.addMonths(expiredAt, -totalMonths));
		}else {
			update.setBeginAt(beginAt);
		}
		update.setExpiredAt(expiredAt);
		update.setLeaveAt(leaveAt);
		enyanRentService.updateRecord(update);
	}

	@RequestMapping(value = "/testMail",method = RequestMethod.POST)
	public ExecuteResult<RestRent> testMail(@RequestBody RestRent restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<RestRent> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getOrderNum())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		if (Constant.IS_PRODUCT){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		if (!Constant.TEST_ACCOUNT.contains(restObj.getEmail()) && !Constant.TEST_ACCOUNT_PAY.contains(restObj.getEmail())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		EnyanRent queryObj = new EnyanRent();
		queryObj.setOrderNum(restObj.getOrderNum());
		List<EnyanRent> queryList = enyanRentService.findRecords(queryObj);
		if (queryList.isEmpty() == true){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			result.addErrorMessage(ReturnInfo.ERROR_E1);
			return result;
		}
		queryObj = queryList.get(0);
		//rentDetail 暂时用于传递mailCode
		sendEmailService.sendMailOfRent(queryObj,restObj.getMailCode());

		return result;
	}

	public boolean testDoPay(RestRent restObj, HttpServletRequest request){
		EnyanRent query = new EnyanRent();
		query.setOrderNum(restObj.getOrderNum());
		List<EnyanRent>  list = enyanRentService.findRecords(query);
		if (list.isEmpty() == true){
			return false;
		}
		EnyanRent rent = list.get(0);
		OrderPayInfo payInfo = new OrderPayInfo();
		BigDecimal price = BookUtil.getRentPrice(rent.getRentType(),rent.getIsAuto());
		payInfo.addAlipayTestWithFee(BookUtil.getRentFee(price,restObj.getToRentMonths()));

		shopController.payRentSuccess(rent, payInfo, restObj.getLang(), restObj.getToRentMonths());
		return true;
	}

}
