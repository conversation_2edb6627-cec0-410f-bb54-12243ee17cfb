package com.aaron.spring.api.v4.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/7/19
 * @Modified By:
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestRedeemCodeNoteInfo implements Serializable {
   private static final long serialVersionUID = 2512930054619746302L;
   private String emailToGift;//受赠对象email
   private String emailToRedeem;//兑换人email
   private String dateToRedeem;//兑换日期

   public String getEmailToGift() {
      return emailToGift;
   }

   public void setEmailToGift(String emailToGift) {
      this.emailToGift = emailToGift;
   }

   public String getEmailToRedeem() {
      return emailToRedeem;
   }

   public void setEmailToRedeem(String emailToRedeem) {
      this.emailToRedeem = emailToRedeem;
   }

   public String getDateToRedeem() {
      return dateToRedeem;
   }

   public void setDateToRedeem(String dateToRedeem) {
      this.dateToRedeem = dateToRedeem;
   }
}
