package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestBlog;
import com.aaron.spring.model.EnyanBlog;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.EnyanBlogService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Author: Aaron <PERSON>
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
@Slf4j
@RestController("RestBlogControllerV4")
@RequestMapping("/api/v4/blog")
public class RestBlogController extends RestBaseController {

	@Resource
	private EnyanBlogService enyanBlogService;

	@LoginAnonymous
	@RequestMapping(value = "/page",method = RequestMethod.POST)
	public PageResult<RestBlog> page(@RequestBody RestBlog restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		PageResult<RestBlog> pageResult = new PageResult<>();
		if (null == restObj.getPage()){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}

		EnyanBlog searchObj = new EnyanBlog();
		Page<EnyanBlog> page = new Page();
		page.setCurrentPage(restObj.getPage());
		page.setPageSize(pageResult.getPageSize());
		searchObj.setPage(page);
		searchObj.setIsDeleted(0);

		if (null != restObj.getPublisherId()){//根据出版社ID获取
			searchObj.setPublisherId(restObj.getPublisherId());
		}

		if (null != restObj.getOrderBy()){
			if (1 == restObj.getOrderBy()){//0:默认；1:最新；2：热门; 3: 点赞
				OrderObj orderObj = new OrderObj("create_at", InterfaceContant.OrderBy.DESC);
				searchObj.addOrder(orderObj);
			}else if (2 == restObj.getOrderBy()){
				OrderObj orderObj = new OrderObj("read_count",InterfaceContant.OrderBy.DESC);
				searchObj.addOrder(orderObj);
			}else if (3 == restObj.getOrderBy()){
				OrderObj orderObj = new OrderObj("like_count",InterfaceContant.OrderBy.DESC);
				searchObj.addOrder(orderObj);
			}else {//默认，按照优先级次序
				OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
				searchObj.addOrder(orderObj);
			}
		}else{
			OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
			searchObj.addOrder(orderObj);
		}

//        OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
//        enyanBook.addOrder(orderObj);
		page = enyanBlogService.queryRecords(searchObj.getPage(),searchObj);

		for (EnyanBlog obj : page.getRecords()){
			RestBlog tmp = new RestBlog();
			tmp.initFrom(obj);
			pageResult.getResult().add(tmp);
		}
		pageResult.setCurrentPage(page.getCurrentPage());
		pageResult.setTotalRecord(page.getTotalRecord());
		return pageResult;
	}

	@LoginAnonymous
	@RequestMapping(value = "/blogInfo",method = RequestMethod.POST)
	public ExecuteResult<RestBlog> blogInfo(@RequestBody RestBlog restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		ExecuteResult<RestBlog> result = new ExecuteResult<>();
		if (null == restObj.getBlogId()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		EnyanBlog blog = enyanBlogService.queryRecordByPrimaryKey(restObj.getBlogId()).getResult();
		if (null == blog){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		RestBlog tmp = new RestBlog();
		tmp.initFrom(blog);
		result.setResult(tmp);
		return result;
	}

	@LoginAnonymous
	@RequestMapping(value = "/like",method = RequestMethod.POST)
	public ExecuteResult<RestBlog> like(@RequestBody RestBlog restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		ExecuteResult<RestBlog> result = new ExecuteResult<>();
		if (null == restObj.getBlogId()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		enyanBlogService.updateBlogLikeCountById(restObj.getBlogId());
		return result;
	}
}
