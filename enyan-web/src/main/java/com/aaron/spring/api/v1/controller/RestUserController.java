package com.aaron.spring.api.v1.controller;

import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v1.model.RestUser;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.controller.HomeController;
import com.aaron.spring.entity.Mail;
import com.aaron.spring.model.AuthUser;
import com.aaron.spring.model.EmailHistory;
import com.aaron.spring.model.EnyanOrderDetail;
import com.aaron.spring.service.*;
import com.aaron.util.EmailUtils;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2018/8/24
 * @Modified By:
 */
@RestController("RestUserControllerV1")
@RequestMapping("/api/v1/user")
public class RestUserController extends RestBaseController {
    private final Log logger = LogFactory.getLog(RestUserController.class);

    @Resource
    private AuthUserService authUserService;

    @Resource
    private EmailHistoryService emailHistoryService;

    @Resource
    private EmailService emailService;

    @Resource
    private EnyanPublisherService enyanPublisherService;

    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;


    @RequestMapping(value = "/msg",method = RequestMethod.GET)
    public ExecuteResult<RestUser> msg(){
        RestUser authUser = new RestUser();
        authUser.setName("name");
        authUser.setToken("token");
        authUser.setUserId(11L);

        ExecuteResult<RestUser> result = new ExecuteResult<>();
        result.setResult(authUser);
        //result.addErrorMessage("error....");
        //logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }



    @RequestMapping(value = "/login",method = RequestMethod.POST)
    public ExecuteResult<RestUser> login(@RequestBody RestUser restUser){
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail())||StringUtils.isBlank(restUser.getPasswd())){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }

        ExecuteResult<AuthUser> authUserExecuteResult = authUserService.getUserByEmail(restUser.getEmail());
        AuthUser authUser = authUserExecuteResult.getResult();
        if (authUser == null){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }

        if (!authUserService.isPasswordValid(authUser.getUserPassword(),restUser.getPasswd(),authUser.getSalt())){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }
        if (!authUser.getIsActive().equals(Constant.BYTE_VALUE_1)){
            result.addErrorMessage(ReturnInfo.ERROR_ACCOUNT_NOT_ACTIVE);
            return result;
        }

        RestUser user = new RestUser();
        user.setEmail(authUser.getEmail());
        user.setName(authUser.getUserName());
        user.setToken("token");
        user.setUserId(authUser.getUserId());
        user.setAccess_token("accessToken");
        user.setRefresh_token("refresh_token");
        user.setExpires_in(86400); //24h

        result.setSuccessMessage("successMsg");

        result.setResult(user);
        //result.addErrorMessage("error....");
        //logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }

    @RequestMapping(value = "/myInfo",method = RequestMethod.POST)
    public ExecuteResult<RestUser> myInfo(@RequestBody RestUser restUser){
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }

        ExecuteResult<AuthUser> authUserExecuteResult = authUserService.getUserByEmail(restUser.getEmail());
        AuthUser authUser = authUserExecuteResult.getResult();
        RestUser user = new RestUser();
        if (null == authUser){
            user.setEmail("");
            user.setName("");
            user.setMyBookNum(0L);
            user.setMyMsgNum(0L);
            user.setMyNoteNum(0L);
            return result;
        }

        EnyanOrderDetail enyanOrderDetail = new EnyanOrderDetail();
        enyanOrderDetail.setUserEmail(restUser.getEmail());
        long bookCount = enyanOrderDetailService.countOrderDetailByOrder(enyanOrderDetail);


        user.setEmail(authUser.getEmail());
        user.setName(authUser.getUserName());
        user.setMyBookNum(bookCount);
        user.setMyMsgNum(0L);
        user.setMyNoteNum(0L);

        result.setSuccessMessage("successMsg");

        result.setResult(user);
        //result.addErrorMessage("error....");
        //logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }

    @RequestMapping(value = "/passwdForget",method = RequestMethod.POST)
    public ExecuteResult<RestUser> passwdForget(@RequestBody RestUser restUser, HttpServletRequest request){
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }
        ExecuteResult<AuthUser> authUserExecuteResult = authUserService.getUserByEmail(restUser.getEmail());
        AuthUser authUser = authUserExecuteResult.getResult();
        if (authUser == null){
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }
        boolean success = false;
        try {
            int yearMonthDay = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            EmailHistory history = emailHistoryService.getEmailHistoryByName(restUser.getEmail(), EBookConstant.EmailType.PASSWORD_FORGET,yearMonthDay).getResult();
            if (null == history) {
                history = new EmailHistory();
                history.setEmail(restUser.getEmail());
                history.setEmailType(EBookConstant.EmailType.PASSWORD_FORGET);
                history.setSendAt(System.currentTimeMillis());
                history.setRepeatTimes(0);
                history.setYearMonthDay(yearMonthDay);
                emailHistoryService.addRecord(history);
                success = true;
            } else {
                long between = System.currentTimeMillis() - history.getSendAt();
                if (between < HomeController.EMAIL_TOO_MUCH_TIME_BETWEEN || history.getRepeatTimes() >= 10) {//小于10min 或者 多余10次

                    result.addErrorMessage(ReturnInfo.ERROR_EMAIL_TOOMUCH);
                    return result;
                } else {
                    history.setSendAt(System.currentTimeMillis());
                    emailHistoryService.refreshEmailHistory(history.getEmailId());
                    success = true;
                }
            }
            if (success) {
                this.sendActiveEmail(history, request);

                result.setSuccessMessage(ReturnInfo.SUCCESS_EMAIL_SEND);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        RestUser user = new RestUser();
        user.setEmail(restUser.getEmail());
        result.setResult(user);
        //result.addErrorMessage("error....");
        //logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }

    @RequestMapping(value = "/loginActionJson",method = RequestMethod.POST)
    public ExecuteResult<AuthUser> loginActionJson(@RequestBody AuthUser authUser){
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        result.setResult(authUser);
        //result.addErrorMessage("error....");
        logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }

    private void sendActiveEmail(EmailHistory history, HttpServletRequest request) {
        try {
            String json = JSONObject.toJSONString(history);
            String text = EmailUtils.encriptEmailCode(json, HomeController.getEmailClickAesKey(history));

            text = URLEncoder.encode(text, "UTF-8");

            String path = request.getContextPath();
            String basePath = request.getScheme() + "://" + request.getServerName();
            if (request.getServerPort() == 80){
                basePath =basePath + path + "/";
            }else {
                basePath = basePath + ":" + request.getServerPort() + path + "/";
            }

            String url = basePath + HomeController.getEmailUrlAction(history) + text;

            Mail mail = new Mail();
            mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
            mail.setSubject(HomeController.getEmailObject(history,request,localeResolver));
            mail.setTo(history.getEmail());//
            //mail.setTo("<EMAIL>");
            mail.setFtl(HomeController.getEmailFtl(history,request,localeResolver));
            Map<String, Object> model = new HashMap<>();
            model.put("emailToName", history.getEmail());
            model.put("signature", "恩道电子书");
            model.put("location", DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
            model.put("emailUrl", url);
            model.put("home",basePath);

            mail.setModel(model);

            emailService.sendSimpleMessage(mail);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
