package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.common.BitSetUtil;
import com.aaron.spring.model.EnyanPlan;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2020-11-10
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestPlan extends RestBaseDTO {
    private static final long serialVersionUID = -634284153900928730L;

    private Long bookId;

    private String name;

    private Long startFrom;

    private Integer hasBuy;

    private Integer isDeleted;

    private Long updateTime;

    private String finished;

    private List<RestPlan> list;

    private Integer checkinType;

    private Integer toDay;

    public void initFrom(EnyanPlan obj){
        this.setEmail(obj.getUserEmail());
        this.bookId = obj.getBookId();
        this.name = obj.getName();
        this.startFrom = obj.getStartFrom();
        this.hasBuy = obj.getHasBuy();
        this.isDeleted = obj.getIsDeleted();
        this.updateTime = obj.getUpdateTime();
        if (obj.getFinishedBitSet() != null){
            this.finished = BitSetUtil.encode(obj.getFinishedBitSet());
        }else{
            this.finished = "";
        }
    }


    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getStartFrom() {
        return startFrom;
    }

    public void setStartFrom(Long startFrom) {
        this.startFrom = startFrom;
    }

    public Integer getHasBuy() {
        return hasBuy;
    }

    public void setHasBuy(Integer hasBuy) {
        this.hasBuy = hasBuy;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getFinished() {
        return finished;
    }

    public void setFinished(String finished) {
        this.finished = finished;
    }

    public List<RestPlan> getList() {
        return list;
    }

    public void setList(List<RestPlan> list) {
        this.list = list;
    }

    public Integer getCheckinType() {
        return checkinType;
    }

    public void setCheckinType(Integer checkinType) {
        this.checkinType = checkinType;
    }

    public Integer getToDay() {
        return toDay;
    }

    public void setToDay(Integer toDay) {
        this.toDay = toDay;
    }

    @Override
    public String toString() {
        return "RestPlan{" +
                "bookId=" + bookId +
                ", name='" + name + '\'' +
                ", startFrom=" + startFrom +
                ", hasBuy=" + hasBuy +
                ", isDeleted=" + isDeleted +
                ", updateTime=" + updateTime +
                ", finished='" + finished + '\'' +
                ", list=" + list +
                '}';
    }

    public static  void main(String[] args){
        printInit("com.aaron.spring.api.v3.model.RestPlan","EnyanPlan");
    }
}
