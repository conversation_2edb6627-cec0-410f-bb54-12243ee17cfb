package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.LoginRequired;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestPodFavorite;
import com.aaron.spring.api.v4.model.RestPodcast;
import com.aaron.spring.model.PodFavorite;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.PodFavoriteService;
import com.aaron.spring.service.PodPodcastService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Author: <PERSON>
 * @Description: 播客收藏API控制器
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Slf4j
@RestController("RestPodFavoriteControllerV4")
@RequestMapping("/api/v4/podcast/favorite")
public class RestPodFavoriteController extends RestBaseController {

    // @Resource
    // private PodFavoriteService podFavoriteService;
    
    // @Resource
    // private PodPodcastService podPodcastService;

    // /**
    //  * 获取用户收藏的播客列表
    //  * @param restObj 请求参数
    //  * @param request HTTP请求
    //  * @return 收藏的播客分页列表
    //  */
    // @LoginRequired
    // @RequestMapping(value = "/list", method = RequestMethod.POST)
    // public PageResult<RestPodFavorite> list(@RequestBody RestPodFavorite restObj, HttpServletRequest request) {
    //     restObj.initHeaderValue(request);

    //     PageResult<RestPodFavorite> pageResult = new PageResult<>();
    //     if (null == restObj.getPage() || null == restObj.getUserId()) {
    //         pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
    //         return pageResult;
    //     }

    //     Page<PodFavorite> page = new Page();
    //     page.setCurrentPage(restObj.getPage());
    //     page.setPageSize(pageResult.getPageSize());
        
    //     page = podFavoriteService.queryUserFavorites(page, restObj.getUserId());

    //     for (PodFavorite obj : page.getRecords()) {
    //         RestPodFavorite tmp = new RestPodFavorite();
    //         tmp.initFrom(obj);
            
    //         // 查询播客信息补充完整
    //         if (obj.getPodcastId() != null) {
    //             PodPodcast podcast = podPodcastService.queryRecordByPrimaryKey(obj.getPodcastId()).getResult();
    //             if (podcast != null) {
    //                 tmp.setPodcastTitle(podcast.getTitle());
    //                 tmp.setPodcastCoverImageUrl(podcast.getCoverImageUrl());
    //                 tmp.setAuthorName(podcast.getAuthorName());
    //             }
    //         }
            
    //         pageResult.getResult().add(tmp);
    //     }
    //     pageResult.setCurrentPage(page.getCurrentPage());
    //     pageResult.setTotalRecord(page.getTotalRecord());
    //     return pageResult;
    // }

    // /**
    //  * 收藏播客
    //  * @param restObj 请求参数
    //  * @param request HTTP请求
    //  * @return 操作结果
    //  */
    // @LoginRequired
    // @RequestMapping(value = "/add", method = RequestMethod.POST)
    // public ExecuteResult<RestPodFavorite> add(@RequestBody RestPodFavorite restObj, HttpServletRequest request) {
    //     restObj.initHeaderValue(request);

    //     ExecuteResult<RestPodFavorite> result = new ExecuteResult<>();
    //     if (null == restObj.getUserId() || null == restObj.getPodcastId()) {
    //         result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
    //         return result;
    //     }

    //     ExecuteResult<PodFavorite> serviceResult = podFavoriteService.addFavorite(restObj.getUserId(), 
    //             restObj.getUserEmail(), restObj.getPodcastId());
        
    //     if (serviceResult.isSuccess()) {
    //         RestPodFavorite resultObj = new RestPodFavorite();
    //         resultObj.initFrom(serviceResult.getResult());
    //         result.setResult(resultObj);
    //     } else {
    //         result.setErrorMessages(serviceResult.getErrorMessages());
    //     }
        
    //     return result;
    // }

    // /**
    //  * 取消收藏播客
    //  * @param restObj 请求参数
    //  * @param request HTTP请求
    //  * @return 操作结果
    //  */
    // @LoginRequired
    // @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    // public ExecuteResult<String> cancel(@RequestBody RestPodFavorite restObj, HttpServletRequest request) {
    //     restObj.initHeaderValue(request);

    //     ExecuteResult<String> result = new ExecuteResult<>();
    //     if (null == restObj.getUserId() || null == restObj.getPodcastId()) {
    //         result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
    //         return result;
    //     }

    //     return podFavoriteService.cancelFavorite(restObj.getUserId(), restObj.getPodcastId());
    // }
    
    // /**
    //  * 检查用户是否收藏了某播客
    //  * @param restObj 请求参数
    //  * @param request HTTP请求
    //  * @return 操作结果
    //  */
    // @LoginRequired
    // @RequestMapping(value = "/check", method = RequestMethod.POST)
    // public ExecuteResult<Boolean> check(@RequestBody RestPodFavorite restObj, HttpServletRequest request) {
    //     restObj.initHeaderValue(request);

    //     ExecuteResult<Boolean> result = new ExecuteResult<>();
    //     if (null == restObj.getUserId() || null == restObj.getPodcastId()) {
    //         result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
    //         return result;
    //     }

    //     boolean isFavorite = podFavoriteService.checkUserFavorite(restObj.getUserId(), restObj.getPodcastId());
    //     result.setResult(isFavorite);
    //     return result;
    // }
}
