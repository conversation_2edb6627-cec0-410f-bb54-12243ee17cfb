package com.aaron.spring.api.v4.model;

import com.aaron.common.NameAndValue;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.model.CurrencyType;
import com.aaron.spring.model.EnyanBookList;
import com.aaron.spring.model.EnyanBookSet;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/9/1
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestBookSet extends RestBaseDTO {
	@Serial
	private static final long serialVersionUID = -7850740491683579507L;

	private Long setId;

	private String setName;

	private String bannerUrl;

	private String setAbstract;

	private Integer discountValue;

	private Integer isDiscountValid;

	private BigDecimal price;

	private BigDecimal priceCurrency;//新的价格

	private BigDecimal priceDiscount;

	private List<RestBook> bookList;//bookList

	private List<NameAndValue> bookVersions;

	private Integer canAllBuy;

	public void initFrom(EnyanBookSet obj){
		this.setId = obj.getSetId();
		this.setName = obj.getSetName();
		this.bannerUrl = obj.getBannerUrl();
		this.setAbstract = obj.getSetAbstract();
		this.canAllBuy = obj.getCanAllBuy();
		if (null != obj.getDiscountValue() && obj.getDiscountValue() < 100){
			this.discountValue = obj.getDiscountValue();
			this.isDiscountValid = obj.getIsDiscountValid();
			this.price = obj.getPrice();
			this.priceDiscount = obj.getPriceDiscount();
		}
		if (null != obj.getBookWebInfo()){
			this.bookVersions = obj.getBookWebInfo().getVersions();
		}
	}

	public void initFrom(EnyanBookSet obj, CurrencyType currencyType){
		this.initFrom(obj);
		if (null != obj.getPriceDiscount()){
			this.priceCurrency = BookUtil.getCurrencyByPriceValue(obj.getPriceDiscount(),currencyType);
		}
	}

	public void initFromBookList(EnyanBookList obj){
		this.setId = obj.getSetId();
		this.setName = obj.getSetName();
		this.bannerUrl = obj.getBannerUrl();
		this.setAbstract = obj.getSetAbstract();
		this.canAllBuy = obj.getCanAllBuy();
		if (null != obj.getDiscountValue() && obj.getDiscountValue() < 100){
			this.discountValue = obj.getDiscountValue();
			this.isDiscountValid = obj.getIsDiscountValid();
			this.price = obj.getPrice();
			this.priceDiscount = obj.getPriceDiscount();
		}
		if (null != obj.getBookWebInfo()){
			this.bookVersions = obj.getBookWebInfo().getVersions();
		}
	}

	public void initFromBookList(EnyanBookList obj, CurrencyType currencyType){
		this.initFromBookList(obj);
		if (null != obj.getPriceDiscount()){
			this.priceCurrency = BookUtil.getCurrencyByPriceValue(obj.getPriceDiscount(),currencyType);
		}
	}

	public Long getSetId() {
		return setId;
	}

	public void setSetId(Long setId) {
		this.setId = setId;
	}

	public String getSetName() {
		return setName;
	}

	public void setSetName(String setName) {
		this.setName = setName;
	}

	public String getBannerUrl() {
		return bannerUrl;
	}

	public void setBannerUrl(String bannerUrl) {
		this.bannerUrl = bannerUrl;
	}

	public String getSetAbstract() {
		return setAbstract;
	}

	public void setSetAbstract(String setAbstract) {
		this.setAbstract = setAbstract;
	}

	public Integer getDiscountValue() {
		return discountValue;
	}

	public void setDiscountValue(Integer discountValue) {
		this.discountValue = discountValue;
	}

	public Integer getIsDiscountValid() {
		return isDiscountValid;
	}

	public void setIsDiscountValid(Integer isDiscountValid) {
		this.isDiscountValid = isDiscountValid;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getPriceCurrency() {
		return priceCurrency;
	}

	public void setPriceCurrency(BigDecimal priceCurrency) {
		this.priceCurrency = priceCurrency;
	}

	public BigDecimal getPriceDiscount() {
		return priceDiscount;
	}

	public void setPriceDiscount(BigDecimal priceDiscount) {
		this.priceDiscount = priceDiscount;
	}

	public List<RestBook> getBookList() {
		return bookList;
	}

	public void setBookList(List<RestBook> bookList) {
		this.bookList = bookList;
	}

	public List<NameAndValue> getBookVersions() {
		return bookVersions;
	}

	public void setBookVersions(List<NameAndValue> bookVersions) {
		this.bookVersions = bookVersions;
	}

	public Integer getCanAllBuy() {
		return canAllBuy;
	}

	public void setCanAllBuy(Integer canAllBuy) {
		this.canAllBuy = canAllBuy;
	}

	public static  void main(String[] args){
		printInit("com.aaron.spring.api.v4.model.RestBookSet","EnyanBookSet");
	}
}
