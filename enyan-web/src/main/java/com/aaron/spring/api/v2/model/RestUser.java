package com.aaron.spring.api.v2.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.AuthUser;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/8/24
 * @Modified By:
 */
public class RestUser extends RestBaseDTO{
    private static final long serialVersionUID = -8766886224327763411L;
    private String name;
    private String email;
    private String passwd;

    private String deviceName;
    private String deviceType;

    private Long userId;
    private Long myBookNum;
    private Long myNoteNum;
    private Long myMsgNum;
    private int expires_in;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    public Long getMyBookNum() {
        return myBookNum;
    }

    public void setMyBookNum(Long myBookNum) {
        this.myBookNum = myBookNum;
    }

    public Long getMyNoteNum() {
        return myNoteNum;
    }

    public void setMyNoteNum(Long myNoteNum) {
        this.myNoteNum = myNoteNum;
    }

    public Long getMyMsgNum() {
        return myMsgNum;
    }

    public void setMyMsgNum(Long myMsgNum) {
        this.myMsgNum = myMsgNum;
    }
    public int getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(int expires_in) {
        this.expires_in = expires_in;
    }

    public static  void main(String[] args){
        printInit("com.aaron.spring.api.v2.model.RestUser","AuthUser");
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }
}
