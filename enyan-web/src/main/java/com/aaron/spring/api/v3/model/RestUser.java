package com.aaron.spring.api.v3.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.AuthUser;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/8/24
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestUser extends RestBaseDTO{
    private static final long serialVersionUID = -8766886224327763411L;
    private String name;
    private String passwd;

    private String deviceName;
    private String deviceType;

    private String sign;

    private Long userId;
    private Long myBookNum;
    private Long myNoteNum;
    private Long myMsgNum;
    private Long myFavoriteNum;

    private List<Long> bookIdList;
    private Integer expires_in;

    private String lang;

    private  String rawSign;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    public Long getMyBookNum() {
        return myBookNum;
    }

    public void setMyBookNum(Long myBookNum) {
        this.myBookNum = myBookNum;
    }

    public Long getMyNoteNum() {
        return myNoteNum;
    }

    public void setMyNoteNum(Long myNoteNum) {
        this.myNoteNum = myNoteNum;
    }

    public Long getMyMsgNum() {
        return myMsgNum;
    }

    public void setMyMsgNum(Long myMsgNum) {
        this.myMsgNum = myMsgNum;
    }

    public Integer getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(Integer expires_in) {
        this.expires_in = expires_in;
    }

    public static  void main(String[] args){
        printInit("com.aaron.spring.api.v2.model.RestUser","AuthUser");
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public List<Long> getBookIdList() {
        return bookIdList;
    }

    public void setBookIdList(List<Long> bookIdList) {
        this.bookIdList = bookIdList;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getRawSign() {
        return rawSign;
    }

    public void setRawSign(String rawSign) {
        this.rawSign = rawSign;
    }

    public Long getMyFavoriteNum() {
        return myFavoriteNum;
    }

    public void setMyFavoriteNum(Long myFavoriteNum) {
        this.myFavoriteNum = myFavoriteNum;
    }

    @Override
    public String toString() {
        return "RestUser{" +
                "name='" + name + '\'' +
                ", passwd='" + passwd + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", deviceType='" + deviceType + '\'' +
                ", sign='" + sign + '\'' +
                ", userId=" + userId +
                '}';
    }
}
