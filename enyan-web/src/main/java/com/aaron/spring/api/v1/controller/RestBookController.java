package com.aaron.spring.api.v1.controller;

import com.aaron.crypto.EpubEncryption;
import com.aaron.drm.model.DrmInfo;
import com.aaron.drm.model.Licenses;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v1.model.RestBook;
import com.aaron.spring.api.RestList;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanOrderDetail;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanOrderDetailService;
import com.aaron.util.ExecuteResult;
import com.aaron.util.FilePropertiesUtil;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2018/8/29
 * @Modified By:
 */
@RestController("RestBookControllerV1")
@RequestMapping("/api/v1/book")
//@Deprecated
public class RestBookController extends RestBaseController {
    private final Log logger = LogFactory.getLog(RestBookController.class);
    private static final String ERROR_NAME = "error.txt";
    private static final String epubBaseDir = FilePropertiesUtil.props.getProperty("epubBaseDir");//
    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private EnyanBookService enyanBookService;

    @RequestMapping(value = "/books",method = RequestMethod.POST)
    public ExecuteResult<List<RestBook>> books(@RequestBody RestBook restBook){
        ExecuteResult<List<RestBook>> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restBook.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        List<RestBook> bookList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            RestBook book = new RestBook();
            book.setBookId(i+1L);
            book.setName("书籍所以没关系"+(15+i));
            book.setAuthor("author");
            book.setDateStr("2008-1-"+(i+1));
            book.setLanguage((i%2 == 0)?"sc":"tc");
            book.setImgUrl("imgUrl");
            book.setFileName("markTC"+(15+i)+".epub");
            bookList.add(book);
        }
        result.setResult(bookList);
        return result;
    }

    @RequestMapping(value = "/booksPage",method = RequestMethod.POST)
    public PageResult<RestBook> booksPage(@RequestBody RestBook restBook, HttpServletRequest request){
        if (StringUtils.isBlank(restBook.getEmail())){
            PageResult<RestBook> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        EnyanOrderDetail paramOrderDetail = new EnyanOrderDetail();
        paramOrderDetail.setUserEmail(restBook.getEmail());

        Page<EnyanOrderDetail> searchPage = new Page<>();
        searchPage.setCurrentPage(restBook.getPage());
        if (!Constant.IS_PRODUCT){
            searchPage.setPageSize(1);
        }

        Page<EnyanOrderDetail> orderDetailPage = enyanOrderDetailService.findRecordsByOrder(searchPage,paramOrderDetail);

        PageResult<RestBook> page = new PageResult<>(orderDetailPage.getCurrentPage(),orderDetailPage.getTotalRecord(),orderDetailPage.getPageSize());
        for (EnyanOrderDetail enyanOrderDetail:orderDetailPage.getRecords()){
            ExecuteResult<EnyanBook> bookExecuteResult = enyanBookService.queryRecordByPrimaryKey(enyanOrderDetail.getBookId());
            EnyanBook enyanBook = bookExecuteResult.getResult();

            RestBook book = new RestBook();
            book.setBookId(enyanOrderDetail.getBookId());
            book.setName(enyanOrderDetail.getBookTitle());
            if (StringUtils.isNotBlank(enyanBook.getAuthor())){
                book.setAuthor(StringUtils.replace(enyanBook.getAuthor(),"#"," "));
            }
            book.setDateStr(DateFormatUtils.format(enyanOrderDetail.getPurchasedAt(), Constant.DATE_FORMAT_PATTERN));
            book.setLanguage(enyanBook.getIsInCn().equals(Constant.BYTE_VALUE_1)?"sc":"tc");
            book.setImgUrl(enyanBook.getBookCoverApp());
            book.setFileName(enyanBook.getBookId()+".epub");
            book.setIsbn(enyanBook.getBookIsbn());
            book.setSize(enyanBook.getSize());

            page.getResult().add(book);
        }
        return page;
    }

    @RequestMapping(value = "/booksPageIds",method = RequestMethod.POST)
    public PageResult<RestBook> booksPageIds(@RequestBody RestList<Long> restList, HttpServletRequest request){
        if (StringUtils.isBlank(restList.getEmail())){
            PageResult<RestBook> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }
       /* String path = request.getContextPath();
        String basePath = request.getScheme() + "://" + request.getServerName();
        if (request.getServerPort() == 80 || request.getServerPort() == 443){
            basePath = Constant.IMG_SERVER + "book_image/";
        }else {
            basePath = basePath + ":" + request.getServerPort() + path + "/book_image/";
        }*/

        if (null == restList.getList()|| restList.getList().isEmpty() ||restList.getList().size() > 20){
            PageResult<RestBook> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        List<EnyanBook> bookList = enyanBookService.findBookByIds(restList.getList());
        List<EnyanBook> newBookList = new ArrayList<>();
        for (Long bookId:restList.getList()){
            if (bookId < 0){//导入的书籍
                EnyanBook importBook = new EnyanBook();
                importBook.setBookId(bookId);
                newBookList.add(importBook);
                continue;
            }
            EnyanBook book =  this.getBookById(bookId,bookList);
            if (null != book){
                newBookList.add(book);
            }
        }
        PageResult<RestBook> page = new PageResult<>();
        for (EnyanBook enyanBook:newBookList){
            RestBook book = new RestBook();
            book.setBookId(enyanBook.getBookId());
            if (book.getBookId() < 0){//导入的书籍
                book.setName("");
                book.setAuthor("");
                //book.setDateStr(DateFormatUtils.format(enyanOrderDetail.getPurchasedAt(), Constant.DATE_FORMAT_PATTERN));
                book.setLanguage("sc");
                book.setImgUrl("");
                book.setFileName("");
                book.setIsbn("");
                book.setSize("");
            }else{
                book.setName(enyanBook.getBookTitle());
                if (StringUtils.isNotBlank(enyanBook.getAuthor())){
                    book.setAuthor(StringUtils.replace(enyanBook.getAuthor(),"#"," "));
                }
                //book.setDateStr(DateFormatUtils.format(enyanOrderDetail.getPurchasedAt(), Constant.DATE_FORMAT_PATTERN));
                book.setLanguage(enyanBook.getIsInCn().equals(Constant.BYTE_VALUE_1)?"sc":"tc");
                book.setImgUrl(enyanBook.getBookCoverApp());
                book.setFileName(enyanBook.getBookId()+".epub");
                book.setIsbn(enyanBook.getBookIsbn());
                book.setSize(enyanBook.getSize());
            }

            page.getResult().add(book);
        }
        return page;
    }

    @RequestMapping(value = "/search",method = RequestMethod.POST)
    public ExecuteResult<List<RestBook>> search(@RequestBody RestBook restBook){
        ExecuteResult<List<RestBook>> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restBook.getName()) || StringUtils.isBlank(restBook.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        List<RestBook> bookList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            RestBook book = new RestBook();
            book.setBookId(i+1L);
            book.setName("书籍所以没关系"+(15+i));
            book.setAuthor("作者");
            book.setDateStr("2008-1-"+(i+1));
            book.setLanguage((i%2 == 0)?"sc":"tc");
            book.setImgUrl("imgUrl");
            book.setFileName("markTC"+(15+i)+".epub");
            bookList.add(book);
        }
        result.setResult(bookList);
        return result;
    }

    @RequestMapping(value = "/searchPage",method = RequestMethod.POST)
    public PageResult<RestBook> searchPage(@RequestBody RestBook restBook, HttpServletRequest request){
        if (StringUtils.isBlank(restBook.getName()) || StringUtils.isBlank(restBook.getEmail())){
            PageResult<RestBook> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        EnyanOrderDetail paramOrderDetail = new EnyanOrderDetail();
        paramOrderDetail.setUserEmail(restBook.getEmail());
        paramOrderDetail.setBookTitle("%"+restBook.getName()+"%");


        Page<EnyanOrderDetail> searchPage = new Page<>();
        searchPage.setCurrentPage(restBook.getPage());
        if (!Constant.IS_PRODUCT){
            searchPage.setPageSize(1);
        }

        Page<EnyanOrderDetail> orderDetailPage = enyanOrderDetailService.searchRecordsByUserAndBookAndAuthorList(searchPage,paramOrderDetail);

        PageResult<RestBook> page = new PageResult<>(orderDetailPage.getCurrentPage(),orderDetailPage.getTotalRecord(),orderDetailPage.getPageSize());
        for (EnyanOrderDetail enyanOrderDetail:orderDetailPage.getRecords()){
            ExecuteResult<EnyanBook> bookExecuteResult = enyanBookService.queryRecordByPrimaryKey(enyanOrderDetail.getBookId());
            EnyanBook enyanBook = bookExecuteResult.getResult();

            RestBook book = new RestBook();
            book.setBookId(enyanOrderDetail.getBookId());
            book.setName(enyanOrderDetail.getBookTitle());
            if (StringUtils.isNotBlank(enyanBook.getAuthor())){
                book.setAuthor(StringUtils.replace(enyanBook.getAuthor(),"#"," "));
            }
            book.setDateStr(DateFormatUtils.format(enyanOrderDetail.getPurchasedAt(), Constant.DATE_FORMAT_PATTERN));
            book.setLanguage(enyanBook.getIsInCn().equals(Constant.BYTE_VALUE_1)?"sc":"tc");
            book.setImgUrl(enyanBook.getBookCoverApp());
            book.setFileName(enyanBook.getBookId()+".epub");
            book.setIsbn(enyanBook.getBookIsbn());

            page.getResult().add(book);
        }
        return page;
    }
    @RequestMapping(value = "/download")
    public ResponseEntity<byte[]> downloadEpub(@RequestBody RestBook restBook, HttpServletRequest request){
        ResponseEntity<byte[]> entity = null;
        try {
            //System.out.println("downloadEpub");
            HttpHeaders headers = new HttpHeaders();

            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            if (restBook.getBookId() == null || StringUtils.isBlank(restBook.getEmail())){
                headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
                entity = new ResponseEntity<>("error001".getBytes(), headers, HttpStatus.OK);
                return entity;
            }
            EnyanOrderDetail paramOrderDetail = new EnyanOrderDetail();
            paramOrderDetail.setUserEmail(restBook.getEmail());
            paramOrderDetail.setBookId(restBook.getBookId());

            List<EnyanOrderDetail> list = enyanOrderDetailService.searchRecordsByUserList(paramOrderDetail);
            if (null == list || list.isEmpty()){
                headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
                entity = new ResponseEntity<>("error002".getBytes(), headers, HttpStatus.OK);
                return entity;
            }

            EnyanOrderDetail orderDetail = list.get(0);
            if (StringUtils.isBlank(orderDetail.getDrminfo())){
                headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
                entity = new ResponseEntity<>("error003".getBytes(), headers, HttpStatus.OK);
                return entity;
            }
            DrmInfo drmInfo = JSONObject.parseObject(orderDetail.getDrminfo(),DrmInfo.class);
            if (StringUtils.isBlank(drmInfo.getLcpInfo().getLicenseUuid())){
                //下载 licenseID 并重新更新数据
                Licenses licenses = enyanOrderDetailService.getLicenseByPurchaseId(drmInfo.getLcpInfo().getPurchseId());
                drmInfo.getLcpInfo().setLicenseUuid(licenses.getID());
                EnyanOrderDetail newOrderDetail = new EnyanOrderDetail();
                newOrderDetail.setDrminfo(JSONObject.toJSONString(drmInfo));
                newOrderDetail.setOrderDetailId(orderDetail.getOrderDetailId());
                enyanOrderDetailService.updateRecord(newOrderDetail);
            }
            //根据 licenseID 下载书籍

            /*
            EpubEncryption epubEncryption = new EpubEncryption(orderDetail.getUserId(),restBook.getBookId(),epubBaseDir,restBook.getDeviceID());
            String fileName = this.getFileName(restBook);
            String filePath = epubEncryption.getDestUserEpubPath();

            filePath = epubEncryption.getEpubPath();//测试使用

            //filePath = "/Users/<USER>/Documents/soft/Teams_osx.dmg";
            File file = new File(filePath);
            if (!file.exists()){
                epubEncryption.zipEpub();
            }
            //headers.set("Content-Length",String.valueOf(file.length()));
            headers.setContentDispositionFormData("attachment", fileName);//告知浏览器以下载方式打开
            //headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            headers.setContentLength(file.length());
            */
            byte[] fileByte = enyanOrderDetailService.downloadLcpFiles(drmInfo);
            headers.setContentLength(fileByte.length);
            String fileName = this.getFileName(restBook);
            headers.setContentDispositionFormData("attachment", fileName);//告知浏览器以下载方式打开
            entity = new ResponseEntity<>(fileByte, headers, HttpStatus.OK);
            //System.out.println("file.length:"+file.length());
            return entity;
        }catch (Exception e){
            logger.error("下载的问题：Connection reset by peer");
            HttpHeaders headers = new HttpHeaders();

            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
            entity = new ResponseEntity<>("error004".getBytes(), headers, HttpStatus.OK);//HttpStatus.SERVICE_UNAVAILABLE
            return entity;
        }
        //return entity;
    }
    @RequestMapping(value = "/download2")
    public ResponseEntity<byte[]> downloadEpub2(@RequestBody RestBook restBook, HttpServletRequest request){
        ResponseEntity<byte[]> entity = null;
        try {
            //System.out.println("downloadEpub");
            HttpHeaders headers = new HttpHeaders();

            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            if (restBook.getBookId() == null || StringUtils.isBlank(restBook.getEmail())){
                headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
                entity = new ResponseEntity<>("error001".getBytes(), headers, HttpStatus.OK);
                return entity;
            }
            EnyanOrderDetail paramOrderDetail = new EnyanOrderDetail();
            paramOrderDetail.setUserEmail(restBook.getEmail());
            paramOrderDetail.setBookId(restBook.getBookId());

            List<EnyanOrderDetail> list = enyanOrderDetailService.searchRecordsByUserList(paramOrderDetail);
            if (null == list || list.isEmpty()){
                headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
                entity = new ResponseEntity<>("error002".getBytes(), headers, HttpStatus.OK);
                return entity;
            }

            EnyanOrderDetail orderDetail = list.get(0);
            EpubEncryption epubEncryption = new EpubEncryption(orderDetail.getUserId(),restBook.getBookId(),epubBaseDir,restBook.getDeviceID());
            String fileName = this.getFileName(restBook);
            String filePath = epubEncryption.getDestUserEpubPath();

            filePath = epubEncryption.getEpubPath();//测试使用

            //filePath = "/Users/<USER>/Documents/soft/Teams_osx.dmg";
            File file = new File(filePath);
            if (!file.exists()){
                epubEncryption.zipEpub();
            }
            //headers.set("Content-Length",String.valueOf(file.length()));
            headers.setContentDispositionFormData("attachment", fileName);//告知浏览器以下载方式打开
            //headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            headers.setContentLength(file.length());

            entity = new ResponseEntity<>(FileUtils.readFileToByteArray(file), headers, HttpStatus.OK);
            //System.out.println("file.length:"+file.length());
            return entity;
        }catch (IOException e){
            logger.error("下载的问题：Connection reset by peer");
        }catch (Exception e){
            logger.error(e.getMessage());
        }
        return entity;
    }

    private String getFileName(RestBook restBook){
        //return "1.epub";
        return restBook.getBookId() + ".epub";
    }

    private EnyanBook getBookById(Long id, List<EnyanBook> bookList){
        for (EnyanBook enyanBook : bookList){
            if (enyanBook.getBookId() == id){
                return enyanBook;
            }
        }
        return null;
    }
}
