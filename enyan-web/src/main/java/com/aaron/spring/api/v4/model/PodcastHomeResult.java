package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 播客首页数据组合对象，包含最新单集和推荐栏目列表
 */
@ApiModel(description = "首页数据返回结果")
@Data
@NoArgsConstructor
public class PodcastHomeResult extends RestBaseDTO {
    private static final long serialVersionUID = -2648651113836986366L;

    @ApiModelProperty(value = "最新单集列表")
    private List<RestEpisode> latestEpisodes;
    
    @ApiModelProperty(value = "推荐栏目列表")
    private List<RestPodcast> recommendedTopics;
}
