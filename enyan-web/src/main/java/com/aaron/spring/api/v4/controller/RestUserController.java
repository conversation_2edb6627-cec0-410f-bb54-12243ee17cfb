package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.exception.RestException;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestDeviceLimit;
import com.aaron.spring.api.v4.model.RestUser;
import com.aaron.spring.common.APIConstant;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.common.WebUtil;
import com.aaron.spring.controller.HomeController;
import com.aaron.spring.entity.Mail;
import com.aaron.spring.model.*;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.*;
import com.aaron.util.*;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 *  BusinessException 捕获异常 3XX
 * @Author: Aaron Hao
 * @Date: Created in  2018/8/24
 * @Modified By:
 */
@Slf4j
@RestController("RestUserControllerV4")
@RequestMapping("/api/v4/user")
public class RestUserController extends RestBaseController {
    //private final Log log = LogFactory.getLog(RestUserController.class);

    @Resource
    private AuthUserService authUserService;

    @Resource
    private EmailHistoryService emailHistoryService;

    @Resource
    private EmailService emailService;

//    @Resource
//    private EnyanPublisherService enyanPublisherService;

    @Resource
    private EnyanOrderService enyanOrderService;

    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private EnyanWishService enyanWishService;

    @Resource
    private EnyanBookBuyService enyanBookBuyService;

    @Resource
    private EnyanCartService enyanCartService;

    @Resource
    private EnyanPlanNoteService enyanPlanNoteService;

    @Resource
    private EnyanPlanService enyanPlanService;

    @Resource
    private EnyanRedeemCodeService enyanRedeemCodeService;

    @Resource
    private EnyanReaderHighlightService enyanReaderHighlightService;

    @RequestMapping(value = "/msg",method = RequestMethod.GET)
    public ExecuteResult<RestUser> msg(){
        RestUser authUser = new RestUser();
        authUser.setName("name");
        authUser.setToken("token");
        authUser.setUserId(11L);

        ExecuteResult<RestUser> result = new ExecuteResult<>();
        result.setResult(authUser);
        //result.addErrorMessage("error....");
        //logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }

    @LoginAnonymous
    @RequestMapping(value = "/reg",method = RequestMethod.POST)
    public ExecuteResult<RestUser> reg(@RequestBody RestUser restUser, HttpServletRequest request){
        ExecuteResult<RestUser> result = new ExecuteResult<>();

        if (StringUtils.isBlank(restUser.getEmail())||StringUtils.isBlank(restUser.getPasswd())
                ||StringUtils.isBlank(restUser.getName()) || StringUtils.isBlank(restUser.getSign())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (restUser.getName().length() > 14 || restUser.getPasswd().length() < 6 || restUser.getPasswd().length() > 20){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (!EmailValidatorUtil.validate(restUser.getEmail())) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        //log.error("sign:{},rawSign:{}",restUser.getSign(),restUser.getRawSign());
        try {

            String jsonString = AESUtil.decrypt(restUser.getSign(),Constant.REST_REG_API_AES_KEY,Constant.REST_REG_API_AES_IV);
            RestUser signUser = JSONObject.parseObject(jsonString,RestUser.class);

            if (null == signUser){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }

            if (restUser.getName().equals(signUser.getName()) == false || restUser.getEmail().equals(signUser.getEmail()) == false
                    ||restUser.getPasswd().equals(signUser.getPasswd()) == false){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }

            AuthUser authUser = new AuthUser();
            authUser.setEmail(restUser.getEmail().trim().toLowerCase());
            authUser.setUserPassword(restUser.getPasswd());
            authUser.setUserName(restUser.getName());
            if (null != authUserService.getUserByEmail(authUser.getEmail()).getResult()) {
                result.addErrorMessage(ReturnInfo.ERROR_EMAIL_EXIST);
                return result;
            }
            authUserService.reg(authUser); //暂时屏蔽

            int yearMonthDay = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            EmailHistory history = new EmailHistory();
            history.setEmail(authUser.getEmail());
            history.setEmailType(EBookConstant.EmailType.REG_ACTIVE);
            history.setSendAt(System.currentTimeMillis());
            history.setRepeatTimes(0);
            history.setYearMonthDay(yearMonthDay);
            emailHistoryService.addRecord(history);
            this.sendActiveEmail(history, restUser.getLang(),authUserService);

            RestUser user = new RestUser();
            user.setEmail(authUser.getEmail());
            user.setName(authUser.getUserName());
            result.setResult(user);
            result.setSuccessMessage("successMsg");
        //String json = JSONObject.toJSONString(history);


            //String emailCode = EmailUtils.encriptEmailCode(json, Constant.EMAIL_SEND_REG_ACTIVE_AES_KEY);
            //logger.info("json:"+json);
            //logger.info("emailCode:"+emailCode);

            //emailCode = URLEncoder.encode(emailCode, "UTF-8");


        } catch (Exception e) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            throw new RestException(Integer.valueOf(303), InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "用户注册="+restUser);
        }finally {
            return result;
        }
    }

    @LoginAnonymous
    @RequestMapping(value = "/login",method = RequestMethod.POST)
    public ExecuteResult<RestUser> login(@RequestBody RestUser restUser, HttpServletRequest request){
        restUser.initHeaderValueExceptEmail(request);

        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail())||StringUtils.isBlank(restUser.getPasswd())
                ||StringUtils.isBlank(restUser.getDeviceID())||StringUtils.isBlank(restUser.getDeviceName())
                ||StringUtils.isBlank(restUser.getDeviceType())){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            if (APIConstant.DeviceFrom.iOS.equals(restUser.getDeviceFrom())){
                result.addErrorMessage("E1");
            }
            return result;
        }
        if (!EmailValidatorUtil.validate(restUser.getEmail())) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            if (APIConstant.DeviceFrom.iOS.equals(restUser.getDeviceFrom())){
                result.addErrorMessage("E2");
            }
            return result;
        }
        ExecuteResult<AuthUser> authUserExecuteResult = authUserService.getUserByEmail(restUser.getEmail());
        AuthUser authUser = authUserExecuteResult.getResult();
        if (authUser == null){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            if (APIConstant.DeviceFrom.iOS.equals(restUser.getDeviceFrom())){
                result.addErrorMessage("E3");
            }
            return result;
        }

        if (!authUserService.isPasswordValid(authUser.getUserPassword(),restUser.getPasswd(),authUser.getSalt())){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }
        if (!authUser.getIsActive().equals(Constant.BYTE_VALUE_1)){
            result.addErrorMessage(ReturnInfo.ERROR_ACCOUNT_NOT_ACTIVE);
            return result;
        }

        String deviceName = restUser.getDeviceName();
        if (deviceName.length() > 40){
            deviceName = deviceName.substring(0,39) + "...";
        }

        UserInfo userInfo = authUserService.loadUserInfoByEmail(restUser.getEmail());
        if (null == userInfo){
            userInfo = new UserInfo();
            userInfo.setUsername(restUser.getEmail());
            CustomUserDetail customUserDetail = new CustomUserDetail();
            customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
            userInfo.setCustomUserDetail(customUserDetail);
            ExecuteResult retResult = authUserService.addUserInfo(userInfo);
            if (retResult.isSuccess() == false){
                throw new RestException(Integer.valueOf(301), InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "添加UserInfo 错误="+restUser);
            }
        }
        if (null == userInfo.getCustomUserDetail().getDeviceLimitList()){
            userInfo.getCustomUserDetail().setDeviceLimitList(new ArrayList<>());
        }
        boolean isValidRequest = false;
        String token = Constant.ACCESS_TOKEN_NAME;
        for (DeviceLimit deviceLimit:userInfo.getCustomUserDetail().getDeviceLimitList()){
            if (restUser.getDeviceID().equals(deviceLimit.getDevice())){
                token = deviceLimit.getToken();
                isValidRequest = true; //本设备重新登录
                break;
            }
        }
        if (isValidRequest == false){//重新比对一下deviceType及deviceName，如果两个都相等，则直接删除当前的（等同于替换）
            for (DeviceLimit deviceLimit:userInfo.getCustomUserDetail().getDeviceLimitList()){
                if (deviceName.equals(deviceLimit.getDeviceName())
                        && restUser.getDeviceType().equals(deviceLimit.getDeviceType())){
                    userInfo.getCustomUserDetail().getDeviceLimitList().remove(deviceLimit);
                    break;
                }
            }
        }
        if (isValidRequest == false){
            if (Constant.TEST_ACCOUNT.contains(restUser.getEmail())) {//是测试账户，单独设置
                if (userInfo.getCustomUserDetail().getDeviceLimitList().size() >= Constant.MAX_DEVICE_TEST_LIMIT){//超出设备数目
                    result.addErrorMessage(ReturnInfo.ERROR_ACTIVE_DXCEED);
                    return result;
                }
                token = Constant.TEST_TOKEN_VALUE;
            }else {
                if (userInfo.getCustomUserDetail().getDeviceLimitList().size() >= Constant.MAX_DEVICE_LIMIT){//超出设备数目
                    result.addErrorMessage(ReturnInfo.ERROR_ACTIVE_DXCEED);
                    return result;
                }
                token = UUID.randomUUID().toString();
            }

            DeviceLimit deviceLimit = new DeviceLimit();
            deviceLimit.setDevice(restUser.getDeviceID());
            deviceLimit.setToken(token);
            deviceLimit.setDeviceName(deviceName);
            deviceLimit.setDeviceType(restUser.getDeviceType());
            deviceLimit.setLoginTime(System.currentTimeMillis());

            userInfo.getCustomUserDetail().getDeviceLimitList().add(deviceLimit);
            ExecuteResult retResult = authUserService.updateUserInfo(userInfo);
            if (retResult.isSuccess() == false){
                throw new RestException(Integer.valueOf(302), InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "更新User 错误="+restUser);
            }
        }

        RestUser user = new RestUser();
        user.setEmail(authUser.getEmail().toLowerCase());
        user.setName(authUser.getUserName());
        user.setSex(authUser.getSex());
        user.setToken(token);
        user.setUserId(authUser.getUserId());
        user.setAccess_token(token);
        user.setRefresh_token(Constant.REFRESH_TOKEN_NAME);
        user.setExpires_in(86400); //86400 24h

        result.setSuccessMessage("successMsg");

        result.setResult(user);
        //result.addErrorMessage("error....");
        //logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }


    /**
     *
     *  通过email重新发送激活email
     * @param restUser
     * @param request
     * @Date: 2018/4/5
     */
    @LoginAnonymous
    @RequestMapping(value = "/resendActiveEmail",method = RequestMethod.POST)
    public ExecuteResult<RestUser> resendActiveEmail(@RequestBody RestUser restUser, HttpServletRequest request) {
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        try {
            if (StringUtils.isBlank(restUser.getEmail())||StringUtils.isBlank(restUser.getSign())){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
            if (!EmailValidatorUtil.validate(restUser.getEmail())) {
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
            //log.error("active sign:{},rawSign:{}",restUser.getSign(),restUser.getRawSign());

            String jsonString = AESUtil.decrypt(restUser.getSign(),Constant.REST_REG_API_AES_KEY,Constant.REST_REG_API_AES_IV);
            RestUser signUser = JSONObject.parseObject(jsonString,RestUser.class);
            if (null == signUser){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }

            if (restUser.getEmail().equals(signUser.getEmail()) == false){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }

            int yearMonthDay = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            EmailHistory historyNew = emailHistoryService.getEmailHistoryByName(restUser.getEmail(), EBookConstant.EmailType.REG_ACTIVE,yearMonthDay).getResult();
            Boolean shouldSendEmail = false;
            if (null == historyNew) {//没有email发送记录
                historyNew = new EmailHistory();
                historyNew.setSendAt(System.currentTimeMillis());
                historyNew.setYearMonthDay(yearMonthDay);
                historyNew.setRepeatTimes(0);
                historyNew.setEmail(restUser.getEmail());
                historyNew.setEmailType(EBookConstant.EmailType.REG_ACTIVE);
                shouldSendEmail = true;

                emailHistoryService.addRecord(historyNew);
            }else {
                long between = System.currentTimeMillis() - historyNew.getSendAt();
                if (between < HomeController.EMAIL_TOO_MUCH_TIME_BETWEEN || historyNew.getRepeatTimes() > 10) {//小于10min 或者 多余10次
                    result.addErrorMessage(ReturnInfo.ERROR_EMAIL_TOOMUCH);
                    return result;
                } else {
                    historyNew.setSendAt(System.currentTimeMillis());
                    emailHistoryService.refreshEmailHistory(historyNew.getEmailId());
                    shouldSendEmail = true;
                }
            }
            if (shouldSendEmail){
                this.sendActiveEmail(historyNew, restUser.getLang(),authUserService);
            }
        } catch (Exception e) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            throw new RestException(Integer.valueOf(304), InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "用户重新激活="+restUser);
        }
        return result;
    }

    @RequestMapping(value = "/myInfo",method = RequestMethod.POST)
    public ExecuteResult<RestUser> myInfo(@RequestBody RestUser restUser, HttpServletRequest req){
        restUser.initHeaderValue(req);
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }
//        System.out.println("id:"+req.getAttribute(Constant.CURRENT_USER_ID));
//        System.out.println("email:"+this.getRestEmail(req));
        ExecuteResult<AuthUser> authUserExecuteResult = authUserService.getUserByEmail(restUser.getEmail());
        AuthUser authUser = authUserExecuteResult.getResult();
        RestUser user = new RestUser();
        if (null == authUser){
            user.setEmail("");
            user.setName("");
            user.setBirthday("");
            user.setSex(0);
            user.setMyBookNum(0L);
            user.setMyMsgNum(0L);
            user.setMyNoteNum(0L);
            user.setMyCartNum(0L);
            result.setResult(user);
            return result;
        }

        //EnyanOrderDetail enyanOrderDetail = new EnyanOrderDetail();
        //enyanOrderDetail.setUserEmail(restUser.getEmail());
//        List<Long> bookIdList = new ArrayList<>();
        List<EnyanBookBuy> orderDetailList = enyanBookBuyService.findBookIDAndNameByEmail(restUser.getEmail());
        /*
        List<Long> bookIdList = orderDetailList.stream().map(detail -> {
            return detail.getBookId();
        }).collect(Collectors.toList());
        */
        List<Long> bookIdList = orderDetailList.stream().map(detail ->  detail.getBookId()).collect(Collectors.toList());
        //long bookCount = enyanOrderDetailService.countOrderDetailByOrder(enyanOrderDetail);

        if (Constant.IS_PRODUCT == false){
            //bookIdList.addAll(Lists.newArrayList(1L,2L,3L));
        }

        long wishCount = enyanWishService.countOfWishByEmail(restUser.getEmail());
        long myCartNum = enyanCartService.countOfCartByEmail(restUser.getEmail());

        user.setEmail(authUser.getEmail().toLowerCase());
        user.setName(authUser.getUserName());
        user.setSex(authUser.getSex());
        user.setBirthday(authUser.getBirthday());
        user.setMyBookNum(bookIdList.size() + 0L);
        user.setMyMsgNum(0L);
        user.setMyNoteNum(0L);
        user.setMyFavoriteNum(wishCount);
        user.setBookIdList(bookIdList);
        user.setMyCartNum(myCartNum);

        result.setSuccessMessage("successMsg");

        result.setResult(user);
        //result.addErrorMessage("error....");
        //logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }

    @RequestMapping(value = "/passwdForget",method = RequestMethod.POST)
    public ExecuteResult<RestUser> passwdForget(@RequestBody RestUser restUser, HttpServletRequest request){
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }
        if (!EmailValidatorUtil.validate(restUser.getEmail())) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        ExecuteResult<AuthUser> authUserExecuteResult = authUserService.getUserByEmail(restUser.getEmail());
        AuthUser authUser = authUserExecuteResult.getResult();
        if (authUser == null){
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }
        boolean success = false;
        try {
            int yearMonthDay = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            EmailHistory history = emailHistoryService.getEmailHistoryByName(restUser.getEmail(), EBookConstant.EmailType.PASSWORD_FORGET,yearMonthDay).getResult();
            if (null == history) {
                history = new EmailHistory();
                history.setEmail(restUser.getEmail());
                history.setEmailType(EBookConstant.EmailType.PASSWORD_FORGET);
                history.setSendAt(System.currentTimeMillis());
                history.setRepeatTimes(0);
                history.setYearMonthDay(yearMonthDay);
                emailHistoryService.addRecord(history);
                success = true;
            } else {
                long between = System.currentTimeMillis() - history.getSendAt();
                if (between < HomeController.EMAIL_TOO_MUCH_TIME_BETWEEN || history.getRepeatTimes() >= 10) {//小于10min 或者 多余10次

                    result.addErrorMessage(ReturnInfo.ERROR_EMAIL_TOOMUCH);
                    return result;
                } else {
                    history.setSendAt(System.currentTimeMillis());
                    emailHistoryService.refreshEmailHistory(history.getEmailId());
                    success = true;
                }
            }
            if (success) {
                this.sendActiveEmail(history, restUser.getLang(),authUserService);

                result.setSuccessMessage(ReturnInfo.SUCCESS_EMAIL_SEND);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        RestUser user = new RestUser();
        user.setEmail(restUser.getEmail());
        result.setResult(user);
        //result.addErrorMessage("error....");
        //logger.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }

    @RequestMapping(value = "/loginActionJson",method = RequestMethod.POST)
    public ExecuteResult<AuthUser> loginActionJson(@RequestBody AuthUser authUser){
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        result.setResult(authUser);
        //result.addErrorMessage("error....");
        log.info(authUser.getUserName());
        //modelMap.addAttribute(authUser);
        return result;
    }

    @RequestMapping(value = "/revoke",method = RequestMethod.POST)
    public ExecuteResult<RestUser> revoke(@RequestBody RestUser restUser, HttpServletRequest req){
        restUser.initHeaderValue(req);
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail()) || StringUtils.isBlank(restUser.getPasswd())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (Constant.TEST_ACCOUNT.contains(restUser.getEmail())) {//是测试账户，不可以注销
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        ExecuteResult<AuthUser> authUserExecuteResult = authUserService.getUserByEmail(restUser.getEmail());
        AuthUser authUser = authUserExecuteResult.getResult();
        if (authUser == null){
            result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
            return result;
        }
        if (!authUserService.isPasswordValid(authUser.getUserPassword(),restUser.getPasswd(),authUser.getSalt())){
            result.addErrorMessage(ReturnInfo.ERROR_PASSWD_INVALID);
            return result;
        }
        String newEmail = restUser.getEmail();
        if (newEmail.length() > 70){//数据最多可以存储96，为避免溢出，需要提前处理一下长度
            newEmail = newEmail.substring(0, 70);
        }
        Float random = RandomUtils.nextFloat(0, 1f)*10000;
        newEmail = newEmail + "_revoked_"+random.intValue();
        authUserService.revokeLcpUser(restUser.getEmail(), newEmail);
        authUserService.revokeUser(restUser.getEmail(), newEmail);
        enyanBookBuyService.revokeUser(restUser.getEmail(), newEmail);
        enyanOrderDetailService.revokeUser(restUser.getEmail(), newEmail);
        enyanOrderService.revokeUser(restUser.getEmail(), newEmail);
        enyanWishService.revokeUser(restUser.getEmail(), newEmail);
        enyanCartService.revokeUser(restUser.getEmail(), newEmail);
        enyanPlanNoteService.revokeUser(restUser.getEmail(), newEmail);
        enyanPlanService.revokeUser(restUser.getEmail(), newEmail);
        enyanRedeemCodeService.revokeUser(restUser.getEmail(), newEmail);
        enyanReaderHighlightService.revokeUser(restUser.getEmail(), newEmail);
        return result;
    }

    @RequestMapping(value = "/updateName",method = RequestMethod.POST)
    public ExecuteResult<RestUser> updateName(@RequestBody RestUser restUser, HttpServletRequest request){
        restUser.initHeaderValue(request);
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail()) || null == restUser.getName()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        try {
            AuthUser authUser = new AuthUser();
            authUser.setEmail(restUser.getEmail());
            authUser.setSex(restUser.getSex());//0:默认；1：男；2：女
            authUser.setBirthday(restUser.getBirthday());
            authUser.setNickName(restUser.getName());
            authUserService.updateName(authUser);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RestUser user = new RestUser();
        user.setEmail(restUser.getEmail());
        result.setResult(restUser);
        return result;
    }

    @RequestMapping(value = "/updateSex",method = RequestMethod.POST)
    public ExecuteResult<RestUser> updateSex(@RequestBody RestUser restUser, HttpServletRequest request){
        restUser.initHeaderValue(request);
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail()) || null == restUser.getSex()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        try {
            AuthUser authUser = new AuthUser();
            authUser.setEmail(restUser.getEmail());
            authUser.setSex(restUser.getSex());//0:默认；1：男；2：女
            authUser.setBirthday(restUser.getBirthday());
            authUserService.updateSex(authUser);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RestUser user = new RestUser();
        user.setEmail(restUser.getEmail());
        result.setResult(restUser);
        return result;
    }

    @RequestMapping(value = "/updateBirthday",method = RequestMethod.POST)
    public ExecuteResult<RestUser> updateBirthday(@RequestBody RestUser restUser, HttpServletRequest request){
        restUser.initHeaderValue(request);
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail()) || StringUtils.isBlank(restUser.getBirthday()) || restUser.getBirthday().length() != 10){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        try {
            AuthUser authUser = new AuthUser();
            authUser.setEmail(restUser.getEmail());
            authUser.setSex(restUser.getSex());
            authUser.setBirthday(restUser.getBirthday());
            authUserService.updateBirthday(authUser);
        } catch (Exception e) {
            e.printStackTrace();
        }
        result.setResult(restUser);
        return result;
    }

    @RequestMapping(value = "/updatePasswd",method = RequestMethod.POST)
    public ExecuteResult<RestUser> updatePassword(@RequestBody RestUser restUser, HttpServletRequest request){
        restUser.initHeaderValue(request);
        ExecuteResult<RestUser> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restUser.getEmail()) || StringUtils.isBlank(restUser.getPasswdOriginal()) || StringUtils.isBlank(restUser.getPasswd())
                    || StringUtils.isBlank(restUser.getPasswdAgain())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (restUser.getPasswd().equals(restUser.getPasswdAgain()) == false){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (restUser.getPasswd().length() < 6 || restUser.getPasswd().length() > 20){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        try {
            AuthUser user = authUserService.getUserByEmail(restUser.getEmail()).getResult();

            if (null == user){
                result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
                return result;
            }
            if (!authUserService.isPasswordValid(user.getUserPassword(),restUser.getPasswdOriginal(),user.getSalt())){
                result.addErrorMessage(ReturnInfo.ERROR_PASSWD_INVALID);
                return result;
            }
            AuthUser authUser = new AuthUser();
            authUser.setEmail(restUser.getEmail());
            authUser.setUserPassword(restUser.getPasswd());

            authUserService.updatePasswd(authUser);
            //修改密码，则解绑所有设备
            UserInfo userInfo = authUserService.loadUserInfoByEmail(restUser.getEmail());
            if (null != userInfo){
                userInfo.getCustomUserDetail().setDeviceLimitList(new ArrayList<>());
                authUserService.updateUserInfo(userInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        result.setResult(new RestUser());
        return result;
    }

    @RequestMapping(value = "/myDevice",method = RequestMethod.POST)
    public PageResult<RestDeviceLimit> myDevice(@RequestBody RestUser restUser, HttpServletRequest request){
        restUser.initHeaderValue(request);
        PageResult<RestDeviceLimit> result = new PageResult<>();
        if (StringUtils.isBlank(restUser.getEmail()) ){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        try {
            UserInfo userInfo = authUserService.loadUserInfoByEmail(restUser.getEmail());
            if (null == userInfo){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
            List<RestDeviceLimit> list = new ArrayList<>();
            for (DeviceLimit deviceLimit : userInfo.getCustomUserDetail().getDeviceLimitList()){
                RestDeviceLimit obj = new RestDeviceLimit();
                obj.initFrom(deviceLimit);
                list.add(obj);
            }
            result.setResult(list);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @RequestMapping(value = "/removeDevice",method = RequestMethod.POST)
    public PageResult<RestDeviceLimit> removeDevice(@RequestBody RestDeviceLimit restUser, HttpServletRequest request){
        restUser.initHeaderValue(request);
        PageResult<RestDeviceLimit> result = new PageResult<>();
        if (StringUtils.isBlank(restUser.getEmail()) || StringUtils.isBlank(restUser.getDeviceCode())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        try {
            UserInfo userInfo = authUserService.loadUserInfoByEmail(restUser.getEmail());
            if (null == userInfo){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
            List<RestDeviceLimit> list = new ArrayList<>();
            int removeIndex = -1;
            for (int i = 0; i < userInfo.getCustomUserDetail().getDeviceLimitList().size(); i++) {
                DeviceLimit deviceLimit = userInfo.getCustomUserDetail().getDeviceLimitList().get(i);
                if (deviceLimit.getDevice().equals(restUser.getDeviceCode())){
                    removeIndex = i;
                    continue;
                }
                RestDeviceLimit obj = new RestDeviceLimit();
                obj.initFrom(deviceLimit);
                list.add(obj);
            }
            if (removeIndex >= 0){
                userInfo.getCustomUserDetail().getDeviceLimitList().remove(removeIndex);
                authUserService.updateUserInfo(userInfo);
            }
            result.setResult(list);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    private void sendActiveEmail(EmailHistory history, String lang, AuthUserService authUserService) {
        try {
            String json = JSONObject.toJSONString(history);
            String text = EmailUtils.encriptEmailCode(json, HomeController.getEmailClickAesKey(history));

            text = URLEncoder.encode(text, "UTF-8");

            String basePath = WebUtil.getBasePath();

            String url = basePath + HomeController.getEmailUrlAction(history) + text;

            Mail mail = new Mail();
            mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
            mail.setSubject(HomeController.getEmailObjectByLang(history,lang));
            mail.setTo(history.getEmail());//
            //mail.setTo("<EMAIL>");
            mail.setFtl(HomeController.getEmailFtlByLang(history,lang));
            Map<String, Object> model = new HashMap<>();
            model.put("emailToName", authUserService.getUserNameByEmail(history.getEmail()));
            model.put("signature", "恩道电子书");
            model.put("location", DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
            model.put("emailUrl", url);
            model.put("home",basePath);

            mail.setModel(model);

            emailService.sendSimpleMessage(mail);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
