package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.Pair;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestFeedback;
import com.aaron.spring.api.v4.model.RestStuEnroll;
import com.aaron.spring.api.v4.model.RestStuInfo;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.EnyanFeedback;
import com.aaron.spring.model.StuCheckin;
import com.aaron.spring.model.StuEnroll;
import com.aaron.spring.model.StuInfo;
import com.aaron.spring.service.EnyanFeedbackService;
import com.aaron.spring.service.StuEnrollService;
import com.aaron.spring.service.StuInfoService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2023/2/8
 * @Modified By:
 */
@Slf4j
@RestController("RestCheckinControllerV4")
@RequestMapping(path = {"/api/v4/checkin","/front/v4/checkin"})
public class RestCheckinController extends RestBaseController {
	@Resource
	private EnyanFeedbackService enyanFeedbackService;

	@Resource
	private StuInfoService stuInfoService;

	@Resource
	private StuEnrollService stuEnrollService;

	@LoginAnonymous
	@RequestMapping(value = "/login",method = RequestMethod.POST)
	public ExecuteResult<RestStuInfo> login(@RequestBody RestStuInfo restObj, HttpServletRequest request){
		ExecuteResult<RestStuInfo> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getPasswd()) ){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		StuInfo obj = stuInfoService.getRecordByEmail(restObj.getEmail());
		if (null == obj){
			result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
			return result;
		}
		if (obj.getPassword().equals(restObj.getPasswd()) == false){
			result.addErrorMessage(ReturnInfo.ERROR_NAME_PASSWD_INVALID);
			return result;
		}
		restObj.initFrom(obj);
		restObj.initAddressInfo();
		restObj.setStuId(null);
		restObj.setTerms(null);
		restObj.setPasswd(null);
		result.setResult(restObj);
		return result;
	}

	@LoginAnonymous
	@RequestMapping(value = "/enroll",method = RequestMethod.POST)
	public ExecuteResult<RestStuEnroll> enroll(@RequestBody RestStuEnroll restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<RestStuEnroll> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		StuEnroll obj = stuEnrollService.getRecordByEmail(restObj.getEmail());
		if (null == obj){
			result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
			return result;
		}
		Date current = new Date();
		Pair<Boolean, Integer> status = this.getStatusByType(obj,EBookConstant.CheckIn.Type.ENROLL, current.getTime(), false);
		if (status.getKey() == true){
			restObj.setStatus(status.getValue());
			restObj.setTime(current.getTime());
			restObj.setEnrollCode(obj.getEnrollCode());
			result.setResult(restObj);
			return result;
		}
		stuEnrollService.updateRecordToEnrollByEmail(obj,status.getValue(),current);
		restObj.setStatus(status.getValue());
		restObj.setType(Integer.parseInt(EBookConstant.CheckIn.Type.ENROLL));
		restObj.setTime(current.getTime());
		restObj.setEnrollCode(obj.getEnrollCode());
		result.setResult(restObj);
		return result;
	}

	@LoginAnonymous
	@RequestMapping(value = "/checkin",method = RequestMethod.POST)
	public ExecuteResult<RestStuEnroll> checkin(@RequestBody RestStuEnroll restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<RestStuEnroll> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		StuEnroll obj = stuEnrollService.getRecordByEmail(restObj.getEmail());
		if (null == obj){
			result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
			return result;
		}
		Long current = System.currentTimeMillis();
		Pair<Boolean, Integer> status  = this.getStatusByType(obj,restObj.getType()+"", current, false);
		if (status.getKey() == true){
			restObj.setStatus(status.getValue());
			restObj.setTime(current);
			restObj.setEnrollCode(obj.getEnrollCode());
			result.setResult(restObj);
			return result;
		}
		stuEnrollService.updateRecordToCheckinByEmail(obj,restObj.getType(),status.getValue(),current);
		restObj.setStatus(status.getValue());
		restObj.setTime(current);
		result.setResult(restObj);
		return result;
	}

	@LoginAnonymous
	@RequestMapping(value = "/manual",method = RequestMethod.POST)
	public ExecuteResult<RestStuEnroll> manual(@RequestBody RestStuEnroll restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<RestStuEnroll> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEnrollCode()) || null == restObj.getType()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		StuEnroll obj = stuEnrollService.getRecordByEnrollCode(restObj.getEnrollCode());
		if (null == obj){
			result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
			return result;
		}
		Date currentDate = new Date();
		String type = restObj.getType()+"";
		Pair<Boolean, Integer> status = this.getStatusByType(obj,type, currentDate.getTime(), true);
		if (status.getKey() == true){
			restObj.setStatus(status.getValue());
			restObj.setTime(currentDate.getTime());
			restObj.setEnrollCode(obj.getEnrollCode());
			result.setResult(restObj);
			return result;
		}
		if (EBookConstant.CheckIn.Type.ENROLL.equals(type)){//如果是报到，则单独处理
			stuEnrollService.updateRecordToEnrollByEnrollCode(obj,status.getValue(),currentDate);
		}else{
			stuEnrollService.updateRecordToCheckinByEnrollCode(obj,restObj.getType(),status.getValue(),currentDate.getTime());
		}
		restObj.setStatus(status.getValue());
		restObj.setTime(currentDate.getTime());
		result.setResult(restObj);
		return result;
	}

	@LoginAnonymous
	@RequestMapping(value = "/history",method = RequestMethod.POST)
	public ExecuteResult<List<StuCheckin>> history(@RequestBody RestStuEnroll restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<List<StuCheckin>> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		StuEnroll obj = stuEnrollService.getRecordByEmail(restObj.getEmail());
		if (null == obj){
			result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
			return result;
		}
		result.setResult(obj.getStuCheckinInfo().getCheckinList());
		return result;
	}

	@LoginAnonymous
	@RequestMapping(value = "/getStudentInfo",method = RequestMethod.POST)
	public ExecuteResult<RestStuInfo> getStudent(@RequestBody RestStuInfo restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<RestStuInfo> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEnrollCode())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		StuInfo obj = stuInfoService.getRecordByEnrollCode(restObj.getEnrollCode());
		if (null == obj){
			result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
			return result;
		}
		restObj.initFrom(obj);
		result.setResult(restObj);
		return result;
	}

	@RequestMapping(value = "/add",method = RequestMethod.POST)
	public ExecuteResult<RestFeedback> add(@RequestBody RestFeedback restObj, HttpServletRequest request){
		ExecuteResult<RestFeedback> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getContent()) || null == restObj.getType() 
				    || StringUtils.isBlank(restObj.getFeedEmail()) || restObj.getContent().length() > 500){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanFeedback feedback = new EnyanFeedback();
		feedback.setContent(restObj.getContent());
		feedback.setFeedEmail(restObj.getFeedEmail());
		feedback.setDeviceFrom(restObj.getDeviceFrom());
		feedback.setDeviceVersion(restObj.getDeviceVersion());
		feedback.setType(restObj.getType());
		feedback.setIsDeleted(0);
		feedback.setDoneType(0);
		feedback.setCreateAt(new Date());
		enyanFeedbackService.addRecord(feedback);
		return result;
	}
	/**
	 * <p></p>
	 * @param type
	 * @param current
	 * @param isManual 迟到后，同工协助签到则为“补签”，否则为“迟到”
	 * @return <是否已经签到过,状态>
	 * @since : 2023/5/11
	 **/
	private Pair<Boolean,Integer> getStatusByType(StuEnroll enroll, String type, Long current, Boolean isManual){
		Integer typeInt = Integer.parseInt(type);
		List<StuCheckin> checkinList = enroll.getStuCheckinInfo().getCheckinList().stream().filter(obj -> obj.getType() == typeInt).toList();
		if (checkinList.isEmpty() == false){
			StuCheckin checkin = checkinList.get(0);
			if (isManual == false){//不是手动签到
				if (checkin.getStatus() != EBookConstant.CheckIn.Status.DEFAULT
						    && checkin.getStatus() != EBookConstant.CheckIn.Status.ABSENCE){//如果已经扫过码，则不需要重新扫码
					return new Pair<>(true,checkin.getStatus());
				}
			}
		}

		Integer defaultStatus = isManual? EBookConstant.CheckIn.Status.SUPPLEMENT : EBookConstant.CheckIn.Status.LATE;
		Long compare = EBookConstant.CheckIn.CheckinTimeMap.get(type);
		if (null == compare){
			return new Pair<>(false,defaultStatus);
		}
		if (current < compare){//签到
			//return EBookConstant.CheckIn.Status.ATTENDANCE;
			return new Pair<>(false,EBookConstant.CheckIn.Status.ATTENDANCE);
		}
		//return defaultStatus;//迟到或补签
		return new Pair<>(false,defaultStatus);
	}
}
