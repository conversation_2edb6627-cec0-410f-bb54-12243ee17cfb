package com.aaron.spring.api.v4.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.EnyanComment;
import com.aaron.spring.model.EnyanFeedback;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serial;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/2/8
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestComment extends RestBaseDTO {
	@Serial
	private static final long serialVersionUID = -810235216032802834L;
	private Long dataId;

	private String nickName;

	private Integer sex;

	private Long bookId;

	private String title;

	private String content;

	private String star;

	private Integer commentCount;

	private Integer likeCount;

	private Long parentId;

	private Integer isMine;

	private Date createAt;

	private Integer isDeleted;

	public void initFrom(EnyanComment obj){
		this.dataId = obj.getDataId();
		//this.setEmail(obj.getEmail());
		this.nickName = obj.getNickName();
		this.sex = obj.getSex();
		if (obj.getIsDeleted() == 0){
			this.title = obj.getTitle();
			this.content = obj.getContent();
		}
		this.bookId = obj.getBookId();
		this.star = obj.getStar();
		this.commentCount = obj.getCommentCount() > 0 ? obj.getCommentCount() : 0;
		this.likeCount = obj.getLikeCount();
		this.parentId = obj.getParentId();
		this.createAt = obj.getCreateAt();
		this.isDeleted = obj.getIsDeleted();
	}

	public void initFrom(EnyanComment obj,String ownEmail){
		this.initFrom(obj);
		if (obj.getEmail().equals(ownEmail)) {
			this.isMine = 1;
		}else{
			this.isMine = 0;
		}
	}

	public Long getDataId() {
		return dataId;
	}

	public void setDataId(Long dataId) {
		this.dataId = dataId;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public Long getBookId() {
		return bookId;
	}

	public void setBookId(Long bookId) {
		this.bookId = bookId;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getStar() {
		return star;
	}

	public void setStar(String star) {
		this.star = star;
	}

	public Integer getCommentCount() {
		return commentCount;
	}

	public void setCommentCount(Integer commentCount) {
		this.commentCount = commentCount;
	}

	public Integer getLikeCount() {
		return likeCount;
	}

	public void setLikeCount(Integer likeCount) {
		this.likeCount = likeCount;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Date getCreateAt() {
		return createAt;
	}

	public void setCreateAt(Date createAt) {
		this.createAt = createAt;
	}

	public Integer getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Integer getIsMine() {
		return isMine;
	}

	public void setIsMine(Integer isMine) {
		this.isMine = isMine;
	}

	public static  void main(String[] args){
		//printInit("com.aaron.spring.api.v4.model.RestComment","EnyanComment");
		printSwiftModel("com.aaron.spring.api.v4.model.RestComment","EnyanComment");
	}
}
