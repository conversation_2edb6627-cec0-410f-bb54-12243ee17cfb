package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.*;
import com.aaron.spring.common.OrderUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.alipay.mobile.OrderUtils1_0_Global;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: Aaron <PERSON>
 * @Description:
 * @Date: Created in  2021/4/23
 * @Modified By:
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestOrder<T> extends RestBaseDTO {
   private static final long serialVersionUID = -8951216004246924175L;
   private Long orderId;

   private String orderNum;

   private Long userId;

   private String userName;

   private String orderTitle;

   private Long purchasedAt;

   private Long expiredAt;

   private BigDecimal orderDiscount;//促销减

   private BigDecimal orderTotal;//合计

   private BigDecimal orderCoupon;//优惠码

   private BigDecimal orderPay;//支付

   private BigDecimal priceCurrency;//新的价格

//   private String orderCurrency;

   private Integer isValid;

   private Integer isPaid;

   private String payInfo;

   private String orderType;

//   private Byte isCounted;

//   private String orderDetail;

   private OrderTitleInfo orderTitleInfo;

   private OrderDetailInfo orderDetailInfo;

//   private OrderPayInfo orderPayInfo;

   /**
    * 订单里的书籍列表
    * */
   @JsonIgnore
   @JSONField(serialize = false)
   private Map<Long, EnyanBook> bookMap;

//   private List<RestBook> records;

   private List<T> list;

   private String couponCode;

   private Integer fromCart;

   private String alipaySign;

   private String alipaySignHK;

   private String cardSign;

   private Integer payType;

   public void initFrom(EnyanOrder obj, CurrencyType currencyType){
      this.initDefault(obj);
      if (Constant.BYTE_VALUE_0.equals(obj.getIsPaid()) && Constant.BYTE_VALUE_1.equals(obj.getIsValid())){//没有付费的合法订单才需要汇率转化金额
         this.priceCurrency = currencyType.getCurrencyByPriceValue(obj.getOrderDetailInfo().getAmountHkd());
      }
   }

   private void initDefault(EnyanOrder obj){
      this.orderId = obj.getOrderId();
      this.orderNum = obj.getOrderNum();
      this.userId = obj.getUserId();
//      this.userName = obj.getUserName();
      this.setEmail(obj.getUserEmail());
//      this.orderTitle = obj.getOrderTitle();
      if (null != obj.getPurchasedAt()){
         this.purchasedAt = obj.getPurchasedAt().getTime();
      }
      if (null != obj.getExpiredAt()){
         this.expiredAt = obj.getExpiredAt().getTime();
      }
      this.orderDiscount = obj.getOrderDiscount();
      this.orderTotal = obj.getOrderTotal();
      //this.orderCurrency = obj.getOrderCurrency();
      this.isValid = Constant.BYTE_VALUE_1.equals(obj.getIsValid()) ? 1 : 0;
      this.isPaid = Constant.BYTE_VALUE_1.equals(obj.getIsPaid()) ? 1 : 0;
      if (null != obj.getOrderPayInfo() && null != obj.getOrderPayInfo().getCharge()){
         this.payInfo = obj.getOrderPayInfo().getCharge().payTypeDescription();
         this.payType = obj.getOrderPayInfo().getCharge().getPayType();
      }
      if (null != obj.getOrderType()){
         switch (obj.getOrderType()){
            case EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY:
               this.orderType = EBookConstant.OrderType.ORDER_NAME_EBOOK_SINGLE_BUY;//电子书单本
               break;
            case EBookConstant.OrderType.ORDER_REDEEM_BUY:
               this.orderType = EBookConstant.OrderType.ORDER_NAME_REDEEM_BUY;//购买兑换码
               break;
            case EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE:
               this.orderType = EBookConstant.OrderType.ORDER_NAME_REDEEM_EXCHANGE;// 兑换码兑换
               break;
            case EBookConstant.OrderType.ORDER_BOOK_BUY:
               this.orderType = EBookConstant.OrderType.ORDER_NAME_BOOK_BUY;// 书籍直接购买
               break;
            case EBookConstant.OrderType.ORDER_EBOOK_SET_BUY:
               this.orderType = EBookConstant.OrderType.ORDER_NAME_EBOOK_SET_BUY;// 电子书套装
               break;
            case EBookConstant.OrderType.ORDER_FROM_RENT_BUY:
               this.orderType = EBookConstant.OrderType.ORDER_NAME_FROM_RENT_BUY;// 先租后买转换的购买订单
               break;
         }
      }
      list = new ArrayList<>();
      if (null != obj.getOrderDetailInfo().getCartDiscountInfoList()){
         for (CartDiscountInfo cartDiscountInfo:obj.getOrderDetailInfo().getCartDiscountInfoList()){
            /*
            for (ProductInfo productInfo : cartDiscountInfo.getProductInfoList()){
               RestBook book = new RestBook();
               book.initFromProduct(productInfo);
               list.add((T) book);
            }*/
            RestOrderDiscount orderDiscount = new RestOrderDiscount();
            orderDiscount.initFrom(cartDiscountInfo);
            list.add((T)orderDiscount);
         }
      }else {
         CartDiscountInfo cartDiscountInfo = new CartDiscountInfo();
         cartDiscountInfo.setProductInfoList(obj.getOrderDetailInfo().getProductInfoList());

         RestOrderDiscount orderDiscount = new RestOrderDiscount();
         orderDiscount.initFrom(cartDiscountInfo);
         list.add((T)orderDiscount);
         /*
         for (ProductInfo productInfo : obj.getOrderDetailInfo().getProductInfoList()){
            RestBook book = new RestBook();
            book.initFromProduct(productInfo);
            list.add((T)book);
         }*/
      }
      if (null != obj.getOrderDetailInfo().getAmountHkdMiddle() && obj.getOrderDetailInfo().getAmountHkdMiddle().floatValue() > 0){
         this.orderTotal = obj.getOrderDetailInfo().getAmountHkdMiddle();
      }else {
         this.orderTotal = obj.getOrderDetailInfo().getAmountHkd();
      }

      if (null !=obj.getOrderTitleInfo()){
         this.orderTitle = obj.getOrderTitleInfo().getProductDescription();
      }

      this.orderDiscount = obj.getOrderDetailInfo().getAmountDiscount();
      this.orderCoupon = obj.getOrderDetailInfo().getAmountCoupon();
      this.orderPay = obj.getOrderDetailInfo().getAmountHkd();
      /*
      for (CartDiscountInfo cartDiscountInfo: orderDetailInfo.getCartDiscountInfoList()){
         for (ProductInfo productInfo : cartDiscountInfo.getProductInfoList()){
            RestBook book = new RestBook();
            book.initFromProduct(productInfo);
            records.add(book);
         }
      }*/
//      this.orderTitleInfo = obj.getOrderTitleInfo();
//      this.orderDetailInfo = obj.getOrderDetailInfo();
      this.bookMap = obj.getBookMap();
      if (this.getIsPaid() == 0 && isValid == 1){
         this.alipaySign = OrderUtils1_0_Global.getSignedOrderInfo(obj);
         this.alipaySignHK = OrderUtils1_0_Global.getSignedOrderInfo(obj,true);
         this.cardSign = OrderUtil.creditSign(obj);
      }
   }

   public Long getOrderId() {
      return orderId;
   }

   public void setOrderId(Long orderId) {
      this.orderId = orderId;
   }

   public String getOrderNum() {
      return orderNum;
   }

   public void setOrderNum(String orderNum) {
      this.orderNum = orderNum;
   }

   public Long getUserId() {
      return userId;
   }

   public void setUserId(Long userId) {
      this.userId = userId;
   }

   public String getUserName() {
      return userName;
   }

   public void setUserName(String userName) {
      this.userName = userName;
   }

   public String getOrderTitle() {
      return orderTitle;
   }

   public void setOrderTitle(String orderTitle) {
      this.orderTitle = orderTitle;
   }

   public Long getPurchasedAt() {
      return purchasedAt;
   }

   public void setPurchasedAt(Long purchasedAt) {
      this.purchasedAt = purchasedAt;
   }

   public Long getExpiredAt() {
      return expiredAt;
   }

   public void setExpiredAt(Long expiredAt) {
      this.expiredAt = expiredAt;
   }

   public BigDecimal getOrderDiscount() {
      return orderDiscount;
   }

   public void setOrderDiscount(BigDecimal orderDiscount) {
      this.orderDiscount = orderDiscount;
   }

   public BigDecimal getOrderTotal() {
      return orderTotal;
   }

   public void setOrderTotal(BigDecimal orderTotal) {
      this.orderTotal = orderTotal;
   }

   public Integer getIsValid() {
      return isValid;
   }

   public void setIsValid(Integer isValid) {
      this.isValid = isValid;
   }

   public Integer getIsPaid() {
      return isPaid;
   }

   public void setIsPaid(Integer isPaid) {
      this.isPaid = isPaid;
   }

   public String getPayInfo() {
      return payInfo;
   }

   public void setPayInfo(String payInfo) {
      this.payInfo = payInfo;
   }

   public String getOrderType() {
      return orderType;
   }

   public void setOrderType(String orderType) {
      this.orderType = orderType;
   }

   public OrderTitleInfo getOrderTitleInfo() {
      return orderTitleInfo;
   }

   public void setOrderTitleInfo(OrderTitleInfo orderTitleInfo) {
      this.orderTitleInfo = orderTitleInfo;
   }

   public OrderDetailInfo getOrderDetailInfo() {
      return orderDetailInfo;
   }

   public void setOrderDetailInfo(OrderDetailInfo orderDetailInfo) {
      this.orderDetailInfo = orderDetailInfo;
   }

   public Map<Long, EnyanBook> getBookMap() {
      return bookMap;
   }

   public void setBookMap(Map<Long, EnyanBook> bookMap) {
      this.bookMap = bookMap;
   }

   public BigDecimal getOrderCoupon() {
      return orderCoupon;
   }

   public void setOrderCoupon(BigDecimal orderCoupon) {
      this.orderCoupon = orderCoupon;
   }

   public BigDecimal getOrderPay() {
      return orderPay;
   }

   public void setOrderPay(BigDecimal orderPay) {
      this.orderPay = orderPay;
   }

   public List<T> getList() {
      return list;
   }

   public void setList(List<T> list) {
      this.list = list;
   }

   public String getCouponCode() {
      return couponCode;
   }

   public void setCouponCode(String couponCode) {
      this.couponCode = couponCode;
   }

   public Integer getFromCart() {
      return fromCart;
   }

   public void setFromCart(Integer fromCart) {
      this.fromCart = fromCart;
   }

   public BigDecimal getPriceCurrency() {
      return priceCurrency;
   }

   public void setPriceCurrency(BigDecimal priceCurrency) {
      this.priceCurrency = priceCurrency;
   }

   public String getAlipaySign() {
      return alipaySign;
   }

   public void setAlipaySign(String alipaySign) {
      this.alipaySign = alipaySign;
   }

   public String getCardSign() {
      return cardSign;
   }

   public void setCardSign(String cardSign) {
      this.cardSign = cardSign;
   }

   public String getAlipaySignHK() {
      return alipaySignHK;
   }

   public void setAlipaySignHK(String alipaySignHK) {
      this.alipaySignHK = alipaySignHK;
   }

   public Integer getPayType() {
      return payType;
   }

   public void setPayType(Integer payType) {
      this.payType = payType;
   }

   public static void main(String[] args) {
      printInit("com.aaron.spring.api.v3.model.RestOrder","EnyanOrder");
   }
}
