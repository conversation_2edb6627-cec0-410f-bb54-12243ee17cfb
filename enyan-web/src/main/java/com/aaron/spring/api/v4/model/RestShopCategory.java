package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.CurrencyType;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanCategory;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/12
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestShopCategory extends RestBaseDTO {
   private static final long serialVersionUID = -7822502236717198925L;
   private Long categoryId;

   private String categoryNameSc;

   private String categoryNameTc;

   private String categoryNameEn;

   /**
    * 长banner
    * */
   private String bannerUrl;

   /**
    * 封面的URL
    * */
   private String bookCoverUrl;

   private List<RestBook> records ;

   public void initFrom(EnyanCategory obj, CurrencyType currencyType){
      this.categoryId = obj.getCategoryId();
      this.categoryNameSc = obj.getCategoryName();
      this.categoryNameTc = obj.getCategoryNameTc();
      this.categoryNameEn = obj.getCategoryNameEn();
      this.bannerUrl = obj.getBannerUrl();
      this.bookCoverUrl = obj.getBookCoverUrl();
      if (null != obj.getPage() && null != obj.getPage().getRecords()
                  && obj.getPage().getRecords().isEmpty() == false){
         List<RestBook> bookList = new ArrayList<>();
         for (Object book : obj.getPage().getRecords()){
            if (book instanceof EnyanBook){
               RestBook tmp =  new RestBook();
               tmp.initFrom((EnyanBook) book,currencyType);
               bookList.add(tmp);
            }
         }
         this.records = bookList;
      }else if (null != obj.getRecords() && obj.getRecords().isEmpty() == false){
         List<RestBook> bookList = new ArrayList<>();
         for (Object book : obj.getRecords()){
            if (book instanceof EnyanBook){
               RestBook tmp =  new RestBook();
               tmp.initFrom((EnyanBook) book,currencyType);
               bookList.add(tmp);
            }
         }
         this.records = bookList;
      }
   }

   public Long getCategoryId() {
      return categoryId;
   }

   public void setCategoryId(Long categoryId) {
      this.categoryId = categoryId;
   }

   public String getCategoryNameSc() {
      return categoryNameSc;
   }

   public void setCategoryNameSc(String categoryNameSc) {
      this.categoryNameSc = categoryNameSc;
   }

   public String getCategoryNameTc() {
      return categoryNameTc;
   }

   public void setCategoryNameTc(String categoryNameTc) {
      this.categoryNameTc = categoryNameTc;
   }

   public String getCategoryNameEn() {
      return categoryNameEn;
   }

   public void setCategoryNameEn(String categoryNameEn) {
      this.categoryNameEn = categoryNameEn;
   }

   public String getBannerUrl() {
      return bannerUrl;
   }

   public void setBannerUrl(String bannerUrl) {
      this.bannerUrl = bannerUrl;
   }

   public String getBookCoverUrl() {
      return bookCoverUrl;
   }

   public void setBookCoverUrl(String bookCoverUrl) {
      this.bookCoverUrl = bookCoverUrl;
   }

   public List<RestBook> getRecords() {
      return records;
   }

   public void setRecords(List<RestBook> records) {
      this.records = records;
   }

   public static void main(String[] args) {
//      printProperty("com.aaron.spring.api.v3.model.RestShopCategory","EnyanCategory");
      printInit("com.aaron.spring.api.v3.model.RestShopCategory","EnyanCategory");
   }
}
