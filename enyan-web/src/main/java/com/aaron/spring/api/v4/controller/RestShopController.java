package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.GeoIPRequired;
import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.Pair;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.CreditInfo;
import com.aaron.common.NameAndValue;
import com.aaron.common.OrderObj;
import com.aaron.drm.model.DrmInfo;
import com.aaron.exception.DownloadException;
import com.aaron.exception.RestException;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.RestCard;
import com.aaron.spring.api.RestList;
import com.aaron.spring.api.v4.model.*;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.common.PdfGenaratorUtil;
import com.aaron.spring.controller.ShopController;
import com.aaron.spring.model.*;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.*;
import com.aaron.spring.common.OrderUtil;
import com.aaron.util.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.stripe.config.StripeConfig;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.net.RequestOptions;
import com.stripe.util.StripeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BusinessException 捕获异常 9XX
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2021/4/12
 * @Modified By:
 */
@Slf4j
@RestController("RestShopControllerV4")
@RequestMapping(path = {"/api/v4/shop","/front/v4/shop"})
public class RestShopController extends RestBaseController{
    @Resource
    private EnyanBookBuyService enyanBookBuyService;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanOrderService enyanOrderService;

    @Resource
    private AuthUserService authUserService;

    @Resource
    private EnyanWishService enyanWishService;

    @Resource
    private EnyanCartService enyanCartService;

    @Resource
    private EnyanRedeemCodeService enyanRedeemCodeService;

    @Resource
    private EnyanCouponService enyanCouponService;

    @Resource
    private EnyanBookSetService enyanBookSetService;

    @Resource
    private EnyanBookListService enyanBookListService;

    @Resource
    private LogService  logService;

    @Resource
    private PdfGenaratorUtil pdfGenaratorUtil;

    @Resource
    private EnyanSubscriptionService enyanSubscriptionService;

    @GeoIPRequired
    @LoginAnonymous
    @RequestMapping(value = "/index",method = RequestMethod.POST)
    public ExecuteResult<RestShopIndex> index(@RequestBody RestBook restObj, HttpServletRequest request){
        ExecuteResult<RestShopIndex> result = new ExecuteResult<>();

        restObj.initHeaderValue(request);
        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());


        RestShopIndex shopIndex = new RestShopIndex();
        List<RestShopCategory> shopCategories = new ArrayList<>();
        for (EnyanCategory category: Constant.indexCategoryList){
            RestShopCategory shopCategory = new RestShopCategory();
            shopCategory.initFrom(category,currencyType);
            shopCategories.add(shopCategory);
        }
        shopIndex.setShopCategories(shopCategories);

        List<RestBook> editorList = new ArrayList<>();
        for (EnyanBook book:Constant.indexRecommendedList){
            RestBook tmp = new RestBook();
            tmp.initFrom(book,currencyType);
            editorList.add(tmp);
        }
        shopIndex.setEditorList(editorList);
        result.setResult(shopIndex);
        return result;
   }

    @GeoIPRequired
    @LoginAnonymous
    @RequestMapping(value = "/base",method = RequestMethod.POST)
    public ExecuteResult<RestShopIndex> base(@RequestBody RestBook restObj, HttpServletRequest request){
        ExecuteResult<RestShopIndex> result = new ExecuteResult<>();

        restObj.initHeaderValue(request);
        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

        RestShopIndex shopIndex = Constant.shopIndexSet.get(currencyType.getCurrency());
        if (null != shopIndex){
            result.setResult(shopIndex);
            return result;
        }
        shopIndex = new RestShopIndex();
        if (Constant.shopIndex.isInit() == false){
            enyanBookService.initIndexAllInfo();
        }
        shopIndex.initWithShopIndex(Constant.shopIndex, currencyType);
        Constant.shopIndexSet.put(currencyType.getCurrency(),shopIndex);
        result.setResult(shopIndex);
        return result;
    }

    @GeoIPRequired
    @LoginAnonymous
    @RequestMapping(value = "/bookCategory",method = RequestMethod.POST)
    public PageResult<RestBook> bookCategory(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        PageResult<RestBook> pageResult = new PageResult<>();
        if (null == restObj.getPage()){
            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }

        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
        EnyanBook enyanBook = new EnyanBook();
        Page<EnyanBook> page = new Page();
        page.setCurrentPage(restObj.getPage());
        page.setPageSize(pageResult.getPageSize());
        enyanBook.setPage(page);
        enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);

        if (null != restObj.getCategoryId() && restObj.getCategoryId() > 0){
            enyanBook.setCategoryId(restObj.getCategoryId());
        }
        if (null != restObj.getFreeType() && restObj.getFreeType() == 1){//是否免费
            enyanBook.setPrice(Constant.VALUE_0);
        }
        if (null != restObj.getSalesModel() && restObj.getSalesModel() == 1){//是否预售
            enyanBook.setSalesModel(1);
        }
        if (null != restObj.getDiscountType() && restObj.getDiscountType() == 1){//N件折商品
            enyanBook.setDiscountIsValid(Constant.BYTE_VALUE_1);
        }
        if (null != restObj.getPublisherId() && restObj.getPublisherId() > 0){//根据出版社ID获取
            enyanBook.setPublisherId(restObj.getPublisherId());
        }
        if (null != restObj.getSetId() && restObj.getSetId() > 0){//根据出版社ID获取
            enyanBook.setSetId(restObj.getSetId());
        }
        if (null != restObj.getSpecialOffer()){
            enyanBook.setSpecialOffer(restObj.getSpecialOffer());
        }

        if (null != restObj.getOrderBy()){
            if (1 == restObj.getOrderBy()){//0:默认；1:最新；2：热门
                OrderObj orderObj = new OrderObj("opensale_at",InterfaceContant.OrderBy.DESC);
                enyanBook.addOrder(orderObj);
            }else if (2 == restObj.getOrderBy()){
                OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.DESC);
                enyanBook.addOrder(orderObj);
            }else {
                OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
                enyanBook.addOrder(orderObj);
            }
        }else{
            OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }
        OrderObj orderObjId = new OrderObj("book_id",InterfaceContant.OrderBy.ASC);
        enyanBook.addOrder(orderObjId);

        if (StringUtils.isNotBlank(restObj.getBookLanguage())){//语言
            switch (restObj.getBookLanguage()){
                case "sc":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_1);
                    break;
                case "tc":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_2);
                    break;
                case "en":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_3);
                    break;
            }
        }

//        OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
//        enyanBook.addOrder(orderObj);
        page = enyanBookService.queryRecords(enyanBook.getPage(),enyanBook);
        for (EnyanBook book : page.getRecords()){
            book.resetByArea(restObj.getArea());
            RestBook tmp = new RestBook();
            tmp.initFrom(book,currencyType);
            tmp.setRecommendedCaption(book.getRecommendedCaption());
            pageResult.getResult().add(tmp);
        }
        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());
        if (page.getTotalRecord() > 0 && null != restObj.getSetId()){//有书系信息
            EnyanBookSet bookSet = enyanBookSetService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
            if (null != bookSet ){
                pageResult.setSuccessMessage(bookSet.getSetAbstract());
            }
        }
        return pageResult;
    }

    @GeoIPRequired
    @LoginAnonymous
    @RequestMapping(value = "/bookSearch",method = RequestMethod.POST)
    public PageResult<RestBook> bookSearch(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        PageResult<RestBook> pageResult = new PageResult<>();
        if (null == restObj.getPage() || StringUtils.isBlank(restObj.getName())){
            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }

        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
        EnyanBook enyanBook = new EnyanBook();
        Page<EnyanBook> page = new Page();
        page.setCurrentPage(restObj.getPage());
        page.setPageSize(pageResult.getPageSize());
        enyanBook.setPage(page);
        enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);
        enyanBook.setSearchText(restObj.getName());

        if (null != restObj.getCategoryId() && restObj.getCategoryId() > 0){
            enyanBook.setCategoryId(restObj.getCategoryId());
        }
        if (null != restObj.getFreeType() && restObj.getFreeType() == 1){//是否免费
            enyanBook.setPrice(Constant.VALUE_0);
        }
        if (null != restObj.getSalesModel() && restObj.getSalesModel() == 1){//是否预售
            enyanBook.setSalesModel(1);
        }
        if (null != restObj.getDiscountType() && restObj.getDiscountType() == 1){//N件折商品
            enyanBook.setDiscountIsValid(Constant.BYTE_VALUE_1);
        }
        if (null != restObj.getOrderBy()){///0:默认；1:最新；2：热门
            if (restObj.getOrderBy() == 1){
                OrderObj orderObj = new OrderObj("opensale_at",InterfaceContant.OrderBy.DESC);
                enyanBook.addOrder(orderObj);
            }else if (restObj.getOrderBy() == 2){
                OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.DESC);
                enyanBook.addOrder(orderObj);
            }else {
                OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
                enyanBook.addOrder(orderObj);
            }
        }
        if (StringUtils.isNotBlank(restObj.getBookLanguage())){//语言
            switch (restObj.getBookLanguage()){
                case "sc":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_1);
                    break;
                case "tc":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_2);
                    break;
                case "en":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_3);
                    break;
            }
        }
        page = enyanBookService.searchBookByTitleOrAuthor(enyanBook);
        for (EnyanBook book : page.getRecords()){
            book.resetByArea(restObj.getArea());
            RestBook tmp = new RestBook();
            tmp.initFrom(book,currencyType);
            tmp.setRecommendedCaption(book.getRecommendedCaption());
            pageResult.getResult().add(tmp);
        }
        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());
        return pageResult;
    }

    @GeoIPRequired
    @LoginAnonymous
    @RequestMapping(value = "/bookInfo",method = RequestMethod.POST)
    public ExecuteResult<RestBook> bookInfo(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<RestBook> result = new ExecuteResult<>();
        if (null == restObj.getBookId() ){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
        EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(restObj.getBookId()).getResult();
        if (null == enyanBook){
            result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
            return result;
        }
        enyanBook.resetByArea(restObj.getArea());
        if (StringUtils.isNotBlank(restObj.getEmail())){
            long countOfBook = enyanBookBuyService.countOfBookByEmailAndBookId(restObj.getEmail(), restObj.getBookId());
            if (countOfBook > 0){
                restObj.setHasBuy(1);
            }else {
                restObj.setHasBuy(0);
            }
        }else{
            restObj.setHasBuy(0);
        }
        if (0 == restObj.getHasBuy()){//未购买的书籍才需要检查是否添加购物车
            if (StringUtils.isNotBlank(restObj.getEmail())){
                long countOfCart = enyanCartService.countOfCartByEmailAndBookId(restObj.getEmail(), restObj.getBookId());
                if (countOfCart > 0){
                    restObj.setHasAddCart(1);
                }else {
                    restObj.setHasAddCart(0);
                }
            }else{
                restObj.setHasAddCart(0);
            }
        }else {
            restObj.setHasAddCart(1);
        }

        if (StringUtils.isNotBlank(restObj.getEmail())){
            long countOfWish = enyanWishService.countOfWishByEmailAndBookId(restObj.getEmail(), restObj.getBookId());
            if (countOfWish > 0){
                restObj.setHasAddFavorite(1);
            }else {
                restObj.setHasAddFavorite(0);
            }
        }else {
            restObj.setHasAddFavorite(0);
        }

        restObj.initFrom(enyanBook, currencyType);
        restObj.setBookDescription(enyanBook.getBookDescription());
        restObj.setRecommendedCaption(enyanBook.getRecommendedCaption());
        restObj.setBookCatalogue(enyanBook.getBookCatalogue());
        restObj.setBookAbstract(enyanBook.getBookAbstract());

        result.setResult(restObj);
        return result;
    }

    @RequestMapping(value = "/orders",method = RequestMethod.POST)
    public PageResult<RestOrder> orders(@RequestBody RestOrder restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        PageResult<RestOrder> pageResult = new PageResult<>();
        if (null == restObj.getPage() || StringUtils.isBlank(restObj.getEmail())){
            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }
        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
        EnyanOrder searchObj = new EnyanOrder();
        searchObj.setUserEmail(restObj.getEmail());
        Page<EnyanOrder> page = new Page();
        page.setCurrentPage(restObj.getPage());
        page.setPageSize(pageResult.getPageSize());
        searchObj.setPage(page);

        page = enyanOrderService.queryRecords(searchObj.getPage(),searchObj);
        for (EnyanOrder obj : page.getRecords()){
            RestOrder tmp = new RestOrder();
            tmp.initFrom(obj,currencyType);
            pageResult.getResult().add(tmp);
        }
        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());
        return pageResult;
    }

    @RequestMapping(value = "/orderCancel",method = RequestMethod.POST)
    public ExecuteResult<String> orderCancel(@RequestBody RestOrder restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getOrderId()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
//        enyanWishService.deleteByEmailAndBookIdList(restObj.getEmail(), restObj.getList());
        EnyanOrder order = enyanOrderService.queryRecordByPrimaryKey(restObj.getOrderId()).getResult();
        if (null == order || !order.getUserEmail().equals(restObj.getEmail())
                    || Constant.BYTE_VALUE_1.equals(order.getIsPaid()) || Constant.BYTE_VALUE_0.equals(order.getIsValid())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanOrder newOrder = new EnyanOrder();
        newOrder.setOrderId(restObj.getOrderId());
        newOrder.setIsValid(Constant.BYTE_VALUE_0);
        enyanOrderService.updateRecord(newOrder);

        String orderDetailInfo = order.getOrderDetail();
        OrderDetailInfo detailInfo = JSON.parseObject(orderDetailInfo, OrderDetailInfo.class);

        //订单取消，销量-1
        List<Long> bookIdList = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:detailInfo.getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                bookIdList.add(productInfo.getCode());
            }
        }
        if (bookIdList.size() > 0){
            enyanBookService.updateBookSaleVolumeMinus(bookIdList);
        }
        return result;
    }

    @RequestMapping(value = "/orderDel",method = RequestMethod.POST)
    public ExecuteResult<String> orderDel(@RequestBody RestOrder restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getOrderId()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
//        enyanWishService.deleteByEmailAndBookIdList(restObj.getEmail(), restObj.getList());
        EnyanOrder order = enyanOrderService.queryRecordByPrimaryKey(restObj.getOrderId()).getResult();
        if (null == order || !order.getUserEmail().equals(restObj.getEmail())
                    || Constant.BYTE_VALUE_1.equals(order.getIsPaid())
                    || Constant.BYTE_VALUE_1.equals(order.getIsValid())){//只能删除 已经 取消的订单
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanOrder newOrder = new EnyanOrder();
        newOrder.setOrderId(restObj.getOrderId());
        newOrder.setIsValid(Constant.BYTE_VALUE_0);
        enyanOrderService.deleteOrder(newOrder);

        String orderDetailInfo = order.getOrderDetail();
        OrderDetailInfo detailInfo = JSON.parseObject(orderDetailInfo, OrderDetailInfo.class);

        //订单取消，销量-1
        List<Long> bookIdList = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:detailInfo.getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                bookIdList.add(productInfo.getCode());
            }
        }
        if (bookIdList.size() > 0){
            enyanBookService.updateBookSaleVolumeMinus(bookIdList);
        }
        return result;
    }

    /**
     * <p>只用于普通书籍的修改，不能有套装书</p>
     * @param restList
     * @param request
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v3.model.RestOrder>
     * @since : 2021/4/26
     **/
    @GeoIPRequired
    @RequestMapping(value = "/orderSubmit",method = RequestMethod.POST)
    public ExecuteResult<RestOrder<RestBook>> orderSubmit(@RequestBody RestOrder<Long> restList, HttpServletRequest request){
        restList.initHeaderValue(request);

        ExecuteResult<RestOrder<RestBook>> result = new ExecuteResult<>();

        if (StringUtils.isBlank(restList.getEmail()) || null == restList.getList()|| restList.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanCoupon enyanCoupon = null;
        if (StringUtils.isNotBlank(restList.getCouponCode())){//处理优惠码
            enyanCoupon = enyanCouponService.getCouponByCode(restList.getCouponCode());
            String errorMsg = this.errorMsgOfCoupon(restList.getEmail(), restList.getCouponCode(),enyanCoupon);
            if (StringUtils.isNotBlank(errorMsg)){
                result.addErrorMessage(errorMsg);
                return result;
            }
        }

        boolean hasAddOrder;
        EnyanOrder order = new EnyanOrder();
        HashSet<Long> newBookIdHashSet = new HashSet<>();
        List<Long> newBookIdList ;

        List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restList.getEmail());
        HashSet<Long> booksHaveBuy = new HashSet<>();
        for (EnyanBookBuy bookBuy:bookBuyList){
            booksHaveBuy.add(bookBuy.getBookId());
        }
        for (Long bookId:restList.getList()){
            if (bookId < 0){//导入的书籍
                continue;
            }
            if (booksHaveBuy.contains(bookId)){//已经购买
                continue;
            }
            newBookIdHashSet.add(bookId);//去重
        }

        if (newBookIdHashSet.isEmpty()){//没有可以购买的书籍
            result.addErrorMessage(ReturnInfo.ERROR_BOOK_BUY_NONE);
            return result;
        }
        newBookIdList = new ArrayList<>(newBookIdHashSet);

        Collections.sort(newBookIdList, (o1, o2) -> o1.compareTo(o2));
        String orderBookHash = newBookIdList.hashCode()+"";

        EnyanOrder queryOrder = new EnyanOrder();
        queryOrder.setOrderBookHash(orderBookHash);
        queryOrder.setUserEmail(restList.getEmail());
        queryOrder.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);
        List<EnyanOrder> orderList = enyanOrderService.findRecordsByOrder(queryOrder);
        if (orderList == null || orderList.isEmpty()){//有时候会出现订单重复的问题，如果订单已经存在，则不需要保存
            hasAddOrder = false;
        }else {
            EnyanOrder orderDb = orderList.get(0);
            /*
            if (Constant.BYTE_VALUE_1.equals(orderDb.getIsPaid())
                        || Constant.BYTE_VALUE_1.equals(orderDb.getIsValid())){//有可能有人会重复刷订单，对已经购买的订单，后退刷新订单信息
                result.addErrorMessage(ReturnInfo.ERROR_ORDER_EXIST);
                return result;
            }*/
            if (Constant.BYTE_VALUE_1.equals(orderDb.getIsPaid())){//有可能有人会重复刷订单，对已经购买的订单，后退刷新订单信息
                result.addErrorMessage(ReturnInfo.ERROR_ORDER_EXIST);
                return result;
            }
            hasAddOrder = true;
            order.setOrderId(orderDb.getOrderId());
            order.setOrderNum(orderDb.getOrderNum());
        }

        List<EnyanBook> bookList = enyanBookService.findBookByIds(newBookIdList);
        for (EnyanBook book:bookList){
            if (EBookConstant.BookType.EBOOK_SET == book.getBookType()
                        || !Constant.BYTE_VALUE_1.equals(book.getShelfStatus())){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
        }
        /*
        List<EnyanBook> newBookList = new ArrayList<>();
        for (Long bookId:newBookIdList){
            EnyanBook book =  this.getBookById(bookId,bookList);
            if (null != book){
                newBookList.add(book);
            }
        }*/

        AuthUser authUser = authUserService.getUserByEmail(restList.getEmail()).getResult();
        if (null == authUser){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restList.getLang());
        for (EnyanBook book:bookList){
            book.resetByArea(restList.getArea());
            ProductInfo productInfo = new ProductInfo(book);
            cartInfo.addProduct(productInfo, 1);
        }

        OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);
        OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
        try {
            if (hasAddOrder == false){//已经添加的订单，不需要重新生成订单号
                order.setOrderNum(OrderUtil.getOrderId());
            }
        }catch (Exception e){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        order.setOrderBookHash(orderBookHash);

        order.setOrderTitleInfo(titleInfo);
        order.setOrderDetailInfo(detailInfo);

        order.setIsValid(Constant.BYTE_VALUE_1);
        order.setIsPaid(Constant.BYTE_VALUE_0);
        order.setIsCounted(Constant.BYTE_VALUE_0);

        order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);//只购买普通的单本书

        order.setUserId(authUser.getUserId());
//        order.setUserName(authUser.getEmail());
        order.setUserEmail(authUser.getEmail());
        order.setPurchasedAt(new Date());
        order.setExpiredAt(DateUtils.addHours(order.getPurchasedAt(),2));

        order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
        order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountHkd())));

        order.setOrderFrom(EBookConstant.OrderFrom.APP);
        /*
        EnyanOrder queryOrder = new EnyanOrder();
        queryOrder.setOrderNum(order.getOrderNum());
        queryOrder.setUserEmail(order.getUserEmail());
        List<EnyanOrder> orderList = enyanOrderService.selectByExample(queryOrder);
        if (orderList == null || orderList.isEmpty()){//有时候会出现订单重复的问题，如果订单已经存在，则不需要保存
            ExecuteResult<EnyanOrder> resultOrder = enyanOrderService.addRecord(order);
            if (!resultOrder.isSuccess()){
                log.error(result.getErrorMessageString());
            }
        }else {
            EnyanOrder orderDb = orderList.get(0);
            if (Constant.BYTE_VALUE_1.equals(orderDb.getIsPaid())){//有可能有人会重复刷订单，对已经购买的订单，后退刷新订单信息
                result.addErrorMessage(ReturnInfo.ERROR_ORDER_INVALID);
                return result;
            }
        }*/

        /**
         * 业务逻辑：处理订单总金额和优惠码逻辑
         * 1. 如果订单总金额大于0，则需要进行支付流程，并处理优惠码：
         *    - 验证优惠码是否存在
         *    - 设置订单详情中的优惠码和优惠金额
         *    - 根据优惠码重新计算产品价格
         *    - 检查是否满足优惠码的最低消费限制
         *    - 更新订单总金额为优惠后的金额
         * 2. 如果订单总金额等于0（免费订单）：
         *    - 创建支付信息对象并标记为免费
         *    - 直接标记订单为已支付状态
         * 3. 根据hasAddOrder标志决定是更新现有订单还是添加新订单
         */
        if (order.getOrderTotal().doubleValue() > 0){
            if (StringUtils.isNotBlank(restList.getCouponCode())){
                if (enyanCoupon.getCouponValue()!= order.getOrderDetailInfo().getAmountCoupon().intValue()){
                    order.getOrderDetailInfo().setCouponCode(enyanCoupon.getCouponCode().toUpperCase());//设置优惠码，用于记录一个人只能使用一次
                    order.getOrderDetailInfo().setAmountCoupon(new BigDecimal(enyanCoupon.getCouponValue()+""));//设置优惠金额
                    order.getOrderDetailInfo().resetProductListByCoupon(enyanCoupon);
                    if (order.getOrderDetailInfo().getTotalFeeBeforeCoupon().compareTo(new BigDecimal(enyanCoupon.getMinLimitValue())) == -1){//小于最小满减限额
                        result.addErrorMessage(ReturnInfo.ERROR_COUPON_LIMIT);
                        return result;
                    }
                    order.setOrderTotal(new BigDecimal(String.valueOf(order.getOrderDetailInfo().getAmountHkd())));
                }
            }
        }else{
            OrderPayInfo orderPayInfo = new OrderPayInfo();
            orderPayInfo.addFree();
            order.setOrderPayInfo(orderPayInfo);
            order.setIsPaid(Constant.BYTE_VALUE_1);
        }

        if (hasAddOrder) {
            enyanOrderService.updateRecord(order);
        }else {
            enyanOrderService.addRecord(order);
        }

        if (order.getOrderTotal().doubleValue() > 0){

        }else {//免费书直接分拆订单
            this.paySuccess(order,order.getOrderPayInfo(),restList.getLang());
        }

//        if (null != restList.getFromCart() && restList.getFromCart() == 1){//从购物车来的订单，直接清除购物车的数据
//
//        }
        enyanCartService.deleteCarts(restList.getEmail(), restList.getList());
        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restList.getCurrency());
        RestOrder restOrder = new RestOrder();
        restOrder.initFrom(order,currencyType);
        result.setResult(restOrder);
        return result;
    }

    @RequestMapping(value = "/cardPay",method = RequestMethod.POST)
    public ExecuteResult<RestOrder<RestBook>> orderPayCard(@RequestBody RestOrder restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<RestOrder<RestBook>> result = new ExecuteResult<>();

        if (StringUtils.isBlank(restObj.getOrderNum()) || StringUtils.isBlank(restObj.getCardSign())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanOrder paramOrder = new EnyanOrder();
        paramOrder.setOrderNum(restObj.getOrderNum());

        List<EnyanOrder> list = enyanOrderService.findRecordsWithBLOBsByOrder(paramOrder);
        if (null == list || list.isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanOrder order = list.get(0);
        String key = OrderUtil.creditSign(order);

        if (order.getIsValid().equals(Constant.BYTE_VALUE_0) || order.getIsPaid().equals(Constant.BYTE_VALUE_1)){//已经支付或非法订单返回错误
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        RestCard card;
        try {
            String json = AESUtil.decrypt(restObj.getCardSign(), key, Constant.REST_CARD_API_AES_IV);
            card = JSON.parseObject(json, RestCard.class);
        } catch (Exception e) {
            throw new RestException(901, ReturnInfo.ERROR_PARAM_INVALID, "卡问题，email="+restObj.getEmail());
        }
        if (null == card){
            throw new RestException(902, ReturnInfo.ERROR_CARD_INVALID, "卡不存在，email="+restObj.getEmail());
        }

        CreditInfo creditInfo = new CreditInfo();
        creditInfo.setPayCvc(card.getCvc());
        creditInfo.setPayExpireYear(card.getExp_year());
        creditInfo.setPayExpireMonth(card.getExp_month());
        creditInfo.setPayNumber(card.getNumber());
        creditInfo.checkCredit();
        if (!creditInfo.isValid()){
            throw new RestException(903, ReturnInfo.ERROR_CARD_INVALID, "卡不存在，email="+restObj.getEmail()+"card="+ card);
        }

        //String token = StripeUtil.getToken(creditInfo, order.getUserEmail());
        Pair<Boolean,String> pairToken = StripeUtil.getTokenInPair(creditInfo,order.getUserEmail());
        if (pairToken.getKey() == false){
            throw new RestException(904, ReturnInfo.ERROR_CARD_INVALID, pairToken.getValue()+",卡不存在，email="+restObj.getEmail()+",card="+ card);
        }
        String token = pairToken.getValue();
        RequestOptions requestOptions = (new RequestOptions.RequestOptionsBuilder()).setApiKey(StripeConfig.SECRET_KEY).build();
        Map<String, Object> chargeMap = new HashMap<>();
        //付款金额，必填
        BigDecimal fee = order.getOrderTotal().multiply(Constant.VALUE_100);
        int total_fee = fee.intValue();

        //chargeMap.put("amount", 400);//100分
        chargeMap.put("amount", total_fee);//100分
        chargeMap.put("currency", "hkd");//cny usd
        chargeMap.put("source", token); // obtained via Stripe.js
        chargeMap.put("description",order.getOrderNum());
        log.debug("card:{}",card);

        try {
            Charge charge = Charge.create(chargeMap, requestOptions);
            //Card card = (Card) charge.getSource();
            //System.out.println("country:"+card.getAccount()+card.getCustomer()+card.getCountry());
            result.setSuccessMessage(this.getMessage("pay.success",request));

            OrderPayInfo payInfo = new OrderPayInfo();
            payInfo.addStripe(charge,true);
            this.paySuccess(order,payInfo,restObj.getLang());

            order.setIsPaid(Constant.BYTE_VALUE_1);
            CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
            RestOrder restOrder = new RestOrder();
            restOrder.initFrom(order,currencyType);
            result.setResult(restOrder);

            return result;
        } catch (StripeException e) {
            e.printStackTrace();
            throw new RestException(905, ReturnInfo.ERROR_PAY_FAIL, "卡不存在，email="+restObj.getEmail()+"card="+ card+",error="+e.getMessage());
        }



        /*
        //this.logService.sendTimeLog(card.toString());
        ///: 后期整合Stripe
        OrderPayInfo payInfo = new OrderPayInfo();
        //payInfo.addStripe(charge,true);
        payInfo.addFree();
        this.paySuccess(order,payInfo,restObj.getLang());

        order.setIsPaid(Constant.BYTE_VALUE_1);
        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
        RestOrder restOrder = new RestOrder();
        restOrder.initFrom(order,currencyType);
        result.setResult(restOrder);
        */

//        return result;
    }

    /**
     * <p>购买兑换码</p>
     * @param restObj
     * @param request
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v3.model.RestOrder>
     * @since : 2021/4/26
     **/
    @GeoIPRequired
    @RequestMapping(value = "/orderRedeemCodeSubmit",method = RequestMethod.POST)
    public ExecuteResult<RestOrder<RestBook>> redeemCodeOrderSubmit(@RequestBody RestBook restObj, HttpServletRequest request){
        ///TODO 暂时没有启用，需要增加 支付成功后的相应操作及优惠码
        restObj.initHeaderValue(request);

        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

        ExecuteResult<RestOrder<RestBook>> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail())
                    || null == restObj.getQuantity()
                    || null == restObj.getBookId()
                    || restObj.getQuantity() > 100 || restObj.getQuantity() < 0){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (null == restObj.getBookType()){
            restObj.setBookType(0);
        }
        EnyanCoupon enyanCoupon = null;
        if (StringUtils.isNotBlank(restObj.getCouponCode())){//处理优惠码
            enyanCoupon = enyanCouponService.getCouponByCode(restObj.getCouponCode());
            String errorMsg = this.errorMsgOfCoupon(restObj.getEmail(), restObj.getCouponCode(),enyanCoupon);
            if (StringUtils.isNotBlank(errorMsg)){
                result.addErrorMessage(errorMsg);
                return result;
            }
        }

        boolean hasAddOrder;
        EnyanOrder order = new EnyanOrder();
        String orderBookHash = restObj.getBookId().hashCode()+"_"+restObj.getBookType();
        String productDescription = null;
        EnyanOrder queryOrder = new EnyanOrder();
        queryOrder.setOrderBookHash(orderBookHash);
        queryOrder.setUserEmail(restObj.getEmail());
        queryOrder.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_BUY);
        List<EnyanOrder> orderList = enyanOrderService.findRecordsByOrder(queryOrder);
        if (orderList == null || orderList.isEmpty()){//有时候会出现订单重复的问题，如果订单已经存在，则不需要保存
            hasAddOrder = false;
        }else {
            EnyanOrder orderDb = orderList.get(0);
            if (Constant.BYTE_VALUE_1.equals(orderDb.getIsPaid())
                        || Constant.BYTE_VALUE_1.equals(orderDb.getIsValid())){//有可能有人会重复刷订单，对已经购买的订单，后退刷新订单信息
                result.addErrorMessage(ReturnInfo.ERROR_ORDER_EXIST);
                return result;
            }
            hasAddOrder = true;
            order.setOrderId(orderDb.getOrderId());
            order.setOrderNum(orderDb.getOrderNum());
        }

        AuthUser authUser = authUserService.getUserByEmail(restObj.getEmail()).getResult();
        if (null == authUser){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        List<EnyanBook> list = new ArrayList<>();
        if (restObj.getBookType() == EBookConstant.BookType.EBOOK_SINGLE){//单个的书籍
            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(restObj.getBookId()).getResult();
            if (null == enyanBook || !Constant.BYTE_VALUE_1.equals(enyanBook.getShelfStatus())){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
            enyanBook.resetByArea(restObj.getArea());
            list.add(enyanBook);
        } else if (restObj.getBookType() == EBookConstant.BookType.EBOOK_SET) {//书系
            EnyanBookSet bookSet = enyanBookSetService.queryRecordByPrimaryKey(restObj.getBookId()).getResult();
            if (null == bookSet){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
            list = enyanBookService.findBookIdAndNameAndPriceListBySetId(bookSet.getSetId());
            productDescription = bookSet.getSetName();
            if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
                BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
                Byte isSingleValid = Constant.BYTE_VALUE_1;
                for (EnyanBook book : list){
                    book.setDiscountSingleIsValid(isSingleValid);
                    book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

                    book.setDiscountId(null);
                    book.setDiscountIsValid(Constant.BYTE_VALUE_0);
                }
            }
        } else if (restObj.getBookType() == EBookConstant.BookType.EBOOK_MENU) {//书单
            EnyanBookList bookSet = enyanBookListService.queryRecordByPrimaryKey(restObj.getBookId()).getResult();
            if (null == bookSet || StringUtils.isBlank(bookSet.getBookIdText())){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
            list = enyanBookService.findBookIDAndNameAndPriceByIdsString(bookSet.getBookIdText());
            productDescription = bookSet.getSetName();
            if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
                BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
                Byte isSingleValid = Constant.BYTE_VALUE_1;
                for (EnyanBook book : list){
                    book.setDiscountSingleIsValid(isSingleValid);
                    book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

                    book.setDiscountId(null);
                    book.setDiscountIsValid(Constant.BYTE_VALUE_0);
                }
            }
        }

        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restObj.getLang());

        for (EnyanBook enyanBook : list){
            ProductInfo productInfo = new ProductInfo(enyanBook, EBookConstant.OrderType.ORDER_REDEEM_BUY);
            cartInfo.addProduct(productInfo,restObj.getQuantity());
        }

        OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo,productDescription);
        titleInfo.setBookType(restObj.getBookType());
        OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
        try {
            if (hasAddOrder == false){//已经添加的订单，不需要重新生成订单号
                order.setOrderNum(OrderUtil.getOrderId());
            }
        }catch (Exception e){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        order.setOrderTitleInfo(titleInfo);
        order.setOrderDetailInfo(detailInfo);

        order.setIsValid(Constant.BYTE_VALUE_1);
        order.setIsPaid(Constant.BYTE_VALUE_0);
        order.setIsCounted(Constant.BYTE_VALUE_1);//兑换码默认直接统计

        order.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_BUY);

        order.setUserId(authUser.getUserId());
//        order.setUserName(authUser.getEmail());
        order.setUserEmail(authUser.getEmail());
        order.setPurchasedAt(new Date());
        order.setExpiredAt(DateUtils.addHours(order.getPurchasedAt(),2));

        order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
        order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountHkd())));
        order.setOrderFrom(EBookConstant.OrderFrom.APP);

        if (order.getOrderTotal().doubleValue() > 0){
            if (StringUtils.isNotBlank(restObj.getCouponCode())){
                if (enyanCoupon.getCouponValue()!= order.getOrderDetailInfo().getAmountCoupon().intValue()){
                    order.getOrderDetailInfo().setCouponCode(enyanCoupon.getCouponCode().toUpperCase());//设置优惠码，用于记录一个人只能使用一次
                    order.getOrderDetailInfo().setAmountCoupon(new BigDecimal(enyanCoupon.getCouponValue()+""));//设置优惠金额
                    order.getOrderDetailInfo().resetProductListByCoupon(enyanCoupon);
                    if (order.getOrderDetailInfo().getTotalFeeBeforeCoupon().compareTo(new BigDecimal(enyanCoupon.getMinLimitValue())) == -1){//小于最小满减限额
                        result.addErrorMessage(ReturnInfo.ERROR_COUPON_LIMIT);
                        return result;
                    }
                    order.setOrderTotal(new BigDecimal(String.valueOf(order.getOrderDetailInfo().getAmountHkd())));
                }
            }
        }else{
            OrderPayInfo orderPayInfo = new OrderPayInfo();
            orderPayInfo.addFree();
            order.setOrderPayInfo(orderPayInfo);
            order.setIsPaid(Constant.BYTE_VALUE_1);
        }

        if (hasAddOrder) {
            enyanOrderService.updateRecord(order);
        }else {
            enyanOrderService.addRecord(order);
        }

        if (order.getOrderTotal().doubleValue() > 0){

        }else {//免费书直接分拆订单
            new Thread(new Runnable(){
                @Override
                public void run() {
                    paySuccess(order,order.getOrderPayInfo(),restObj.getLang());
                }
            }
            ).start();
            order.setIsPaid(Constant.BYTE_VALUE_1);
        }

        RestOrder restOrder = new RestOrder();
        restOrder.initFrom(order,currencyType);
        result.setResult(restOrder);
        return result;
    }

    /**
     * <p>兑换码兑换检查阶段</p>
     * <p>1、先进行兑换码兑换检查阶段</p>
     * <p>2、如果是单本书，直接对话，否则返回canBuy与hasBuy</p>
     * <p>3、如果是非单本书，直接兑换</p>
     * @param restObj
     * @param request
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v3.model.RestRedeemCode>
     * @since : 2025/3/24
     **/
    @RequestMapping(value = "/redeemCodeToExchange",method = RequestMethod.POST)
    public ExecuteResult<RestCart> redeemCodeToExchange(@RequestBody RestRedeemCode restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<RestCart> result = new ExecuteResult<>();

        if (StringUtils.isBlank(restObj.getCode()) == true || StringUtils.isBlank(restObj.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        AuthUser authUser = authUserService.getUserByEmail(restObj.getEmail()).getResult();
        if (null == authUser){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
//        enyanRedeemCode.setUserEmail(restObj.getEmail());
        enyanRedeemCode.setCode(restObj.getCode());
        List<EnyanRedeemCode> list= this.enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);
        if (null == list || list.isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
            return result;
        }
        Long bookId = 0L;
        EnyanRedeemCode newRedeemCode = list.get(0);
        RedeemCodeNoteInfo redeemCodeNoteInfo = newRedeemCode.getRedeemCodeNoteInfo();
        if (null == redeemCodeNoteInfo){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
            return result;
        }

        if (newRedeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_HAS_EXCHANGE);
            return result;
        }

        if (null != newRedeemCode.getEndAt()){//有截止日期
            Date current = new Date();
            if (current.compareTo(newRedeemCode.getEndAt()) > 0){//超时
                result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_EXPIRED);
                return result;
            }
        }
        List<EnyanBook> allRedeemBookList = redeemCodeNoteInfo.getBooksToRedeemList();
        if (null == allRedeemBookList || allRedeemBookList.isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_HAS_EXCHANGE);
            return result;
        }
        Long[] bookIds = null;
        if (newRedeemCode.getType() == EBookConstant.RedeemType.SPECIAL){
            bookId = redeemCodeNoteInfo.getBooksToRedeemList().get(0).getBookId();
            bookIds = new Long[]{bookId};
        }else if (newRedeemCode.getType() == EBookConstant.RedeemType.ALL) {
            bookIds = allRedeemBookList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);
        }
        List<EnyanBook> hasBuyList = enyanBookBuyService.findBookListHasBuyByEmailAndIds(restObj.getEmail(), bookIds);
        redeemCodeNoteInfo.setBooksHasBuyList(hasBuyList);

        List<EnyanBook> canBuyList = allRedeemBookList.stream().filter(o1 -> hasBuyList.stream().noneMatch(o2 -> o2.getBookTitle().equals(o1.getBookTitle()))).collect(Collectors.toList());
        if (null == canBuyList || canBuyList.isEmpty()){//书籍都已经购买
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_BOOK_EXIST);
            return result;
        }

        if(newRedeemCode.getType() != EBookConstant.RedeemType.SPECIAL){//如果不是单本书，则直接返回canBuy与hasBuy
            RestCart restCart = new RestCart();
            List<RestBook> canBuyRestList = new ArrayList<>();
            for (EnyanBook enyanBook:canBuyList){
                RestBook restBook = new RestBook();
                restBook.initFrom(enyanBook);
                canBuyRestList.add(restBook);
            }
            restCart.setNotBuyList(canBuyRestList);

            List<RestBook> hasBuyRestList = new ArrayList<>();
            for (EnyanBook enyanBook:hasBuyList){
                RestBook restBook = new RestBook();
                restBook.initFrom(enyanBook);
                hasBuyRestList.add(restBook);
            }
            restCart.setBuyList(hasBuyRestList);
            restCart.setConfirm(false);

            result.setResult(restCart);
            return result;
        }

        Long[] bookIdsCanBuy = canBuyList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);

        List<EnyanBook> bookList = enyanBookService.findBookByIdsArray(bookIdsCanBuy);
        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restObj.getLang());
        EnyanOrder order ;
        try {
            if (null == bookIds || bookIds.length==0){
                result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
                return result;
            }

//            EnyanOrderDetail queryOrderDetail = new EnyanOrderDetail();
//            queryOrderDetail.setBookId(bookId);
//            queryOrderDetail.setUserEmail(restObj.getEmail());
            for (EnyanBook enyanBook : bookList){
                ProductInfo productInfo = new ProductInfo(enyanBook, EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);
                cartInfo.addProduct(productInfo,1);
            }

            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderDetailInfo(detailInfo);
            order.setOrderDetail(JSON.toJSONString(detailInfo));

            order.setOrderTitleInfo(titleInfo);
            order.setOrderTitle(JSON.toJSONString(titleInfo));

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_1);
            order.setIsCounted(Constant.BYTE_VALUE_1);

            order.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);

            order.setUserId(authUser.getUserId());
            order.setUserEmail(restObj.getEmail());
            order.setPurchasedAt(new Date());
            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(Constant.VALUE_0);
            order.setOrderFrom(EBookConstant.OrderFrom.APP);

            OrderPayInfo orderPayInfo = new OrderPayInfo();
            orderPayInfo.addRedeemCode(newRedeemCode);
            order.setOrderPayInfo(orderPayInfo);
            order.setPayInfo(JSON.toJSONString(orderPayInfo));
            //EnyanOrder savedOrder = this.enyanOrderService.addRecord(order).getResult();

            redeemCodeNoteInfo.setDateToRedeem(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            redeemCodeNoteInfo.setEmailToRedeem(restObj.getEmail());

            EnyanRedeemCode redeemCodeToUpdate = new EnyanRedeemCode();
            redeemCodeToUpdate.setRedeemCodeNoteInfo(newRedeemCode.getRedeemCodeNoteInfo());
            redeemCodeToUpdate.setRedeemCodeId(newRedeemCode.getRedeemCodeId());
            redeemCodeToUpdate.setStatus(EBookConstant.RedeemStatus.USE);
            //this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);
            /*
            this.enyanOrderDetailService.saveOrderFromRedeem(order,redeemCodeToUpdate);
            enyanPlanService.updatePlanHasBuy(order.getUserEmail(),order.getOrderDetailInfo());*/
            this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);//先更改兑换码状态

            new Thread(new Runnable(){
                @Override
                public void run() {
                    enyanOrderService.saveOrderRedeem(order, bookList,redeemCodeToUpdate);
                    ShopController.sendMailOfOrder(order, restObj.getLang(), getMessageSource(), getEmailService(), authUserService);
                    sendLogOfOrder(order);
                }
            }
            ).start();
        } catch (Exception e) {
            e.printStackTrace();
        }
        RestCart restCart = new RestCart();
        restCart.setConfirm(true);
        result.setResult(restCart);

        return result;
    }

    /**
     * <p>兑换码兑换（单本）</p>
     * @param restObj
     * @param request
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v3.model.RestRedeemCode>
     * @since : 2021/7/19
     **/
    @RequestMapping(value = "/redeemCodeExchange",method = RequestMethod.POST)
    public ExecuteResult<RestCart> redeemCodeExchangeSingle(@RequestBody RestRedeemCode restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<RestCart> result = new ExecuteResult<>();

        if (StringUtils.isBlank(restObj.getCode()) == true || StringUtils.isBlank(restObj.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        AuthUser authUser = authUserService.getUserByEmail(restObj.getEmail()).getResult();
        if (null == authUser){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
//        enyanRedeemCode.setUserEmail(restObj.getEmail());
        enyanRedeemCode.setCode(restObj.getCode());
        List<EnyanRedeemCode> list= this.enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);
        if (null == list || list.isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
            return result;
        }
        Long bookId = 0L;
        EnyanRedeemCode newRedeemCode = list.get(0);
        RedeemCodeNoteInfo redeemCodeNoteInfo = newRedeemCode.getRedeemCodeNoteInfo();
        if (null == redeemCodeNoteInfo){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
            return result;
        }

        if (newRedeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_HAS_EXCHANGE);
            return result;
        }

        if (null != newRedeemCode.getEndAt()){//有截止日期
            Date current = new Date();
            if (current.compareTo(newRedeemCode.getEndAt()) > 0){//超时
                result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_EXPIRED);
                return result;
            }
        }
        List<EnyanBook> allRedeemBookList = redeemCodeNoteInfo.getBooksToRedeemList();
        if (null == allRedeemBookList || allRedeemBookList.isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_HAS_EXCHANGE);
            return result;
        }
        Long[] bookIds = null;
        if (newRedeemCode.getType() == EBookConstant.RedeemType.SPECIAL){
            bookId = redeemCodeNoteInfo.getBooksToRedeemList().get(0).getBookId();
            bookIds = new Long[]{bookId};
        }else if (newRedeemCode.getType() == EBookConstant.RedeemType.ALL) {//此为兼容旧版App，只支持单本书的兑换
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
            //bookIds = allRedeemBookList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);
        }
        List<EnyanBook> hasBuyList = enyanBookBuyService.findBookListHasBuyByEmailAndIds(restObj.getEmail(), bookIds);
        redeemCodeNoteInfo.setBooksHasBuyList(hasBuyList);

        List<EnyanBook> canBuyList = allRedeemBookList.stream().filter(o1 -> hasBuyList.stream().noneMatch(o2 -> o2.getBookTitle().equals(o1.getBookTitle()))).collect(Collectors.toList());
        if (null == canBuyList || canBuyList.isEmpty()){//书籍都已经购买
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_BOOK_EXIST);
            return result;
        }

        Long[] bookIdsCanBuy = canBuyList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);

        List<EnyanBook> bookList = enyanBookService.findBookByIdsArray(bookIdsCanBuy);
        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restObj.getLang());
        EnyanOrder order ;
        try {
            if (null == bookIds || bookIds.length==0){
                result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
                return result;
            }

//            EnyanOrderDetail queryOrderDetail = new EnyanOrderDetail();
//            queryOrderDetail.setBookId(bookId);
//            queryOrderDetail.setUserEmail(restObj.getEmail());
            for (EnyanBook enyanBook : bookList){
                ProductInfo productInfo = new ProductInfo(enyanBook, EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);
                cartInfo.addProduct(productInfo,1);
            }

            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderDetailInfo(detailInfo);
            order.setOrderDetail(JSON.toJSONString(detailInfo));

            order.setOrderTitleInfo(titleInfo);
            order.setOrderTitle(JSON.toJSONString(titleInfo));

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_1);
            order.setIsCounted(Constant.BYTE_VALUE_1);

            order.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);

            order.setUserId(authUser.getUserId());
            order.setUserEmail(restObj.getEmail());
            order.setPurchasedAt(new Date());
            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(Constant.VALUE_0);
            order.setOrderFrom(EBookConstant.OrderFrom.APP);

            OrderPayInfo orderPayInfo = new OrderPayInfo();
            orderPayInfo.addRedeemCode(newRedeemCode);
            order.setOrderPayInfo(orderPayInfo);
            order.setPayInfo(JSON.toJSONString(orderPayInfo));
            //EnyanOrder savedOrder = this.enyanOrderService.addRecord(order).getResult();

            redeemCodeNoteInfo.setDateToRedeem(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            redeemCodeNoteInfo.setEmailToRedeem(restObj.getEmail());

            EnyanRedeemCode redeemCodeToUpdate = new EnyanRedeemCode();
            redeemCodeToUpdate.setRedeemCodeNoteInfo(newRedeemCode.getRedeemCodeNoteInfo());
            redeemCodeToUpdate.setRedeemCodeId(newRedeemCode.getRedeemCodeId());
            redeemCodeToUpdate.setStatus(EBookConstant.RedeemStatus.USE);
            //this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);
            /*
            this.enyanOrderDetailService.saveOrderFromRedeem(order,redeemCodeToUpdate);
            enyanPlanService.updatePlanHasBuy(order.getUserEmail(),order.getOrderDetailInfo());*/
            this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);//先更改兑换码状态

            new Thread(new Runnable(){
                @Override
                public void run() {
                    enyanOrderService.saveOrderRedeem(order, bookList,redeemCodeToUpdate);
                    ShopController.sendMailOfOrder(order, restObj.getLang(), getMessageSource(), getEmailService(), authUserService);
                    sendLogOfOrder(order);
                }
            }
            ).start();
        } catch (Exception e) {
            e.printStackTrace();
        }
        RestCart restCart = new RestCart();
        restCart.setConfirm(true);
        result.setResult(restCart);

        return result;
    }

    /**
     * <p>兑换码兑换（单本或套装书）</p>
     * @param restObj
     * @param request
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v3.model.RestRedeemCode>
     * @since : 2021/7/19
     **/
    @RequestMapping(value = "/redeemCodeExchangeMulti",method = RequestMethod.POST)
    public ExecuteResult<RestCart> redeemCodeExchangeMulti(@RequestBody RestRedeemCode restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<RestCart> result = new ExecuteResult<>();

        if (StringUtils.isBlank(restObj.getCode()) == true || StringUtils.isBlank(restObj.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        AuthUser authUser = authUserService.getUserByEmail(restObj.getEmail()).getResult();
        if (null == authUser){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
//        enyanRedeemCode.setUserEmail(restObj.getEmail());
        enyanRedeemCode.setCode(restObj.getCode());
        List<EnyanRedeemCode> list= this.enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);
        if (null == list || list.isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
            return result;
        }
        Long bookId = 0L;
        EnyanRedeemCode newRedeemCode = list.get(0);
        RedeemCodeNoteInfo redeemCodeNoteInfo = newRedeemCode.getRedeemCodeNoteInfo();
        if (null == redeemCodeNoteInfo){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
            return result;
        }

        if (newRedeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_HAS_EXCHANGE);
            return result;
        }

        if (null != newRedeemCode.getEndAt()){//有截止日期
            Date current = new Date();
            if (current.compareTo(newRedeemCode.getEndAt()) > 0){//超时
                result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_EXPIRED);
                return result;
            }
        }
        List<EnyanBook> allRedeemBookList = redeemCodeNoteInfo.getBooksToRedeemList();
        if (null == allRedeemBookList || allRedeemBookList.isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_HAS_EXCHANGE);
            return result;
        }
        Long[] bookIds = null;
        if (newRedeemCode.getType() == EBookConstant.RedeemType.SPECIAL){
            bookId = redeemCodeNoteInfo.getBooksToRedeemList().get(0).getBookId();
            bookIds = new Long[]{bookId};
        }else if (newRedeemCode.getType() == EBookConstant.RedeemType.ALL) {
            bookIds = allRedeemBookList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);
        }
        List<EnyanBook> hasBuyList = enyanBookBuyService.findBookListHasBuyByEmailAndIds(restObj.getEmail(), bookIds);
        redeemCodeNoteInfo.setBooksHasBuyList(hasBuyList);

        List<EnyanBook> canBuyList = allRedeemBookList.stream().filter(o1 -> hasBuyList.stream().noneMatch(o2 -> o2.getBookTitle().equals(o1.getBookTitle()))).collect(Collectors.toList());
        if (null == canBuyList || canBuyList.isEmpty()){//书籍都已经购买
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_BOOK_EXIST);
            return result;
        }

        Long[] bookIdsCanBuy = canBuyList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);

        List<EnyanBook> bookList = enyanBookService.findBookByIdsArray(bookIdsCanBuy);
        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restObj.getLang());
        EnyanOrder order ;
        try {
            if (null == bookIds || bookIds.length==0){
                result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_INVALID);
                return result;
            }

//            EnyanOrderDetail queryOrderDetail = new EnyanOrderDetail();
//            queryOrderDetail.setBookId(bookId);
//            queryOrderDetail.setUserEmail(restObj.getEmail());
            for (EnyanBook enyanBook : bookList){
                ProductInfo productInfo = new ProductInfo(enyanBook, EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);
                cartInfo.addProduct(productInfo,1);
            }

            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderDetailInfo(detailInfo);
            order.setOrderDetail(JSON.toJSONString(detailInfo));

            order.setOrderTitleInfo(titleInfo);
            order.setOrderTitle(JSON.toJSONString(titleInfo));

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_1);
            order.setIsCounted(Constant.BYTE_VALUE_1);

            order.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);

            order.setUserId(authUser.getUserId());
            order.setUserEmail(restObj.getEmail());
            order.setPurchasedAt(new Date());
            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(Constant.VALUE_0);
            order.setOrderFrom(EBookConstant.OrderFrom.APP);

            OrderPayInfo orderPayInfo = new OrderPayInfo();
            orderPayInfo.addRedeemCode(newRedeemCode);
            order.setOrderPayInfo(orderPayInfo);
            order.setPayInfo(JSON.toJSONString(orderPayInfo));
            //EnyanOrder savedOrder = this.enyanOrderService.addRecord(order).getResult();

            redeemCodeNoteInfo.setDateToRedeem(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            redeemCodeNoteInfo.setEmailToRedeem(restObj.getEmail());

            EnyanRedeemCode redeemCodeToUpdate = new EnyanRedeemCode();
            redeemCodeToUpdate.setRedeemCodeNoteInfo(newRedeemCode.getRedeemCodeNoteInfo());
            redeemCodeToUpdate.setRedeemCodeId(newRedeemCode.getRedeemCodeId());
            redeemCodeToUpdate.setStatus(EBookConstant.RedeemStatus.USE);
            //this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);
            /*
            this.enyanOrderDetailService.saveOrderFromRedeem(order,redeemCodeToUpdate);
            enyanPlanService.updatePlanHasBuy(order.getUserEmail(),order.getOrderDetailInfo());*/
            this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);//先更改兑换码状态

            new Thread(new Runnable(){
                @Override
                public void run() {
                    enyanOrderService.saveOrderRedeem(order, bookList,redeemCodeToUpdate);
                    ShopController.sendMailOfOrder(order, restObj.getLang(), getMessageSource(), getEmailService(), authUserService);
                    sendLogOfOrder(order);
                }
            }
            ).start();
        } catch (Exception e) {
            e.printStackTrace();
        }
        RestCart restCart = new RestCart();
        restCart.setConfirm(true);
        result.setResult(restCart);

        return result;
    }


    @RequestMapping(value = "/redeemCodePage",method = RequestMethod.POST)
    public PageResult<RestRedeemCode> redeemCodePage(@RequestBody RestRedeemCode restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        if (StringUtils.isBlank(restObj.getEmail())){
            PageResult<RestRedeemCode> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        EnyanRedeemCode queryObj = new EnyanRedeemCode();
        queryObj.setUserEmail(restObj.getEmail());
        if (null != restObj.getStatus()){
            queryObj.setStatus(restObj.getStatus());
        }
        OrderObj orderObj = new OrderObj("create_time",InterfaceContant.OrderBy.DESC);
        queryObj.addOrder(orderObj);

        Page<EnyanRedeemCode> searchPage = new Page<>();
        searchPage.setCurrentPage(restObj.getPage());

        Page<EnyanRedeemCode> codePage = enyanRedeemCodeService.queryRecords(searchPage,queryObj);

        PageResult<RestRedeemCode> page = new PageResult<>(codePage.getCurrentPage(),codePage.getTotalRecord(),codePage.getPageSize());
        for (EnyanRedeemCode redeemCode:codePage.getRecords()){
            RestRedeemCode restRedeemCode = new RestRedeemCode();
            restRedeemCode.setCode(redeemCode.getCode());
            restRedeemCode.setCreateTime(redeemCode.getCreateTime());
            restRedeemCode.setStatus(redeemCode.getStatus());

            EnyanOrderDetail orderDetail = redeemCode.getRedeemCodeNoteInfo().getEnyanOrderDetail();
            if (null != orderDetail && null != orderDetail.getPurchasedAt()){
                restRedeemCode.setExchangeTime(orderDetail.getPurchasedAt().getTime());
            }

            RestRedeemCodeNoteInfo restRedeemCodeNoteInfo = new RestRedeemCodeNoteInfo();
            restRedeemCodeNoteInfo.setDateToRedeem(redeemCode.getRedeemCodeNoteInfo().getDateToRedeem());
            restRedeemCodeNoteInfo.setEmailToGift(redeemCode.getRedeemCodeNoteInfo().getEmailToGift());
            restRedeemCodeNoteInfo.setEmailToRedeem(redeemCode.getRedeemCodeNoteInfo().getEmailToRedeem());
            restRedeemCode.setRedeemNameDescription(redeemCode.getRedeemCodeNoteInfo().getBookNameDescription());

            restRedeemCode.setRedeemNoteInfo(restRedeemCodeNoteInfo);
            List<RestBook> booksToRedeem = new ArrayList<>();
            for (EnyanBook enyanBook : redeemCode.getRedeemCodeNoteInfo().getBooksToRedeemList()){
                RestBook book = new RestBook();
                book.setBookId(enyanBook.getBookId());
                book.setName(enyanBook.getBookTitle());
                booksToRedeem.add(book);
            }
            restRedeemCode.setBooksToRedeem(booksToRedeem);
            page.getResult().add(restRedeemCode);
        }
        return page;
    }

    @RequestMapping(value = "/sendRedeemCodeEmail",method = RequestMethod.POST)
    public ExecuteResult<RestRedeemCode> sendRedeemCodeEmail(@RequestBody RestRedeemCode restObj, HttpServletRequest request){
        ExecuteResult<RestRedeemCode> result = new ExecuteResult<>();
        restObj.initHeaderValue(request);

        if (StringUtils.isNotBlank(restObj.getEmail()) == false
                    || StringUtils.isNotBlank(restObj.getCode()) == false
                    || StringUtils.isNotBlank(restObj.getToEmail()) == false
                    || StringUtils.isNotBlank(restObj.getSendText()) == false){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
        enyanRedeemCode.setUserEmail(restObj.getEmail());
        enyanRedeemCode.setCode(restObj.getCode());

        List<EnyanRedeemCode> list =  enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);

        if (null == list || list.isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_SEND);
            return result;
        }
        try{
            EnyanRedeemCode redeemCode = list.get(0);
            if (redeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
                result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_SEND);
                return result;
            }
            RedeemCodeNoteInfo redeemCodeNoteInfo = redeemCode.getRedeemCodeNoteInfo();
            if (org.springframework.util.StringUtils.hasLength(redeemCodeNoteInfo.getEmailToGift())){//已经发送email
                result.addErrorMessage(ReturnInfo.ERROR_REDEEMCODE_SEND);
                return result;
            }
            redeemCodeNoteInfo.setEmailToGift(restObj.getToEmail());

            EnyanRedeemCode newRedeemCode = new EnyanRedeemCode();
            newRedeemCode.setRedeemCodeId(redeemCode.getRedeemCodeId());
            newRedeemCode.setNote(JSON.toJSONString(redeemCodeNoteInfo));

            GiftSendDTO giftSendDTO = new GiftSendDTO();
            giftSendDTO.setEmail(restObj.getToEmail());
            giftSendDTO.setSendText(restObj.getSendText());
            giftSendDTO.setGiftcode(redeemCode.getCode());

            enyanRedeemCodeService.updateRecord(newRedeemCode);
            ShopController.sendMailOfRedeemCode(redeemCode, restObj.getLang(), giftSendDTO, getMessageSource(), getEmailService(),authUserService);
//            this.sendMailOfRedeemCode(redeemCode,giftSendDTO,request);
        }catch (Exception e){
            e.printStackTrace();
        }

        return result;
    }

    @GeoIPRequired
    @RequestMapping(value = "/cart",method = RequestMethod.POST)
    public ExecuteResult<RestCart> cart(@RequestBody RestCart restObj, HttpServletRequest request){
        /*
        if (StringUtils.isBlank(restObj.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }*/
        restObj.initHeaderValue(request);
        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

        ExecuteResult<RestCart> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        List<EnyanBook> list = enyanCartService.searchAllByEmail(restObj.getEmail());
        List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

        HashSet<Long> booksHaveBuy = new HashSet<>();
        for (EnyanBookBuy bookBuy:bookBuyList){
            booksHaveBuy.add(bookBuy.getBookId());
        }

        List<RestBook> buyList = new ArrayList<>();
        List<RestBook> notBuyList = new ArrayList<>();
        for (EnyanBook enyanBook:list){
            enyanBook.resetByArea(restObj.getArea());
            RestBook tmp = new RestBook();
            tmp.initFrom(enyanBook,currencyType);
            if(booksHaveBuy.contains(enyanBook.getBookId().longValue())){
                buyList.add(tmp);
            }else {
                notBuyList.add(tmp);
            }
        }

        RestCart restCart = new RestCart();
//        restCart.setBuyList(buyList);
        restCart.setNotBuyList(notBuyList);
        result.setResult(restCart);
        return result;
    }

    @RequestMapping(value = "/cartAdd",method = RequestMethod.POST)
    public ExecuteResult<String> cartAdd(@RequestBody RestList<Long> restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getList() || restObj.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        for (Long bookId : restObj.getList()){
            if (bookId <= 0){
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                return result;
            }
        }
        Date currentDate = new Date();
        for (Long bookId : restObj.getList()){
            EnyanCart enyanCart = new EnyanCart();
            enyanCart.setUserEmail(restObj.getEmail());
            enyanCart.setBookId(bookId);
            enyanCart.setQuantity(1);
            enyanCart.setAddAt(currentDate);
            enyanCartService.addRecord(enyanCart);
        }
        return result;
    }

    @RequestMapping(value = "/cartAddWithSetId",method = RequestMethod.POST)
    public ExecuteResult<String> cartAddWithSet(@RequestBody RestBookSet restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (null == restObj.getSetId()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        List<EnyanBook> bookList = enyanBookService.findBookIdAndNameListBySetId(restObj.getSetId());

        Date currentDate = new Date();
        for (EnyanBook book : bookList){
            EnyanCart enyanCart = new EnyanCart();
            enyanCart.setUserEmail(restObj.getEmail());
            enyanCart.setBookId(book.getBookId());
            enyanCart.setQuantity(1);
            enyanCart.setAddAt(currentDate);
            enyanCartService.addRecord(enyanCart);
        }
        return result;
    }

    @RequestMapping(value = "/cartAddWithListId",method = RequestMethod.POST)
    public ExecuteResult<String> cartAddWithList(@RequestBody RestBookSet restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (null == restObj.getSetId()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanBookList bookSet = enyanBookListService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
        if (null == bookSet ){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        String bookIdText = bookSet.getBookIdText();
        if (StringUtils.isNotBlank(bookIdText) == false){
            bookIdText = "";
        }
        String[] ids = bookIdText.split(",");

        Date currentDate = new Date();
        for (String id : ids){
            EnyanCart enyanCart = new EnyanCart();
            enyanCart.setUserEmail(restObj.getEmail());
            enyanCart.setBookId(Long.parseLong(id));
            enyanCart.setQuantity(1);
            enyanCart.setAddAt(currentDate);
            enyanCartService.addRecord(enyanCart);
        }
        return result;
    }


    @RequestMapping(value = "/cartDel",method = RequestMethod.POST)
    public ExecuteResult<String> cartDel(@RequestBody RestList<Long> restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getList() || restObj.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        enyanCartService.deleteCarts(restObj.getEmail(), restObj.getList());
        return result;
    }

    @GeoIPRequired
    @RequestMapping(value = "/favorites",method = RequestMethod.POST)
    public PageResult<RestBook> wishes(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        PageResult<RestBook> pageResult = new PageResult<>();
        if (null == restObj.getPage() || StringUtils.isBlank(restObj.getEmail())){
            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }
        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

        EnyanWish searchObj = new EnyanWish();
//        searchObj.setUserId(authUser.getUserId());
        searchObj.setUserEmail(restObj.getEmail());
        OrderObj orderObj = new OrderObj("wished_at",InterfaceContant.OrderBy.DESC);
        searchObj.addOrder(orderObj);
        Page<EnyanBook> page = new Page();
        page.setCurrentPage(restObj.getPage());
        page.setPageSize(pageResult.getPageSize());
        searchObj.setPage(page);

        page = enyanWishService.queryWishes(page,searchObj);
        for (EnyanBook book : page.getRecords()){
            book.resetByArea(restObj.getArea());
            RestBook tmp = new RestBook();
            tmp.initFrom(book,currencyType);
            tmp.setRecommendedCaption(book.getRecommendedCaption());
            if (StringUtils.isBlank(book.getCartId())){
                tmp.setHasAddCart(0);
            }else {
                tmp.setHasAddCart(1);
            }
            pageResult.getResult().add(tmp);
        }
        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());

        return pageResult;
    }

    @RequestMapping(value = "/favoriteAdd",method = RequestMethod.POST)
    public ExecuteResult<String> wishAdd(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getBookId() || restObj.getBookId() <= 0){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        EnyanWish enyanWish = new EnyanWish();
        enyanWish.setBookId(restObj.getBookId());
        enyanWish.setUserEmail(restObj.getEmail());
        enyanWish.setWishedAt(new Date());

        enyanWishService.addRecord(enyanWish);

        return result;
    }

    @RequestMapping(value = "/favoriteDel",method = RequestMethod.POST)
    public ExecuteResult<String> wishDel(@RequestBody RestList<Long> restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getList() || restObj.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        //enyanWishService.deleteByEmailAndBookIdList(restObj.getEmail(), restObj.getList());
        for (Long bookId:restObj.getList()){
            enyanWishService.deleteByEmailAndBookId(restObj.getEmail(), bookId);
        }
        return result;
    }

    @RequestMapping(value = "/couponCode",method = RequestMethod.POST)
    public ExecuteResult<RestCoupon> couponCode(@RequestBody RestCoupon<Long> restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<RestCoupon> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getCouponCode())
                    || null == restObj.getList()|| restObj.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanCoupon enyanCoupon = enyanCouponService.getCouponByCode(restObj.getCouponCode());
        if (null == enyanCoupon){
            result.addErrorMessage(ReturnInfo.ERROR_COUPON_INVALID);
            return result;
        }
        restObj.setUseMax(enyanCoupon.getUseMax());
        restObj.setMinLimitValue(new BigDecimal(enyanCoupon.getMinLimitValue()+""));
        restObj.setValue(new BigDecimal(enyanCoupon.getCouponValue()+""));
        result.setResult(restObj);

        String errorMsg = this.errorMsgOfCoupon(restObj.getEmail(), restObj.getCouponCode(),enyanCoupon);
        if (StringUtils.isNotBlank(errorMsg)){
            result.addErrorMessage(errorMsg);
            return result;
        }
        if (enyanCoupon.getCouponType() == 1){//指定书籍
            if (null == enyanCoupon.getBookSet()){
                restObj.setList(new ArrayList<>());
            }else {
                List<Long> newList = new ArrayList<>();
                for (long bookId:restObj.getList()){//一定要使用long
                    if (enyanCoupon.getBookSet().contains(bookId)){
                        newList.add(bookId);
                    }
                }
                restObj.setList(newList);
            }
        }
        return result;
    }

    @RequestMapping(value = "/downloadInvoice")
    public ResponseEntity<byte[]> downloadInvoice(@RequestBody RestOrder restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ResponseEntity<byte[]> entity = null;
        //System.out.println("downloadEpub");
        HttpHeaders headers = new HttpHeaders();

        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
        headers.add("Accept-Ranges","bytes");

       /* if (0 < restBook.getBookId()){
            throw new DownloadException(Integer.valueOf(200), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "书籍ID>0，book="+restBook);
        }*/

        if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getOrderNum())){
            throw new DownloadException(Integer.valueOf(201), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "下载订单数据，order="+restObj);
        }

        List<EnyanOrder> result = enyanOrderService.findRecordsBlobByOrderNum(restObj.getOrderNum());

        if (result == null || result.isEmpty()){
            throw new DownloadException(Integer.valueOf(202), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "下载订单数据，order="+restObj);
        }
        EnyanOrder order = result.get(0);
        if (!Constant.BYTE_VALUE_1.equals(order.getIsValid()) || 1 == order.getIsDeleted() || !restObj.getEmail().equals(order.getUserEmail())){
            throw new DownloadException(Integer.valueOf(203), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "下载订单数据，order="+restObj);
        }

        OrderDetailInfo detailInfo = order.getOrderDetailInfo();

        List<ProductInfo> list = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:detailInfo.getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                if (productInfo.isDiscountAnyIsValid() == false){
                    productInfo.setPriceHKDDiscount(productInfo.getPriceHkd());
                }
                list.add(productInfo);
            }
        }

        try {
            Map<String, Object> map = new HashMap<>();
            map.put("user",UserUtils.getCurrentLoginUser());
            map.put("order",order);
            map.put("list",list);
            map.put("locale", localeResolver.resolveLocale(request));

            byte[] dataBytes = pdfGenaratorUtil.exportToByte(map,"shop/invoice.html");
            headers.setContentLength(dataBytes.length);
            String fileName = order.getOrderNum() + ".pdf";
            headers.setContentDispositionFormData("attachment", fileName);//告知浏览器以下载方式打开
            entity = new ResponseEntity<>(dataBytes, headers, HttpStatus.OK);
            return entity;
        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
            log.error("下载的问题：Connection reset by peer - download");

            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            headers.setContentDispositionFormData("attachment", ERROR_NAME);//告知浏览器以下载方式打开
            entity = new ResponseEntity<>("error004".getBytes(), headers, HttpStatus.INTERNAL_SERVER_ERROR);//HttpStatus.SERVICE_UNAVAILABLE
            throw new DownloadException(Integer.valueOf(207), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE,"order:"+restObj+",msg="+e.getMessage());
        }finally {
            return entity;
        }
    }

    @LoginAnonymous
    @RequestMapping(value = "/subscribe",method = RequestMethod.POST)
    public ExecuteResult<String> subscribe(@RequestBody RestUser restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restObj.getEmail()) || restObj.getEmail().length() > 100
                    || EmailValidatorUtil.validate(restObj.getEmail()) == false){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        EnyanSubscription record = new EnyanSubscription();
        record.setEmail(restObj.getEmail());
        record.setCreateAt(new Date());
        enyanSubscriptionService.addRecord(record);
        return result;
    }

    /**
     * <p>从list中获取某ID的数据</p>
     * @param id
     * @param bookList
     * @return com.aaron.spring.model.EnyanBook
     * @since : 2021/4/28
     **/
    private EnyanBook getBookById(long id, List<EnyanBook> bookList){
        for (EnyanBook enyanBook : bookList){
            if (enyanBook.getBookId() == id){
                return enyanBook;
            }
        }
        return null;
    }
    /**
     * <p>判断是否优惠码可用；非空则为有问题</p>
     * @param email
     * @param couponCode
     * @return java.lang.String
     * @since : 2021/4/28
     **/
    private String errorMsgOfCoupon(String email, String couponCode, EnyanCoupon enyanCoupon){
        if (StringUtils.isBlank(couponCode)){
            return  ReturnInfo.ERROR_PARAM_INVALID;
        }
        if (null == enyanCoupon){
            return  ReturnInfo.ERROR_COUPON_INVALID;
        }
        if (enyanCoupon.getCouponStatus() == 0){
            return ReturnInfo.ERROR_COUPON_INVALID;
        }
        if (enyanCoupon.getBuyCount() >= enyanCoupon.getBuyMax()){//已经购买的数量大于可购买数
            return ReturnInfo.ERROR_COUPON_OUT;
        }
        if (enyanCoupon.getBeginTime().getTime() > System.currentTimeMillis()){//早于开始的时间
            return ReturnInfo.ERROR_COUPON_INVALID;
        }
        if (enyanCoupon.getEndTime().getTime()< System.currentTimeMillis()){
            return ReturnInfo.ERROR_COUPON_EXPIRED;
        }
        /*
         * 关于优惠码的最小满减金额限定，需要app自己根据 MinLimitValue 进行判断，因为关系到多语言的国际化，服务器端就不处理了
         * 生成订单时 返回 error.coupon.limit
         * 其他返回 invalid
         * */
        long count = enyanOrderService.selectCountByCouponCodeAndEmail(enyanCoupon.getCouponCode(),email);
        if (count >= enyanCoupon.getUseMax()){//超过使用次数
            return ReturnInfo.ERROR_COUPON_ONLY_TIMES;
        }
        return null;
    }

    protected void paySuccess(EnyanOrder order, OrderPayInfo orderPayInfo, String lang) {
        log.debug("paySuccess:");
//        if (StringUtils.isBlank(lang)){
//            lang = InterfaceContant.LocaleLang.SC;
//        }
        order.setOrderPayInfo(orderPayInfo);
        if (order.getOrderDetailInfo()==null && org.springframework.util.StringUtils.hasLength(order.getOrderDetail())){
            String orderDetailInfo = order.getOrderDetail();

            OrderDetailInfo detailInfo = JSON.parseObject(orderDetailInfo, OrderDetailInfo.class);
            order.setOrderDetailInfo(detailInfo);
        }

        log.debug("paySuccess updateRecord");

        enyanOrderService.saveOrderHasPay(order);

//        this.sendMailOfOrder(order,request);
        ShopController.sendMailOfOrder(order, lang, getMessageSource(), getEmailService(), authUserService);
        this.sendLogOfOrder(order);
    }

    protected void sendLogOfOrder(EnyanOrder order) {
        log.debug("sendLogOfOrder:");
        if (org.springframework.util.StringUtils.hasLength(order.getUserEmail()) == false){
            return;
        }
        logService.sendOrderLog(order);
    }
}
