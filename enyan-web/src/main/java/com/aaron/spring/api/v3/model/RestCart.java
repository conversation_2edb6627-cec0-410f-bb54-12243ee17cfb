package com.aaron.spring.api.v3.model;

import com.aaron.spring.api.RestBaseDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/26
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestCart extends RestBaseDTO {
    private static final long serialVersionUID = 248904474032271822L;
    private List<RestBook> buyList;//已经购买的List
    private List<RestBook> notBuyList;//已经购买的List

    public List<RestBook> getBuyList() {
        return buyList;
    }

    public void setBuyList(List<RestBook> buyList) {
        this.buyList = buyList;
    }

    public List<RestBook> getNotBuyList() {
        return notBuyList;
    }

    public void setNotBuyList(List<RestBook> notBuyList) {
        this.notBuyList = notBuyList;
    }
}
