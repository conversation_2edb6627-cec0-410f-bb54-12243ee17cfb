package com.aaron.spring.api.v3.controller;

import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.RestConfig;
import com.aaron.spring.api.v4.model.RestBook;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBookBuy;
import com.aaron.spring.service.EnyanBookBuyService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-06-10
 * @Modified By:
 */
@Slf4j
@RestController("RestConfigControllerV3")
@RequestMapping("/api/v3/config")
public class RestConfigController  extends RestBaseController {

    @Resource
    private EnyanBookBuyService enyanBookBuyService;

    @RequestMapping(value = "/launch",method = RequestMethod.POST)
    public ExecuteResult<RestConfig> launch(@RequestBody RestConfig restObj, HttpServletRequest request) throws CloneNotSupportedException {
        restObj.initHeaderValue(request);
        ExecuteResult<RestConfig> result = new ExecuteResult<>();
        result.setResult(Constant.DEFAULT_REST_CONFIG);
        if (StringUtils.isBlank(restObj.getEmail())){
            return result;
        }
        if (null != restObj.getOnlyConfig() && restObj.getOnlyConfig() == true){
            return result;
        }
        RestConfig config = (RestConfig) Constant.DEFAULT_REST_CONFIG.clone();
        result.setResult(config);

        List<RestBook> bookList = new ArrayList<>();
        List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookBaseInfoByEmail(restObj.getEmail());
        for (EnyanBookBuy bookBuy:bookBuyList){
            RestBook restBook = new RestBook();
            restBook.setReadLocation(bookBuy.getReadInfo());
            restBook.setReadTime(bookBuy.getReadTime());

            restBook.setGroupName(bookBuy.getGroupName());
            restBook.setGroupNameTime(bookBuy.getGroupNameTime());

            restBook.setBookId(bookBuy.getBookId());
            restBook.setEmail(restObj.getEmail());
//            restBook.setNotShowDiscount(null);
            bookList.add(restBook);
            /*
            if (StringUtils.isNotBlank(bookBuy.getReadInfo())){
                log.debug("location:{}",bookBuy.getReadInfo().length());
            }*/
        }
        config.setBooks(bookList);
        return result;
    }
}
