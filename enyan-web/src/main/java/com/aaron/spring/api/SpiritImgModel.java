package com.aaron.spring.api;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  12/4/20
 * @Modified By:
 */
public class SpiritImgModel implements Serializable {
    private static final long serialVersionUID = -490278488215431775L;

    private String benifitImgCn;

    private String benifitImgHk;

    private String whyImgCn;

    private String whyImgHk;

    private String lectioDivinaImgCn;

    private String lectioDivinaImgHk;

    public String getBenifitImgCn() {
        return benifitImgCn;
    }

    public void setBenifitImgCn(String benifitImgCn) {
        this.benifitImgCn = benifitImgCn;
    }

    public String getBenifitImgHk() {
        return benifitImgHk;
    }

    public void setBenifitImgHk(String benifitImgHk) {
        this.benifitImgHk = benifitImgHk;
    }

    public String getWhyImgCn() {
        return whyImgCn;
    }

    public void setWhyImgCn(String whyImgCn) {
        this.whyImgCn = whyImgCn;
    }

    public String getWhyImgHk() {
        return whyImgHk;
    }

    public void setWhyImgHk(String whyImgHk) {
        this.whyImgHk = whyImgHk;
    }

    public String getLectioDivinaImgCn() {
        return lectioDivinaImgCn;
    }

    public void setLectioDivinaImgCn(String lectioDivinaImgCn) {
        this.lectioDivinaImgCn = lectioDivinaImgCn;
    }

    public String getLectioDivinaImgHk() {
        return lectioDivinaImgHk;
    }

    public void setLectioDivinaImgHk(String lectioDivinaImgHk) {
        this.lectioDivinaImgHk = lectioDivinaImgHk;
    }
}
