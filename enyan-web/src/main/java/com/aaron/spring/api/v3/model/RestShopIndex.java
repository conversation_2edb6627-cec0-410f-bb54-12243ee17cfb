package com.aaron.spring.api.v3.model;

import com.aaron.spring.api.RestBaseDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/12
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestShopIndex extends RestBaseDTO {
    private static final long serialVersionUID = -2648658213836986366L;
    private List<RestBook> editorList;//推荐
    private List<RestShopCategory> shopCategories;

    public List<RestBook> getEditorList() {
        return editorList;
    }

    public void setEditorList(List<RestBook> editorList) {
        this.editorList = editorList;
    }

    public List<RestShopCategory> getShopCategories() {
        return shopCategories;
    }

    public void setShopCategories(List<RestShopCategory> shopCategories) {
        this.shopCategories = shopCategories;
    }
}
