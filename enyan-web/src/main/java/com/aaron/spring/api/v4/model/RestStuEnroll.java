package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.StuCheckinInfo;
import com.aaron.spring.model.StuEnroll;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serial;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/5/11
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestStuEnroll extends RestBaseDTO {
	@Serial
	private static final long serialVersionUID = 2209322556651335414L;
	private Long dataId;

	private Integer stuId;

	private String lastName;

	private String firstName;

	private Date enrollAt;

	private Integer enrollStatus;

	private String enrollCode;

	private Integer type;

	private Integer status;

	private Long time;

	private String checkin;

	private Integer terms;

	private StuCheckinInfo stuCheckinInfo;

	public void initFrom(StuEnroll obj){
		this.dataId = obj.getDataId();
		this.stuId = obj.getStuId();
		this.lastName = obj.getLastName();
		this.firstName = obj.getFirstName();
		this.setEmail(obj.getEmail());
		this.enrollAt = obj.getEnrollAt();
		this.enrollStatus = obj.getEnrollStatus();
		this.enrollCode = obj.getEnrollCode();
		this.checkin = obj.getCheckin();
		this.terms = obj.getTerms();
		this.stuCheckinInfo = obj.getStuCheckinInfo();
	}

	public Long getDataId() {
		return dataId;
	}

	public void setDataId(Long dataId) {
		this.dataId = dataId;
	}

	public Integer getStuId() {
		return stuId;
	}

	public void setStuId(Integer stuId) {
		this.stuId = stuId;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public Date getEnrollAt() {
		return enrollAt;
	}

	public void setEnrollAt(Date enrollAt) {
		this.enrollAt = enrollAt;
	}

	public Integer getEnrollStatus() {
		return enrollStatus;
	}

	public void setEnrollStatus(Integer enrollStatus) {
		this.enrollStatus = enrollStatus;
	}

	public String getEnrollCode() {
		return enrollCode;
	}

	public void setEnrollCode(String enrollCode) {
		this.enrollCode = enrollCode;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getTime() {
		return time;
	}

	public void setTime(Long time) {
		this.time = time;
	}

	public String getCheckin() {
		return checkin;
	}

	public void setCheckin(String checkin) {
		this.checkin = checkin;
	}

	public Integer getTerms() {
		return terms;
	}

	public void setTerms(Integer terms) {
		this.terms = terms;
	}

	public StuCheckinInfo getStuCheckinInfo() {
		return stuCheckinInfo;
	}

	public void setStuCheckinInfo(StuCheckinInfo stuCheckinInfo) {
		this.stuCheckinInfo = stuCheckinInfo;
	}

	public static  void main(String[] args){
		printInit("com.aaron.spring.api.v4.model.RestStuEnroll","StuEnroll");
	}
}
