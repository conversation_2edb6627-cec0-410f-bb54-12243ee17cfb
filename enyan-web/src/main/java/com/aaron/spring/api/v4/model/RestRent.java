package com.aaron.spring.api.v4.model;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.CurrencyType;
import com.aaron.spring.model.EnyanRent;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: Aaron <PERSON>
 * @Description:
 * @Date: Created in  2022/10/24
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestRent extends RestBaseDTO {
	private static final long serialVersionUID = 8327117237850469171L;

	private Long dataId;

	private Long bookId;

	private String orderNum;

	private Long publisherId;

	private Integer rentType;

	private Integer rentLang;

	private Integer isValid;

	private Integer rentStatus;

	private Integer isAuto;

	private Integer isPaid;

	private Integer leaveBuy;

	private BigDecimal rentPrice;

	private BigDecimal rentPriceCurrency;

	private BigDecimal totalFee;

	private BigDecimal totalFeeCurrency;

	private Integer toRentMonths;//要订阅的月份数

	private String toOrderNum;//退订时购买的订单号

	private Integer totalMonths;//已经订阅总月份数

	private String creditInfo;//信用卡信息

	private Long leaveAt;

	private Long beginAt;

	private Long expiredAt;

	private Long createAt;

	private String rentDetail;

	private String payType;

	/**
	 * 退出类型（0：直接退出；1：购买全部；2：部分购买）
	 * */
	private Integer leaveType;//

	private List<Long> list;

	public void initFrom(EnyanRent obj){
		this.initDefault(obj);
	}

	public void initDefault(EnyanRent obj){
		this.dataId = obj.getRentId();
		this.orderNum = obj.getOrderNum();
		this.rentPrice = obj.getRentPrice();
		//this.publisherId = obj.getPublisherId();
		this.rentType = obj.getRentType();
		this.rentLang = obj.getRentLang();
		this.isValid = obj.getIsValid();//订单状态，-1：已取消；0：默认(创建订单,未支付)；1：合法；
		this.rentStatus = obj.getRentStatus();
		this.isAuto = obj.getIsAuto();
		this.isPaid = obj.getIsPaid();
		this.leaveBuy = obj.getLeaveBuy();
		this.totalFee = obj.getTotalFee();
		this.totalMonths = obj.getTotalMonths();
		this.leaveAt = null == obj.getLeaveAt()? null : obj.getLeaveAt().getTime();
		this.beginAt = null == obj.getBeginAt()? null : obj.getBeginAt().getTime();
		this.expiredAt = null == obj.getExpiredAt()? null : obj.getExpiredAt().getTime();
		this.createAt = null == obj.getCreateAt()? null : obj.getCreateAt().getTime();
		//this.rentDetail = obj.getRentDetail();
	}

	public void initFrom(EnyanRent obj, CurrencyType currencyType){
		this.initDefault(obj);
		if (null != obj.getTotalFee()){
			this.totalFeeCurrency = currencyType.getCurrencyByPriceValue(obj.getTotalFee());
		}
		if (null != obj.getRentPrice()){
			this.rentPriceCurrency = currencyType.getCurrencyByPriceValue(obj.getRentPrice());
		}
	}

	public void setFeeByCurrency(CurrencyType currencyType){
		if (null != this.getTotalFee()){
			this.totalFeeCurrency = currencyType.getCurrencyByPriceValue(this.getTotalFee());
		}
	}

	/**
	 * <p>在config中使用的数据</p>
	 * @param obj
	 * @return void
	 * @since : 2022/11/29
	 **/
	public void initToConfig(EnyanRent obj){
		this.dataId = obj.getRentId();
		this.orderNum = obj.getOrderNum();
		//this.publisherId = obj.getPublisherId();
		this.rentType = obj.getRentType();
		this.rentLang = obj.getRentLang();
		this.isValid = obj.getIsValid();//订单状态，-1：已取消；0：默认(创建订单,未支付)；1：合法；
		//this.rentStatus = obj.getRentStatus();
		this.isAuto = obj.getIsAuto();
		//this.isPaid = obj.getIsPaid();
		//this.leaveBuy = obj.getLeaveBuy();
		//this.totalFee = obj.getTotalFee();
		//this.leaveAt = null == obj.getLeaveAt()? null : obj.getLeaveAt().getTime();
		//this.beginAt = null == obj.getBeginAt()? null : obj.getBeginAt().getTime();
		this.expiredAt = null == obj.getExpiredAt()? null : obj.getExpiredAt().getTime();
		//this.createAt = null == obj.getCreateAt()? null : obj.getCreateAt().getTime();
		//this.rentDetail = obj.getRentDetail();
		this.setEmail(obj.getUserEmail());
	}

	public Long getDataId() {
		return dataId;
	}

	public void setDataId(Long dataId) {
		this.dataId = dataId;
	}

	public Long getBookId() {
		return bookId;
	}

	public void setBookId(Long bookId) {
		this.bookId = bookId;
	}

	public String getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(String orderNum) {
		this.orderNum = orderNum;
	}

	public Long getPublisherId() {
		return publisherId;
	}

	public void setPublisherId(Long publisherId) {
		this.publisherId = publisherId;
	}

	public Integer getRentType() {
		return rentType;
	}

	public void setRentType(Integer rentType) {
		this.rentType = rentType;
	}

	public Integer getRentLang() {
		return rentLang;
	}

	public void setRentLang(Integer rentLang) {
		this.rentLang = rentLang;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Integer getRentStatus() {
		return rentStatus;
	}

	public void setRentStatus(Integer rentStatus) {
		this.rentStatus = rentStatus;
	}

	public Integer getIsAuto() {
		return isAuto;
	}

	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}

	public Integer getIsPaid() {
		return isPaid;
	}

	public void setIsPaid(Integer isPaid) {
		this.isPaid = isPaid;
	}

	public Integer getLeaveBuy() {
		return leaveBuy;
	}

	public void setLeaveBuy(Integer leaveBuy) {
		this.leaveBuy = leaveBuy;
	}

	public BigDecimal getTotalFee() {
		return totalFee;
	}

	public BigDecimal getTotalFeeCurrency() {
		return totalFeeCurrency;
	}

	public void setTotalFeeCurrency(BigDecimal totalFeeCurrency) {
		this.totalFeeCurrency = totalFeeCurrency;
	}

	public void setTotalFee(BigDecimal totalFee) {
		this.totalFee = totalFee;
	}

	public Integer getToRentMonths() {
		return toRentMonths;
	}

	public void setToRentMonths(Integer toRentMonths) {
		this.toRentMonths = toRentMonths;
	}

	public String getToOrderNum() {
		return toOrderNum;
	}

	public void setToOrderNum(String toOrderNum) {
		this.toOrderNum = toOrderNum;
	}

	public Integer getTotalMonths() {
		return totalMonths;
	}

	public void setTotalMonths(Integer totalMonths) {
		this.totalMonths = totalMonths;
	}

	public String getCreditInfo() {
		return creditInfo;
	}

	public void setCreditInfo(String creditInfo) {
		this.creditInfo = creditInfo;
	}

	public Long getLeaveAt() {
		return leaveAt;
	}

	public void setLeaveAt(Long leaveAt) {
		this.leaveAt = leaveAt;
	}

	public Long getBeginAt() {
		return beginAt;
	}

	public void setBeginAt(Long beginAt) {
		this.beginAt = beginAt;
	}

	public Long getExpiredAt() {
		return expiredAt;
	}

	public void setExpiredAt(Long expiredAt) {
		this.expiredAt = expiredAt;
	}

	public String getRentDetail() {
		return rentDetail;
	}

	public void setRentDetail(String rentDetail) {
		this.rentDetail = rentDetail;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public Long getCreateAt() {
		return createAt;
	}

	public void setCreateAt(Long createAt) {
		this.createAt = createAt;
	}

	public Integer getLeaveType() {
		return leaveType;
	}

	public void setLeaveType(Integer leaveType) {
		this.leaveType = leaveType;
	}

	public List<Long> getList() {
		return list;
	}

	public void setList(List<Long> list) {
		this.list = list;
	}

	public BigDecimal getRentPrice() {
		return rentPrice;
	}

	public void setRentPrice(BigDecimal rentPrice) {
		this.rentPrice = rentPrice;
	}

	public BigDecimal getRentPriceCurrency() {
		return rentPriceCurrency;
	}

	public void setRentPriceCurrency(BigDecimal rentPriceCurrency) {
		this.rentPriceCurrency = rentPriceCurrency;
	}

	@Override
	public String toString() {
		return "RestRent{" +
				       "dataId=" + dataId +
				       ", bookId=" + bookId +
				       ", orderNum='" + orderNum + '\'' +
				       ", isValid=" + isValid +
				       ", expiredAt=" + (null == expiredAt?"": DateFormatUtils.format(new Date(expiredAt), InterfaceContant.DateFormatCustom.DATETIME)) +
				       '}';
	}
	public static  void main(String[] args){
		printInit("com.aaron.spring.api.v4.model.RestRent","EnyanRent");
	}
}
