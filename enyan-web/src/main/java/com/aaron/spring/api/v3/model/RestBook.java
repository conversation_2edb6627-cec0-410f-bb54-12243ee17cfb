package com.aaron.spring.api.v3.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.CurrencyType;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanDiscount;
import com.aaron.spring.model.ProductInfo;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/8/24
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestBook extends RestBaseDTO{
    private static final long serialVersionUID = 7547767821343573759L;
    private String name;
    private String author;
    private Long bookId;
    private String dateStr;
    private String bookLanguage;
    private String version;
    private String imgUrl;
    private String imgUrlFull;
    private String fileName;
    private String isbn;
    private String ebookIsbn;
    private String esin;
    private String size;
    private Integer salesModel;//1:预售；0：正常
    private BigDecimal price;
    private BigDecimal priceDiscount;//折扣后的价格
    private BigDecimal priceCurrency;//新的价格
    private String discountMsg;

    private Integer discountSingleIsValid;//单个折扣
    private Integer discountIsValid;//N件折

    private Long discountId;

    private Integer discountPercent;//单件折的折扣信息

    private Integer cumulatePackage;

    private Integer cumulateDiscount;

    private Integer cumulatePackageMuti;

    private Integer cumulateDiscountMuti;

    private Long categoryId;

    private Long publisherId;

    private String publisherName;

    private String bookCatalogue;

    private String bookDescription;//图书简介

    private String bookAbstract;//图书摘要，书名下方

    private String recommendedCaption;//推荐语，列表中展示

    private String bookSample;

    private Integer orderBy;//0:默认；1:最新；2：热门

    private Integer freeType; //1:免费；0：付费

//    private Integer presaleType;//1:预售；0：正常

    private Integer discountType;//1:N件折；0：正常

    private Integer hasBuy;//已经购买 1:已购买；0：没有

    private Integer hasAddCart;//已经加入购物车 1:已加入；0：没有

    private Integer hasAddFavorite;//已经加入收藏 1:已加入；0：没有

    private Integer quantity;//订单展示数量

    private Integer specialOffer;//是否特价

    private Integer shelfStatus;//是否下架（0已下架，1上架）

    private String couponCode;

    private Integer canTts;//是否可以听书（0不可以，1可以）

    private String readLocation;

    private String groupName;

    private Long readTime;

    private Long groupNameTime;

    //private Long publisherId;

    @JSONField(serialize = false)
    private Boolean notShowDiscount;//是否需要显示折扣信息

    private List<Long> list;

    public void initFrom(EnyanBook obj){
        this.initFrom(obj, "");
    }

    public void initFrom(EnyanBook obj, String currentCurrency){
        this.initDefault(obj);
        this.priceCurrency = BookUtil.getCurrencyPrice(obj,currentCurrency);
    }

    public void initFrom(EnyanBook obj, CurrencyType currencyType){
        this.initDefault(obj);
        this.priceCurrency = BookUtil.getCurrencyPrice(obj,currencyType);
    }

    private void initDefault(EnyanBook obj){
        this.name = obj.getBookTitle();
        this.author = obj.getAuthor();
        this.bookId = obj.getBookId();
        this.dateStr = obj.getPublishedAt();//DateFormatUtils.format(obj.getOpensaleAt(), Constant.DATE_FORMAT_PATTERN);
        this.bookLanguage = Constant.BYTE_VALUE_1.equals(obj.getIsInCn())?"sc":"tc";
        this.shelfStatus = Constant.BYTE_VALUE_1.equals(obj.getShelfStatus())?1:0;
        this.version = obj.getBookDrmRef();
        this.imgUrl = obj.getBookCoverApp();
        this.imgUrlFull = obj.getBookCover();
        this.fileName = obj.getBookId()+".epub";
        this.isbn = obj.getBookIsbn();
        this.ebookIsbn = obj.getEbookIsbn();
        this.esin = obj.getBookEsin();
        this.size = obj.getSize();
        this.salesModel = obj.getSalesModel();
        this.price = obj.getPriceHkd();
        this.priceDiscount = BookUtil.getPriceDiscount(obj);
        this.publisherName = obj.getPublisherName();
        this.publisherId = obj.getPublisherId();
        this.bookSample = obj.getBookSample();
        if (null == priceDiscount){//如果没有折扣价，就直接将原价设置为折扣价，原价则设置为空；使用的时候直接使用discount
            priceDiscount = price;
            price = null;
        }

        this.discountSingleIsValid = 0;
//        this.discountMsg = BookUtil.getDiscountMsg(obj);
        this.discountPercent = getDiscountSinglePercent(obj,this);

        this.discountIsValid = 0;
        this.discountId = 0L;

        if (Constant.BYTE_VALUE_1.equals(obj.getDiscountIsValid())){
            EnyanDiscount enyanDiscount = Constant.discountMap.get(obj.getDiscountId());
            if (null != enyanDiscount){
                this.cumulatePackage = enyanDiscount.getCumulatePackage();
                this.cumulateDiscount = enyanDiscount.getCumulateDiscount();
                this.cumulatePackageMuti = enyanDiscount.getCumulatePackageMuti();
                this.cumulateDiscountMuti = enyanDiscount.getCumulateDiscountMuti();
                this.discountIsValid = 1;
            }
            this.discountId = obj.getDiscountId();
        }
        this.bookAbstract = obj.getBookAbstract();
        this.recommendedCaption = obj.getRecommendedCaption();
        //HKD CNY TWD USD CAD EUR AUD SGD MYR IDR
        this.canTts = obj.getCanTts();
    }
    /**
     * <p>初始化订单产品信息</p>
     * @param obj
     * @return void
     * @since : 2021/4/23
     **/
    public void initFromProduct(ProductInfo obj){
        this.name = obj.getName();
        this.bookId = obj.getCode();
        this.quantity = obj.getQuantity().intValue();
        if (obj.isDiscountAnyIsValid()){
            this.price = obj.getPriceHkd();
            this.priceDiscount = obj.getPriceHKDDiscount();
        }else {
            this.priceDiscount = obj.getPriceHkd();
        }
    }

    /**
     * <p>单件折的数据</p>
     * @param enyanBook
     * @return java.lang.String
     * @since : 2021/5/19
     **/
    public static Integer getDiscountSinglePercent(EnyanBook enyanBook, RestBook restBook){
        if (enyanBook.isNotShowDiscount()){
            return null;
        }
        EnyanDiscount enyanDiscount = Constant.discountMap.get(enyanBook.getDiscountSingleId());
        if (null == enyanDiscount){
            return null;
        }
        if (!Constant.BYTE_VALUE_1.equals(enyanDiscount.getIsShow())){
            return null;
        }
        if (!Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())){
            return null;
        }
        if (Constant.BYTE_VALUE_0.equals(enyanBook.getDiscountSingleType())){
            restBook.setDiscountSingleIsValid(1);
            return enyanBook.getDiscountSingleValue();
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getBookLanguage() {
        return bookLanguage;
    }

    public void setBookLanguage(String bookLanguage) {
        this.bookLanguage = bookLanguage;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getImgUrlFull() {
        return imgUrlFull;
    }

    public void setImgUrlFull(String imgUrlFull) {
        this.imgUrlFull = imgUrlFull;
    }

    public String getIsbn() {
        return isbn;
    }

    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    public String getEbookIsbn() {
        return ebookIsbn;
    }

    public void setEbookIsbn(String ebookIsbn) {
        this.ebookIsbn = ebookIsbn;
    }

    public String getEsin() {
        return esin;
    }

    public void setEsin(String esin) {
        this.esin = esin;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Integer getSalesModel() {
        return salesModel;
    }

    public void setSalesModel(Integer salesModel) {
        this.salesModel = salesModel;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPriceDiscount() {
        return priceDiscount;
    }

    public void setPriceDiscount(BigDecimal priceDiscount) {
        this.priceDiscount = priceDiscount;
    }

    public BigDecimal getPriceCurrency() {
        return priceCurrency;
    }

    public void setPriceCurrency(BigDecimal priceCurrency) {
        this.priceCurrency = priceCurrency;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public String getDiscountMsg() {
        return discountMsg;
    }

    public void setDiscountMsg(String discountMsg) {
        this.discountMsg = discountMsg;
    }

    public Integer getCumulatePackage() {
        return cumulatePackage;
    }

    public void setCumulatePackage(Integer cumulatePackage) {
        this.cumulatePackage = cumulatePackage;
    }

    public Integer getCumulateDiscount() {
        return cumulateDiscount;
    }

    public void setCumulateDiscount(Integer cumulateDiscount) {
        this.cumulateDiscount = cumulateDiscount;
    }

    public Integer getCumulatePackageMuti() {
        return cumulatePackageMuti;
    }

    public void setCumulatePackageMuti(Integer cumulatePackageMuti) {
        this.cumulatePackageMuti = cumulatePackageMuti;
    }

    public Integer getCumulateDiscountMuti() {
        return cumulateDiscountMuti;
    }

    public void setCumulateDiscountMuti(Integer cumulateDiscountMuti) {
        this.cumulateDiscountMuti = cumulateDiscountMuti;
    }

    public Integer getDiscountIsValid() {
        return discountIsValid;
    }

    public void setDiscountIsValid(Integer discountIsValid) {
        this.discountIsValid = discountIsValid;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(Integer orderBy) {
        this.orderBy = orderBy;
    }

    public Integer getFreeType() {
        return freeType;
    }

    public void setFreeType(Integer freeType) {
        this.freeType = freeType;
    }

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public Integer getHasBuy() {
        return hasBuy;
    }

    public void setHasBuy(Integer hasBuy) {
        this.hasBuy = hasBuy;
    }

    public Integer getHasAddCart() {
        return hasAddCart;
    }

    public void setHasAddCart(Integer hasAddCart) {
        this.hasAddCart = hasAddCart;
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public String getBookCatalogue() {
        return bookCatalogue;
    }

    public void setBookCatalogue(String bookCatalogue) {
        this.bookCatalogue = bookCatalogue;
    }

    public String getBookDescription() {
        return bookDescription;
    }

    public void setBookDescription(String bookDescription) {
        this.bookDescription = bookDescription;
    }

    public String getBookAbstract() {
        return bookAbstract;
    }

    public void setBookAbstract(String bookAbstract) {
        this.bookAbstract = bookAbstract;
    }

    public String getBookSample() {
        return bookSample;
    }

    public void setBookSample(String bookSample) {
        this.bookSample = bookSample;
    }

    public String getRecommendedCaption() {
        return recommendedCaption;
    }

    public void setRecommendedCaption(String recommendedCaption) {
        this.recommendedCaption = recommendedCaption;
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public Integer getHasAddFavorite() {
        return hasAddFavorite;
    }

    public void setHasAddFavorite(Integer hasAddFavorite) {
        this.hasAddFavorite = hasAddFavorite;
    }

    public Integer getDiscountPercent() {
        return discountPercent;
    }

    public void setDiscountPercent(Integer discountPercent) {
        this.discountPercent = discountPercent;
    }

    public String getPublisherName() {
        return publisherName;
    }

    public void setPublisherName(String publisherName) {
        this.publisherName = publisherName;
    }

    public Integer getDiscountSingleIsValid() {
        return discountSingleIsValid;
    }

    public void setDiscountSingleIsValid(Integer discountSingleIsValid) {
        this.discountSingleIsValid = discountSingleIsValid;
    }

    public Integer getSpecialOffer() {
        return specialOffer;
    }

    public void setSpecialOffer(Integer specialOffer) {
        this.specialOffer = specialOffer;
    }

    public Integer getShelfStatus() {
        return shelfStatus;
    }

    public void setShelfStatus(Integer shelfStatus) {
        this.shelfStatus = shelfStatus;
    }

    public Integer getCanTts() {
        return canTts;
    }

    public void setCanTts(Integer canTts) {
        this.canTts = canTts;
    }

    public Boolean getNotShowDiscount() {
        return notShowDiscount;
    }

    public void setNotShowDiscount(Boolean notShowDiscount) {
        this.notShowDiscount = notShowDiscount;
    }

    public String getReadLocation() {
        return readLocation;
    }

    public void setReadLocation(String readLocation) {
        this.readLocation = readLocation;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Long getReadTime() {
        return readTime;
    }

    public void setReadTime(Long readTime) {
        this.readTime = readTime;
    }

    public Long getGroupNameTime() {
        return groupNameTime;
    }

    public void setGroupNameTime(Long groupNameTime) {
        this.groupNameTime = groupNameTime;
    }

    public List<Long> getList() {
        return list;
    }

    public void setList(List<Long> list) {
        this.list = list;
    }

    public static void main(String[] args) {
        printInit("com.aaron.spring.api.v3.model.RestBook","EnyanBook");
    }
}
