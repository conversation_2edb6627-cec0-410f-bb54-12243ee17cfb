package com.aaron.spring.api;

import com.aaron.spring.model.EnyanReading;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/6/28
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestReading extends RestBaseDTO{

	private static final long serialVersionUID = 4401405384105688525L;

	@JSONField(name = "name")
	private String dataName;

	@JSONField(name = "imgUrl")
	private String dataImgUrl;

	@JSONField(name = "toUrl")
	private String dataToUrl;

	@JSONField(name = "buyUrl")
	private String dataBuyUrl;

	private Integer dataStatus;

	public void initFrom(EnyanReading obj){
		this.dataName = obj.getDataName();
		this.dataImgUrl = obj.getDataImgUrl();
		this.dataToUrl = obj.getDataToUrl();
		this.dataBuyUrl = obj.getDataBuyUrl();
		this.dataStatus = obj.getDataStatus();
	}

	public String getDataName() {
		return dataName;
	}

	public void setDataName(String dataName) {
		this.dataName = dataName;
	}

	public String getDataImgUrl() {
		return dataImgUrl;
	}

	public void setDataImgUrl(String dataImgUrl) {
		this.dataImgUrl = dataImgUrl;
	}

	public String getDataToUrl() {
		return dataToUrl;
	}

	public void setDataToUrl(String dataToUrl) {
		this.dataToUrl = dataToUrl;
	}

	public String getDataBuyUrl() {
		return dataBuyUrl;
	}

	public void setDataBuyUrl(String dataBuyUrl) {
		this.dataBuyUrl = dataBuyUrl;
	}

	public Integer getDataStatus() {
		return dataStatus;
	}

	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}

	public static  void main(String[] args){
		printInit("com.aaron.spring.api.RestReading","EnyanReading");
	}
}
