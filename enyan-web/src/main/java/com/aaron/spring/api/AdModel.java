package com.aaron.spring.api;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-06-10
 * @Modified By:
 */
public class AdModel implements Serializable {
    private static final long serialVersionUID = 3676655913780091404L;
    @JsonIgnore
    private  int height;

    /*0:无跳过按钮; 1:文字跳过; 2:倒计时; 3:文字左、数字右; 4:文字右、数字左; 5:圆形文字; 6:圆形进度文字*/
    private  int skipBtnType = 3;

    private  int duration = 4;

    @JsonIgnore
    private  int width;

    @JSONField(serialize = false)
    private  int animationType;

    private  String imgUrl = "https://ehome.endao.co/book_image/detail/1591689891303.png";

    private String toUrl = "https://ebook.endao.co";

    private Long endDate = -1L;

    private Long beginDate = -1L;

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getSkipBtnType() {
        return skipBtnType;
    }

    public void setSkipBtnType(int skipBtnType) {
        this.skipBtnType = skipBtnType;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getAnimationType() {
        return animationType;
    }

    public void setAnimationType(int animationType) {
        this.animationType = animationType;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getToUrl() {
        return toUrl;
    }

    public void setToUrl(String toUrl) {
        this.toUrl = toUrl;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Long getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Long beginDate) {
        this.beginDate = beginDate;
    }
}
