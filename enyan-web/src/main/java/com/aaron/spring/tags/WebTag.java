package com.aaron.spring.tags;

import com.aaron.spring.common.Constant;
import com.aaron.spring.common.WebUtil;
import org.springframework.context.NoSuchMessageException;
import org.springframework.web.servlet.tags.HtmlEscapingAwareTag;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import java.io.IOException;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/4/9
 * @Modified By:
 */
public class WebTag extends HtmlEscapingAwareTag {
    public static final String CACHE_URL = "https://ehome.endao.co";
    public static final String LOCAL_URL = "http://localhost:8080";
//    public static final String TEST_URL = "https://ebookstore.endao.co";
//    public static final String PRODUCT_URL = "https://ebook.endao.co";
//    public static final String PRODUCT_LOGIN_URL = "https://ebook.endao.co/login";
//    public static final String PRODUCT_REG_URL = "https://ebook.endao.co/reg";
//    public static final String TEST_LOGIN_URL = "https://ebookstore.endao.co/login";
    private String type;
    private String value;

    @Override
    protected int doStartTagInternal() throws Exception {
        return EVAL_BODY_INCLUDE;
    }

    @Override
    public int doEndTag() throws JspException {
        try {
            // Resolve the unescaped message.
            String msg = resolveMessage();

            // HTML and/or JavaScript escape, if demanded.
            msg = htmlEscape(msg);
            writeMessage(msg);

        }
        catch (IOException ex) {
            throw new JspTagException(ex.getMessage(), ex);
        }
        catch (NoSuchMessageException ex) {
            //throw new JspTagException(getNoSuchMessageExceptionDescription(ex));
        }
        return EVAL_PAGE;
    }
    protected String resolveMessage() throws JspException, NoSuchMessageException {

        if ("login".equals(type)){
            if (Constant.IS_TEST || Constant.IS_PRODUCT){
                return WebUtil.getBasePathLogin();
            }
            return value;
        }
        if ("reg".equals(type)){
            if (Constant.IS_TEST || Constant.IS_PRODUCT){
                return WebUtil.getBasePathReg();
            }
            return value;
        }
        if ("test".equals(type)){//用于测试人员使用
            if (Constant.IS_TEST){
                return WebUtil.getBasePathLogin();
            }
            return value;
        }
        if (null != value){
            if (Constant.IS_LOCAL){
                return LOCAL_URL+value;
            }
            if (Constant.IS_TEST){
                return value;
            }
            return CACHE_URL+value;
        }

        // All we have is a specified literal text.
        return "";
    }
    protected void writeMessage(String msg) throws IOException {
        pageContext.getOut().write(String.valueOf(msg));
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
