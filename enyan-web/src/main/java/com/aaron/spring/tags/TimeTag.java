package com.aaron.spring.tags;

import com.aaron.spring.common.Constant;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.NoSuchMessageException;
import org.springframework.web.servlet.tags.HtmlEscapingAwareTag;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import java.io.IOException;
import java.util.Date;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/4/9
 * @Modified By:
 */
public class TimeTag extends HtmlEscapingAwareTag {
    public static final String PATTERN_BASIC = "yyyy-MM-dd HH:mm:ss";
    public static final String LOCAL_URL = "http://localhost:8080";
    private Long value;
    private String pattern;
    private String style;

    @Override
    protected int doStartTagInternal() throws Exception {
        return EVAL_BODY_INCLUDE;
    }

    @Override
    public int doEndTag() throws JspException {
        try {
            // Resolve the unescaped message.
            String msg = resolveMessage();

            // HTML and/or JavaScript escape, if demanded.
            msg = htmlEscape(msg);
            writeMessage(msg);

        }
        catch (IOException ex) {
            throw new JspTagException(ex.getMessage(), ex);
        }
        catch (NoSuchMessageException ex) {
            //throw new JspTagException(getNoSuchMessageExceptionDescription(ex));
        }
        return EVAL_PAGE;
    }
    protected String resolveMessage() throws JspException, NoSuchMessageException {

        if (null == value){
            return "";
        }
        Date date = new Date(value);
        if (StringUtils.isBlank(pattern)){
            pattern = PATTERN_BASIC;
        }

        // All we have is a specified literal text.
        return DateFormatUtils.format(date,pattern);
    }
    protected void writeMessage(String msg) throws IOException {
        pageContext.getOut().write(String.valueOf(msg));
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }
}
