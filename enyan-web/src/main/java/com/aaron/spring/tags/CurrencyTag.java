package com.aaron.spring.tags;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.CurrencyType;
import com.aaron.spring.model.EnyanBook;
import com.aaron.util.CookieUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.NoSuchMessageException;
import org.springframework.web.servlet.tags.HtmlEscapingAwareTag;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import java.io.IOException;

/**
 *
 *
 * 人民币        ¥CNY	  RMB¥
 * 新台币	    $TWD	  NT$
 * 港元  	    $HKD	  HK$
 * 美元 	    $USD	  US$
 * 马来西亚林吉特 RM MYR   RM
 * 印尼盾       Rp IDR	  Rp
 * 加拿大元	    $CAD	  CA$
 * 新加坡元  	$SGD	  SG$
 * 澳大利亚元	$AUD	  AU$
 * 欧元	        €EUR	   €
 * @Author: <PERSON>
 * @Date: Created in  2018/4/9
 * @Modified By:
 */
public class CurrencyTag extends HtmlEscapingAwareTag {


    private boolean headerMin=false;//是缩小版的header

    private boolean headerNormal=false;//是正常版的header

    private boolean priceName = false; //显示price的标识

    @Override
    protected int doStartTagInternal() throws Exception {
        return EVAL_BODY_INCLUDE;
    }

    @Override
    public int doEndTag() throws JspException {
        try {
            // Resolve the unescaped message.
            String msg = resolveMessage();

            // HTML and/or JavaScript escape, if demanded.
            msg = htmlEscape(msg);
            writeMessage(msg);

        }
        catch (IOException ex) {
            throw new JspTagException(ex.getMessage(), ex);
        }
        catch (NoSuchMessageException ex) {
            //throw new JspTagException(getNoSuchMessageExceptionDescription(ex));
        }
        return EVAL_PAGE;
    }
    protected String resolveMessage() throws JspException, NoSuchMessageException {

        StringBuffer stringBuffer = new StringBuffer();
        if (headerMin){
            for (int i = 0; i < CurrencyType.values().length; i++) {
                //<a class="text-lg" href="/shop/currency/TW/${fn:replace(requestScope['javax.servlet.forward.request_uri'], "/shop/", "")}">$ USD</a>
                // /shop/currency/¥CNY//shop/index
                // /shop/currency/CN/index
                if (i != 0){
                    if (i%2==0){
                        stringBuffer.append("<br/>");
                    }
                    if (i%2==1){
                        stringBuffer.append("&nbsp;&nbsp;&#124;&nbsp;&nbsp;");
                    }
                }
                CurrencyType currencyType = CurrencyType.values()[i];
                stringBuffer.append("<a class=\"text-lg\" href=\"/currency/");
                stringBuffer.append(currencyType.getHeaderName());
                String toUrl = (null == this.pageContext.getRequest().getAttribute("javax.servlet.forward.request_uri") ? null : this.pageContext.getRequest().getAttribute("javax.servlet.forward.request_uri").toString());
                if (StringUtils.isEmpty(toUrl)){
                    toUrl = "index";
                }
                if (!toUrl.startsWith("/")) {
                    stringBuffer.append("/");
                }
                stringBuffer.append(toUrl);
                stringBuffer.append("\">");
                stringBuffer.append(currencyType.getHeaderName());
                stringBuffer.append("</a>");
            }
            return stringBuffer.toString();
        }
        if (headerNormal){
            String currentCurrency = this.pageContext.getRequest().getAttribute("currency").toString();
            //<a class="text-lg" href="/shop/currency/TW/${fn:replace(requestScope['javax.servlet.forward.request_uri'], "/shop/", "")}">$ USD</a>
            // /shop/currency/¥CNY//shop/index
            // /shop/currency/CN/index

            /**
             <div class="lang-currency-switcher dropdown-toggle" id="currencyCN">
             <span class="currency" id="currency-active">$ USD</span>
             </div>
             <div class="dropdown-menu" >
             <a class="dropdown-item" href="/shop/currency/CN/${fn:replace(requestScope['javax.servlet.forward.request_uri'], "/shop/", "")}" id="currency-active-not">¥ CNY</a>
             </div>
             */
            for (CurrencyType currencyType:CurrencyType.values()){
                if (currencyType.getHeaderName().equals(currentCurrency)){
                    stringBuffer.append("<div class=\"lang-currency-switcher dropdown-toggle\" id=\"currencyCN\"><span class=\"currency\" id=\"currency-active\">");
                    stringBuffer.append(currencyType.getHeaderName());
                    stringBuffer.append("</span></div>");
                }
            }
            stringBuffer.append("<div class=\"dropdown-menu\" >");
            for (CurrencyType currencyType:CurrencyType.values()){
                if (currencyType.getHeaderName().equals(currentCurrency)){
                    continue;
                }
                stringBuffer.append("<a class=\"dropdown-item\" href=\"/currency/");
                stringBuffer.append(currencyType.getHeaderName());
                String toUrl = this.pageContext.getRequest().getAttribute("javax.servlet.forward.request_uri").toString();
                if (StringUtils.isEmpty(toUrl) || toUrl.equals("/")){
                    toUrl = "index";
                }
                if (!toUrl.startsWith("/")) {
                    stringBuffer.append("/");
                }
                stringBuffer.append(toUrl);

                stringBuffer.append("\" id=\"currency-active-not\">");
                stringBuffer.append(currencyType.getHeaderName());
                stringBuffer.append("</a>");
            }
            stringBuffer.append("</div>");
            /*
            for (int i = 0; i < CurrencyType.values().length; i++) {
                CurrencyType currencyType = CurrencyType.values()[i];
                if (currencyType.getHeaderName().equals(currentCurrency)){
                    stringBuffer.append("<div class=\"lang-currency-switcher dropdown-toggle\" id=\"currencyCN\"><span class=\"currency\" id=\"currency-active\">");
                    stringBuffer.append(currencyType.getHeaderName());
                    stringBuffer.append("</span></div>");
                }else{
                    stringBuffer.append("<div class=\"dropdown-menu\" >");
                    stringBuffer.append("<a class=\"dropdown-item\" href=\"/shop/currency/");
                    stringBuffer.append(currencyType.getHeaderName());
                    stringBuffer.append("/");
                    stringBuffer.append(this.pageContext.getRequest().getAttribute("javax.servlet.forward.request_uri").toString().replace("/shop/",""));
                    stringBuffer.append("\" id=\"currency-active-not\">");
                    stringBuffer.append(currencyType.getHeaderName());
                    stringBuffer.append("</a></div>");
                }
            }*/
            return stringBuffer.toString();
        }
        if (priceName){
            String currentCurrency = this.pageContext.getRequest().getAttribute("currency").toString();
            return CurrencyType.getPriceNameByHeaderName(currentCurrency).getPriceName();
        }
        return "";
    }


    protected void writeMessage(String msg) throws IOException {
        pageContext.getOut().write(String.valueOf(msg));
    }

    public void setHeaderMin(boolean headerMin) {
        this.headerMin = headerMin;
    }

    public void setHeaderNormal(boolean headerNormal) {
        this.headerNormal = headerNormal;
    }

    public void setPriceName(boolean priceName) {
        this.priceName = priceName;
    }

    public static void main(String[] args) {
    }
}
