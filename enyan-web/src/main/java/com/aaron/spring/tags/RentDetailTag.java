package com.aaron.spring.tags;

import com.aaron.spring.common.AaronHtmlUtils;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.EnyanRent;
import com.aaron.spring.model.EnyanRentDetail;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.NoSuchMessageException;
import org.springframework.web.servlet.tags.HtmlEscapingAwareTag;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import java.io.IOException;
import java.util.Date;

/**
 *
 * @Author: Aaron <PERSON>
 * @Date: Created in  2018/4/9
 * @Modified By:
 */
public class RentDetailTag extends HtmlEscapingAwareTag {
    private String infoType;
    private EnyanRentDetail enyanRentDetail;
    private boolean trimEmpty;

    @Override
    protected int doStartTagInternal() throws Exception {
        return EVAL_BODY_INCLUDE;
    }

    @Override
    public int doEndTag() throws JspException {
        try {
            // Resolve the unescaped message.
            String msg = resolveMessage();

            // HTML and/or JavaScript escape, if demanded.
            msg = htmlEscape(msg);
            writeMessage(msg);

        }
        catch (IOException ex) {
            throw new JspTagException(ex.getMessage(), ex);
        }
        catch (NoSuchMessageException ex) {
            //throw new JspTagException(getNoSuchMessageExceptionDescription(ex));
        }
        return EVAL_PAGE;
    }
    protected String resolveMessage() throws JspException, NoSuchMessageException {
        if (StringUtils.isBlank(infoType)){
            return "";
        }

        if ("rentName".equals(infoType)){
            return BookUtil.getBookNameInRentType(enyanRentDetail.getRentType(),enyanRentDetail.getRentLang());
        }

        // All we have is a specified literal text.
        return "";
    }
    protected void writeMessage(String msg) throws IOException {
        pageContext.getOut().write(String.valueOf(msg));
    }



    public String getInfoType() {
        return infoType;
    }

    public void setInfoType(String infoType) {
        this.infoType = infoType;
    }

    public EnyanRentDetail getEnyanRentDetail() {
        return enyanRentDetail;
    }

    public void setEnyanRentDetail(EnyanRentDetail enyanRentDetail) {
        this.enyanRentDetail = enyanRentDetail;
    }

    public boolean isTrimEmpty() {
        return trimEmpty;
    }

    public void setTrimEmpty(boolean trimEmpty) {
        this.trimEmpty = trimEmpty;
    }
}
