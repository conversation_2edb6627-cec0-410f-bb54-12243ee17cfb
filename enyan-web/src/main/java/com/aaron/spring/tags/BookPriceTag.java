package com.aaron.spring.tags;

import com.aaron.common.Money;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.CurrencyType;
import com.aaron.spring.model.EnyanBook;
import org.springframework.context.NoSuchMessageException;
import org.springframework.web.servlet.tags.HtmlEscapingAwareTag;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Locale;

import static com.aaron.common.Money.DEFAULT_ROUNDING;

/**
 *
 * @Author: Aaron <PERSON>
 * @Date: Created in  2018/4/9
 * @Modified By:
 */
public class BookPriceTag extends HtmlEscapingAwareTag {

    private EnyanBook enyanBook;

    private boolean isDiscount = false;

    private String currency;

    private boolean index=false;//是首页

    private boolean indexBottom=false;//是首页下部

    private boolean category=false;//是分类页

    private boolean bookInfo=false;//是详情页

    private boolean bookWish=false;//是收藏

    private boolean cart = false;//是购物车

    private boolean order = false;//是我的订单页面

    private boolean orderConfirm = false;//是我的订单确定页面

    private boolean orderDetail = false;//是我的订单确定页面

    private boolean bookSet = false;//是书系页面

    private float priceToDo;//要处理的价格

    private String currentCurrency;//中间变量
    private CurrencyType currencyType;//中间变量

    @Override
    protected int doStartTagInternal() throws Exception {
        return EVAL_BODY_INCLUDE;
    }

    @Override
    public int doEndTag() throws JspException {
        try {
            // Resolve the unescaped message.
            String msg = resolveMessage();

            // HTML and/or JavaScript escape, if demanded.
            msg = htmlEscape(msg);
            writeMessage(msg);

        }
        catch (IOException ex) {
            throw new JspTagException(ex.getMessage(), ex);
        }
        catch (NoSuchMessageException ex) {
            //throw new JspTagException(getNoSuchMessageExceptionDescription(ex));
        }
        return EVAL_PAGE;
    }
    protected String resolveMessage() throws JspException, NoSuchMessageException {
        currentCurrency = this.pageContext.getRequest().getAttribute("currency") == null ? null:this.pageContext.getRequest().getAttribute("currency").toString();
        currencyType = CurrencyType.getPriceNameByHeaderName(currentCurrency);
        if (null != enyanBook){
            isDiscount = enyanBook.getDiscountSingleIsValid().equals(Constant.BYTE_VALUE_1);
        }

        StringBuffer stringBuffer = new StringBuffer();
        if (index){
            stringBuffer.append("<h6 class=\"text-muted\">");
            if (isDiscount){
                stringBuffer.append("<del>");
                stringBuffer.append(getPrice());
                stringBuffer.append("</del>");

                stringBuffer.append("<span class=\"text-danger\">");
                stringBuffer.append(getDiscountPrice());
                stringBuffer.append("</span>");
            }else {
                stringBuffer.append("<span class=\"text-danger\">");
                stringBuffer.append(getPrice());
                stringBuffer.append("</span>");
            }

            stringBuffer.append("</h6>");

            //stringBuffer.append("<h6 class=\"text-muted\">");
            stringBuffer.append("<span class=\"h7 text-primary\">(");
            stringBuffer.append(getCurrencyPrice());
            stringBuffer.append(")</span>");
//            stringBuffer.append("</h6>");

            return stringBuffer.toString();
        }
        if (indexBottom||category||bookWish){
            stringBuffer.append("<h4 class=\"product-price\">");
            if (isDiscount){
                stringBuffer.append("<del>");
                stringBuffer.append(getPrice());
                stringBuffer.append("</del>");

                stringBuffer.append("<span class=\"text-danger\">");
                stringBuffer.append(getDiscountPrice());
                stringBuffer.append("</span>");
            }else {
                stringBuffer.append("<span class=\"text-danger\">");
                stringBuffer.append(getPrice());
                stringBuffer.append("</span>");
            }
            stringBuffer.append("</h4>");

            stringBuffer.append("<h4 class=\"product-price\">");
            stringBuffer.append("<span class=\"text-sm text-danger\">(");
            stringBuffer.append(getCurrencyPrice());
            stringBuffer.append(")</span>");
            stringBuffer.append("</h4>");

            return stringBuffer.toString();
        }
        if (bookSet){
            stringBuffer.append("<h4 class=\"product-price text-danger\">");
            if (isDiscount){
                stringBuffer.append("<del>");
                stringBuffer.append(getPrice());
                stringBuffer.append("</del>");

                stringBuffer.append("<span>");
                stringBuffer.append(getDiscountPrice());
                stringBuffer.append("</span>");
            }else {
                stringBuffer.append("<span>");
                stringBuffer.append(getPrice());
                stringBuffer.append("</span>");
            }

            stringBuffer.append("<span>(");
            stringBuffer.append(getCurrencyPrice());
            stringBuffer.append(")</span>");
            stringBuffer.append("</h4>");

            return stringBuffer.toString();
        }
        if (bookWish){
            if (isDiscount){
                stringBuffer.append("<h4 class=\"product-price\">");
                stringBuffer.append("<del>");
                stringBuffer.append(getPrice());
                stringBuffer.append("</del>");
                stringBuffer.append("</h4>");

                stringBuffer.append("<h4 class=\"product-price\">");
                stringBuffer.append("<span class=\"text-danger\">");
                stringBuffer.append(getDiscountPrice());
                stringBuffer.append("</span>");
                stringBuffer.append("</h4>");
            }else {
                stringBuffer.append("<h4 class=\"product-price\">");
                stringBuffer.append("<span class=\"text-danger\">");
                stringBuffer.append(getPrice());
                stringBuffer.append("</span>");
                stringBuffer.append("</h4>");
            }


            stringBuffer.append("<h4 class=\"product-price\">");
            stringBuffer.append("<span class=\"text-sm text-danger\">(");
            stringBuffer.append(getCurrencyPrice());
            stringBuffer.append(")</span>");
            stringBuffer.append("</h4>");

            return stringBuffer.toString();
        }
        if (bookInfo){
            if (isDiscount){
                stringBuffer.append("<del class=\"text-muted text-normal\">");
                stringBuffer.append(getPrice());
                stringBuffer.append("</del>&nbsp;");

                stringBuffer.append("<span class=\"text-primary\">");
                stringBuffer.append(getDiscountPrice());
                stringBuffer.append("</span>");
            }else {
                stringBuffer.append("<span class=\"text-primary\">");
                stringBuffer.append(getPrice());
                stringBuffer.append("</span>");
            }

            stringBuffer.append("<span class=\"h6 text-primary\">(");
            stringBuffer.append(getCurrencyPrice());
            stringBuffer.append(")</span>");

            return stringBuffer.toString();
        }

        if (cart){
            return getCurrencyByPriceValue(new BigDecimal(priceToDo));
        }

        if (order){
            stringBuffer.append("<div class=\"column text-lg\">(");
            stringBuffer.append(getCurrencyByPrice(new BigDecimal(priceToDo)));
            stringBuffer.append(")</div>");
            return stringBuffer.toString();
        }
        if (orderDetail){
            stringBuffer.append("<span class=\"text-medium\">(");
            stringBuffer.append(getCurrencyByPrice(new BigDecimal(priceToDo)));
            stringBuffer.append(")</span>");
            return stringBuffer.toString();
        }
        if (orderConfirm){
            stringBuffer.append("<h4 class=\"product-price\">");
            stringBuffer.append("<span class=\"text-medium\">(");
            stringBuffer.append(getCurrencyByPrice(new BigDecimal(priceToDo)));
            stringBuffer.append(")</span>");
            stringBuffer.append("</h4>");

            return stringBuffer.toString();
        }
        if (isDiscount){
            return "HK$"+enyanBook.getPriceHKDDiscount();
        }else {
            return "HK$"+enyanBook.getPriceHkd();
        }

        /*if (Locale.TRADITIONAL_CHINESE.getCountry().equals(currency)){
            if (isDiscount){
                return "$"+enyanBook.getPriceUSDDiscount();
            }else {
                return "$"+enyanBook.getPriceUsd();
            }
        }else {
            if (isDiscount){
                return "¥"+enyanBook.getPriceCnyDiscount();
            }else {
                return "¥"+enyanBook.getPriceCny();
            }
        }*/
    }

    private String getDiscountPrice(){
        return "HK$"+enyanBook.getPriceHKDDiscount();
       /* if (Locale.TRADITIONAL_CHINESE.getCountry().equals(currency)){
            return "$"+enyanBook.getPriceUSDDiscount();
        }else {
            return "¥"+enyanBook.getPriceCnyDiscount();
        }*/
    }

    private String getPrice(){
        return "HK$"+enyanBook.getPriceHkd();
        /*if (Locale.TRADITIONAL_CHINESE.getCountry().equals(currency)){
            return "$"+enyanBook.getPriceUsd();
        }else {
            return "¥"+enyanBook.getPriceCny();
        }*/
    }
    /**
     *
     * HK$120
     * @Date: 2020-01-17
     */
    private String getCurrencyPrice(){
        if (null == enyanBook){
            return "";
        }
        if (isDiscount){
            return getCurrencyByPrice(enyanBook.getPriceHKDDiscount());
        }
        return getCurrencyByPrice(enyanBook.getPriceHkd());
    }
    /**
     *
     * HK$120
     * @param price
     * @Date: 2020-01-17
     */
    private String getCurrencyByPrice(BigDecimal price){
        if (null == price){
            return "";
        }
        return currencyType.getPriceName()+ getCurrencyByPriceValue(price);
        //return currencyType.getPriceName()+ (price);
    }
    //根据价格获取currency价格
    /**
     *
     * 根据价格获取currency价格: 120
     * @param price
     * @Date: 2020-01-17
     */
    private String getCurrencyByPriceValue(BigDecimal price){
        if (null == price){
            return "";
        }
        if (CurrencyType.HKD.getValue().doubleValue() > 0){
            BigDecimal value = price.multiply(currencyType.getValue()).divide(CurrencyType.HKD.getValue(),Constant.NUM_SCALE_2,BigDecimal.ROUND_HALF_UP);
            //System.out.println(CurrencyType.HKD.getValue()+","+value.toString()+","+currencyType.getHeaderName());
            return value.toString();
        }
        return "";
    }
    protected void writeMessage(String msg) throws IOException {
        pageContext.getOut().write(String.valueOf(msg));
    }

    public void setEnyanBook(EnyanBook enyanBook) {
        this.enyanBook = enyanBook;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public void setIndex(boolean index) {
        this.index = index;
    }

    public void setIndexBottom(boolean indexBottom) {
        this.indexBottom = indexBottom;
    }

    public void setCategory(boolean category) {
        this.category = category;
    }

    public void setBookInfo(boolean bookInfo) {
        this.bookInfo = bookInfo;
    }

    public void setBookWish(boolean bookWish) {
        this.bookWish = bookWish;
    }

    public void setPriceToDo(float priceToDo) {
        this.priceToDo = priceToDo;
    }

    public void setCart(boolean cart) {
        this.cart = cart;
    }

    public void setOrder(boolean order) {
        this.order = order;
    }

    public void setOrderConfirm(boolean orderConfirm) {
        this.orderConfirm = orderConfirm;
    }

    public void setOrderDetail(boolean orderDetail) {
        this.orderDetail = orderDetail;
    }

    public boolean isBookSet() {
        return bookSet;
    }

    public void setBookSet(boolean bookSet) {
        this.bookSet = bookSet;
    }
}
