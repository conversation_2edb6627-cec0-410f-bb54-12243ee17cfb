package com.aaron.spring.controller;

import com.aaron.spring.model.DataRentStat;
import com.aaron.spring.service.DataRentStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/9/27
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/dataRentStat")
public class DataRentStatController extends BaseController{
	@Resource
	private DataRentStatService dataRentStatService;

	@GetMapping("/ui")
	public String index(Model model, DataRentStat dto){
		//dto = dataRentStatService.basicInfo();
		model.addAttribute("dto", dto);
		return "admin/dataRentStatUI";
	}

	@PostMapping("/list")
	public String list(HttpServletRequest request, Model model, DataRentStat dto){
		if (null == dto){
			dto = new DataRentStat();
		}

		Map<String, Object> queryParams = new HashMap<>();
		//orderDetail.getPage().setPageSize(1);
		// 高级查询条件：
		String startDate, endDate ;

		String rangeDate = request.getParameter("rangeDate");
		if (StringUtils.hasLength(rangeDate) == false){
			this.setErrorMsg(model, this.getMessage("error.time.startOrEnd.null",request));
			model.addAttribute("dto",dto);
			return "admin/dataRentStatUI";
		}
		String[] rangeDateArray = rangeDate.split("-");
		if (rangeDateArray.length != 2){
			this.setErrorMsg(model, this.getMessage("error.time.startOrEnd.null",request));
			model.addAttribute("dto",dto);
			return "admin/dataRentStatUI";
		}
		startDate = rangeDateArray[0].trim();
		endDate = rangeDateArray[1].trim();

		if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
			this.setErrorMsg(model, this.getMessage("error.time.startOrEnd.null",request));
			model.addAttribute("dto",dto);
			return "admin/dataRentStatUI";
		}

		queryParams.put("startDate",startDate);
		queryParams.put("endDate",endDate);

		String searchType = request.getParameter("searchType");

		searchType = StringUtils.hasLength(searchType) == false?"0":searchType;

		List<DataRentStat> list = null;
		String toUrl = "admin/dataRentStatListSum";
		switch (searchType){
			case "0"://day
				list = dataRentStatService.findDataByDay(startDate, endDate);
				toUrl = "admin/dataRentStatList";
				break;
			case "1"://week
				break;
			case "2"://month
				list = dataRentStatService.findDataByMonth(startDate, endDate);
				break;
			case "3"://year
				list = dataRentStatService.findDataByYear(startDate, endDate);
				break;
		}

		queryParams.put("searchType",searchType);
		model.addAttribute("list",list);
		model.addAttribute("dto",dto);
		return toUrl;
	}
}
