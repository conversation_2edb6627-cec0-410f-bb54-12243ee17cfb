package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.excel.ExcelImportUtil;
import com.aaron.excel.FileUtil;
import com.aaron.excel.util.ExcelReaderMultiFromBook;
import com.aaron.excel.util.ExcelReaderMultiFromCheckin;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.SystemUtil;
import com.aaron.spring.model.StuEnroll;
import com.aaron.spring.model.StuInfo;
import com.aaron.spring.service.EnyanCategoryService;
import com.aaron.spring.service.StuEnrollService;
import com.aaron.spring.service.StuInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2023/5/10
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/checkin")
public class CheckinController extends BaseController{
	@Resource
	private StuInfoService stuInfoService;

	@Resource
	private StuEnrollService stuEnrollService;

	@RequestMapping(value = "/students")
	public String students(HttpServletRequest req, StuInfo record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		List<StuInfo> list = stuInfoService.findRecordsInCurrentTerms();
		modelMap.addAttribute("list",list);
		modelMap.addAttribute("explan","文章管理");
		return "checkin/studentList.html";
	}

	@RequestMapping(value = "/print")
	public String print(HttpServletRequest req, StuInfo record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		List<StuInfo> list = stuInfoService.findRecordsInCurrentTerms();
		modelMap.addAttribute("list",list);
		modelMap.addAttribute("explan","文章管理");
		return "checkin/print.html";
	}

	@RequestMapping(value = "/enrolls")
	public String enrolls(HttpServletRequest req, StuInfo record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		List<StuEnroll> list = stuEnrollService.findRecordsInCurrentTerms();
		modelMap.addAttribute("list",list);
		modelMap.addAttribute("explan","文章管理");
		return "checkin/enrollList.html";
	}

	@RequestMapping(value = "/import")
	public String importInfo(HttpServletRequest req, StuInfo record, ModelMap modelMap){
		return "checkin/import.html";
	}

	@PostMapping(value = "/importExcelAction")
	public String importAction(@RequestParam(value = "file") MultipartFile file, StuInfo record, ModelMap modelMap){
		log.debug("importAction");
		//学员名	电子邮件	密码	报到码	班级	宿舍	组名	是否组长	讨论房间
		if (file == null || file.isEmpty()) {
			this.setErrorMsg(modelMap, "请选择Excel！");
			return "checkin/import.html";
		}

		File picFile ;
		try {
			// 获取图片的文件名
			String fileName = file.getOriginalFilename();
			// 获取图片的扩展名
			String extensionName = fileName.substring(fileName.lastIndexOf(".") + 1);

			// 新的图片文件名 = 获取时间戳+"."图片扩展名
			String newFileName = fileName+"_"+ System.currentTimeMillis() + "." + extensionName;


			//enyanImgService.addRecord(enyanImg);

			String filePath = SystemUtil.getExcelDir()+newFileName;
			picFile = new File(filePath);
			file.transferTo(picFile);
			//picFile.setWritable(true);//设置可写权限
			//picFile.setExecutable(true);//设置可执行权限
			//picFile.setReadable(true);//设置可读权限

			Runtime.getRuntime().exec("chmod 777 -R " + filePath);

			ExcelReaderMultiFromCheckin excelReaderMuti = new ExcelReaderMultiFromCheckin();
			excelReaderMuti.setBatchReadBeginRow(1);//从1开始
			excelReaderMuti.setStuEnrollService(stuEnrollService);
			excelReaderMuti.setStuInfoService(stuInfoService);
			ExcelImportUtil.readExcel(filePath, excelReaderMuti, excelReaderMuti, excelReaderMuti.getBatchReadBeginRow());

			log.error("game over...");

		} catch (Exception e) {
			log.error("上传图片失败.", e);
			modelMap.addAttribute("msg", "上传Excel失败！");
			this.setErrorMsg(modelMap, "上传Excel失败！");

			return "checkin/import.html";
		}
		return "redirect:/checkin/students";
	}


	@RequestMapping(value = "/template-checkin", method = RequestMethod.GET)
	public ResponseEntity<byte[]> excelDownload(HttpServletRequest request, ModelMap modelMap){
		log.debug("excelDownload");
		ResponseEntity<byte[]> entity = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型

			String templateName = "checkinImport.xlsx";
			String fileName = "学员导入Excel.xlsx";

			File file = new File(FileUtil.getTemplatePath(templateName));
			String downloadFileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");;

			headers.setContentDispositionFormData("attachment", downloadFileName);//告知浏览器以下载方式打开
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
			entity = new ResponseEntity<>(FileUtils.readFileToByteArray(file), headers, HttpStatus.OK);
			return entity;
			//ExcelExportUtil.createExcel();
		}catch (Exception e){
			e.printStackTrace();
			log.error(e.getMessage());
		}
		return entity;
	}

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		StuInfo record = stuInfoService.queryRecordByPrimaryKey(id).getResult();

		modelMap.addAttribute("record",record);
		modelMap.addAttribute("publisherList", Constant.publishersList);
		return "admin/blogAdd";
	}
}
