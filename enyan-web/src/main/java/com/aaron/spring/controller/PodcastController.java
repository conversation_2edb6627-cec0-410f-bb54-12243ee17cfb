package com.aaron.spring.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.service.PodPodcastService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description: 播客后台管理控制器
 */
@Slf4j
@Controller
@RequestMapping("/podcast")
public class PodcastController extends BaseController {

    @Resource
    private PodPodcastService podPodcastService;
    
    @RequestMapping(value = "/list")
    public String list(HttpServletRequest req, PodPodcast record, ModelMap modelMap) {
        if (null == record) {
            record = new PodPodcast();
        }
        if (null == record.getPage()) {
            record.setPage(new Page());
        }
        
        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            record.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            record.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件
        Map<String, Object> queryParams = new HashMap<>();
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        
        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType) {
                case "0": // 按名称搜索
                    record.setTitle(searchText);
                    break;
                case "1": // 按ID搜索
                    record.setPodcastId(Long.parseLong(searchText));
                    break;
            }
        }

        Page<PodPodcast> page = podPodcastService.queryRecords(record.getPage(), record);
        modelMap.put("page", page);
        modelMap.put("record", record);
        
        return "podcast/list";
    }
    
    @RequestMapping("/addUI")
    public String addPodcastUI(PodPodcast record, ModelMap modelMap) {
        log.debug("method addPodcastUI");
        record.setIsDeleted(0);
        modelMap.put("record", record);
        return "podcast/podcastAdd";
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public String savePodcast(HttpServletRequest request, PodPodcast record, ModelMap modelMap) {
        log.debug("method savePodcast：" + record);
        
        // 参数验证
        if (StringUtils.hasLength(record.getTitle()) == false) {
            this.setErrorMsg(modelMap, "请填写播客名称");
            modelMap.addAttribute("record", record);
            return "podcast/podcastAdd";
        }
        
        if (StringUtils.hasLength(record.getAuthorName()) == false) {
            this.setErrorMsg(modelMap, "请填写作者名称");
            modelMap.addAttribute("record", record);
            return "podcast/podcastAdd";
        }

        // 新增逻辑
        if (null == record.getPodcastId()) {
            this.setSuccessMsg(modelMap, "添加播客成功");
            record.setEpisodeCount(0);
            record.setIsPublished(0);
            record.setIsDeleted(0);
            record.setCreatedAt(new Date());
            podPodcastService.addRecord(record);
        } 
        // 修改逻辑
        else {
            this.setSuccessMsg(modelMap, "修改播客成功");
            podPodcastService.updateRecord(record);
        }
        
        return "redirect:/podcast/list";
    }

    @RequestMapping("/del-{id}")
    public String deletePodcast(@PathVariable Long id) {
        podPodcastService.deleteRecordByPrimaryKey(id);
        return "redirect:/podcast/list";
    }

    @RequestMapping(value = "/get-{id}")
    public String get(@PathVariable Long id, ModelMap modelMap) {
        ExecuteResult<PodPodcast> result = podPodcastService.queryRecordByPrimaryKey(id);
        modelMap.put("podcast", result.getResult());
        return "podcast/podcastAdd";
    }
    

}
