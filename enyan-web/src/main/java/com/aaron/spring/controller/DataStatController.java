package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.excel.ExcelExportUtil;
import com.aaron.excel.ExcelTemplateUtil;
import com.aaron.excel.FileUtil;
import com.aaron.excel.GroupCode;
import com.aaron.spring.common.ExcelDownloadUtil;
import com.aaron.spring.model.DataStat;
import com.aaron.spring.model.EnyanSubscription;
import com.aaron.spring.service.DataStatService;
import com.aaron.spring.service.EnyanSubscriptionService;
import com.aaron.util.UserUtils;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.ParseException;
import java.util.*;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2021/9/27
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/dataStat")
public class DataStatController extends BaseController{
	@Resource
	private DataStatService dataStatService;

	@Resource
	private EnyanSubscriptionService enyanSubscriptionService;

	@PostMapping("/post")
	public String post(){
		return "";
	}

	@GetMapping("/ui")
	public String index(Model model, DataStat dto){
		dto = dataStatService.basicInfo();
		model.addAttribute("dto", dto);
		return "admin/dataStatUI";
	}

	@PostMapping("/list")
	public String list(HttpServletRequest request, Model model, DataStat dto) throws ParseException {
		if (null == dto){
			dto = new DataStat();
		}

		Map<String, Object> queryParams = new HashMap<>();
		//orderDetail.getPage().setPageSize(1);
		// 高级查询条件：
		String startDate, endDate ;

		String rangeDate = request.getParameter("rangeDate");
		if (StringUtils.hasLength(rangeDate) == false){
			this.setErrorMsg(model, this.getMessage("error.time.startOrEnd.null",request));
			model.addAttribute("dto",dto);
			return "admin/dataStatUI";
		}
		String[] rangeDateArray = rangeDate.split("-");
		if (rangeDateArray.length != 2){
			this.setErrorMsg(model, this.getMessage("error.time.startOrEnd.null",request));
			model.addAttribute("dto",dto);
			return "admin/dataStatUI";
		}
		startDate = rangeDateArray[0].trim();
		endDate = rangeDateArray[1].trim();

		if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
			this.setErrorMsg(model, this.getMessage("error.time.startOrEnd.null",request));
			model.addAttribute("dto",dto);
			return "admin/dataStatUI";
		}

		queryParams.put("startDate",startDate);
		queryParams.put("endDate",endDate);

		String searchType = request.getParameter("searchType");

		searchType = StringUtils.hasLength(searchType) == false?"0":searchType;

		List<DataStat> list = null;

		switch (searchType){
			case "0"://day
				list = dataStatService.findDataByDay(startDate, endDate);
				break;
			case "1"://week
				break;
			case "2"://month
				list = dataStatService.findDataByMonth(startDate, endDate);
				break;
			case "3"://year
				list = dataStatService.findDataByYear(startDate, endDate);
				break;
		}
		JSONObject object = new JSONObject();
		JSONArray arrayTotal = new JSONArray();
		JSONArray array1 = new JSONArray();
		JSONArray array2 = new JSONArray();
		JSONArray array3 = new JSONArray();
		JSONArray array4 = new JSONArray();
		JSONArray array5 = new JSONArray();
		JSONArray array6 = new JSONArray();

		long minX = 0;
		for (DataStat dataStat:list){
			//int index = Integer.parseInt(dataStat.getDateString());
			String index = dataStat.getDateString();
			//Date date = DateUtils.parseDate(dataStat.getDateString(), InterfaceContant.DateFormatCustom.NONE_DATE);
			//long index = date.getTime();//+6*3600*1000
			JSONArray tmp1 = new JSONArray(index,dataStat.getSalesVolume());//销量
			JSONArray tmp2 = new JSONArray(index,dataStat.getOrderCount());//订单数
			JSONArray tmp3 = new JSONArray(index,dataStat.getOrderFeeCount());//付费订单数
			JSONArray tmp4 = new JSONArray(index,dataStat.getUserBuyCount());//下单用户数
			JSONArray tmp5 = new JSONArray(index,dataStat.getIncomeTotal());//销售额(HK$)
			JSONArray tmp6 = new JSONArray(index,dataStat.getUserNewCount());//新增注册数

			array1.add(tmp1);
			array2.add(tmp2);
			array3.add(tmp3);
			array4.add(tmp4);
			array5.add(tmp5);
			array6.add(tmp6);

		}
		arrayTotal.add(array1);
		arrayTotal.add(array2);
		arrayTotal.add(array3);
		arrayTotal.add(array4);
		arrayTotal.add(array5);
		arrayTotal.add(array6);
		queryParams.put("searchType",searchType);
		model.addAttribute("list",list);
		model.addAttribute("dto",dto);
		model.addAttribute("arrayTotal",arrayTotal);
		return "admin/dataStatList";
	}

	@RequestMapping(value = "/excelDownloadSubscribe", method = RequestMethod.GET)
	public ResponseEntity<byte[]> excelDownloadSubscribe(HttpServletRequest request, ModelMap modelMap){
		log.debug("excelDownload");
		ResponseEntity<byte[]> entity = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型

			if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_ADMIN) == false){
				headers.setContentDispositionFormData("attachment", "excel.txt");//告知浏览器以下载方式打开
				entity = new ResponseEntity<>("下载失败001".getBytes(), headers, HttpStatus.OK);
				return entity;
			}

			List<EnyanSubscription> list = enyanSubscriptionService.findAllRecores();
			if (list.isEmpty()){
				//logger.info("downloadAcsm 下载失败");
				headers.setContentDispositionFormData("attachment", "excel.txt");//告知浏览器以下载方式打开
				headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
				entity = new ResponseEntity<>("无数据002".getBytes(), headers, HttpStatus.OK);
				return entity;
			}
			List<String> excelHeaders = Arrays.asList("序号","Email","时间");
			List<List<String>> dataList = new ArrayList<>();
			int i = 1;
			for (EnyanSubscription subscription:list){
				List<String> data = new ArrayList<>(3);
				data.add(i++ +"");
				data.add(subscription.getEmail());
				data.add(DateFormatUtils.format(subscription.getCreateAt(),"yyyy-MM-dd HH:mm:ss"));
				dataList.add(data);
			}
			/*
			dataList = this.getExcelData(list,redeemCodeList,refundList,downloadType,excelHeaders.size());

			XSSFWorkbook book = ExcelTemplateUtil.createHeaders(excelHeaders);
			if (book.getNumberOfSheets() > 0) {
				Row rowDel = book.getSheetAt(0).getRow(2);
				if (rowDel != null) {
					book.getSheetAt(0).removeRow(rowDel);
				}
			}
			SXSSFWorkbook workbook = new SXSSFWorkbook(book);
			String filePath = FileUtil.getExportPath(GroupCode.OTHER, DateFormatUtils.format(new Date(),"yyyyMMddHHmmss"))+".xlsx";
			File file = new File(filePath);
			ExcelExportUtil.createExcel(dataList, filePath, 2, workbook);
			*/
			File file = ExcelDownloadUtil.getExcelFile(dataList, excelHeaders, GroupCode.OTHER);

			String fileName = "订阅的Email.xlsx";
			String downloadFileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");;

			headers.setContentDispositionFormData("attachment", downloadFileName);//告知浏览器以下载方式打开
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
			entity = new ResponseEntity<>(FileUtils.readFileToByteArray(file), headers, HttpStatus.OK);
			return entity;
			//ExcelExportUtil.createExcel();
		}catch (Exception e){
			e.printStackTrace();
			log.error(e.getMessage());
		}
		return entity;
	}
}
