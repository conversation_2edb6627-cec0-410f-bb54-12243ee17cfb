package com.aaron.spring.controller;

import com.aaron.a4j.util.ResultUtils;
import com.aaron.annotation.LimitRequest;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.data.model.SearchBook;
import com.aaron.data.repository.elasticsearch.BookSearchRepository;
import com.aaron.drm.util.DRMUtil;
import com.aaron.excel.ExcelImportUtil;
import com.aaron.excel.util.ExcelReaderMutiFromHighlight;
import com.aaron.excel.util.ExcelReaderMutiFromHighlightTest;
import com.aaron.exception.SystemException;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.security.function.service.UserService;
import com.aaron.spring.api.v4.controller.RestRentController;
import com.aaron.spring.api.v4.model.RestRent;
import com.aaron.spring.common.*;
import com.aaron.spring.dao.PayRateRepository;
import com.aaron.spring.entity.Mail;
import com.aaron.spring.model.*;
import com.aaron.spring.service.*;
import com.aaron.spring.task.OrderTaskJob;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2018/4/28
 * @Modified By:
 */
@Controller
@RequestMapping("/test")
public class TestController extends ShopController{

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private BaseDataService baseDataService;

    @Resource
    private EnyanOrderService enyanOrderService;

    @Resource
    private EmailService emailService;

    @Resource
    private EnyanWishService enyanWishService;

    @Resource
    private AuthUserService authUserService;

    @Resource
    private UserService userService;

    //@Resource
    private EnyanAcsmService enyanAcsmService;

    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    //@Resource
    private PayRateRepository payRateRepository;

    @Resource
    private EnyanBalanceService enyanBalanceService;

    @Resource
    private EnyanConfigService enyanConfigService;

    @Resource
    private EnyanReaderHighlightService enyanReaderHighlightService;

    @Resource
    private EnyanCartService enyanCartService;

    @Resource
    private DataStatService dataStatService;

    @Resource
    private DataRentStatService dataRentStatService;

    @Resource
    private GeoIPLocationService geoIPLocationService;

    @Resource
    private EnyanBookBuyService enyanBookBuyService;

    @Resource
    private RestRentController restRentController;

    @Resource
    private OrderTaskJob orderTaskJob;

    @Resource
    private EnyanRentService enyanRentService;

    @Resource
    private EnyanPlanNoteService enyanPlanNoteService;

    @Resource
    private EnyanPlanService enyanPlanService;

    @Resource
    private EnyanRedeemCodeService enyanRedeemCodeService;

    /**
     *
     * 重置订单明细信息
     * 3、https://ebook.endao.co/test/testHello
     * http://localhost:8080/test/testHello
     * @param
     * @Date: 2022-03-9
     */
    @ResponseBody
    @RequestMapping(value = "/testHello")
    @LimitRequest(count = 3, time = 60000)
    public Map<String, Object> testHello(){
        Date today = new Date();
        return ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * <p>测试支付</p>
     * https://ebook.endao.co/test/testPay-E11882004907
     * https://ebookstore.endao.co/test/testPay-E11882004907
     * http://localhost:8080/test/testPay-E11882004907
     * @param orderNum
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @since : 2022/5/25
     **/
    @ResponseBody
    @RequestMapping(value = "/testPay-{orderNum}")
    public Map<String, Object> testPay(@PathVariable("orderNum")String orderNum,
                          ModelMap modelMap, HttpServletRequest request){
        //http://localhost:8080/test/testPay-ED512094895935066112

        EnyanOrder enyanOrder = new EnyanOrder();
        enyanOrder.setOrderNum(orderNum);
        List<EnyanOrder> list = enyanOrderService.findRecordsWithBLOBsByOrder(enyanOrder);
        //logger.debug("list size:"+list.size());
        if (!list.isEmpty()){
            enyanOrder = list.get(0);
            OrderPayInfo orderPayInfo = new OrderPayInfo();
            //logger.info("shop orderPayInfo.addAlipay()");
            //orderPayInfo.addAlipay(params,true);
            orderPayInfo.addDirect();
            this.paySuccess(enyanOrder,orderPayInfo,request);
        }
        return ResultUtils.getSuccessResultData(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * <p>测试支付</p>
     * https://ebook.endao.co/test/testAlipay-E11882004907
     * https://e.bookapp.cc/test/testAlipay-E11149829284
     * https://ebookstore.endao.co/test/testAlipay-E11882004907
     * http://localhost:8080/test/testAlipay-E11882004907
     * @param orderNum
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @since : 2022/5/25
     **/
    @ResponseBody
    @RequestMapping(value = "/testAlipay-{orderNum}")
    public Map<String, Object> testPayDirectWithAlipayFee(@PathVariable("orderNum")String orderNum,
                                       ModelMap modelMap, HttpServletRequest request){
        //http://localhost:8080/test/testPay-ED512094895935066112

        EnyanOrder enyanOrder = new EnyanOrder();
        enyanOrder.setOrderNum(orderNum);
        List<EnyanOrder> list = enyanOrderService.findRecordsWithBLOBsByOrder(enyanOrder);
        //logger.debug("list size:"+list.size());
        if (!list.isEmpty()){
            enyanOrder = list.get(0);
            OrderPayInfo orderPayInfo = new OrderPayInfo();
            //logger.info("shop orderPayInfo.addAlipay()");
            //orderPayInfo.addAlipay(params,true);
            orderPayInfo.addDirectWithAlipayFee();
            this.paySuccess(enyanOrder,orderPayInfo,request);
        }
        return ResultUtils.getSuccessResultData(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * <p>回收书籍</p>
     * https://ebook.endao.co/test/testRecycle-E11882004907-6
     * https://ebookstore.endao.co/test/testRecycle-E11882004907-6
     * http://localhost:8080/test/testRecycle-E11882004907-6
     * @param orderNum
     * @param bookId
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @since : 2022/5/10
     **/
    @ResponseBody
    @RequestMapping(value = "/testRecycle-{orderNum}-{bookId}")
    public Map<String, Object> testRecycle(@PathVariable("orderNum")String orderNum, @PathVariable("bookId")Long bookId){
        //http://localhost:8080/test/testPay-ED512094895935066112

        enyanBookBuyService.recycleBookByOrderNumAndBookId(orderNum, bookId);
        Date today = new Date();
        return ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * <p>发送密送email</p>
     * https://ebook.endao.co/test/testEmailSimple
     * https://ebookstore.endao.co/test/testEmailSimple
     * http://localhost:8080/test/testEmailSimple
     * @param request
     * @return: java.lang.String
     * @since : 2021/2/23
     */
    @ResponseBody
    @RequestMapping(value = "/testEmailSimple")
    public Map<String, Object> testEmailSimple(ModelMap modelMap, HttpServletRequest request){
        //http://localhost:8080/test/testEmailSimple
        this.sendMailTest(request);
        return ResultUtils.getSuccessResultData(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }
    /**
     * <p>发送密送email</p>
     * https://ebook.endao.co/test/testBccEmailSimple
     * https://ebookstore.endao.co/test/testBccEmailSimple
     * http://localhost:8080/test/testBccEmailSimple
     * @param request
     * @return: java.lang.String
     * @since : 2021/2/23
     */
    @ResponseBody
    @RequestMapping(value = "/testBccEmailSimple")
    public Map<String, Object> testBccEmailSimple(HttpServletRequest request){
        //http://localhost:8080/test/testBccEmailSimple
        this.sendMailBccTest(request);
        return ResultUtils.getSuccessResultData(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 重置首页的书籍
     * https://ebook.endao.co/test/resetIndexBooks
     * http://localhost:8080/test/resetIndexBooks
     * @param
     * @Date: 2020-05-06
     */
    @ResponseBody
    @RequestMapping(value = "/resetIndexBooks")
    public Map<String, Object> resetIndexBooks(){
        enyanBookService.initIndexAllInfo();

        return ResultUtils.getSuccessResultData(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }
    /**
     *
     * 重置订单明细信息
     * 3、https://ebook.endao.co/test/testCountOrder
     * http://localhost:8080/test/testCountOrder
     * @param
     * @Date: 2019-07-10
     */
    @ResponseBody
    @RequestMapping(value = "/testCountOrder")
    public Map<String, Object> testCountOrder(){
        Date today = new Date();
        today = DateUtils.setHours(today,0);
        today = DateUtils.setMinutes(today,0);
        today = DateUtils.setSeconds(today,0);
        today = DateUtils.setMilliseconds(today,0);

        enyanOrderDetailService.splitOrders(today);

        return ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
    }

    @ResponseBody
    @RequestMapping(value = "/testOrderExpired")
    public Map<String, Object> testOrderExpired(){

        ExecuteResult result = enyanOrderService.updateOrderExpired();

        return ResultUtils.getSuccessResultData(result);
    }

    @ResponseBody
    @RequestMapping(value = "/testRedis")
    public Map<String, Object> testRedis(){
        //http://localhost:8080/test/testRedis
        String key = "userName";

        redisTemplate.opsForValue().set(key,"1234");

        Date today = new Date();

        Map<String,Object> resultMap = ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
        resultMap.put(key,redisTemplate.opsForValue().get(key));

        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value = "/testAddRate")
    public Map<String, Object> testAddRate(){
        //http://localhost:8080/test/testAddRate

        Date today = new Date();

        EnyanPayRate enyanPayRate = new EnyanPayRate();
        enyanPayRate.setRateValue("1222");
        enyanPayRate.setRateType(1);
        enyanPayRate.setRateTime(33333);
        enyanPayRate.setRateDate(55522);
        enyanPayRate.setPayRateId(1L);

        payRateRepository.save(enyanPayRate);

	    return ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
    }

    @ResponseBody
    @RequestMapping(value = "/testBalance")
    public Map<String, Object> testBalance(){
        //http://localhost:8080/test/testBalance

        Date date = DateUtils.addMonths(new Date(),-1);
        String yearMonthStr = DateFormatUtils.format(date,"yyyyMM");
        int yearMonth = Integer.parseInt(yearMonthStr);
        enyanBalanceService.createEnyanBalanceInfo(yearMonth);

        Map<String,Object> resultMap = ResultUtils.getSuccessResultData(yearMonth);

        return resultMap;
    }
    @ResponseBody
    @RequestMapping(value = "/testBalance2")
    public Map<String, Object> testBalance2(){
        //http://localhost:8080/test/testBalance2

        Date date = DateUtils.addMonths(new Date(),-2);
        String yearMonthStr = DateFormatUtils.format(date,"yyyyMM");
        int yearMonth = Integer.parseInt(yearMonthStr);
        enyanBalanceService.createEnyanBalanceInfo(yearMonth);

	    return ResultUtils.getSuccessResultData(yearMonth);
    }

    /**
     *
     * https://ebook.endao.co/test/testResetPinyin
     * https://ebookstore.endao.co/test/testResetPinyin
     * http://localhost:8080/test/testResetPinyin
     * @param
     * @Date: 2019-07-10
     */
    @ResponseBody
    @RequestMapping(value = "/testResetPinyin")
    public Map<String, Object> testResetPinyin(){
        //http://localhost:8080/test/testBalance2

        Date today = new Date();

        Long currentBookId = 0L;
        while (true){
            List<EnyanBook> list = enyanBookService.findTop1BasicBookGTBookId(currentBookId);
            if (null == list || list.isEmpty() == true){
                break;
            }
            EnyanBook book = list.get(0);
            enyanBookService.resetBookPinyin(book.getBookId(),book.getBookTitle());
            currentBookId++;
        }

	    return ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * https://ebook.endao.co/test/testResetPinyinByIds?param=1997,1998,1999,2000,2001,2002,2003,2004,2005
     * https://ebookstore.endao.co/test/testResetPinyinByIds?param=1997,1998,1999,2000,2001,2002,2003,2004,2005
     * http://localhost:8080/test/testResetPinyinByIds?param=1997,1998,1999,2000,2001,2002,2003,2004,2005
     * @param
     * @Date: 2019-07-10
     */
    @ResponseBody
    @RequestMapping(value = "/testResetPinyinByIds")
    public Map<String, Object> testResetPinyinByIds(HttpServletRequest request){
        //http://localhost:8080/test/testBalance2
        String param = request.getParameter("param");
        Date today = new Date();
        if (StringUtils.isEmpty(param)){
            return ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
        }

        List<Long> longs = Arrays
                                   .stream(param.split(","))
                                   .map(Long::parseLong)
                                   .collect(Collectors.toList());

        List<EnyanBook> bookList = enyanBookService.findBookByIds(longs);

        for (EnyanBook book : bookList){
            enyanBookService.resetBookPinyin(book.getBookId(),book.getBookTitle());
        }

	    return ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * https://ebook.endao.co/test/testChangeVendorUserPubs
     * https://ebookstore.endao.co/test/testChangeVendorUserPubs
     * http://localhost:8080/test/testChangeVendorUserPubs
     * @param
     * @Date: 2019-07-10
     */
    @ResponseBody
    @RequestMapping(value = "/testChangeVendorUserPubs")
    public Map<String, Object> testChangeVendorUserPubs(HttpServletRequest request){
        //http://localhost:8080/test/testBalance2
        Date today = new Date();

        List<AuthUser> list = authUserService.findAllVendorUsers();
        for (AuthUser authUser : list){
            //System.out.println(authUser.getEmail());
            authUserService.changeUserInfoPubsByEmail(authUser.getEmail());
        }

	    return ResultUtils.getSuccessResultData(DateFormatUtils.format(today,"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 发送邮件
     * @Date: 2018/2/22
     */
    protected void sendMailTest(HttpServletRequest request) {

        Mail mail = new Mail();
        mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
        mail.setSubject("恩道电子书-simple email");
        mail.setTo("<EMAIL>");//
        //mail.setTo("<EMAIL>");
        mail.setFtl("email-simple.ftl");
        Map<String,Object> model = new HashMap<>();
        model.put("emailToName","<EMAIL>");
        model.put("signature","恩道电子书");
        model.put("location", DateFormatUtils.format(new Date(),"yyyy-MM-dd"));
        if ("<EMAIL>".equals(mail.getTo())){
            //throw  new SystemException(101, null, mail.baseInfo(),"Email发送失败", null);
        }

        mail.setModel(model);
        emailService.sendSimpleMessage(mail);
        try {

        }catch (SystemException e){
            System.out.println("--------------------");
            throw  e;
        }

    }

    protected void sendMailBccTest(HttpServletRequest request) {

        Mail mail = new Mail();
        mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
        mail.setSubject("恩道电子书-simple email");
        //mail.setTo("<EMAIL>");//
        List<String> bccList = new ArrayList<>();
        bccList.add("<EMAIL>");
        bccList.add("<EMAIL>");
        mail.setBccList(bccList);
        //mail.setTo("<EMAIL>");
        mail.setFtl("email-simple.ftl");
        Map<String,Object> model = new HashMap<>();
        model.put("emailToName","<EMAIL>");
        model.put("signature","恩道电子书");
        model.put("location", DateFormatUtils.format(new Date(),"yyyy-MM-dd"));


        mail.setModel(model);

        emailService.sendSimpleMessage(mail);
    }

    /**
     *
     * 重新初始化用户的DRM信息 测试一下
     * 1、清空 OrderDetail drmInfo信息： update enyan_order_detail set drmInfo = ''
     * 2、删除purchase、license、license_status、license_view数据：drop create
     * 3、https://ebook.endao.co/test/resetOrderDetailAndPurchaseInfo
     * @param
     * @Date: 2019-07-10
     */
    @ResponseBody
    @RequestMapping(value = "/resetOrderDetailAndPurchaseInfo")
    public Map<String, Object> resetOrderDetailAndPurchaseInfo(){
        enyanOrderDetailService.resetOrderDetailAndPurchaseInfo();

	    return ResultUtils.getSuccessResultData("resetOrderDetailAndPurchaseInfo:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 重新初始化用户的DRM信息 测试一下
     * https://ebook.endao.co/test/resetOrderDetailAndPurchaseInfoByEmail?param=11
     * https://ebookstore.endao.co/test/resetOrderDetailAndPurchaseInfoByEmail?param=11
     * http://localhost:8080/test/resetOrderDetailAndPurchaseInfoByEmail?param=11
     * @param
     * @Date: 2019-07-10
     */
    @ResponseBody
    @RequestMapping(value = "/resetOrderDetailAndPurchaseInfoByEmail")
    public Map<String, Object> resetOrderDetailAndPurchaseInfoByEmail(HttpServletRequest request){
        //http://localhost:8080/test/testBalance2
        String param = request.getParameter("param");
        enyanOrderDetailService.resetOrderDetailAndPurchaseInfoByEmail(param);

	    return ResultUtils.getSuccessResultData("resetOrderDetailAndPurchaseInfoByEmail:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * example:
     * https://ebook.endao.co/test/resetOrderDetailAndCountry
     * http://localhost:8080/test/resetOrderDetailAndCountry
     * @param
     * @Date: 2019-07-10
     */
    @ResponseBody
    @RequestMapping(value = "/resetOrderDetailAndCountry")
    public Map<String, Object> resetOrderDetailAndCountry(){
        enyanOrderDetailService.resetOrderDetailAndCountry();

	    return ResultUtils.getSuccessResultData("resetOrderDetailAndCountry:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     *  修改订单列表里的图片信息
     * @param
     * @Date: 2019-11-14
     */
    @ResponseBody
    @RequestMapping(value = "/resetOrderDetailProductInfo")
    public Map<String, Object> resetOrderDetailProductInfo(){
        List<EnyanOrder> list = this.enyanOrderService.findRecordsWithBLOBsByOrder(new EnyanOrder());
        for (EnyanOrder order:list){
            EnyanOrder orderToUpdate = new EnyanOrder();
            orderToUpdate.setOrderId(order.getOrderId());

            OrderDetailInfo orderDetailInfo = order.getOrderDetailInfo();
            for (ProductInfo productInfo:orderDetailInfo.getProductInfoList()){
                this.updateProductInfo(productInfo);
            }
            orderToUpdate.setOrderDetailInfo(orderDetailInfo);
            this.enyanOrderService.updateRecord(orderToUpdate);
        }

	    return ResultUtils.getSuccessResultData("resetOrderDetailAndPurchaseInfo:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    @ResponseBody
    @RequestMapping(value = "/resetCartProductInfo")
    public Map<String, Object> resetCartProductInfo(){
        List<EnyanOrder> list = this.enyanOrderService.findRecordsWithBLOBsByOrder(new EnyanOrder());
        for (EnyanOrder order:list){
            EnyanOrder orderToUpdate = new EnyanOrder();
            orderToUpdate.setOrderId(order.getOrderId());

            if (StringUtils.hasLength(order.getOrderDetail()) == false){
                continue;
            }
            OrderDetailInfo orderDetailInfo = JSONObject.parseObject(order.getOrderDetail(),OrderDetailInfo.class);
            for (ProductInfo productInfo:orderDetailInfo.getProductInfoList()){
                this.updateProductInfo(productInfo);
            }
            orderToUpdate.setOrderDetailInfo(orderDetailInfo);
            this.enyanOrderService.updateRecord(orderToUpdate);
        }

	    return ResultUtils.getSuccessResultData("resetCartProductInfo:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/initPayRate
     * http://localhost:8080/test/initPayRate
     * @param
     * @Date: 2020-01-17
     */
    @ResponseBody
    @RequestMapping(value = "/initPayRate")
    public Map<String, Object> initPayRate(){
        enyanConfigService.initPayRateConfigFromFixerIO();

	    return ResultUtils.getSuccessResultData("initPayRate:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/initHighlightsToMongo
     * https://ebookstore.endao.co/test/initHighlightsToMongo
     * http://localhost:8080/test/initHighlightsToMongo
     * @param
     * @Date: 2020-01-17
     */
    @ResponseBody
    @RequestMapping(value = "/initHighlightsToMongo")
    public Map<String, Object> initHighlightsToMongo(){
        long start = System.currentTimeMillis();
        long count = enyanReaderHighlightService.countInDB();
        Page<EnyanReaderHighlights> page = new Page<>();
        page.setTotalRecord(count);
        page.setPageSize(100);
        EnyanReaderHighlights highlight = new EnyanReaderHighlights();
        for (int i = 1; i <= page.getTotalPage(); i++) {
            page.setCurrentPage(i);
            Page<EnyanReaderHighlights> newPage = enyanReaderHighlightService.queryRecords(page, highlight);
            if (newPage.getRecords().isEmpty() == false){
                enyanReaderHighlightService.addHighlightsOnly(newPage.getRecords());
            }
        }
        long end = System.currentTimeMillis();
        long between = end - start;

	    return ResultUtils.getSuccessResultData("initHighlightsToMongo:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss")+",countInDB="+count+",between="+between);
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/updatePayRate
     * http://localhost:8080/test/updatePayRate
     * @param
     * @Date: 2020-01-17
     */
    @ResponseBody
    @RequestMapping(value = "/updatePayRate")
    public Map<String, Object> updatePayRate(){
        enyanConfigService.updatePayRateConfigFromFixerIO();

	    return ResultUtils.getSuccessResultData("updatePayRate:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/testClearDeviceLimit?userName=<EMAIL>
     * http://localhost:8080/test/testClearDeviceLimit?userName=<EMAIL>
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testClearDeviceLimit")
    public Map<String, Object> testClearUserDeviceLimit(ModelMap modelMap, HttpServletRequest request){
        //http://localhost:8080/test/<EMAIL>
        String userName = request.getParameter("userName");
        if (StringUtils.hasLength(userName) == false){
            Map<String,Object> resultMap =  ResultUtils.getFailedResultData("userName is null");
            return  resultMap;
        }
        UserInfo userInfo = authUserService.loadUserInfoByEmail(userName);
        if (null == userInfo || null == userInfo.getCustomUserDetail()){
            Map<String,Object> resultMap = ResultUtils.getFailedResultData(userName +" info is null");
            return  resultMap;
        }
        userInfo.getCustomUserDetail().setDeviceLimitList(new ArrayList<>());
        authUserService.updateUserInfo(userInfo);
	    return ResultUtils.getSuccessResultData(userName + ":" +DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/testMoveCart
     * https://ebookstore.endao.co/test/testMoveCart
     * http://localhost:8080/test/testMoveCart
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testMoveCart")
    public Map<String, Object> testMoveCart(ModelMap modelMap, HttpServletRequest request){
        //http://localhost:8080/test/<EMAIL>
        List<UserInfo> list = authUserService.findUserInfos();
        Date date = new Date();
        for (UserInfo info:list){
            for (Long productId : info.getCustomUserDetail().getCartInfoGerenal().getProductIdList()){
                EnyanCart enyanCart = new EnyanCart();
                enyanCart.setBookId(productId);
                enyanCart.setAddAt(date);
                enyanCart.setUserEmail(info.getUsername());
                enyanCart.setQuantity(1);
                enyanCartService.addRecord(enyanCart);
            }
        }

	    return ResultUtils.getSuccessResultData("userName" + ":" +DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }
    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/testMoveWish
     * https://ebookstore.endao.co/test/testMoveWish
     * http://localhost:8080/test/testMoveWish
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testMoveWish")
    public Map<String, Object> testMoveWish(ModelMap modelMap, HttpServletRequest request){
        //http://localhost:8080/test/<EMAIL>
        List<EnyanBook> list = enyanWishService.findDistinctUserIdAsBookIdList();
        for (EnyanBook book:list){
            Long userId = book.getBookId();
            AuthUser user = authUserService.getUserByID(userId).getResult();
            enyanWishService.updateEmailById(user.getEmail(), userId);
        }
	    return ResultUtils.getSuccessResultData("userName" + ":" +DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 重新计算费用
     * example:
     * https://ebook.endao.co/test/updateOrderDetailFee
     * https://ebookstore.endao.co/test/updateOrderDetailFee
     * http://localhost:8080/test/updateOrderDetailFee
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/updateOrderDetailFee")
    public Map<String, Object> updateOrderDetailFee(){
        enyanOrderDetailService.updateOrderDetailFee();

	    return ResultUtils.getSuccessResultData("updateOrderDetailFee:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 重新计算费用
     * example:
     * https://ebook.endao.co/test/testDataStat
     * https://ebookstore.endao.co/test/testDataStat
     * http://localhost:8080/test/testDataStat
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testDataStat")
    public Map<String, Object> testDataStat(){
        dataStatService.saveDataYesterday();

	    return ResultUtils.getSuccessResultData("testDataStat:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 重新计算费用
     * example:
     * https://ebook.endao.co/test/testDataRentStat
     * https://ebookstore.endao.co/test/testDataRentStat
     * http://localhost:8080/test/testDataRentStat
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testDataRentStat")
    public Map<String, Object> testDataRentStat(){
        dataRentStatService.saveDataYesterday();

	    return ResultUtils.getSuccessResultData("testDataStat:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 重新计算费用
     * example:
     * https://ebook.endao.co/test/testUpdateDataStat?day=20210925
     * https://ebookstore.endao.co/test/testDataStat
     * http://localhost:8080/test/testDataStat
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testUpdateDataStat")
    public Map<String, Object> testUpdateDataStat(HttpServletRequest request){
        String day = request.getParameter("day");
        if (StringUtils.hasLength(day) == false){
            return ResultUtils.getFailedResultData("testUpdateDataStat day="+day);
        }
        dataStatService.updateDataInDay(day);

	    return ResultUtils.getSuccessResultData("testUpdateDataStat:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/resetLCPUserPasswd
     * http://localhost:8080/test/resetLCPUserPasswd
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/resetLCPUserPasswd")
    public Map<String, Object> resetLCPUserPasswd(){
        List<User> userList = authUserService.findAllLCPUsers();
        if (null != userList ){
            for (User user:userList){
                String drmInfo = DRMUtil.getDefaultHintPasswd(user.getEmail());
                if (!drmInfo.equals(user.getPassword())){
                    System.out.println("email wrong:"+ user.getEmail());
                }
            }
        }

	    return ResultUtils.getSuccessResultData("resetOrderDetailFee:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/importHighlights
     * http://localhost:8080/test/importHighlights
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/importHighlights")
    public Map<String, Object> importHighlights(){
        try {
            String filePath = SystemUtil.getExcelDir()+ "enyan_reader_highlights.xlsx";
            Runtime.getRuntime().exec("chmod 777 -R " + filePath);

            ExcelReaderMutiFromHighlight mutiImport = new ExcelReaderMutiFromHighlight();
            mutiImport.setBatchReadBeginRow(2);//从1开始
            mutiImport.setEnyanReaderHighlightService(enyanReaderHighlightService);
            ExcelImportUtil.readExcel(filePath, mutiImport, mutiImport, mutiImport.getBatchReadBeginRow());
        }catch (Exception e){
            System.out.println(e);
        }

	    return ResultUtils.getSuccessResultData("importHighlights:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/testLangScAndTc
     * https://ebookstore.endao.co/test/testLangScAndTc
     * http://localhost:8080/test/testLangScAndTc
     * @param
     * @Date: 2020-02-25
     */
    @RequestMapping(value = "/testLangScAndTc")
    public String testLangScAndTc(ModelMap modelMap, HttpServletRequest request){
        //http://localhost:8080/test/<EMAIL>
        List<EnyanBook> list = enyanBookService.findBookBasicInfo(null);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("<table>");
        stringBuffer.append("<tr>");
        stringBuffer.append("<td>");
        stringBuffer.append("原数据");
        stringBuffer.append("</td>");
        stringBuffer.append("<td>");
        stringBuffer.append("简体数据");
        stringBuffer.append("</td>");
        stringBuffer.append("<td>");
        stringBuffer.append("繁体数据");
        stringBuffer.append("</td>");
        stringBuffer.append("<td>");
        stringBuffer.append("数据类型");
        stringBuffer.append("</td>");
        stringBuffer.append("<tr/>");
        for (EnyanBook book:list){
            String name = book.getBookTitle();
            String nameSc = AaronJF.f2j(name);
            String nameTc = AaronJF.j2f(name);
            stringBuffer.append("<tr>");
            stringBuffer.append("<td>");
            stringBuffer.append(name);
            stringBuffer.append("</td>");
            stringBuffer.append("<td>");
            stringBuffer.append(nameSc);
            stringBuffer.append("</td>");
            stringBuffer.append("<td>");
            stringBuffer.append(nameTc);
            stringBuffer.append("</td>");
            stringBuffer.append("<td>");
            if (name.equals(nameSc)){
                stringBuffer.append("<font color = 'green'>是简体</font>");
            }else if (name.equals(nameTc)){
                stringBuffer.append("<font color = 'blue'>是繁体</font>");
            }else{
                stringBuffer.append("<font color = 'red'>都不是</font>");
            }
            stringBuffer.append("</td>");
            stringBuffer.append("<tr/>");
        }
        stringBuffer.append("<table/>");
        modelMap.addAttribute("msg",stringBuffer.toString());
//        Map<String,Object> resultMap = ResultUtils.getSuccessResultData(stringBuffer.toString());
        return "/test/excelImport";
    }

    /**
     *
     *
     * example:
     * https://ebook.endao.co/test/testEditBookRecommendedCaption
     * http://localhost:8080/test/testEditBookRecommendedCaption
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testEditBookRecommendedCaption")
    public Map<String, Object> testEditBookRecommendedCaption(HttpServletRequest request){
        //http://localhost:8080/test/testEmailSimple
//        EnyanOrder orderExample = new EnyanOrder();
//        orderExample.setStartDate("20210401");
//        List<EnyanOrder> list = enyanOrderService.selectByExampleWithBLOBs(orderExample);

        EnyanBook enyanBook = new EnyanBook();
        Page page = new Page();
        page.setPageSize(-1);
        enyanBook.setPage(page);

        List<EnyanBook> bookList = enyanBookService.findBooksWithBlob(enyanBook);
        for (EnyanBook book : bookList){
            EnyanBook newBook = new EnyanBook();
            newBook.setBookId(book.getBookId());
            String description = book.getBookDescription();
            String text = AaronHtmlUtils.getHtmlText(description);
            if (StringUtils.hasLength(text) == false){
                continue;
            }
            if (book.getBookId() >= 223){
                continue;
            }
            if (text.length() > 99){
                newBook.setRecommendedCaption(text.substring(0, 98));
            }else {
                newBook.setRecommendedCaption(text);
            }
            enyanBookService.updateByPrimaryKeySelective(newBook);
        }

	    return ResultUtils.getSuccessResultData("importHighlights:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 获取IP
     * https://ebook.endao.co/test/testIP
     * http://localhost:8080/test/testIP
     * @param
     * @Date: 2021-05-11
     */
    @ResponseBody
    @RequestMapping(value = "/testIP")
    public Map<String, Object> testIP(HttpServletRequest request){
        String ip = geoIPLocationService.getClientIpAddress(request);
        String country = geoIPLocationService.getLocationCountryISOCode(ip);
        String area = geoIPLocationService.getLocationAreaByCountry(country);

	    return ResultUtils.getSuccessResultData("testIP ip="+ip+",country:="+country+",area="+area+",date="+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 重置获取重复的订单信息
     * 3、https://ebook.endao.co/test/testPrintDuplicateOrderDetail
     * http://localhost:8080/test/testPrintDuplicateOrderDetail
     * @param
     * @Date: 2019-07-10
     */
    @RequestMapping(value = "/testPrintDuplicateOrderDetail")
    public String testPrintDuplicateOrderDetail(ModelMap modelMap, HttpServletRequest request){
        //http://localhost:8080/test/testEmailSimple
//        EnyanOrder orderExample = new EnyanOrder();
//        orderExample.setStartDate("20210401");
//        List<EnyanOrder> list = enyanOrderService.selectByExampleWithBLOBs(orderExample);

        EnyanOrderDetail orderDetail = new EnyanOrderDetail();
        orderDetail.setStartDate("20210401");
        OrderObj orderObj = new OrderObj("purchased_at", InterfaceContant.OrderBy.ASC);
        orderDetail.addOrder(orderObj);
        List<EnyanOrderDetail> detailList = enyanOrderDetailService.findAllOrderDetailList(orderDetail);

        Map<String,String> map = new HashMap<>();
        List<EnyanOrderDetail> duplicateList = new ArrayList<>();
        for (EnyanOrderDetail detail:detailList){
            String key = getMapKey(detail);
            if (map.containsKey(key) == false){
                map.put(key, "1");
                continue;
            }
            duplicateList.add(detail);
        }
        StringBuffer stringBuffer = new StringBuffer();
        duplicateList.forEach(item ->{
            stringBuffer.append(item.getOrderDetailId());
            stringBuffer.append(",");
        });
        orderDetail.setDrminfo(stringBuffer.toString());

        modelMap.addAttribute("list",duplicateList);
        modelMap.addAttribute("dto",orderDetail);
        return "/test/orderDetails-order";
    }

    /**
     *
     * 注销用户，删除划线
     * example:
     * https://ebook.endao.co/test/testRevokeUser?email=<EMAIL>
     * https://ebookstore.endao.co/test/testRevokeUser
     * http://localhost:8080/test/testRevokeUser
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testRevokeUser")
    public Map<String, Object> testRevokeUser(HttpServletRequest request){
        String email = request.getParameter("email");
        if (StringUtils.hasLength(email) == false){
            return ResultUtils.getFailedResultData("testRevokeUser email="+email);
        }
        String newEmail = email;
        if (newEmail.length() > 70){//数据最多可以存储96，为避免溢出，需要提前处理一下长度
            newEmail = newEmail.substring(0, 70);
        }
        Float random = RandomUtils.nextFloat(0, 1f)*10000;
        newEmail = newEmail + "_revoked_"+random.intValue();
        authUserService.revokeLcpUser(email, newEmail);
        authUserService.revokeUser(email, newEmail);
        enyanBookBuyService.revokeUser(email, newEmail);
        enyanOrderDetailService.revokeUser(email, newEmail);
        enyanOrderService.revokeUser(email, newEmail);
        enyanWishService.revokeUser(email, newEmail);
        enyanCartService.revokeUser(email, newEmail);
        enyanPlanNoteService.revokeUser(email, newEmail);
        enyanPlanService.revokeUser(email, newEmail);
        enyanRedeemCodeService.revokeUser(email, newEmail);
        enyanReaderHighlightService.revokeUser(email, newEmail);

	    return ResultUtils.getSuccessResultData("testRevokeUser:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 重置获取重复的订单信息
     * 3、https://ebook.endao.co/test/testExcelImportUI
     * http://localhost:8080/test/testExcelImportUI
     * @param
     * @Date: 2021-05-11
     */
    @RequestMapping(value = "/testExcelImportUI")
    public String testExcelImportUI(ModelMap modelMap, HttpServletRequest request){
        return "/test/excelImport";
    }

    @RequestMapping(value = "/excelImportReaderHighlight", method = RequestMethod.POST)
    public String excelImportReaderHighlight(@RequestParam(value = "uploadExcel", required = false) MultipartFile filedataPic,
                             EnyanRedeemCode enyanRedeemCode , ModelMap modelMap) {
        try {
            // 获取图片的文件名
            String fileName = "Test_"+filedataPic.getOriginalFilename();
            // 获取图片的扩展名
            String extensionName = fileName.substring(fileName.lastIndexOf(".") + 1);

            // 新的图片文件名 = 获取时间戳+"."图片扩展名
            String newFileName = fileName+"_"+ System.currentTimeMillis() + "." + extensionName;


            //enyanImgService.addRecord(enyanImg);

            String filePath = SystemUtil.getExcelDir()+newFileName;
            File picFile = new File(filePath);
            filedataPic.transferTo(picFile);
            //picFile.setWritable(true);//设置可写权限
            //picFile.setExecutable(true);//设置可执行权限
            //picFile.setReadable(true);//设置可读权限

            Runtime.getRuntime().exec("chmod 777 -R " + filePath);

            ExcelReaderMutiFromHighlightTest mutiImport = new ExcelReaderMutiFromHighlightTest();
            mutiImport.setBatchReadBeginRow(2);//从1开始
            mutiImport.setEnyanReaderHighlightService(enyanReaderHighlightService);
            ExcelImportUtil.readExcel(filePath, mutiImport, mutiImport, mutiImport.getBatchReadBeginRow());
        }catch (Exception e){
            System.out.println(e);
        }

        return "/test/excelImport";
    }

    private String getMapKey(EnyanOrderDetail detail){
        return detail.getOrderNum()+","+detail.getBookId();
    }

    /**
     *
     * 修改默认的图片
     * @param productInfo
     * @Date: 2019-11-14
     */
    private void updateProductInfo(ProductInfo productInfo){
        EnyanBook book = this.enyanBookService.queryRecordByPrimaryKey(productInfo.getCode()).getResult();
        if (null != book){
            productInfo.setProductCover(book.getBookCoverApp());
        }
    }

    /**
     * <p>从OrderDetial导入书籍信息</p>
     * https://ebook.endao.co/test/testImportDataFromOrderDetail
     * https://ebookstore.endao.co/test/testImportDataFromOrderDetail
     * http://localhost:8080/test/testImportDataFromOrderDetail
     * @param request
     * @return: java.lang.String
     * @since : 2021/2/23
     */
    @ResponseBody
    @RequestMapping(value = "/testImportDataFromOrderDetail")
    public Map<String, Object> testImportDataFromOrderDetail(HttpServletRequest request){
        //http://localhost:8080/test/testBccEmailSimple
        List<EnyanOrderDetail> list = enyanOrderDetailService.findAllOrderDetailList(new EnyanOrderDetail());
        for (EnyanOrderDetail detail:
             list) {
            enyanBookBuyService.importDataFromOrderDetail(detail);
        }

        return ResultUtils.getSuccessResultData(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * <p>从兑换信息导入书籍信息</p>
     * https://ebook.endao.co/test/testImportDataFromRedeem
     * https://ebookstore.endao.co/test/testImportDataFromRedeem
     * http://localhost:8080/test/testImportDataFromRedeem
     * @param request
     * @return: java.lang.String
     * @since : 2021/2/23
     */
    @ResponseBody
    @RequestMapping(value = "/testImportDataFromRedeem")
    public Map<String, Object> testImportDataFromRedeem(HttpServletRequest request){
        //http://localhost:8080/test/testBccEmailSimple
        EnyanOrder queryOrder = new EnyanOrder();
        queryOrder.setIsPaid(Constant.BYTE_VALUE_1);
        queryOrder.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);//兑换码兑换
        List<EnyanOrder> orderList = enyanOrderService.findRecordsWithBLOBsByOrder(queryOrder);//兑换的订单
        Set<String> redeemOrderSet = new HashSet<>();
        for (EnyanOrder order :
                orderList) {
            if (StringUtils.hasLength(order.getOrderDetail()) == false){//没有订单明细数据
                continue;
            }
            OrderDetailInfo orderDetailInfo = JSONObject.parseObject(order.getOrderDetail(), OrderDetailInfo.class);
            orderDetailInfo.resetFromJson();
            for (ProductInfo info :
                    orderDetailInfo.getProductInfoList()) {//兑换的订单直接加入到电子书里面
                redeemOrderSet.add(order.getUserEmail()+","+info.getCode());//email,code
                EnyanOrderDetail detail = new EnyanOrderDetail();
                detail.setOrderNum(order.getOrderNum());
                detail.setUserEmail(order.getUserEmail());
                detail.setBookId(info.getCode());
                detail.setPurchasedAt(order.getPurchasedAt());
                enyanBookBuyService.importDataFromOrderDetail(detail);
            }
        }
        //因为之前有写入数据的订单，所以在这里处理一下
        Set<String> buyRedeemSet = new HashSet<>();
        EnyanOrderDetail queryDetail = new EnyanOrderDetail();
        queryDetail.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_BUY);//购买的兑换码订单

        StringBuffer buffer = new StringBuffer();
        List<EnyanOrderDetail> orderDetailList = enyanOrderDetailService.findRecordsByOrder(queryDetail);
        for (EnyanOrderDetail orderDetail :
                orderDetailList) {
            String key = orderDetail.getUserEmail()+","+orderDetail.getBookId();//email,code
            buyRedeemSet.add(key);//email,code
            if (redeemOrderSet.contains(key)){//兑换过
                continue;
            }
            EnyanOrderDetail queryBuy = new EnyanOrderDetail();
            queryBuy.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);
            queryBuy.setUserEmail(orderDetail.getUserEmail());
            queryBuy.setBookId(orderDetail.getBookId());
            //没有兑换过，也没有购买过，就删除
            List<EnyanOrderDetail> tmpList = enyanOrderDetailService.findRecordsByOrder(queryBuy);
            if (tmpList.isEmpty() == true){//没有兑换过，也没有购买过，就删除
                EnyanBookBuy queryBookBuy = new EnyanBookBuy();
                queryBookBuy.setUserEmail(orderDetail.getUserEmail());
                queryBookBuy.setBookId(orderDetail.getBookId());
                List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findRecordsByBookBuy(queryBookBuy);
                if (bookBuyList.isEmpty() == false){
                    enyanBookBuyService.delBookBuyAndLicenseByBookBuy(bookBuyList.get(0));
                }
            }
        }
        return ResultUtils.getSuccessResultData(buffer.toString());
        //return ResultUtils.getSuccessResultData(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * <p>从兑换信息导入书籍信息</p>
     * https://ebook.endao.co/test/testImportDataFromRedeemPrint
     * https://ebookstore.endao.co/test/testImportDataFromRedeemPrint
     * http://localhost:8080/test/testImportDataFromRedeemPrint
     * @param request
     * @return: java.lang.String
     * @since : 2021/2/23
     */
    @ResponseBody
    @RequestMapping(value = "/testImportDataFromRedeemPrint")
    public Map<String, Object> testImportDataFromRedeemPrint(HttpServletRequest request){
        //http://localhost:8080/test/testBccEmailSimple
        EnyanOrder queryOrder = new EnyanOrder();
        queryOrder.setIsPaid(Constant.BYTE_VALUE_1);
        queryOrder.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);//兑换码兑换
        List<EnyanOrder> orderList = enyanOrderService.findRecordsWithBLOBsByOrder(queryOrder);//兑换的订单
        Set<String> redeemOrderSet = new HashSet<>();
        for (EnyanOrder order :
                orderList) {
            if (StringUtils.hasLength(order.getOrderDetail()) == false){//没有订单明细数据
                continue;
            }
            OrderDetailInfo orderDetailInfo = JSONObject.parseObject(order.getOrderDetail(), OrderDetailInfo.class);
            orderDetailInfo.resetFromJson();
            for (ProductInfo info :
                    orderDetailInfo.getProductInfoList()) {//兑换的订单直接加入到电子书里面
                redeemOrderSet.add(order.getUserEmail()+","+info.getCode());//email,code
                /*
                EnyanOrderDetail detail = new EnyanOrderDetail();
                detail.setOrderNum(order.getOrderNum());
                detail.setUserEmail(order.getUserEmail());
                detail.setBookId(info.getCode());
                detail.setPurchasedAt(order.getPurchasedAt());
                enyanBookBuyService.importDataFromOrderDetail(detail);*/
            }
        }
        //因为之前有写入数据的订单，所以在这里处理一下
        Set<String> buyRedeemSet = new HashSet<>();
        EnyanOrderDetail queryDetail = new EnyanOrderDetail();
        queryDetail.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_BUY);//购买的兑换码订单

        StringBuffer buffer = new StringBuffer();
        List<EnyanOrderDetail> orderDetailList = enyanOrderDetailService.findRecordsByOrder(queryDetail);
        for (EnyanOrderDetail orderDetail :
                orderDetailList) {
            String key = orderDetail.getUserEmail()+","+orderDetail.getBookId();//email,code
            buyRedeemSet.add(key);//email,code
            if (redeemOrderSet.contains(key)){//兑换过
                continue;
            }
            EnyanOrderDetail queryBuy = new EnyanOrderDetail();
            queryBuy.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);
            queryBuy.setUserEmail(orderDetail.getUserEmail());
            queryBuy.setBookId(orderDetail.getBookId());
            //没有兑换过，也没有购买过，就删除
            List<EnyanOrderDetail> tmpList = enyanOrderDetailService.findRecordsByOrder(queryBuy);
            if (tmpList.isEmpty() == true){//没有兑换过，也没有购买过，就删除
                //enyanBookBuyService.find
                buffer.append(key);
                buffer.append("<br>");
            }
        }
        return ResultUtils.getSuccessResultData(buffer.toString());
        //return ResultUtils.getSuccessResultData(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 测试续费
     * example:
     * https://ebook.endao.co/test/testRentPay?param=RTO10070697331&param2=3
     * https://ebookstore.endao.co/test/testRentPay?param=RTO10070697331&param2=3
     * http://localhost:8080/test/testRentPay?param=RTO10070697331&param2=3
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testRentPay")
    public Map<String, Object> testRentPay(HttpServletRequest request){
        String param = request.getParameter("param");
        String param2 = request.getParameter("param2");
        if (StringUtils.hasLength(param) == false || StringUtils.hasLength(param2) == false){
            return ResultUtils.getFailedResultData("testRentPay ="+param2);
        }
        RestRent rentObj  = new RestRent();
        rentObj.setOrderNum(param);
        rentObj.setToRentMonths(Integer.parseInt(param2));
        restRentController.testDoPay(rentObj, request);

	    return ResultUtils.getSuccessResultData("testRentPay:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 根据订单重置license信息
     * example:
     * https://ebook.endao.co/test/testRentExpiredTime?param=R11776452768&param2=3
     * https://ebookstore.endao.co/test/testRentExpiredTime?param=RTO10070697331&param2=3
     * http://localhost:8080/test/testRentExpiredTime?param=R10069230265&param2=3
     * @param
     * @Date: 2022-12-22
     */
    @ResponseBody
    @RequestMapping(value = "/testRentExpiredTime")
    public Map<String, Object> testRentExpiredTime(HttpServletRequest request){
        String param = request.getParameter("param");
        String param2 = request.getParameter("param2");
        if (StringUtils.hasLength(param) == false ){
            return ResultUtils.getFailedResultData("testRentExpiredTime ="+param);
        }
        EnyanRent queryObj  = new EnyanRent();
        queryObj.setOrderNum(param);
        List<EnyanRent> list = enyanRentService.findRecords(queryObj);
        for (EnyanRent obj :
                list) {
            enyanRentService.updateDRMLicenseToNewExpiredTime(obj,obj.getExpiredAt());
        }

	    return ResultUtils.getSuccessResultData("testRentExpiredTime:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 根据订单重置license信息
     * example:
     * https://ebook.endao.co/test/testAddDrmToRent?param=RTO10070697331&param2=3
     * https://ebookstore.endao.co/test/testAddDrmToRent?param=RTO10070697331&param2=3
     * http://localhost:8080/test/testAddDrmToRent?param=R10359255781&param2=3
     * @param
     * @Date: 2022-12-22
     */
    @ResponseBody
    @RequestMapping(value = "/testAddDrmToRent")
    public Map<String, Object> testAddDrmToRent(HttpServletRequest request){
        String param = request.getParameter("param");
        String param2 = request.getParameter("param2");
        if (StringUtils.hasLength(param) == false ){
            return ResultUtils.getFailedResultData("testAddDrmToRent ="+param2);
        }
        EnyanRent queryObj  = new EnyanRent();
        queryObj.setOrderNum(param);
        List<EnyanRent> list = enyanRentService.findRecordsWithBlob(queryObj);
        for (EnyanRent obj :
                list) {
            enyanRentService.addDRMInfoToRent(obj, obj.getExpiredAt());
        }

	    return ResultUtils.getSuccessResultData("testAddDrmToRent:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
    }

    /**
     *
     * 测试续费
     * example:
     * https://ebook.endao.co/test/testJob?param=1
     * https://ebookstore.endao.co/test/testJob?param=1
     * http://localhost:8080/test/testJob?param=1
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testJob")
    public Map<String, Object> testJob(HttpServletRequest request){
        String param = request.getParameter("param");
        String param2 = request.getParameter("param2");
        if (StringUtils.hasLength(param) == false ){//|| StringUtils.hasLength(param2) == false
            return ResultUtils.getFailedResultData("testJob ="+param);
        }
        switch (param){
            case "1"://宽限期（3天）后设置订阅为自动到期(阅读状态为无效，订阅状态为失效);如果已经订购36个月，昨天到期订单直接到期
                orderTaskJob.scheduleRentExpired();
                break;
            case "2"://定期自动付款（36个月不续费）
                orderTaskJob.scheduleRentAutoPay();
                break;
            case "3"://处理到期前5天的数据直接发邮件（email）
                orderTaskJob.scheduleEmailRentBeforeExpired();
                break;
            case "4"://处理正常阅读状态的到期1天后的数据（发送email）
                orderTaskJob.scheduleEmailRentAfterExpired();
                break;
            case "5"://处理退订1天后的数据：宽限期后1日，提醒已自动退订，但仍有1个月优惠购书期。（email）
                orderTaskJob.scheduleEmailRentAfterGrace();
                break;
            case "6"://昨天退订，订满36个月，系统自动退订成功时（退订时间就是到期时间）（email）
                orderTaskJob.scheduleRentMustEnd();
                break;
            case "7"://购书优惠到期前一周（email）
                orderTaskJob.scheduleRentWillGraceBuyEnd();
                break;
        }

	    return ResultUtils.getSuccessResultData("testJob:"+DateFormatUtils.format(new Date(), InterfaceContant.DateFormatCustom.DATETIME));
    }

    /**
     *
     * 测试修改email并重置密码为 123456
     * example:
     * https://ebook.endao.co/test/testChangeEmail?oldEmail=a&newEmail=2
     * https://ebookstore.endao.co/test/testChangeEmail?oldEmail=1&newEmail=2
     * http://localhost:8080/test/testChangeEmail?oldEmail=1&newEmail=2
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testChangeEmail")
    public Map<String, Object> testChangeEmail(HttpServletRequest request){
        String oldEmail = request.getParameter("oldEmail");//oldEmail
        String newEmail = request.getParameter("newEmail");//newEmail
        if (StringUtils.hasLength(oldEmail) == false || StringUtils.hasLength(newEmail) == false){//|| StringUtils.hasLength(param2) == false
            return ResultUtils.getFailedResultData("testChangeEmail ="+oldEmail);
        }
        authUserService.changeUserEmail(oldEmail,newEmail);

	    return ResultUtils.getSuccessResultData("testChangeEmail:"+DateFormatUtils.format(new Date(), InterfaceContant.DateFormatCustom.DATETIME));
    }

    /**
     *
     * 测试修改email
     * example:
     * https://ebook.endao.co/test/testInitSearch?oldEmail=a&newEmail=2
     * https://ebookstore.endao.co/test/testInitSearch?oldEmail=1&newEmail=2
     * http://localhost:8080/test/testInitSearch?oldEmail=1&newEmail=2
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testInitSearch")
    public Map<String, Object> testInitSearch(HttpServletRequest request){
        enyanBookService.initElasticsearchInfo();

	    return ResultUtils.getSuccessResultData("testInitSearch:"+DateFormatUtils.format(new Date(), InterfaceContant.DateFormatCustom.DATETIME));
    }

    /**
     *
     * 测试修改email
     * example:
     * https://ebook.endao.co/test/testInitSearchDay?oldEmail=a&newEmail=2
     * https://ebookstore.endao.co/test/testInitSearchDay?oldEmail=1&newEmail=2
     * http://localhost:8080/test/testInitSearchDay?oldEmail=1&newEmail=2
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testInitSearchDay")
    public Map<String, Object> testInitSearchDay(HttpServletRequest request){
        enyanBookService.initElasticsearchInfoDay();

	    return ResultUtils.getSuccessResultData("testInitSearchDay:"+DateFormatUtils.format(new Date(), InterfaceContant.DateFormatCustom.DATETIME));
    }

    /**
     *
     * 测试修改email
     * example:
     * https://ebook.endao.co/test/testResetSearchBook?param=606&param2=3
     * https://ebookstore.endao.co/test/testResetSearchBook?param=606&param2=3
     * http://localhost:8080/test/testResetSearchBook?param=606&param2=3
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testResetSearchBook")
    public Map<String, Object> testResetSearchBook(HttpServletRequest request){
        String param = request.getParameter("param");
        String param2 = request.getParameter("param2");
        if (StringUtils.hasLength(param) == false ){
            return ResultUtils.getFailedResultData("testResetSearchBook ="+param2);
        }
        Long bookId = Long.parseLong(param);
        enyanBookService.resetBookElasticSearch(bookId);

	    return ResultUtils.getSuccessResultData("testResetSearchBook:"+DateFormatUtils.format(new Date(), InterfaceContant.DateFormatCustom.DATETIME));
    }

    /**
     *
     * 测试修改email
     * example:
     * https://ebook.endao.co/test/testInitSearch2?oldEmail=a&newEmail=2
     * https://ebookstore.endao.co/test/testInitSearch2?oldEmail=1&newEmail=2
     * http://localhost:8080/test/testInitSearch2?oldEmail=1&newEmail=2
     * @param
     * @Date: 2020-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testInitSearch2")
    public Map<String, Object> testInitSearch2(HttpServletRequest request){
        EnyanBook search = new EnyanBook();
        search.setShelfStatus(Constant.BYTE_VALUE_1);
        search.setPage(new Page<>());
        search.getPage().setPageSize(-1);
        List<EnyanBook> list = enyanBookService.findBooksWithBlob(search);
        for (EnyanBook book:list) {
            /*
            if (book.getShelfStatus().equals(Constant.BYTE_VALUE_1) == false){//没有上架
                continue;
            }*/
            SearchBook searchBook = new SearchBook();
            searchBook.initWithEBook(book);
            //bookSearchRepository.save(searchBook);

            /*
            IndexQuery indexQuery = new IndexQueryBuilder()
                                            .withId(searchBook.getBookId().toString())
                                            .withObject(searchBook)
                                            .build();*/
            //String documentId = elasticsearchOperations.index(indexQuery);
            //elasticsearchOperations.save(searchBook);
        }

	    return ResultUtils.getSuccessResultData("testInitSearch2:"+DateFormatUtils.format(new Date(), InterfaceContant.DateFormatCustom.DATETIME));
    }

    /**
     *
     * 测试添加封禁IP
     * example:
     * https://ebook.endao.co/test/testBlackIPAdd?param=127.0.0.1
     * https://ebookstore.endao.co/test/testBlackIPAdd?param=127.0.0.1
     * http://localhost:8080/test/testBlackIPAdd?param=127.0.0.1
     * @param
     * @Date: 2025-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testBlackIPAdd")
    public Map<String, Object> testBlackIPAdd(HttpServletRequest request){
        String param = request.getParameter("param");
        //String param2 = request.getParameter("param2");
        if (StringUtils.hasLength(param) == false ){
            return ResultUtils.getFailedResultData("BlackIPAdd ="+param);
        }
        Constant.FORBIDDEN_IP.add(param);

        return ResultUtils.getSuccessResultData("BlackIPAdd:"+DateFormatUtils.format(new Date(), InterfaceContant.DateFormatCustom.DATETIME));
    }

    /**
     *
     * 测试重置封禁IP
     * example:
     * https://ebook.endao.co/test/testBlackIPClear?param=127.0.0.1
     * https://ebookstore.endao.co/test/testBlackIPClear?param=127.0.0.1
     * http://localhost:8080/test/testBlackIPClear?param=127.0.0.1
     * @param
     * @Date: 2025-02-25
     */
    @ResponseBody
    @RequestMapping(value = "/testBlackIPClear")
    public Map<String, Object> testBlackIPClear(HttpServletRequest request){
        Constant.FORBIDDEN_IP = new HashSet<>();
        return ResultUtils.getSuccessResultData("testBlackIPClear:"+DateFormatUtils.format(new Date(), InterfaceContant.DateFormatCustom.DATETIME));
    }
}
