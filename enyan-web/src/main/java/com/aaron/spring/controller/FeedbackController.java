package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.CreditInfo;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestFeedback;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanFeedback;
import com.aaron.spring.service.EnyanFeedbackService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/2/13
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/feedbackAdmin")
public class FeedbackController extends BaseController{
	@Resource
	private EnyanFeedbackService enyanFeedbackService;

	@RequestMapping(value = "/list")
	public String list(HttpServletRequest req, EnyanFeedback record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		if (null == record){
			record = new EnyanFeedback();
		}
		if (null == record.getPage()){
			record.setPage(new Page());
		}
		Map<String, Object> queryParams = new HashMap<>();

		// 获取分页参数
		String total = req.getParameter("total");
		String currentPage = req.getParameter("pageNo");

		if (StringUtils.hasLength(total)) {
			record.getPage().setTotalRecord(Integer.parseInt(total));
		}
		if (StringUtils.hasLength(currentPage)) {
			record.getPage().setCurrentPage(Integer.parseInt(currentPage));
		}

		// 高级查询条件：
		String searchText = req.getParameter("searchText");
		String searchType = req.getParameter("searchType");
		if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
			switch (searchType){
				case "0":
					record.setFeedEmail(searchText);
					break;
				case "1":
					break;

			}
			queryParams.put("searchText",searchText);
			queryParams.put("searchType",searchType);
		}

		record.addOrder(new OrderObj("create_at", InterfaceContant.OrderBy.DESC));
//        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
		Page<EnyanFeedback> page = enyanFeedbackService.queryRecords(record.getPage(),record);
		record.setPage(page);
		record.excutePageLand(queryParams);

		modelMap.addAttribute("list",page.getRecords());
		modelMap.addAttribute("pageLand",record.getPageLand());
		modelMap.addAttribute("dto",record);
		modelMap.addAttribute("explan","文章管理");

		return "admin/feedbackList";
	}

	@ResponseBody
	@RequestMapping(value = "/solve")
	public ExecuteResult<String> solve(@RequestBody RestFeedback restObj, ModelMap modelMap,
	                                   HttpServletRequest request, HttpServletResponse response){
		ExecuteResult<String> result = new ExecuteResult<>();
		log.debug("dataId:{}",restObj.getDataId());
		if (null == restObj.getDataId()){
			result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanFeedback update = new EnyanFeedback();
		update.setDataId(restObj.getDataId());
		update.setDoneType(1);
		enyanFeedbackService.updateRecord(update);
		return result;
	}

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		EnyanFeedback record = enyanFeedbackService.queryRecordByPrimaryKey(id).getResult();

		modelMap.addAttribute("record",record);
		modelMap.addAttribute("publisherList", Constant.publishersList);
		return "admin/blogAdd";
	}

	@RequestMapping("/del-{id}")
	public String delById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("method delById");
		enyanFeedbackService.deleteRecordByPrimaryKey(id);
		return "redirect:/blog/list";
	}

	@RequestMapping("/addUI")
	public String addCompanyUI(EnyanFeedback record, ModelMap modelMap){
		log.debug("method addCompanyUI");
		record.setIsDeleted(0);
		modelMap.addAttribute("publisherList",Constant.publishersList);
		modelMap.addAttribute("record",record);
		return "admin/blogAdd";
	}

}
