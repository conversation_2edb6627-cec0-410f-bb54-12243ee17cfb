package com.aaron.spring.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.spring.model.AuthUser;
import com.aaron.spring.model.AuthUserExample;
import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.service.AuthUserService;
import com.aaron.spring.service.EnyanPublisherService;
import com.aaron.util.ExecuteResult;
import com.aaron.util.UserUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.ibatis.reflection.ExceptionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import com.aaron.spring.model.MUser;
import com.aaron.spring.service.MUserServiceI;

import java.util.List;

@Controller
@RequestMapping("/user")
public class UserController extends BaseController{
	private final Log logger = LogFactory.getLog(UserController.class);
	@Resource
	private AuthUserService authUserService;

	@Resource
	private EnyanPublisherService enyanPublisherService;

	@RequestMapping("/login")
	public String login(){
	    return "login";
	}

	@RequestMapping("/adminIndex")
	public String adminIndex(){
		return "admin/adminIndex";
	}

	@RequestMapping("/adminLeft")
	public String adminLeft(){
		return "admin/adminLeft";
	}

	@ResponseBody
	@RequestMapping(value = "/loginActionJson",method = RequestMethod.POST)
	public ExecuteResult<AuthUser> loginActionJson(@RequestBody AuthUser authUser){
		ExecuteResult<AuthUser> result = new ExecuteResult<>();
		result.setResult(authUser);
		//result.addErrorMessage("error....");
		logger.info(authUser.getUserName());
		//modelMap.addAttribute(authUser);
		return result;
	}


	@RequestMapping(value = "/loginAction",method = RequestMethod.POST)
	public String loginAction(AuthUser authUser,ModelMap modelMap){
		ExecuteResult<AuthUser> result = new ExecuteResult<>();
		result.setResult(authUser);
		//result.addErrorMessage("error....");
		logger.info(authUser.getUserName());
		modelMap.addAttribute(authUser);
		return "admin/adminMain";
	}


	@RequestMapping("/reg")
	public String reg(){
		return "reg";
	}
	@RequestMapping("/regAction")
	public String regAction(AuthUser authUser,ModelMap modelMap){
		ExecuteResult<AuthUser> result = authUserService.addRecord(authUser);

		modelMap.addAttribute("result",result);

	    return "reg";
	}

	@RequestMapping("/get/{id}")
	public String getUserById(@PathVariable("id")Long id, HttpServletRequest request){
		//AuthUser muser = authUserService.queryRecordByPrimaryKey(id);
		//request.setAttribute("user", muser);
		return "updateUser";
	}
	@RequestMapping(value = "/listUser")
	public String listUser(HttpServletRequest request) {
		AuthUserExample example = new AuthUserExample();

		//List<AuthUser> list = authUserService.selectByExample(example);
		//request.setAttribute("userlist", list);
		return "admin/listUser";
	}

	@RequestMapping(value = "/addUser")
	public String addUser(AuthUser authUser) {

		//String id = UUID.randomUUID().toString();
		//muser.setId(id);
		//authUserService.insert(authUser);
		return "redirect:/user/listUser.do";
	}
	/*
	@RequestMapping(value = "delete-user", method = RequestMethod.POST)
    public String deleteUser(Long[] userId, RedirectAttributesModelMap modelMap) {
        userService.deleteUser(userId);
        modelMap.addFlashAttribute("resultMsg", "删除成功");
        return "redirect:list-user.shtml";
    }
	* */


	@ResponseBody
	@RequestMapping(value="/jsonReturn")
	public ExecuteResult<AuthUser> jsonReturn(@RequestParam(required = true) Long feeVersionId){
		ExecuteResult<AuthUser> result = new ExecuteResult<>();
		AuthUser user = new AuthUser();
		user.setNickName("Aaron Hao");

		result.setResult(user);

		//authUserService.deleteRecordByPrimaryKey(1L);

		enyanPublisherService.queryRecordByPrimaryKey(1L);
		return result;
		//return enyanPublisherService.queryRecordByPrimaryKey(1L);
	}
    @ResponseBody
    @RequestMapping(value="/jsonReturnPublish")
    public ExecuteResult<EnyanPublisher> jsonReturnPublish(@RequestParam(required = true) Long id){
        return enyanPublisherService.queryRecordByPrimaryKey(id);
        //return enyanPublisherService.queryRecordByPrimaryKey(1L);
    }
    @RequestMapping("/updatePwd")
    public String updatePwd(){
        return "/admin/passwdSet";
    }

    @RequestMapping(value = "/updatePwdAction", method = RequestMethod.POST)
    public String updatePwdAction(AuthUser authUser, ModelMap modelMap,HttpServletRequest request){
        //logger.info("method regAction："+authUser);
        if (UserUtils.isAnonymous()){
            this.setErrorMsg(modelMap, this.getMessage("error.nonlogin",request));
            return "/admin/passwdSet";
        }
        modelMap.addAttribute("authUser",authUser);
        if (StringUtils.isBlank(authUser.getSalt())){//使用salt替代 原密码
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.oldnull",request));
            return "/admin/passwdSet";
        }

        if (StringUtils.isBlank(authUser.getUserPassword())|| StringUtils.isBlank(authUser.getUserPasswordAgain())){
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.null",request));
            return "/admin/passwdSet";
        }
        if (authUser.getUserPassword().length()<6 || authUser.getUserPassword().length() > 20){
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.format",request));
            return "/admin/passwdSet";
        }
        if (!authUser.getUserPassword().equals(authUser.getUserPasswordAgain())){
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.again",request));
            return "/admin/passwdSet";
        }

        String email = UserUtils.getCurrentLoginUser().getEmail();
        AuthUser user = authUserService.getUserByEmail(email).getResult();

        if (null == user){
            this.setErrorMsg(modelMap, this.getMessage("error.email.notexist",request));
            return "/admin/passwdSet";
        }
        if (!authUserService.isPasswordValid(user.getUserPassword(),authUser.getSalt(),user.getSalt())){
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.old.incorect",request));
            return "/admin/passwdSet";
        }
        authUser.setEmail(email);
        authUserService.updatePasswd(authUser);
        this.setSuccessMsg(modelMap,this.getMessage("success.update",request));
        return "/admin/passwdSet";
    }


}
