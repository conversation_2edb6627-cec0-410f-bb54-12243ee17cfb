package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.drm.model.DrmInfo;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.security.function.domain.IUser;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.*;
import com.aaron.spring.service.EnyanBookBuyService;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanOrderService;
import com.aaron.spring.service.EnyanRefundService;
import com.aaron.util.UserUtils;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2021/3/26
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/refund")
public class RefundController extends BaseController{
    @Resource
    private EnyanRefundService enyanRefundService;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanOrderService enyanOrderService;

    @Resource
    private EnyanBookBuyService enyanBookBuyService;

    @RequestMapping(value = "/refundUI")
    public String refundUI(HttpServletRequest request, EnyanRefund refund, ModelMap modelMap){
        if (null == refund){
            refund = new EnyanRefund();
        }
        refund.setDownloadType(EBookConstant.ExcelDownload.REFUND_INFO);
        modelMap.addAttribute("dto",refund);
        return "admin/refundUI";
    }

    @RequestMapping(value = "/refunds")
    public String booksPage(HttpServletRequest req, EnyanRefund refund, ModelMap modelMap){
        //logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
        if (null == refund){
            refund = new EnyanRefund();
        }
        if (null == refund.getPage()){
            refund.setPage(new Page());
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 高级查询条件：
        String startDate, endDate ;
        String rangeDate = req.getParameter("rangeDate");
        if (StringUtils.isEmpty(rangeDate)){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",req));
            modelMap.addAttribute("dto",refund);
            return "admin/refundUI";
        }
        String[] rangeDateArray = rangeDate.split("-");
        if (rangeDateArray.length != 2){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",req));
            modelMap.addAttribute("dto",refund);
            return "admin/refundUI";
        }
        startDate = rangeDateArray[0].trim();
        endDate = rangeDateArray[1].trim();

        if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",req));
            modelMap.addAttribute("dto",refund);
            return "admin/refundUI";
        }
        refund.setStartDate(startDate);
        refund.setEndDate(endDate);
        queryParams.put("rangeDate",rangeDate);
        /*
        String rangeDate = req.getParameter("rangeDate");
        if (StringUtils.isEmpty(rangeDate) == false){
            String[] rangeDateArray = rangeDate.split("-");
            if (rangeDateArray.length == 2){
                startDate = rangeDateArray[0].trim();
                endDate = rangeDateArray[1].trim();
                if (StringUtils.isEmpty(startDate) == false && StringUtils.isEmpty(endDate) == false){
                    queryParams.put("rangeDate",rangeDate);
                    refund.setStartDate(startDate);
                    refund.setEndDate(endDate);
                }
            }
        }*/

        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            refund.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            refund.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件：
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType){
                case "0":
                    refund.setBookTitle(searchText);
                    break;
                case "1":
                    refund.setOrderNum(searchText);
                    break;
                case "2":
                    refund.setUserEmail(searchText);
                    break;

            }
            queryParams.put("searchText",searchText);
            queryParams.put("searchType",searchType);
        }

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            IUser user = UserUtils.getCurrentLoginUser();
            CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
            //if (InterfaceContant.RoleName.ROLE_VENDOR.equals(user.getAuthorities().))
            //orderDetail.setPublisherId(user.getCustomDetail().);
            refund.setPublisherId(customUserDetail.getPublisherId());
        }
        refund.setDownloadType(EBookConstant.ExcelDownload.REFUND_INFO);
        refund.addOrder(new OrderObj("purchased_at", InterfaceContant.OrderBy.DESC));
//        refund.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
        Page<EnyanRefund> page = enyanRefundService.queryRecords(refund.getPage(), refund);
        refund.setPage(page);
        refund.excutePageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand", refund.getPageLand());
        modelMap.addAttribute("refund", refund);
        modelMap.addAttribute("explan","退款管理");

        return "admin/refunds";
    }

    @RequestMapping("/del-{id}")
    public String delRebundById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("method delRebundById");

        enyanRefundService.deleteRecordByPrimaryKey(id);
        return "redirect:/refund/refunds";
    }

    @RequestMapping("/addRefundUI")
    public String addRefundUI(EnyanRefund enyanRefund, ModelMap modelMap){
        log.debug("method addRefundUI");
        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            return "admin/refundAdd";
        }
        this.initBookValue(modelMap);
        return "admin/refundAdd";
    }

    @RequestMapping(value = "/saveRefund", method = RequestMethod.POST)
    public String saveRefund(EnyanRefund enyanRefund, ModelMap modelMap) {
        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            return "admin/refundAdd";
        }
        if (StringUtils.isEmpty(enyanRefund.getReasonContent())) {
            this.setErrorMsg(modelMap, "请填写退款理由！");
            this.initBookValue(modelMap);
            return "admin/refundAdd";
        }
        if (null == enyanRefund.getSalesVolumeDecrease()) {
            this.setErrorMsg(modelMap, "请填写销量变动！");
            this.initBookValue(modelMap);
            return "admin/refundAdd";
        }
        if (null == enyanRefund.getIncomeTotalDecrease()) {
            this.setErrorMsg(modelMap, "请填写销售额变动HKD！");
            this.initBookValue(modelMap);
            return "admin/refundAdd";
        }
        if (StringUtils.isEmpty(enyanRefund.getOrderNum())) {
            this.setErrorMsg(modelMap, "请添加订单号！");
            this.initBookValue(modelMap);
            return "admin/refundAdd";
        }

        String[] ids = enyanRefund.getBookIDs();
        if (ids.length == 0){
            this.setErrorMsg(modelMap, "请正确选择退款的书籍信息！");
            this.initBookValue(modelMap);
            return "admin/refundAdd";
        }
        String paramDate ;
        try {
            Long bookId = Long.parseLong(ids[0]);
            List<EnyanBookBuy> list = enyanBookBuyService.findBookBuyListByOrderNumAndBookId(enyanRefund.getOrderNum(), bookId);
            if (list.isEmpty() == true){
                this.setErrorMsg(modelMap, "请正确选择退款的书籍或订单信息！");
                this.initBookValue(modelMap);
                return "admin/refundAdd";
            }
            EnyanBookBuy bookBuy = list.get(0);
            if (StringUtils.hasLength(bookBuy.getDrminfo()) == true){
                DrmInfo drmInfo = JSONObject.parseObject(bookBuy.getDrminfo(), DrmInfo.class);
                if (null != drmInfo && null != drmInfo.getLcpInfo() && StringUtils.hasLength(drmInfo.getLcpInfo().getLicenseUuid()) == true){
                    String licenseId = drmInfo.getLcpInfo().getLicenseUuid();
                    enyanRefund.setLicenseId(licenseId);
                }
            }

            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookBuy.getBookId()).getResult();
            if (null == enyanBook){
                this.setErrorMsg(modelMap, "书籍不存在！");
                this.initBookValue(modelMap);
                return "admin/refundAdd";
            }
            enyanRefund.setReasonType(0);
            enyanRefund.setOrderNum(bookBuy.getOrderNum());
            enyanRefund.setPublisherId(enyanBook.getPublisherId());
            enyanRefund.setPublisherName(enyanBook.getPublisherName());
            enyanRefund.setUserEmail(bookBuy.getUserEmail());
            enyanRefund.setUserId(bookBuy.getUserId());
            enyanRefund.setPurchasedAt(bookBuy.getPurchasedAt());
            enyanRefund.setBookId(enyanBook.getBookId());
            enyanRefund.setBookTitle(enyanBook.getBookTitle());
            enyanRefund.setCreateTime(new Date());
            enyanRefund.setIsDeleted(0);

            enyanRefundService.addRecord(enyanRefund);
            String currentDate = DateFormatUtils.format(bookBuy.getPurchasedAt(), "yyyyMMdd");
            paramDate = "rangeDate="+currentDate+" - "+currentDate;
        } catch (Exception e) {
            log.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传图片失败！");
            this.setErrorMsg(modelMap, "上传图片失败！");
            this.initBookValue(modelMap);
            return "admin/refundAdd";
        }

        this.setSuccessMsg(modelMap, "添加书籍成功！");
        //20210301 - 20210430
        return "redirect:/refund/refunds?"+paramDate;
    }

    private void initBookValue(ModelMap modelMap){
        modelMap.addAttribute("bookIDsList", Constant.booksList);
    }
}
