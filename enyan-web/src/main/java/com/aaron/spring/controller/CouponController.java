package com.aaron.spring.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanCoupon;
import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.service.EnyanCouponService;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Aaron <PERSON>
 * @Description:
 * @Date: Created in  2021/9/3
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/coupon")
public class CouponController extends BaseController{
	@Resource
	private EnyanCouponService enyanCouponService;

	@RequestMapping(value = "/list")
	public String list(HttpServletRequest req, EnyanCoupon record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		if (null == record){
			record = new EnyanCoupon();
		}
		if (null == record.getPage()){
			record.setPage(new Page());
		}
		Map<String, Object> queryParams = new HashMap<>();

		// 获取分页参数
		String total = req.getParameter("total");
		String currentPage = req.getParameter("pageNo");

		if (StringUtils.hasLength(total)) {
			record.getPage().setTotalRecord(Integer.parseInt(total));
		}
		if (StringUtils.hasLength(currentPage)) {
			record.getPage().setCurrentPage(Integer.parseInt(currentPage));
		}


        // 高级查询条件：
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType){
                case "0":
                    record.setCouponName(searchText);
                    break;
                case "1":
                    break;

            }
            queryParams.put("searchText",searchText);
            queryParams.put("searchType",searchType);
        }

//        book.addOrder(new OrderObj("is_recommended", InterfaceContant.OrderBy.DESC));
//        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
		Page<EnyanCoupon> page = enyanCouponService.queryRecords(record.getPage(),record);
		record.setPage(page);
		record.excutePageLand(queryParams);

		modelMap.addAttribute("list",page.getRecords());
		modelMap.addAttribute("pageLand",record.getPageLand());
		modelMap.addAttribute("record",record);
		modelMap.addAttribute("explan",this.getMessage("coupon.list", req));

		return "admin/coupons";
	}

	@RequestMapping("/addUI")
	public String addUI(EnyanCoupon record, ModelMap modelMap){
		log.debug("method addUI");
		record.setUseMax(1);
		record.setCouponStatus(1);
		record.setCouponType(0);
		modelMap.addAttribute("record",record);
		modelMap.addAttribute("bookIDsList", Constant.booksList);
		return "admin/couponAdd";
	}

	@RequestMapping("/addUIAll")
	public String addUIAll(EnyanCoupon record, ModelMap modelMap){
		log.debug("method addUI");
		record.setUseMax(1);
		record.setCouponStatus(1);
		record.setCouponType(0);
		String[] ids = Constant.booksList.stream().map(value->value.getValue()).collect(Collectors.toList()).toArray(new String[0]);
		record.setBookIDs(ids);
		modelMap.addAttribute("record",record);
		modelMap.addAttribute("bookIDsList", Constant.booksList);
		return "admin/couponAdd";
	}

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		EnyanCoupon record = enyanCouponService.queryRecordByPrimaryKey(id).getResult();
		String rangeDate = DateFormatUtils.format(record.getBeginTime(),"yyyyMMdd")+" - "
				                   + DateFormatUtils.format(record.getEndTime(),"yyyyMMdd");
		record.setRangeDate(rangeDate);
		if (null != record.getBookSet()){
			String[] ids = record.getBookSet().stream().map(String::valueOf).collect(Collectors.toList()).toArray(new String[0]);
			record.setBookIDs(ids);
 		}
		modelMap.addAttribute("bookIDsList", Constant.booksList);
		modelMap.addAttribute("record",record);

		return "admin/couponAdd";
	}

	@RequestMapping("/del-{id}")
	public String delById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("method delById");
		enyanCouponService.deleteRecordByPrimaryKey(id);
		return "redirect:/coupon/list";
	}

	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public String savePublisher(HttpServletRequest request, EnyanCoupon record, ModelMap modelMap){
		log.info("method save："+record);

		if (StringUtils.hasLength(record.getCouponName()) == false){
			this.setErrorMsg(modelMap,"请填写优惠码名称");
			return "admin/couponAdd";
		}
		if (StringUtils.hasLength(record.getCouponCode()) == false){
			this.setErrorMsg(modelMap,"请填写优惠码");
			return "admin/couponAdd";
		}
		record.setCouponCode(record.getCouponCode().toUpperCase());
		// 高级查询条件：
		String startDate, endDate ;
		String rangeDate = request.getParameter("rangeDate");
		if (StringUtils.hasLength(rangeDate) == false){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			return "admin/couponAdd";
		}
		String[] rangeDateArray = rangeDate.split("-");
		if (rangeDateArray.length != 2){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			return "admin/couponAdd";
		}
		startDate = rangeDateArray[0].trim();
		endDate = rangeDateArray[1].trim();
		if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			return "admin/couponAdd";
		}
		try {
			record.setBeginTime(DateUtils.parseDate(startDate,"yyyyMMdd"));
			record.setEndTime(DateUtils.parseDate(endDate+" 23:59:59","yyyyMMdd HH:mm:ss"));
		} catch (ParseException e) {
			e.printStackTrace();
		}
		if (0 == record.getCouponType()){//所有书籍
			record.setBookIDs(null);
		}else {
			if (null != record.getBookIDs() && record.getBookIDs().length>0){
				HashSet<Long> ids = Arrays.stream(record.getBookIDs()).map(value->Long.parseLong(value)).collect(Collectors.toCollection(HashSet::new));
				String jsonString = JSON.toJSONString(ids);
				record.setBookString(jsonString);
			}
		}

		if (null == record.getDataId()){
			EnyanCoupon coupon = enyanCouponService.getCouponByCode(record.getCouponCode());
			if (null != coupon){
				this.setErrorMsg(modelMap, "优惠码已经存在");
				return "admin/couponAdd";
			}
			record.setBuyCount(0);
			record.setIsDeleted(0);
			record.setCreateAt(new Date());
			this.setSuccessMsg(modelMap,"添加优惠码成功");
			enyanCouponService.addRecord(record);
		}else {
			/*
			EnyanCoupon couponToUpdate = new EnyanCoupon();
			couponToUpdate.setCouponName(record.getCouponName());
			couponToUpdate.setDataId(record.getDataId());
			couponToUpdate.setBeginTime(record.getBeginTime());
			couponToUpdate.setEndTime(record.getEndTime());
			couponToUpdate.setCouponStatus(record.getCouponStatus());
			couponToUpdate.setBuyMax(record.getBuyMax());*/
			record.setCouponValue(null);
			record.setMinLimitValue(null);
			this.setSuccessMsg(modelMap,"修改优惠码成功");
			enyanCouponService.updateRecord(record);
		}
		return "redirect:/coupon/list";
	}
}
