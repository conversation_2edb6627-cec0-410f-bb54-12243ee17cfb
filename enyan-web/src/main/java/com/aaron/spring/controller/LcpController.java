package com.aaron.spring.controller;

import com.aaron.crypto.EpubEncryption;
import com.aaron.drm.model.PublicationLcp;
import com.aaron.drm.util.DRMUtil;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.Publication;
import com.aaron.spring.service.PublicationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-11
 * @Modified By:
 */
@Controller
@RequestMapping("/lcp")
public class LcpController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(LcpController.class);
    @Resource
    private PublicationService publicationService;

    @RequestMapping(value = "/publications")
    public String publicationsPage(HttpServletRequest req, Publication publication, ModelMap modelMap) {
        logger.info("publications");
        try {
            if (null == publication) {
                publication = new Publication();
            }
            if (null == publication.getPage()) {
                publication.setPage(new Page());
            }
            Map<String, Object> queryParams = new HashMap<String, Object>();

            // 获取分页参数
            String total = req.getParameter("total");
            String currentPage = req.getParameter("pageNo");

            if (StringUtils.hasLength(total)) {
                publication.getPage().setTotalRecord(Integer.parseInt(total));
            }
            if (StringUtils.hasLength(currentPage)) {
                publication.getPage().setCurrentPage(Integer.parseInt(currentPage));
            }

            // 高级查询条件：
            String searchText = req.getParameter("searchText");
            String searchType = req.getParameter("searchType");
            if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
                switch (searchType) {
                    case "0":
                        publication.setTitle(searchText);
                        break;
                }
                queryParams.put("searchText", searchText);
                queryParams.put("searchType", searchType);
            }

            Page<Publication> page = publicationService.queryRecords(publication.getPage(), publication);

            publication.setPage(page);
            publication.excutePageLand(queryParams);

            modelMap.addAttribute("list", page.getRecords());
            modelMap.addAttribute("pageLand", publication.getPageLand());

            modelMap.addAttribute("publication", publication);
            modelMap.addAttribute("explan", "书籍加密管理");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "admin/publications";
    }

    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getPublicationById(@PathVariable("id") Long id, ModelMap modelMap) {
        logger.info("getPublisherById");
        Publication publication = publicationService.queryRecordByPrimaryKey(id).getResult();
        modelMap.addAttribute("publication", publication);
        return "admin/publicationEdit";
    }

    @RequestMapping("/del-{id}")
    public String delPublicationById(@PathVariable("id") Long id, ModelMap modelMap) {
        logger.info("method delPublicationById");
        publicationService.deleteRecordByPrimaryKey(id);
        return "redirect:/lcp/publications";
    }

    @RequestMapping("/addPublicationUI")
    public String addPublicationUI(Publication publication) {
        logger.info("method addPublicationUI");

        return "admin/publicationAdd";
    }

    @RequestMapping(value = "/savePublication", method = RequestMethod.POST)
    public String savePublication(Publication publication,
                                  @RequestParam(value = "uploadBook", required = false) MultipartFile filedataBook,
                                  ModelMap modelMap) {
        logger.info("method savePublication：" + publication);

        if (StringUtils.hasLength(publication.getTitle()) == false) {
            this.setErrorMsg(modelMap, "请填写书籍名称");
            return "admin/publicationAdd";
        }
        Publication tmpPublication = new Publication();
        tmpPublication.setTitle(publication.getTitle());
        List<Publication> list = publicationService.findPublicationReally(tmpPublication);

        if (null != list && !list.isEmpty()) {
            if (list.get(0).getId() != publication.getId()) {
                this.setErrorMsg(modelMap, "该书籍已存在");
                return "admin/publicationAdd";
            }
        }
        try {
            if (filedataBook != null && !filedataBook.isEmpty()) {
                String fileName = publication.getTitle() + ".epub";
                String epubPath = DRMUtil.EPUB_MASTER_DIR + fileName;

                File bookFile = new File(epubPath);

                filedataBook.transferTo(bookFile);

                PublicationLcp publicationLcp = new PublicationLcp();
                publicationLcp.setStatus("ok");
                publicationLcp.setMasterFilename(fileName);
                publicationLcp.setTitle(publication.getTitle());

                publicationService.addRecordByLcp(publicationLcp);
            }
        } catch (Exception e){
            logger.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传加密电子书失败！");
            this.setErrorMsg(modelMap, "上传加密电子书失败！");
            return "admin/publicationAdd";
        }
        /*
        if (null == publication.getId()){
            this.setSuccessMsg(modelMap,"添加加密书籍成功");
            publicationService.addRecord(publication);
        }else {
            this.setSuccessMsg(modelMap,"修改加密书籍成功");
            publicationService.updateRecord(publication);
        */
            /*EnyanBook enyanBook = new EnyanBook();
            enyanBook.setPublisherId(enyanPublisher.getPublisherId());
            enyanBook.setPublisherName(enyanPublisher.getPublisherName());
            enyanBookService.updateBookByExample(enyanBook);*/

        return "redirect:/lcp/publications";
    }

    @RequestMapping(value = "/updatePublication", method = RequestMethod.POST)
    public String updatePublication(Publication publication, ModelMap modelMap) {
        logger.info("method updatePublication：" + publication);

        if (StringUtils.hasLength(publication.getTitle()) == false) {
            this.setErrorMsg(modelMap, "请填写书籍名称");
            return "admin/publicationEdit";
        }
        Publication tmpPublication = new Publication();
        tmpPublication.setTitle(publication.getTitle());
        List<Publication> list = publicationService.findPublicationReally(tmpPublication);

        if (null != list && !list.isEmpty()) {
            if (list.get(0).getId() != publication.getId()) {
                this.setErrorMsg(modelMap, "该书籍已存在");
                return "admin/publicationEdit";
            }
        }

        if (null == publication.getId()) {
            this.setSuccessMsg(modelMap, "添加加密书籍成功");
            publicationService.addRecord(publication);
        } else {
            this.setSuccessMsg(modelMap, "修改加密书籍成功");
            publicationService.updateRecord(publication);

            /*EnyanBook enyanBook = new EnyanBook();
            enyanBook.setPublisherId(enyanPublisher.getPublisherId());
            enyanBook.setPublisherName(enyanPublisher.getPublisherName());
            enyanBookService.updateBookByExample(enyanBook);*/
        }
        return "redirect:/lcp/publications";
    }
}
