package com.aaron.spring.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanCategory;
import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanCategoryService;
import com.aaron.spring.service.EnyanPublisherService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/16
 * @Modified By:
 */
@Controller
@RequestMapping("/publisher")
public class PublisherController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(PublisherController.class);
    @Resource
    private EnyanPublisherService enyanPublisherService;

    @Resource
    private EnyanBookService enyanBookService;

    @RequestMapping(value = "/publishers")
    public String publishersPage(HttpServletRequest req, EnyanPublisher publisher, ModelMap modelMap){
        logger.info("publishersPage");
        try {
            if (null == publisher){
                publisher = new EnyanPublisher();
            }
            if (null == publisher.getPage()){
                publisher.setPage(new Page());
            }
            Map<String, Object> queryParams = new HashMap<String, Object>();

            // 获取分页参数
            String total = req.getParameter("total");
            String currentPage = req.getParameter("pageNo");

            if (StringUtils.hasLength(total)) {
                publisher.getPage().setTotalRecord(Integer.parseInt(total));
            }
            if (StringUtils.hasLength(currentPage)) {
                publisher.getPage().setCurrentPage(Integer.parseInt(currentPage));
            }

            // 高级查询条件：
            String searchText = req.getParameter("searchText");
            String searchType = req.getParameter("searchType");
            if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
                switch (searchType){
                    case "0":
                        publisher.setPublisherName(searchText);
                        break;
                }
                queryParams.put("searchText",searchText);
                queryParams.put("searchType",searchType);
            }

            Page<EnyanPublisher> page = enyanPublisherService.queryRecords(publisher.getPage(),publisher);

            publisher.setPage(page);
            publisher.excutePageLand(queryParams);

            modelMap.addAttribute("list",page.getRecords());
            modelMap.addAttribute("pageLand",publisher.getPageLand());

            modelMap.addAttribute("enyanCategory",publisher);
            modelMap.addAttribute("explan","出版商管理");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "admin/publishers";
    }
    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getPublisherById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.info("getPublisherById");
        EnyanPublisher enyanPublisher = enyanPublisherService.queryRecordByPrimaryKey(id).getResult();
        modelMap.addAttribute("enyanPublisher",enyanPublisher);
        return "admin/publisherAdd";
    }

    @RequestMapping("/del-{id}")
    public String delPublisherById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.info("method delCategoryById");
        enyanPublisherService.deleteRecordByPrimaryKey(id);
        return "redirect:/publisher/publishers";
    }

    @RequestMapping("/addPublisherUI")
    public String addCategoryUI(EnyanPublisher enyanPublisher){
        logger.info("method addPublisherUI");

        return "admin/publisherAdd";
    }

    @RequestMapping(value = "/savePublisher", method = RequestMethod.POST)
    public String savePublisher(EnyanPublisher enyanPublisher, ModelMap modelMap){
        logger.info("method savePublisher："+enyanPublisher);

        if (StringUtils.hasLength(enyanPublisher.getPublisherName()) == false){
            this.setErrorMsg(modelMap,"请填写出版商名称");
            return "admin/publisherAdd";
        }
        EnyanPublisher publisher = new EnyanPublisher();
        publisher.setPublisherName(enyanPublisher.getPublisherName());
        List<EnyanPublisher> list = enyanPublisherService.findPublishersReally(publisher);

        if (null != list && !list.isEmpty()){
            if (list.get(0).getPublisherId() != enyanPublisher.getPublisherId()){
                this.setErrorMsg(modelMap,"该出版社已存在");
                return "admin/publisherAdd";
            }
        }

        if (null == enyanPublisher.getPublisherId()){
            this.setSuccessMsg(modelMap,"添加出版商成功");
            enyanPublisher.setStartTime(new Date());
            enyanPublisher.setVendorPercent(0);
            enyanPublisherService.addRecord(enyanPublisher);
        }else {
            this.setSuccessMsg(modelMap,"修改出版商成功");
            enyanPublisherService.updateRecord(enyanPublisher);

            /*EnyanBook enyanBook = new EnyanBook();
            enyanBook.setPublisherId(enyanPublisher.getPublisherId());
            enyanBook.setPublisherName(enyanPublisher.getPublisherName());
            enyanBookService.updateBookByExample(enyanBook);*/
        }
        return "redirect:/publisher/publishers";
    }
}
