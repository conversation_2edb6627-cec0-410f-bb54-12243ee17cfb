package com.aaron.spring.task;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.controller.BookController;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanDiscount;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanDiscountService;
import com.aaron.spring.service.LogService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.BinaryClient;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/1/3
 * @Modified By:
 */
@Component
public class BookTaskJob {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private Date currentDate;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanDiscountService enyanDiscountService;

    @Resource
    private LogService logService;
    /**
     *
     *  每天早上1：15触发
     * @param
     * @Date: 2018/1/3
     */
    @Scheduled(cron = "0 15 0 * * ?")
    public void exeDiscountSingleJob(){
        //System.out.println(DateUtils.addDays());
        //System.out.println(DateFormatUtils.format(new Date(),DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.));
        //logger.error(DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(new Date()));

        this.initEveryDay();
        List<EnyanBook> list = enyanBookService.findBooksByDiscountValid();
        boolean hasChangeDiscount = false;
        boolean bookChanged ;
        if (null != list){
            for (EnyanBook enyanBook:list){
                bookChanged = false;
                EnyanBook book = new EnyanBook();
                book.setBookId(enyanBook.getBookId());
                if (!this.isBookDiscountSingleValid(enyanBook)){
                    book.setDiscountSingleIsValid(Constant.BYTE_VALUE_0);
                    bookChanged = true;
                }
                if (!this.isBookDiscountValid(enyanBook)){
                    book.setDiscountIsValid(Constant.BYTE_VALUE_0);
                    bookChanged = true;
                }
                if (bookChanged){
                    enyanBookService.updateByPrimaryKeySelective(book);
                }
            }
        }
        if (hasChangeDiscount){//重载首页推荐
            enyanBookService.initIndexAllInfo();
        }

        enyanBookService.initElasticsearchInfoDay();
        logService.sendTimeLog("重载首页推荐：");
    }

    /**
     * 每隔20秒执行一次
     */
    //@Scheduled(fixedRate = 1000*20)
    //@Scheduled(fixedRate = 1000*60*5)
    public void test(){
        logger.info("当前时间："+DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(new Date()));
    }
    /**
     *
     * 每天的初始化，重置折扣信息
     * @param
     * @Date: 2018/1/3
     */
    private void initEveryDay(){
        this.currentDate = new Date();
        enyanDiscountService.initDiscounts();
    }



    private List<EnyanBook> getDiscountSingleBook(){
        EnyanBook enyanBook = new EnyanBook();
        //enyanBook.setDiscountSingleType(new Byte("1"));
        enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_1);
        Page page = new Page();
        page.setPageSize(-1);
        enyanBook.setPage(page);
        return enyanBookService.findBooks(enyanBook);
    }

    /**
     * <p>单个折扣是否生效</p>
     * @param enyanBook
     * @return: boolean
     * @since : 2020-08-18
     */
    private boolean isBookDiscountSingleValid(EnyanBook enyanBook){
        if (null == enyanBook.getDiscountSingleId()){
            return false;
        }
        EnyanDiscount enyanDiscount = Constant.discountMap.get(enyanBook.getDiscountSingleId());
        if (null == enyanDiscount){
            return false;
        }
        if (Constant.BYTE_VALUE_0.equals(enyanDiscount.getIsValid())){
            return false;
        }
        /*
        if (null == enyanBook.getDiscountSingleStartTime()){
            return false;
        }
        if (null == enyanBook.getDiscountSingleEndTime()){
            return false;
        }
        if (DateUtils.isSameDay(this.currentDate,enyanBook.getDiscountSingleStartTime())){
            return true;
        }
        if (DateUtils.isSameDay(this.currentDate,enyanBook.getDiscountSingleEndTime())){
            return true;
        }
        if (this.currentDate.compareTo(enyanBook.getDiscountSingleStartTime()) == -1){ //-1 小于；1 大于
            return false;
        }
        if (this.currentDate.compareTo(enyanBook.getDiscountSingleEndTime()) == 1){
            return false;
        }*/
        /*
        if (this.currentDate.compareTo(enyanBook.getDiscountSingleStartTime()) == -1
                && !DateUtils.isSameDay(this.currentDate,enyanBook.getDiscountSingleStartTime())){
            return false;
        }
        if (this.currentDate.compareTo(enyanBook.getDiscountSingleEndTime()) == 1
                && !DateUtils.isSameDay(this.currentDate,enyanBook.getDiscountSingleEndTime())){
            return false;
        }*/
        return true;
    }
    /**
     * <p>N件折是否生效</p>
     * @param enyanBook
     * @return: boolean
     * @since : 2020-08-18
     */
    private boolean isBookDiscountValid(EnyanBook enyanBook){
        if (null == enyanBook.getDiscountId()){
            return false;
        }

        EnyanDiscount enyanDiscount = Constant.discountMap.get(enyanBook.getDiscountId());
        if (null == enyanDiscount){
            return false;
        }
        if (Constant.BYTE_VALUE_0.equals(enyanDiscount.getIsValid())){
            return false;
        }
        /*
        if (this.currentDate.compareTo(enyanBook.getDiscountSingleStartTime()) == -1
                && !DateUtils.isSameDay(this.currentDate,enyanBook.getDiscountSingleStartTime())){
            return false;
        }
        if (this.currentDate.compareTo(enyanBook.getDiscountSingleEndTime()) == 1
                && !DateUtils.isSameDay(this.currentDate,enyanBook.getDiscountSingleEndTime())){
            return false;
        }*/
        return true;
    }
    /*
      CRON表达式  含义
    "0 0 12 * * ?"   每天中午十二点触发
    "0 15 10 ? * *"   每天早上10：15触发
    "0 15 10 * * ?"   每天早上10：15触发
    "0 15 10 * * ? *"  每天早上10：15触发
    "0 15 10 * * ? 2005"  2005年的每天早上10：15触发
    "0 * 14 * * ?"   每天从下午2点开始到2点59分每分钟一次触发
    "0 0/5 14 * * ?"  每天从下午2点开始到2：55分结束每5分钟一次触发
    "0 0/5 14,18 * * ?"  每天的下午2点至2：55和6点至6点55分两个时间段内每5分钟一次触发
    "0 0-5 14 * * ?"   每天14:00至14:05每分钟一次触发
    "0 10,44 14 ? 3 WED"	三月的每周三的14：10和14：44触发
    "0 15 10 ? * MON-FRI"	每个周一、周二、周三、周四、周五的10：15触发

    * */
}
