package com.aaron.spring.common;

import com.aaron.spring.model.*;
import com.alipay.config.AlipayConfig;
import com.alipay.util.AlipaySubmit;
import org.springframework.mobile.device.Device;
import org.springframework.security.core.parameters.P;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static com.aaron.spring.controller.ShopController.getAlipayGoodsTradeInformation;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/11/29
 * @Modified By:
 */
public class AaronAlipay {
	/**
	 *
	 */
	private boolean isMobile;
	/**
	 *
	 */
	private Map<String, String> sParaTemp = new HashMap<>();
	/**
	 *用于支付的链接，根据mobile有不同的url（html）
	 */
	private String payUrl;//
	/**
	 *用于转向的页面信息
	 */
	private String redirectUrl;//用于转向的页面信息
	/**
	 * 订单分组信息（书籍购买订单还是先租后买订单）
	 */
	private int orderGroup;//

	/**
	 * 是否是HK
	 */
	private String type;

	public void initFrom(EnyanRent rent, int toRentMonths, boolean isHK, boolean isMobile){
		EnyanOrder order = new EnyanOrder();

		OrderDetailInfo detailInfo = new OrderDetailInfo();
		order.setOrderDetailInfo(detailInfo);

		ProductInfo productInfo = new ProductInfo(rent);
		CartDiscountInfo cartDiscountInfo = new CartDiscountInfo();
		cartDiscountInfo.setDiscountId(Constant.DEFAULT_DISCOUNT_ID);
		cartDiscountInfo.addProduct(productInfo, toRentMonths);
		detailInfo.getCartDiscountInfoList().add(cartDiscountInfo);

		this.orderGroup = EBookConstant.OrderGroup.ORDER_RENT;

		BigDecimal totalFee = BookUtil.getRentFee(rent.getRentPrice(),toRentMonths);
		order.setOrderTotal(totalFee);
		String orderNum = BookUtil.getRentAlipayOrderByRent(rent);
		order.setOrderNum(orderNum);

		this.initDefault(order,isHK,isMobile);
	}

	public void initFrom(EnyanOrder order, boolean isHK, boolean isMobile){
		this.orderGroup = EBookConstant.OrderGroup.ORDER_BUY;
		this.initDefault(order, isHK, isMobile);
	}

	public void initDefault(EnyanOrder order, boolean isHK, boolean isMobile){
/*
		if (Constant.BYTE_VALUE_1.equals(order.getIsPaid())){
			modelMap.addAttribute("showTop","1");
			if (order.getOrderType() == EBookConstant.OrderType.ORDER_REDEEM_BUY){
				return "/shop/checkoutReturnGift";
			}
			return "/shop/checkoutReturn";
		}*/

		//商户订单号，商户网站订单系统中唯一订单号，必填
		String out_trade_no = order.getOrderNum();

		//订单名称，必填
		String subject = out_trade_no;//"恩道電子書 -  # "+out_trade_no;

		//付款金额，必填
		String total_fee = String.valueOf(order.getOrderTotal());

		//商品描述，可空 2019.2.14 添加json body
		//String body = this.getAlipayGoodsBody(order);

		//商品描述，可空 2019.5.17 添加json body
		String tradeInformation = getAlipayGoodsTradeInformation(order);

		//币种，不可空
		//String currency = "USD";

		//把请求参数打包成数组
		sParaTemp.put("service", AlipayConfig.service);
		//注意：必传，PC端是NEW_OVERSEAS_SELLER，移动端是NEW_WAP_OVERSEAS_SELLER
		//		Remarks:Mandatory.For PC: NEW_OVERSEAS_SELLER ;FOR WAP and APP: NEW_WAP_OVERSEAS_SELLER
		sParaTemp.put("product_code", AlipayConfig.product_code);//PC与WAP一起使用
		if (isMobile == true){
			sParaTemp.put("service", AlipayConfig.service_mobile);
			//sParaTemp.put("product_code", "NEW_WAP_OVERSEAS_SELLER"); //WAP
		}
		sParaTemp.put("partner", AlipayConfig.partner);
		sParaTemp.put("_input_charset", AlipayConfig.input_charset);
		sParaTemp.put("notify_url", AlipayConfig.notify_url);
		sParaTemp.put("return_url", AlipayConfig.return_url);
		sParaTemp.put("refer_url", AlipayConfig.refer_url);
		sParaTemp.put("out_trade_no", out_trade_no);
		sParaTemp.put("subject", subject);
		//sParaTemp.put("total_fee", total_fee);
		//sParaTemp.put("rmb_fee", total_fee);

		sParaTemp.put("total_fee", total_fee);
		//sParaTemp.put("total_fee", "0.1");//HKD

		//sParaTemp.put("body", body);
		sParaTemp.put("trade_information", tradeInformation);
		sParaTemp.put("currency", AlipayConfig.currency);
		//其他业务参数根据在线开发文档，添加参数
		//如sParaTemp.put("参数名","参数值");

		//https://global.alipay.com/docs/ac/hkapi/create_forex_trade_wap
		//https://global.alipay.com/docs/ac/website_hk/ux
		if (isHK){
			sParaTemp.put("payment_inst", AlipayConfig.payment_inst_HK);//ALIPAYHK ALIPAYCN
		}else {
			//sParaTemp.put("payment_inst", AlipayConfig.payment_inst_CN);//ALIPAYHK ALIPAYCN
		}

		sParaTemp.put("qr_pay_mode", "4");
		sParaTemp.put("qrcode_width", "200");

		//建立请求

		if (isMobile == true){
			payUrl = AlipaySubmit.buildRequest(sParaTemp,"get","确认");
			redirectUrl = "/showResult";//直接展示转换的代码，用于手机端调用app
			return;
			//return "/showResult";//直接展示转换的代码，用于手机端调用app
		}

		payUrl = AlipaySubmit.buildRequestMethod(sParaTemp);
		redirectUrl = "/shop/alipayConfirm";//新版本的iframe展示
		//log.error("toUrl:{}",toUrl);
		//return "/shop/alipayConfirm";//新版本的iframe展示
	}

	public boolean isMobile() {
		return isMobile;
	}

	public void setMobile(boolean mobile) {
		isMobile = mobile;
	}

	public Map<String, String> getsParaTemp() {
		return sParaTemp;
	}

	public void setsParaTemp(Map<String, String> sParaTemp) {
		this.sParaTemp = sParaTemp;
	}

	public String getPayUrl() {
		return payUrl;
	}

	public void setPayUrl(String payUrl) {
		this.payUrl = payUrl;
	}

	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}

	public int getOrderGroup() {
		return orderGroup;
	}

	public void setOrderGroup(int orderGroup) {
		this.orderGroup = orderGroup;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
