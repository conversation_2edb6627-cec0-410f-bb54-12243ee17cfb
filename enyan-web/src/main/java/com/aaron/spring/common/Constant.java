/**
 * 
 */
package com.aaron.spring.common;

import com.aaron.common.NameAndValue;
import com.aaron.common.SearchType;
import com.aaron.spring.api.AdModel;
import com.aaron.spring.api.RestConfig;
import com.aaron.spring.api.v4.model.RestShopIndex;
import com.aaron.spring.model.*;
import com.aaron.util.FilePropertiesUtil;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * Title: QST Teach
 * </p>
 * 
 * <p>
 * Description: QST
 * </p>
 * 
 * <p>
 * Copyright: Copyright (c) 2015
 * </p>
 * 
 * <p>
 * Company: QST
 * </p>
 * 
 * <AUTHOR>
 * 
 */
public class Constant {

	public static final String VSERION = "1.0";

	public static String SERVLET_MAPPING = "*.action";

	public static String ENVIRONMENT_PATH = "evn.webapp";

	public static String IMG_SERVER = "https://ehome.endao.co/";

	public static final int CN_COUNT = 6;
	public static final int ENG_COUNT = 3;

	public static final boolean IS_PRODUCT = FilePropertiesUtil.props.getProperty("isProduct").equals("true");

	public static final boolean IS_LOCAL = FilePropertiesUtil.props.getProperty("isLocal").equals("true");

	public static final boolean IS_TEST = FilePropertiesUtil.props.getProperty("isTest").equals("true");

	/**
	 * 是否启用灵修的功能
	 * */
	//public static final boolean USE_SPIRIT = true;//是否启用灵修的功能

	public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd";

	public static final int REPORT_PER_WEEK = 5;

	public static String WEEK_TIME = "";
	public static String WEEK_TIME_END = "";

	public static int DATE_YEAR = 2015;

	public static String WEEK_TIME_LAST = "";

	public static String Day_Today = "";

	public static final String CHARSET = "UTF-8";

	public static String ROOTPATH = "";

	public static String VER_SPI = "";

	public static String WEB_FEECODE_FROM = "http://ff/";

	public static String WEB_FEECODE_CODE = "code/code.txt";

	public static String WEB_FEECODE_CODE_NEW = "code/code_new.txt";

	public static String[] items = { "code/code.txt" };

	public static String WEB_DOWNLOAD_INFO = "info/info.txt";

	// public static String WEB_DOWNLOAD_INFO_NEW = "info/info_new.txt";

	public static String FTL_PATH = "WEB-INF/classes/templates/";

	public static String DEFAULT_BUSINESS = "z*0*0*0*0*0*0*0*0*0";// 默认业务

	public static String AES_KEY = "helldas@&ssxagfg";

	public static final String FIXER_IO_API_KEY = "********************************";

	public static boolean USE_URL_REWRITE = false;

	public static final String USER_SESSION_KEY = "user_session";

	public static final Integer ORDER_ASC = 0;

	public static final Integer ORDER_DESC = 1;

	/**
	 * 最多的支持设备数目
	 * */
	public static final Integer MAX_DEVICE_LIMIT = 5;//最多的支持设备数目

	/**
	 * 最多的支持测试设备数目
	 * */
	public static final Integer MAX_DEVICE_TEST_LIMIT = 40;//最多的支持测试设备数目

	/**
	 * 最多的购买赠送码数目
	 * */
	public static final Integer MAX_REDEEM_BUY_LIMIT = 100;//最多的购买赠送码数目

	/**
	 * 保留小数位数 1 位
	 * */
	public static final Integer NUM_SCALE_0 = 0;

	/**
	 * 保留小数位数 1 位
	 * */
	public static final Integer NUM_SCALE_1 = 1;

	/**
	 * 保留小数位数 2 位
	 * */
	public static final Integer NUM_SCALE_2 = 2;

	public static final String SESSION_FILE = "session.s";

	public static String BEANPERFIX = "com.sech.adv.bean.";

    public static String IMG_SMALL_FILEPREFIX = "_Small";

    public static final String ACCESS_TOKEN_NAME = "access_token";

	public static final String TOKEN_NAME = "token";

	public static final String ACCESS_EMAIL = "email";

	public static final String ACCESS_CURRENCY = "currency";

	public static final String ACCESS_LANG = "lang";

	public static final String ACCESS_AREA = "area";

	public static final String ACCESS_DEVICE_FROM = "df";

	public static final String ACCESS_DEVICE_VERSION = "v";

	/**
	 * 默认的导入Excel
	 * */
	public static final String DEFAULT_EXCEL_IMPORT = "<EMAIL>";

    public static final String REFRESH_TOKEN_NAME = "refresh_token";

    public static final String CURRENT_USER_ID = "CURRENT_USER_ID";

    public static String TEST_TOKEN_VALUE = "k9n-2020-Wa7-Yw2-kp5-04654";

    public static final Set<String> TEST_ACCOUNT = new HashSet(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>")); //测试用的账户，可以直接伪装称某些账户（设置email）

	public static final Set<String> TEST_ACCOUNT_PAY = new HashSet(Arrays.asList("<EMAIL>","<EMAIL>","<EMAIL>"));//只是用于测试服务器的测试支付

    //REST API Token
    public static final String EDITION_GUARD_TOKEN = "Token 32b1468b7a25e8966dc2aec5baeaa2d7dd29eaeb";

    public static RestConfig DEFAULT_REST_CONFIG = new RestConfig();

//    public static Coupon DEFAULT_COUPON = new Coupon();
    /**
     * 注册激活发邮件的时候AES key
     * */
    public static final String EMAIL_SEND_REG_ACTIVE_AES_KEY = "fkn#7gz%gRe#SzYd";
    /**
     * 注册激活的时候AES key
     * */
    public static final String EMAIL_REG_ACTIVE_AES_KEY = "eMC#TNs@8gz$srKm";

	/**
	 * 密码修改发邮件的时候AES key
	 * */
	public static final String EMAIL_SEND_PWD_FORGET_AES_KEY = "WWo#SKP%Q8B!LWv3";
    /**
     * 密码修改的时候AES key
     * */
    public static final String EMAIL_PWD_FORGET_AES_KEY = "eBN#TNs@4df$srKm";

	/**
	 * REST注册API的时候AES key
	 * */
	public static final String REST_REG_API_AES_KEY = "t3S#a4N#SYc#u48a";

	/**
	 * REST注册API的时候AES IV
	 * */
	public static final String REST_REG_API_AES_IV = "NV4$FTR#y7Q#6cVb";

	/**
	 * REST注册API的时候AES IV
	 * */
	public static final String REST_CARD_API_AES_IV = "UN8$FMR#p3Q#6cVC";


	/**
	 * REST注册API的时候AES key
	 * */
	public static final String SAVE_CARD_API_AES_KEY = "t6S#a4N#HAc#u04a";

	/**
	 * 保存card的时候AES IV
	 * */
	public static final String SAVE_CARD_API_AES_IV = "AH4$FMR#y9Q#6cVb";

	public static final String EMAIL_FROM = "恩道电子书<<EMAIL>>";
	//public static String EMAIL_FROM = "<EMAIL>";

	public static BigDecimal CNY_RATE = new BigDecimal("6.6");

	/**
	 * 保存的首页缓存信息
	 * */
	public static ShopIndex shopIndex = new ShopIndex();

	/**
	 * 保存各类货币的首页缓存信息
	 * */
	public static Map<String, RestShopIndex> shopIndexSet = new HashMap<>();

    /**
     * 人民币兑港币
     * */
	public static BigDecimal CNY_HK_RATE = new BigDecimal("0.813800");

	public static NameAndValue CATEGORY_DEFAULT;

	/**
	 * 一些封禁的IP，为了应对一些非法访问的IP
	 * */
	public static Set<String> FORBIDDEN_IP = new HashSet<>();

    /**
     * CNY Byte 值
     * */
	public static final Byte CNY_BYTE_VALUE = Byte.valueOf("0");

    /**
     * USD Byte 值
     * */
    public static final Byte USD_BYTE_VALUE = Byte.valueOf("1");
    /**
     * HKD Byte 值
     * */
	public static final Byte HKD_BYTE_VALUE = Byte.valueOf("2");

	public static final Byte BYTE_VALUE_0 = Byte.valueOf("0");

	public static final Byte BYTE_VALUE_1 = Byte.valueOf("1");

	public static final Byte BYTE_VALUE_2 = Byte.valueOf("2");

	public static final Byte BYTE_VALUE_3 = Byte.valueOf("3");

	/**
	 * 默认zero
	 * */
//	public static final BigDecimal DEFAULT_ZERO = new BigDecimal("0");
	/**
	 * 100
	 * */
	public static final BigDecimal VALUE_100 = new BigDecimal("100");
	/**
	 * 0
	 * */
	public static final BigDecimal VALUE_0 = new BigDecimal("0");

	/**
	 * Web的图片宽度
	 * */
	public static final Integer IMG_WEB_WIDTH = 300;

	/**
	 * Web的图片高度
	 * */
	public static final Integer IMG_WEB_HEIGHT = 445;

	/**
	 * App的图片宽度
	 * */
	public static final Integer IMG_APP_WIDTH = 161;//145

	/**
	 * App的图片高度
	 * */
	public static final Integer IMG_APP_HEIGHT = 215;

	/**
	 * App的图片后缀
	 * */
	public static final String IMG_APP_THUMBNAIL = "_Small";

	/**
	 * 未使用
	 * */
    public static final int REDEEM_CODE_STATUS_DEFAULT = 0;
	/**
	 * 已使用
	 * */
	//public static final int REDEEM_CODE_STATUS_USE = 1;
	/**
	 * 特定书籍
	 * */
	//public static final int REDEEM_CODE_TYPE_SPECIAL = 0;
	/**
	 * 任意书籍
	 * */
	//public static final int REDEEM_CODE_TYPE_ANY = 1;
    /**
     * admin 管理员账号
     * */
    public static final Set<String> ROLE_ADMIN_SET = new HashSet<String>() {
        {
            add("<EMAIL>");
            if (Constant.IS_TEST){
	            add("<EMAIL>");
            }
            //add("b");
            //add("c");
        }
    };


	public static String SYS_UPDATE = "";

	/**
	 * 默认的Discount ID
	 * */
	public static final Long DEFAULT_DISCOUNT_ID = -1L;

	/**
	 *
	 * 首页的分类及书籍列表
	 *
	 * * */
	public static List<EnyanCategory> indexCategoryList = new ArrayList<>();

	/**
	 *
	 * 首页的编辑推荐书籍列表
	 *
	 * * */
	public static List<EnyanBook> indexRecommendedList = new ArrayList<>();

    /**
     * 分类列表
     * */
	public static List<NameAndValue> categoriesList;

	/**
	 * 分类管理列表（包含隐藏的）
	 * */
	public static List<NameAndValue> categoriesAllList;

    /**
     * publishers列表
     * */
    public static List<NameAndValue> publishersList;

	/**
	 * discount ValueAndName 列表
	 * */
	public static List<NameAndValue> discountsList;

	/**
	 * 存储book的信息
	 * book ValueAndName 列表
	 * */
	public static List<NameAndValue> booksList;

    /**
     * discount 对象 列表
     * */
    //public static List<EnyanDiscount> discountsInfoList;

	/**
	 * discount 对象 Map列表
	 * */
    public static Map<Long,EnyanDiscount> discountMap;

	/**
	 * 书籍搜索List
	 * */
	public static List<NameAndValue> bookSearchList ;

    /**
     * category搜索List
     * */
    public static List<NameAndValue> categorySearchList ;

	/**
	 * 供应商搜索List
	 * */
	public static List<NameAndValue> publishSearchList;

	/**
	 * 搜索List
	 * */
	public static List<NameAndValue> publicationSearchList;

	/**
	 * 折扣搜索List
	 * */
	public static List<NameAndValue> discountSearchList;

    /**
     * 配置搜索List
     * */
    public static List<NameAndValue> configSearchList;
	/**
	 * 图片搜索List
	 * */
	public static final List<NameAndValue> imgSearchList;

	/**
	 * 兑换码搜索List
	 * */
	public static final List<NameAndValue> redeemSearchList;

	/**
	 * 属灵书籍搜索List
	 * */
	public static final List<NameAndValue> spiritSearchList;

	/**
	 * 退款搜索List
	 * */
	public static final List<NameAndValue> refundSearchList;

	/**
	 * 优惠码搜索List
	 * */
	public static final List<NameAndValue> couponSearchList;

	private static String[] bookSearchStrs = {
	        //"-请选择-",
			"书名"
			,"作者"
			,"出版商名称"
            ,"分类"
    };
    private static String[] categorySearchStrs = {
            //"-请选择-",
            "分类名称"
    };
    private static String[] publisherSearchStrs = {
            //"-请选择-",
            "出版商名称"
    };
	private static String[] publicationSearchStrs = {
			//"-请选择-",
			"加密书籍名称"
	};
	private static String[] discountSearchStrs = {
			//"-请选择-",
			"折扣名称"
	};
	private static String[] configSearchStrs = {
			//"-请选择-",
			"配置名称"
	};
	private static String[] imgSearchStrs = {
			//"-请选择-",
			"文件名称"
	};
	private static String[] redeemSearchStrs = {
			"-请选择-",
			"兑换码批次",
			"兑换码"
	};
	private static String[] spiritSearchStrs = {
			//"-请选择-",
			"书名"
			,"作者"
	};
	private static String[] refundSearchStrs = {
			//"-请选择-",
			"书名"
			//,"订单号"
	};
	private static String[] couponSearchStrs = {
			//"-请选择-",
			"优惠码名"
			//,"订单号"
	};

	static {
		bookSearchList = new ArrayList<>();
        categorySearchList = new ArrayList<>();
        publishSearchList = new ArrayList<>();
		discountSearchList = new ArrayList<>();
        configSearchList = new ArrayList<>();
		imgSearchList = new ArrayList<>();
		publicationSearchList = new ArrayList<>();
		redeemSearchList = new ArrayList<>();
		spiritSearchList = new ArrayList<>();
		refundSearchList = new ArrayList<>();
		couponSearchList = new ArrayList<>();

		for (int i = 0; i < bookSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
            valueAndName.setValue(String.valueOf(i));
            valueAndName.setName(bookSearchStrs[i]);
			bookSearchList.add(valueAndName);
		}
        for (int i = 0; i < categorySearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
            valueAndName.setValue(String.valueOf(i));
            valueAndName.setName(categorySearchStrs[i]);
            categorySearchList.add(valueAndName);
        }
        for (int i = 0; i < publisherSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
            valueAndName.setValue(String.valueOf(i));
            valueAndName.setName(publisherSearchStrs[i]);
            publishSearchList.add(valueAndName);
        }
		for (int i = 0; i < publicationSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
			valueAndName.setValue(String.valueOf(i));
			valueAndName.setName(publicationSearchStrs[i]);
			publicationSearchList.add(valueAndName);
		}
		for (int i = 0; i < discountSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
			valueAndName.setValue(String.valueOf(i));
			valueAndName.setName(discountSearchStrs[i]);
			discountSearchList.add(valueAndName);
		}
        for (int i = 0; i < configSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
            valueAndName.setValue(String.valueOf(i));
            valueAndName.setName(configSearchStrs[i]);
            configSearchList.add(valueAndName);
        }
		for (int i = 0; i < imgSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
			valueAndName.setValue(String.valueOf(i));
			valueAndName.setName(imgSearchStrs[i]);
			imgSearchList.add(valueAndName);
		}
		for (int i = 0; i < redeemSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
			valueAndName.setValue(String.valueOf(i));
			valueAndName.setName(redeemSearchStrs[i]);
			redeemSearchList.add(valueAndName);
		}
		for (int i = 0; i < spiritSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
			valueAndName.setValue(String.valueOf(i));
			valueAndName.setName(spiritSearchStrs[i]);
			spiritSearchList.add(valueAndName);
		}
		for (int i = 0; i < refundSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
			valueAndName.setValue(String.valueOf(i));
			valueAndName.setName(refundSearchStrs[i]);
			refundSearchList.add(valueAndName);
		}
		for (int i = 0; i < couponSearchStrs.length; i++) {
			NameAndValue valueAndName = new NameAndValue();
			valueAndName.setValue(String.valueOf(i));
			valueAndName.setName(couponSearchStrs[i]);
			couponSearchList.add(valueAndName);
		}
		NameAndValue categoryDefault = new NameAndValue();
		categoryDefault.setOther("全部書目");
		categoryDefault.setName("全部书目");
		categoryDefault.setThird("All titles");
		Constant.CATEGORY_DEFAULT = categoryDefault;

		AdModel adModel = new AdModel();
		Constant.DEFAULT_REST_CONFIG.getAdList().add(adModel);
    }
    public static  void main(String[] args){
	    Byte b = Byte.valueOf("-1");
        System.out.println(b);
    }
}
