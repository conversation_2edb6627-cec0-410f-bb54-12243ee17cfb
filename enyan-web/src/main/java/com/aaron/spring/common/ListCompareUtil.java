package com.aaron.spring.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对比两个list的数据
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/9/1
 * @Modified By:
 */
public class ListCompareUtil {
	/**
	 * <p>获取区分后的map
	 * value=-1  被删除的数据
	 * value=0   保持原状的数据
	 * value=1   新增的数据
	 * </p>
	 * @param oldList
	 * @param newList
	 * @return java.util.Map<java.lang.String,java.lang.Integer>
	 * @since : 2021/9/1
	 **/
	public static Map<String,Integer> getDiffrent(List<String> oldList, List<String> newList){
		Map<String,Integer> map = new HashMap<>(oldList.size()+newList.size());
		List<String> diff = new ArrayList<>();
		oldList.forEach(v->{//赋值 -1
			map.put(v, -1);
		});
		newList.forEach(v->{//赋值 1
			Integer cc = map.get(v);
			if (null != cc){
				map.put(v, ++cc);//都有则会赋值0
			}else {
				map.put(v, 1);
			}
		});
		/*
		List<String> maxList = oldList;
		List<String> minList = newList;
		if(newList.size()>oldList.size()){
			maxList = newList;
			minList = oldList;
		}

		for (String string : maxList){
			map.put(string, 1);
		}

		for (String string : minList){
			Integer cc = map.get(string);
			if(cc!=null){
				map.put(string, ++cc);
				continue;
			}
			map.put(string, 1);
		}*/

		for(Map.Entry<String, Integer> entry:map.entrySet()){
			if(entry.getValue()==1)
			{
				diff.add(entry.getKey());
			}
		}
		return map;
	}

	/**
	 * <p>获取相应标识的对比数据
	 * type=-1  被删除的数据
	 * type=0   保持原状的数据
	 * type=1   新增的数据
	 * </p>
	 * @param oldList
	 * @param newList
	 * @param type
	 * @return java.util.List<java.lang.String>
	 * @since : 2021/9/1
	 **/
	public static List<String> getListDiff(List<String> oldList, List<String> newList, int type){
		Map<String,Integer> map = getDiffrent(oldList, newList);
		List<String> diffList = new ArrayList<>();
		map.entrySet().forEach(entry->{
			if (entry.getValue() == type){
				diffList.add(entry.getKey());
			}
		});
		return diffList;
	}

	/**
	 * <p>获取被删除的数据</p>
	 * @param oldList
	 * @param newList
	 * @return java.util.List<java.lang.String>
	 * @since : 2021/9/1
	 **/
	public static List<String> getListDeleted(List<String> oldList, List<String> newList){
		return getListDiff(oldList, newList, -1);
	}

	/**
	 * <p>获取被添加的数据</p>
	 * @param oldList
	 * @param newList
	 * @return java.util.List<java.lang.String>
	 * @since : 2021/9/1
	 **/
	public static List<String> getListAdded(List<String> oldList, List<String> newList){
		return getListDiff(oldList, newList, 1);
	}

	/**
	 * <p>获取重复的数据</p>
	 * @param oldList
	 * @param newList
	 * @return java.util.List<java.lang.String>
	 * @since : 2021/9/1
	 **/
	public static List<String> getListSame(List<String> oldList, List<String> newList){
		return getListDiff(oldList, newList, 0);
	}

	/**
	 * <p>获取相应标识的对比数据
	 * key=-1  被删除的数据
	 * key=0   保持原状的数据
	 * key=1   新增的数据
	 * @return java.util.Map<java.lang.String,java.util.List<java.lang.String>>
	 * @since : 2021/9/1
	 **/
	public static Map<String,List<String>> getDiffListAll(List<String> oldList, List<String> newList){
		Map<String,List<String>> returnMap = new HashMap<>();
		Map<String,Integer> map = getDiffrent(oldList, newList);
		List<String> sameList = new ArrayList<>();
		List<String> addList = new ArrayList<>();
		List<String> delList = new ArrayList<>();
		map.entrySet().forEach(entry->{
			switch (entry.getValue()){
				case -1:
					delList.add(entry.getKey());
					break;
				case 0:
					sameList.add(entry.getKey());
					break;
				case 1:
					addList.add(entry.getKey());
					break;
			}
		});
		returnMap.put("-1", delList);
		returnMap.put("0", sameList);
		returnMap.put("1", addList);
		return returnMap;
	}
}
