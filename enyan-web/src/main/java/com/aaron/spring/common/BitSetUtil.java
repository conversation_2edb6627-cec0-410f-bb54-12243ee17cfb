package com.aaron.spring.common;

import java.sql.Blob;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.BitSet;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  11/19/20
 * @Modified By:
 */
public class BitSetUtil {
    static final Base64.Decoder decoder = Base64.getDecoder();
    static final Base64.Encoder encoder = Base64.getEncoder();

    public static String encode(BitSet bitSet){
        return encoder.encodeToString(bitSet.toByteArray());
    }

    public static String encode(byte[] bytes){
        return encoder.encodeToString(bytes);
    }

    public static BitSet decode(String encodedText){
        return BitSet.valueOf(decoder.decode(encodedText));
    }

    public static BitSet blobToBitSet(Blob blob) {
        byte[] bytes = new byte[0];
        try {
            bytes = blob.getBytes(1, (int)blob.length());
        } catch (SQLException e) {
            e.printStackTrace();
        }
        BitSet bitSet = fromByteArray(bytes);

        return bitSet;
    }
    /**
     * <p>从SQL获取的bytes 需要使用这个方法进行转回，不能使用fromByteArrayJava</p>
     * @param bytes
     * @return: java.util.BitSet
     * @since : 1/12/21
     */
    public static BitSet fromByteArraySQL(byte[] bytes) {
        /*
        BitSet bits = new BitSet();
        for (int i = 0; i < bytes.length * 8; i++) {
            if ((bytes[bytes.length - i / 8 - 1] & (1 << (i % 8))) > 0) {
                bits.set(i);
            }
        }
        return bits;*/
        return BitSet.valueOf(bytes);
    }
    /**
     * <p>直接从Bitset.toByteArray() 需要使用这个方法进行转回，不能使用fromByteArraySQL</p>
     * @param bytes
     * @return: java.util.BitSet
     * @since : 1/12/21
     */
    public static BitSet fromByteArrayJava(byte[] bytes) {
        return BitSet.valueOf(bytes);
    }

    /**
     * <p>还是需要使用官方的方法进行处理</p>
     * @param bytes
     * @return: java.util.BitSet
     * @since : 1/12/21
     */
    public static BitSet fromByteArray(byte[] bytes) {
        return BitSet.valueOf(bytes);
    }

    /**
     * <p>返回string List</p>
     * @param bitSet
     * @return: java.util.List<java.lang.String>
     * @since : 11/19/20
     */
    public static List<String> toStringList(BitSet bitSet){
        if (null == bitSet){
            return null;
        }
        List<String> stringList = new ArrayList<>();
        for (int i = 0; i < bitSet.length(); i++) {
            if (bitSet.get(i)){
                stringList.add(String.valueOf(i));
            }
        }
        return stringList;
    }

    /**
     * <p>返回Integer List</p>
     * @param bitSet
     * @return: java.util.List<java.lang.Integer>
     * @since : 11/19/20
     */
    public static List<Integer> toIntList(BitSet bitSet){
        if (null == bitSet){
            return null;
        }
        List<Integer> intList = new ArrayList<>();
        for (int i = 0; i < bitSet.length(); i++) {
            if (bitSet.get(i)){
                intList.add(i);
            }
        }
        return intList;
    }

    /**
     * <p>使用join 进行区隔</p>
     * @param bitSet
     * @param join
     * @return: java.lang.String
     * @since : 11/19/20
     */
    public static String toJoinString(BitSet bitSet, String join){
        List<String> stringList = toStringList(bitSet);
        if (null == stringList || stringList.isEmpty()){
            return null;
        }
        return String.join(join,stringList);
    }
}

