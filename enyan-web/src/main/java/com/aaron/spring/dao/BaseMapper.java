package com.aaron.spring.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface BaseMapper<T,TExample> {
    long countByExample(TExample example);

    int deleteByExample(TExample example);

    int deleteByPrimaryKey(Long id);

    int insert(T record);

    int insertSelective(T record);

    List<T> selectByExample(TExample example);

    T selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") T record, @Param("example") TExample example);

    int updateByExample(@Param("record") T record, @Param("example") TExample example);

    int updateByPrimaryKeySelective(T record);

    int updateByPrimaryKey(T record);
}
