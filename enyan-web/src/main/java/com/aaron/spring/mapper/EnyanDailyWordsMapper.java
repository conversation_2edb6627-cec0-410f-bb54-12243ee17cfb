package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanDailyWords;
import com.aaron.spring.model.EnyanDailyWordsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanDailyWordsMapper {
    long countByExample(EnyanDailyWordsExample example);

    int deleteByExample(EnyanDailyWordsExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(EnyanDailyWords record);

    int insertSelective(EnyanDailyWords record);

    List<EnyanDailyWords> selectByExample(EnyanDailyWordsExample example);

    EnyanDailyWords selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") EnyanDailyWords record, @Param("example") EnyanDailyWordsExample example);

    int updateByExample(@Param("record") EnyanDailyWords record, @Param("example") EnyanDailyWordsExample example);

    int updateByPrimaryKeySelective(EnyanDailyWords record);

    int updateByPrimaryKey(EnyanDailyWords record);
}