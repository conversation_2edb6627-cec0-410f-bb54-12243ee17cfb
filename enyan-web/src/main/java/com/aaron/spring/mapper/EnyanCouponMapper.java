package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanCoupon;
import com.aaron.spring.model.EnyanCouponExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanCouponMapper {
    long countByExample(EnyanCouponExample example);

    int deleteByExample(EnyanCouponExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(EnyanCoupon record);

    int insertSelective(EnyanCoupon record);

    List<EnyanCoupon> selectByExample(EnyanCouponExample example);

    EnyanCoupon selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") EnyanCoupon record, @Param("example") EnyanCouponExample example);

    int updateByExample(@Param("record") EnyanCoupon record, @Param("example") EnyanCouponExample example);

    int updateByPrimaryKeySelective(EnyanCoupon record);

    int updateByPrimaryKey(EnyanCoupon record);
}