package com.aaron.spring.mapper;

import com.aaron.spring.model.PodTopic;
import com.aaron.spring.model.PodTopicExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PodTopicMapper {
    long countByExample(PodTopicExample example);

    int deleteByExample(PodTopicExample example);

    int deleteByPrimaryKey(Long topicId);

    int insert(PodTopic record);

    int insertSelective(PodTopic record);

    List<PodTopic> selectByExample(PodTopicExample example);

    PodTopic selectByPrimaryKey(Long topicId);

    int updateByExampleSelective(@Param("record") PodTopic record, @Param("example") PodTopicExample example);

    int updateByExample(@Param("record") PodTopic record, @Param("example") PodTopicExample example);

    int updateByPrimaryKeySelective(PodTopic record);

    int updateByPrimaryKey(PodTopic record);
}
