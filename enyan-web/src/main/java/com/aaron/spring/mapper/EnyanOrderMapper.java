package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanOrder;
import com.aaron.spring.model.EnyanOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanOrderMapper {
    long countByExample(EnyanOrderExample example);

    int deleteByExample(EnyanOrderExample example);

    int deleteByPrimaryKey(Long orderId);

    int insert(EnyanOrder record);

    int insertSelective(EnyanOrder record);

    List<EnyanOrder> selectByExampleWithBLOBs(EnyanOrderExample example);

    List<EnyanOrder> selectByExample(EnyanOrderExample example);

    EnyanOrder selectByPrimaryKey(Long orderId);

    int updateByExampleSelective(@Param("record") EnyanOrder record, @Param("example") EnyanOrderExample example);

    int updateByExampleWithBLOBs(@Param("record") EnyanOrder record, @Param("example") EnyanOrderExample example);

    int updateByExample(@Param("record") EnyanOrder record, @Param("example") EnyanOrderExample example);

    int updateByPrimaryKeySelective(EnyanOrder record);

    int updateByPrimaryKeyWithBLOBs(EnyanOrder record);

    int updateByPrimaryKey(EnyanOrder record);
}