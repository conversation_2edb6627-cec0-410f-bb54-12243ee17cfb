package com.aaron.spring.mapper;

import com.aaron.spring.model.EmailHistory;
import com.aaron.spring.model.EmailHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EmailHistoryMapper {
    long countByExample(EmailHistoryExample example);

    int deleteByExample(EmailHistoryExample example);

    int deleteByPrimaryKey(Long emailId);

    int insert(EmailHistory record);

    int insertSelective(EmailHistory record);

    List<EmailHistory> selectByExample(EmailHistoryExample example);

    EmailHistory selectByPrimaryKey(Long emailId);

    int updateByExampleSelective(@Param("record") EmailHistory record, @Param("example") EmailHistoryExample example);

    int updateByExample(@Param("record") EmailHistory record, @Param("example") EmailHistoryExample example);

    int updateByPrimaryKeySelective(EmailHistory record);

    int updateByPrimaryKey(EmailHistory record);
}