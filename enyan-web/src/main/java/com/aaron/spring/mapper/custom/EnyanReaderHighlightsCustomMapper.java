package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanReaderHighlights;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanReaderHighlightsCustomMapper {

    int add(EnyanReaderHighlights record);

    @Update({"update enyan_reader_highlights set resource_href=#{hrefNew},update_time = #{now} where resource_href = #{hrefOld}  and book_id = #{bookId}"})
    int updateHighlightHref(@Param("bookId")long bookId, @Param("hrefOld")String hrefOld, @Param("hrefNew")String hrefNew, @Param("now")long now);

    @Select("select COUNT(1) from enyan_reader_highlights where id = #{id}")
    long countOfHighlightById(@Param("id")String id);
}