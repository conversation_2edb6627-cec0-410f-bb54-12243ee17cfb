package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.StuEnroll;
import com.aaron.spring.model.StuInfo;
import com.aaron.spring.model.StuInfoExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StuInfoCustomMapper {
    @Select("select * from stu_info where terms = #{terms} order by stu_id asc")
    List<StuInfo> findRecordByTerms(@Param("terms") Integer terms);

    @Select("select * from stu_info where terms = #{terms} and email = #{email} limit 1")
    List<StuInfo> getRecordByEmail(@Param("terms") Integer terms, @Param("email") String email);

    @Select("select * from stu_info where terms = #{terms} and enroll_code = #{enrollCode} limit 1")
    List<StuInfo> getRecordByEnrollCode(@Param("terms") Integer terms, @Param("enrollCode") String enrollCode);
}