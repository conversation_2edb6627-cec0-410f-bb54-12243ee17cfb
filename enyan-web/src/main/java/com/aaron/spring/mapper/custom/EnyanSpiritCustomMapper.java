package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanSpirit;
import com.aaron.spring.model.EnyanSpiritExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanSpiritCustomMapper extends BaseCustomMapper{
    @Select("select * from enyan_spirit")
    List<EnyanSpirit> findAllSpirits();

    @Select("select * from enyan_spirit where shelf_status=1 order by recommended_order desc")
    List<EnyanSpirit> findAllSpiritsOnShelf();
}