package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookBuy;
import com.aaron.spring.model.EnyanBookBuyExample;
import com.aaron.spring.model.EnyanOrderDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanBookBuyCustomMapper extends BaseCustomMapper<EnyanBookBuy>{
    long countByExample(EnyanBookBuyExample example);

    int deleteByExample(EnyanBookBuyExample example);

    int deleteByPrimaryKey(Long bookBuyId);

    int insert(EnyanBookBuy record);

    int insertSelective(EnyanBookBuy record);

    List<EnyanBookBuy> selectByExample(EnyanBookBuyExample example);

    EnyanBookBuy selectByPrimaryKey(Long bookBuyId);

    int updateByExampleSelective(@Param("record") EnyanBookBuy record, @Param("example") EnyanBookBuyExample example);

    int updateByExample(@Param("record") EnyanBookBuy record, @Param("example") EnyanBookBuyExample example);

    int updateByPrimaryKeySelective(EnyanBookBuy record);

    int updateByPrimaryKey(EnyanBookBuy record);

    List<EnyanBookBuy> searchRecordsByUserAndBookAndAuthorList(@Param("record") EnyanBookBuy record);

    long countRecordsByUserAndBookAndAuthorList(@Param("record") EnyanBookBuy record);

    /**
     *
     * 因为更换了DRM，更新购书信息的DRM匹配
     * @param newPublicationId
     * @param bookId
     * @Date: 2020-06-22
     */
    /*
    update enyan_book_buy set drmInfo=JSON_SET(drmInfo,'$.lInfo.pubId', #{newPublicationId},'$.lInfo.lId','') where book_id = #{bookId}
    * */
    @Update({"update enyan_book_buy set drmInfo=JSON_SET(drmInfo,'$.lInfo.pubId', #{newPublicationId},'$.lInfo.lId','') where book_id = #{bookId}"})
    int updateOrderDetailPublicationIdByBookId(@Param("newPublicationId") Integer newPublicationId, @Param("bookId") Long bookId);

    @Select("select book_id,book_title from enyan_book_buy where is_deleted = 0 and user_email = #{email}")
    List<EnyanBookBuy> findBookIDAndNameByEmail(@Param("email")String email);

    @Select("select book_id,book_title,read_info,group_name,read_time,group_name_time from enyan_book_buy where is_deleted = 0 and user_email = #{email}")
    List<EnyanBookBuy> findBookBaseInfoByEmail(@Param("email")String email);

    @Select("select book.book_id,book.book_title,book.book_cover_app,read_info,group_name,read_time from enyan_book book INNER JOIN enyan_book_buy buy on book.book_id = buy.book_id and buy.is_deleted = 0 and buy.user_email = #{email}")
    List<EnyanBookBuy> findBookBaseInfoAndPicByEmail(@Param("email")String email);

    @Select("select book_id,book_title,user_email from enyan_book_buy where is_deleted = 0 and book_id = #{bookId}")
    List<EnyanBookBuy> findBookIDAndNameAndEmailByBookID(@Param("bookId")Long bookId);

    @Select("select book_id,book_title from enyan_book_buy where is_deleted = 0 and user_email = #{email} and book_id = #{bookId}")
    List<EnyanBookBuy> getBookIDAndNameByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Select("select * from enyan_book_buy where is_deleted = 0 and user_email = #{email} and book_id = #{bookId}")
    List<EnyanBookBuy> findBookBuyListByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Select("select * from enyan_book_buy where is_deleted = 0 and order_num = #{orderNum} and book_id = #{bookId}")
    List<EnyanBookBuy> findBookBuyListByOrderNumAndBookId(@Param("orderNum")String orderNum, @Param("bookId")Long bookId);

    @Select("select COUNT(1) from enyan_book_buy where is_deleted = 0 and user_email = #{email} and book_id = #{bookId}")
    long countOfBookByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    /**
     * <p>昨天的销量(salesVolume)</p>
     * @param
     * @return long
     * @since : 2021/9/26
     **/
    @Select("select count(1) from enyan_book_buy where is_deleted = 0 and TO_DAYS(NOW())-TO_DAYS(purchased_at) = 1")
    long countOfBooksYesterday();

    /**
     * <p>Day的销量(salesVolume)</p>
     * @param
     * @return long
     * @since : 2021/9/26
     **/
    @Select("select count(1) from enyan_book_buy where is_deleted = 0 and DATE_FORMAT(purchased_at,'%Y%m%d') = #{yyyyMMdd}")
    long countOfBooksByDay(@Param("yyyyMMdd")String yyyyMMdd);

    @Update({"update enyan_book_buy set read_info=#{readInfo},read_time=#{readTime} where book_id = #{bookId} and user_email = #{email}"})
    int updateReadInfoByEmailAndBookId(@Param("readInfo") String readInfo, @Param("readTime") Long readTime, @Param("email")String email, @Param("bookId") Long bookId);

    @Update({"update enyan_book_buy set group_name=#{record.groupName},group_name_time=#{record.groupNameTime} where book_id = #{record.bookId} and user_email = #{record.userEmail}"})
    int updateGroupNameByEmailAndBookId(@Param("record") EnyanBookBuy record);

    @Update({"<script>update enyan_book_buy set group_name=#{record.groupName},group_name_time=#{record.groupNameTime} " +
                     "where user_email = #{record.userEmail} " +
                     "and book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    int updateGroupNameByEmailAndBookIds(@Param("record") EnyanBookBuy record, @Param("ids")Long[] ids);

    @Update({"update enyan_book_buy set read_info=#{record.readInfo},group_name=#{record.groupName},read_time=#{record.readTime},group_name_time=#{record.groupNameTime} where book_id = #{record.bookId} and user_email = #{record.userEmail}"})
    int updateReadInfoAndGroupNameByEmailAndBookId(@Param("record") EnyanBookBuy record);

    @Update({"update enyan_book_buy set user_email = #{revokedEmail} where user_email = #{email}"})
    int revokeUser(@Param("email")String email, @Param("revokedEmail")String revokedEmail);

    @Select({"<script>select book_id,book_title from enyan_book_buy where user_email = #{email} " +
                     "and book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    List<EnyanBook> findBookIDAndNameByEmailAndIds(@Param("email")String email, @Param("ids")Long[] ids);
}