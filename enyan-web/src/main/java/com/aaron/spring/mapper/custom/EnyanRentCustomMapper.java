package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanRent;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface EnyanRentCustomMapper {

    EnyanRent queryRecordBaseInfoByPrimaryKey(Long rentId);
    /**
     * <p>订单已经付款，设置其过期时间</p>
     * @return int
     * @since : 2022/11/4
     **/
    @Update({"update enyan_rent set is_valid = 1, is_paid = 1, rent_status = 1, expired_at = #{record.newExpiredAt}, total_fee = #{record.totalFee}, total_months = #{record.totalMonths} where order_num = #{record.orderNum}"})
    int updateRecordToPaid(@Param("record") EnyanRent rent);

    /**
     * <p>订单已经付款，设置其过期时间，但还没有到当前时间</p>
     * @return int
     * @since : 2023/8/14
     **/
    @Update({"update enyan_rent set expired_at = #{record.newExpiredAt}, total_fee = #{record.totalFee}, total_months = #{record.totalMonths} where order_num = #{record.orderNum}"})
    int updateRecordToPaidOnly(@Param("record") EnyanRent rent);

    /**
     * <p>到期宽限时间之后的数据自动过期(非手动退订)</p>
     * @param graceDays
     * @return int
     * @since : 2022/11/10
     **/
    @Update({"update enyan_rent set is_valid = 0, rent_status = -2, leave_at = NOW() where is_valid = 1 and rent_status <> -1 and TO_DAYS(NOW())-TO_DAYS(expired_at)>= #{graceDays}"})
    int updateRentToExpired(@Param("graceDays")Integer graceDays);


    //@Update({"update enyan_rent set is_valid = -1 where is_valid = 0 and rent_status <> -1 and TO_DAYS(NOW())-TO_DAYS(expired_at)>= #{graceDays}"})
    //int updateOrderExpired();
    /**
     * <p>到期宽限时间之后的数据自动过期(手动退订)</p>
     * @param graceDays
     * @return int
     * @since : 2022/11/10
     **/
    @Update({"update enyan_rent set is_valid = 0 where is_valid = 1 and rent_status = -1 and TO_DAYS(NOW())-TO_DAYS(expired_at)>= #{graceDays}"})
    int updateRentManualToExpired(@Param("graceDays")Integer graceDays);

    /**
     * <p>已经订阅满36个月，昨天到期了，直接自动到期</p>
     * @param
     * @return int
     * @since : 2023/1/11
     **/
    @Update({"update enyan_rent set is_valid = 0, rent_status = -2, leave_at = NOW() where is_valid = 1 and total_months = 36 and TO_DAYS(NOW())-TO_DAYS(expired_at) = 1"})
    int updateRent36MonthsYesterdayToExpired();

    /**
     * <p>仅用于初始化数据，将一些时间重置为空</p>
     * @param
     * @return int
     * @since : 2022/12/6
     **/
    @Update({"update enyan_rent set expired_at = null, leave_at = null, leave_buy = 0, leave_buy_at = null"})
    int resetDateToNull();

    /**
     * <p>处理beforeDays前正常订阅的提醒</p>
     * @param beforeDays
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2022/11/10
     **/
    @Select("select * from enyan_rent where is_valid = 1 and TO_DAYS(expired_at)-TO_DAYS(NOW()) = #{beforeDays}")
    List<EnyanRent> findRecordWillExpired(@Param("beforeDays") Integer beforeDays);

    /**
     * <p>处理beforeDays前正常订阅的自动付费提醒</p>
     * @param beforeDays
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2023/1/11
     **/
    @Select("select * from enyan_rent where is_auto = 1 and is_valid = 1 and rent_status = 1 and TO_DAYS(expired_at)-TO_DAYS(NOW()) = #{beforeDays}")
    List<EnyanRent> findRecordAutoWillExpired(@Param("beforeDays") Integer beforeDays);

    /**
     * <p>今天结束的记录</p>
     * @param totalMonths
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2022/11/24
     **/
    @Select("select * from enyan_rent where total_months = #{totalMonths} and TO_DAYS(expired_at)-TO_DAYS(NOW()) = 0")
    List<EnyanRent> findRecordExpiredToday(@Param("totalMonths") Integer totalMonths);

    /**
     * <p>昨天结束的记录</p>
     * @param totalMonths
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2022/11/24
     **/
    @Select("select * from enyan_rent where total_months = #{totalMonths} and TO_DAYS(NOW())-TO_DAYS(expired_at) = 1")
    List<EnyanRent> findRecordExpiredYesterday(@Param("totalMonths") Integer totalMonths);

    /**
     * <p>昨天退订的记录</p>
     * @param totalMonths
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2022/11/24
     **/
    @Select("select * from enyan_rent where total_months = #{totalMonths} and TO_DAYS(NOW())-TO_DAYS(leave_at) = 1")
    List<EnyanRent> findRecordLeavedYesterday(@Param("totalMonths") Integer totalMonths);

    /**
     * <p>处理正常阅读状态的结束日期后afterDays后的数据</p>
     * @param afterDays
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2022/11/10
     **/
    @Select("select * from enyan_rent where is_valid = 1 and TO_DAYS(NOW())-TO_DAYS(expired_at) = #{afterDays}")
    List<EnyanRent> findRecordHasExpired(@Param("afterDays") Integer afterDays);

    /**
     * <p>处理现在正在阅读状态，但已经在 大于等于 afterDays 之后的所有数据</p>
     * @param afterDays
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2023/1/11
     **/
    @Select("select * from enyan_rent where is_valid = 1 and TO_DAYS(NOW())-TO_DAYS(expired_at) >= #{afterDays}")
    List<EnyanRent> findRecordHasExpiredGEDaysAndIsValid(@Param("afterDays") Integer afterDays);

    /**
     * <p>处理退订后afterDays后的数据</p>
     * @param afterDays
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2022/11/10
     **/
    @Select("select * from enyan_rent where TO_DAYS(NOW())-TO_DAYS(leave_at) = #{afterDays}")
    List<EnyanRent> findRecordHasLeaved(@Param("afterDays") Integer afterDays);

    /**
     * <p>所在时间结束的数据</p>
     * @param dataAt
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2022/11/25
     **/
    @Select("select * from enyan_rent where date_format(expired_at,'%Y-%m-%d') = #{dataAt}")
    List<EnyanRent> findRecordExpiredInDate(@Param("dataAt") String dataAt);

    /**
     * <p>所在时间结束的数据</p>
     * @param dataAt
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2022/11/25
     **/
    @Select("select * from enyan_rent where date_format(leave_at,'%Y-%m-%d') = #{dataAt}")
    List<EnyanRent> findRecordLeaveInDate(@Param("dataAt") String dataAt);


    @Update({"<script>update license set updated = #{now}, rights_end = #{newExpiredTime} " +
                     "where id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    int updateDRMLicenseToNewExpiredTime(@Param("now")Date now, @Param("newExpiredTime")Date newExpiredTime, @Param("ids")String[] ids);

    @Update({"<script>update license_status set license_updated = #{now}, status_updated = #{now}, potential_rights_end = #{newExpiredTime} " +
                     "where license_ref in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    int updateDRMLicenseStatusToNewExpiredTime(@Param("now")Date now, @Param("newExpiredTime")Date newExpiredTime, @Param("ids")String[] ids);

    /**
     * <p>历史订阅量（不考虑是否退订）</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2023/3/8
     **/
    @Select("select count(1) as 'valueInt',rent_type,rent_lang from enyan_rent where is_paid =1 group by rent_type,rent_lang")
    List<EnyanRent> findSumSubscription();

    /**
     * <p>新增订阅量（不考虑是否退订）</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2023/3/8
     **/
    @Select("select count(1) as 'valueInt',rent_type,rent_lang from enyan_rent where TO_DAYS(NOW())-TO_DAYS(begin_at) = 1 and is_valid =1 group by rent_type,rent_lang")
    List<EnyanRent> findNewSubscriptionYesterday();

    /**
     * <p>昨天退订</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2023/3/8
     **/
    @Select("select count(1) as 'valueInt',rent_type,rent_lang from enyan_rent where TO_DAYS(NOW())-TO_DAYS(leave_at) = 1  group by rent_type,rent_lang")
    List<EnyanRent> findSumLeaveYesterday();

    /**
     * <p>退订后的购买书籍的数量</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2023/3/8
     **/
    @Select("select count(1) as 'valueInt',rent_type,rent_lang from enyan_rent where TO_DAYS(NOW())-TO_DAYS(leave_buy_at) = 1  group by rent_type,rent_lang")
    List<EnyanRent> findSumLeaveBuyYesterday();

    /**
     * <p>目前可以阅读订阅书籍的用户数量（包括“正常订阅中”和“已退订但未过期”）</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2023/3/8
     **/
    @Select("select count(1) as 'valueInt',rent_type,rent_lang from enyan_rent where is_valid =1 group by rent_type,rent_lang")
    List<EnyanRent> findSumValidYesterday();

    /**
     * <p>退订时的平均订阅时长</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanRent>
     * @since : 2023/3/8
     **/
    @Select("select round(IFNULL(avg(total_months),'0'),1) as 'valueString',rent_type,rent_lang from enyan_rent where TO_DAYS(NOW())-TO_DAYS(leave_at) = 1 group by rent_type,rent_lang")
    List<EnyanRent> findAvgSubscriptionTimeWhenLeaveYesterday();
}