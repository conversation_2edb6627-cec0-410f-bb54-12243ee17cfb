package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanPlanNote;
import com.aaron.spring.model.EnyanPlanNoteExample;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanPlanNoteCustomMapper {
    long countByExample(EnyanPlanNoteExample example);

    int deleteByExample(EnyanPlanNoteExample example);

    int deleteByPrimaryKey(Long id);

    int insert(EnyanPlanNote record);

    int insertSelective(EnyanPlanNote record);

    List<EnyanPlanNote> selectByExample(EnyanPlanNoteExample example);

    EnyanPlanNote selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") EnyanPlanNote record, @Param("example") EnyanPlanNoteExample example);

    int updateByExample(@Param("record") EnyanPlanNote record, @Param("example") EnyanPlanNoteExample example);

    int updateByPrimaryKeySelective(EnyanPlanNote record);

    int updateByPrimaryKey(EnyanPlanNote record);

    @Update({"update enyan_plan_note set note = #{record.note}, update_time = #{record.updateTime}, is_deleted=#{record.isDeleted}" +
            " where user_email = #{record.userEmail} and book_id = #{record.bookId} and day = #{record.day}"})
    int updateSyncPlanNote(@Param("record") EnyanPlanNote record);

    //SELECT COUNT(1) FROM enyan_book WHERE shelf_status = 1 AND (book_title like #{searchText,jdbcType=VARCHAR} OR author like #{searchText,jdbcType=VARCHAR})
    @Select("select COUNT(1) from enyan_plan_note where user_email=#{record.userEmail} AND book_id=#{record.bookId} AND day=#{record.day}")
    long countByPlanNote(@Param("record")EnyanPlanNote record);

    @Delete({"delete from enyan_plan_note where user_email = #{email}"})
    int deleteAllByEmail(@Param("email")String email);
}