package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.EnyanRedeemCodeExample;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanRedeemCodeCustomMapper {

    List<EnyanRedeemCode> selectByExampleWithBLOBs(EnyanRedeemCodeExample example);

    @Delete({"delete from enyan_redeem_code where user_email = #{email}"})
    int deleteAllByEmail(@Param("email")String email);
}