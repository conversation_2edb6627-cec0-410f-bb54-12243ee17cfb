package com.aaron.spring.mapper;

import com.aaron.spring.model.DataStat;
import com.aaron.spring.model.DataStatExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DataStatMapper {
    long countByExample(DataStatExample example);

    int deleteByExample(DataStatExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(DataStat record);

    int insertSelective(DataStat record);

    List<DataStat> selectByExample(DataStatExample example);

    DataStat selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") DataStat record, @Param("example") DataStatExample example);

    int updateByExample(@Param("record") DataStat record, @Param("example") DataStatExample example);

    int updateByPrimaryKeySelective(DataStat record);

    int updateByPrimaryKey(DataStat record);
}