package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanBookCost;
import com.aaron.spring.model.EnyanBookCostExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanBookCostMapper {
    long countByExample(EnyanBookCostExample example);

    int deleteByExample(EnyanBookCostExample example);

    int deleteByPrimaryKey(Long bookCostId);

    int insert(EnyanBookCost record);

    int insertSelective(EnyanBookCost record);

    List<EnyanBookCost> selectByExample(EnyanBookCostExample example);

    EnyanBookCost selectByPrimaryKey(Long bookCostId);

    int updateByExampleSelective(@Param("record") EnyanBookCost record, @Param("example") EnyanBookCostExample example);

    int updateByExample(@Param("record") EnyanBookCost record, @Param("example") EnyanBookCostExample example);

    int updateByPrimaryKeySelective(EnyanBookCost record);

    int updateByPrimaryKey(EnyanBookCost record);
}