package com.aaron.spring.mapper.custom;


import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanWish;
import com.aaron.spring.model.EnyanWishExample;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 *
 *
 * @Date: Created in  2018/2/3
 * @Modified By:
 */
public interface EnyanWishCustomMapper {
    long searchCountByExample(@Param("record") EnyanWish record, @Param("example") EnyanWishExample example);
    List<EnyanBook> searchByExample(@Param("record") EnyanWish record, @Param("example") EnyanWishExample example);

    @Delete({"delete from enyan_wish where user_email = #{email} and book_id = #{bookId}"})
    int deleteByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    //wish_id
    @Select("select distinct user_id as book_id from enyan_wish")
    List<EnyanBook> findDistinctUserIdAsBookIdList();

    @Update({"update enyan_wish set user_email = #{email}" +
                     " where user_id = #{userId}"})
    int updateEmailById(@Param("email")String toEmail, @Param("userId")Long userId);

    @Delete({"<script>delete from enyan_wish  " +
                     "where user_email = #{email} " +
                     "and book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    int deleteWishes(@Param("email")String email, @Param("ids")Long[] ids);

    @Select("select COUNT(1) from enyan_wish where user_email = #{email}")
    long countOfWishByEmail(@Param("email")String email);

    @Select("select COUNT(1) from enyan_wish where user_email = #{email} and book_id = #{bookId}")
    long countOfWishByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Insert("insert into enyan_wish (wish_id, user_email, book_id, wished_at) values (#{record.wishId}, #{record.userEmail}, #{record.bookId}, #{record.wishedAt})")
    int addWish(@Param("record")EnyanWish record);

    @Update({"update enyan_wish set user_email = #{revokedEmail} where user_email = #{email}"})
    int revokeUser(@Param("email")String email, @Param("revokedEmail")String revokedEmail);
}
