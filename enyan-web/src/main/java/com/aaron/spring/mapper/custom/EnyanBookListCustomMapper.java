package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBookList;
import com.aaron.spring.model.EnyanBookSet;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanBookListCustomMapper {
	@Update({"update enyan_book set_name = #{record.setName} where set_id = #{record.setId}"})
	int updateBookListNameBySetId(@Param("record") EnyanBookSet record);

	@Select("select book_id,book_title,publisher_id,publisher_name,special_offer from enyan_book where special_offer = 1")
	List<EnyanBookSet> findBookBasicInfoSpecialOffer();


	@Select("select * from enyan_book_list where is_index = #{isIndex} order by show_order desc")
	List<EnyanBookList> findBookListByIndex(@Param("isIndex") Integer isIndex);
}