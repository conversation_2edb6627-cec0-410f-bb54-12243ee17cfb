package com.aaron.spring.mapper.custom;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2020-07-22
 * @Modified By:
 */
public interface BaseCustomMapper<T> {
    @Select("${sqlStr}")
    List<LinkedHashMap<String,Object>> getItems(@Param(value = "sqlStr") String sqlStr);

    /**
     * <p>在mybatis里配置驼峰自动匹配之后，可以直接获取List</p>
     * @param sqlStr
     * @return: java.util.List<T>
     * @since : 2020-07-22
     */
    @Select("${sqlStr}")
    List<T> findRecordList(@Param(value = "sqlStr") String sqlStr);

    @Update("${sqlStr}")
    int updateRecord(@Param(value = "sqlStr") String sqlStr);
}
