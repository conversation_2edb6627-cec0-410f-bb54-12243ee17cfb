package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanComment;
import com.aaron.spring.model.EnyanCommentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanCommentMapper {
    long countByExample(EnyanCommentExample example);

    int deleteByExample(EnyanCommentExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(EnyanComment record);

    int insertSelective(EnyanComment record);

    List<EnyanComment> selectByExample(EnyanCommentExample example);

    EnyanComment selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") EnyanComment record, @Param("example") EnyanCommentExample example);

    int updateByExample(@Param("record") EnyanComment record, @Param("example") EnyanCommentExample example);

    int updateByPrimaryKeySelective(EnyanComment record);

    int updateByPrimaryKey(EnyanComment record);
}