package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanImg;
import com.aaron.spring.model.EnyanImgExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanImgMapper {
    long countByExample(EnyanImgExample example);

    int deleteByExample(EnyanImgExample example);

    int deleteByPrimaryKey(Long imgId);

    int insert(EnyanImg record);

    int insertSelective(EnyanImg record);

    List<EnyanImg> selectByExample(EnyanImgExample example);

    EnyanImg selectByPrimaryKey(Long imgId);

    int updateByExampleSelective(@Param("record") EnyanImg record, @Param("example") EnyanImgExample example);

    int updateByExample(@Param("record") EnyanImg record, @Param("example") EnyanImgExample example);

    int updateByPrimaryKeySelective(EnyanImg record);

    int updateByPrimaryKey(EnyanImg record);
}