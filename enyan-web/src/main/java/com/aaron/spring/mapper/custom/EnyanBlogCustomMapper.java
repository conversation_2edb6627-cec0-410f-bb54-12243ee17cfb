package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBlog;
import com.aaron.spring.model.EnyanBlogExample;
import com.aaron.spring.model.EnyanPlan;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanBlogCustomMapper {
    @Update({"update enyan_blog set like_count = like_count + 1 where blog_id = #{blogId}"})
    int updateBlogLikeCountById(@Param("blogId") Long blogId);

    @Update({"update enyan_blog set read_count = read_count + 1 where blog_id = #{blogId}"})
    int updateBlogReadCountById(@Param("blogId") Long blogId);

    @Update({"update enyan_blog set is_deleted = 1 where blog_id = #{blogId}"})
    int updateBlogToDeletedById(@Param("blogId") Long blogId);
}