package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanBookSet;
import com.aaron.spring.model.EnyanBookSetExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanBookSetMapper {
    long countByExample(EnyanBookSetExample example);

    int deleteByExample(EnyanBookSetExample example);

    int deleteByPrimaryKey(Long setId);

    int insert(EnyanBookSet record);

    int insertSelective(EnyanBookSet record);

    List<EnyanBookSet> selectByExample(EnyanBookSetExample example);

    EnyanBookSet selectByPrimaryKey(Long setId);

    int updateByExampleSelective(@Param("record") EnyanBookSet record, @Param("example") EnyanBookSetExample example);

    int updateByExample(@Param("record") EnyanBookSet record, @Param("example") EnyanBookSetExample example);

    int updateByPrimaryKeySelective(EnyanBookSet record);

    int updateByPrimaryKey(EnyanBookSet record);
}