package com.aaron.spring.mapper;

import com.aaron.spring.model.LicenseStatus;
import com.aaron.spring.model.LicenseStatusExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface LicenseStatusMapper {
    long countByExample(LicenseStatusExample example);

    int deleteByExample(LicenseStatusExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(LicenseStatus record);

    int insertSelective(LicenseStatus record);

    List<LicenseStatus> selectByExample(LicenseStatusExample example);

    LicenseStatus selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") LicenseStatus record, @Param("example") LicenseStatusExample example);

    int updateByExample(@Param("record") LicenseStatus record, @Param("example") LicenseStatusExample example);

    int updateByPrimaryKeySelective(LicenseStatus record);

    int updateByPrimaryKey(LicenseStatus record);
}