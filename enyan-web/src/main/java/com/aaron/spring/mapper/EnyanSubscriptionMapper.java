package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanSubscription;
import com.aaron.spring.model.EnyanSubscriptionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanSubscriptionMapper {
    long countByExample(EnyanSubscriptionExample example);

    int deleteByExample(EnyanSubscriptionExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(EnyanSubscription record);

    int insertSelective(EnyanSubscription record);

    List<EnyanSubscription> selectByExample(EnyanSubscriptionExample example);

    EnyanSubscription selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") EnyanSubscription record, @Param("example") EnyanSubscriptionExample example);

    int updateByExample(@Param("record") EnyanSubscription record, @Param("example") EnyanSubscriptionExample example);

    int updateByPrimaryKeySelective(EnyanSubscription record);

    int updateByPrimaryKey(EnyanSubscription record);
}