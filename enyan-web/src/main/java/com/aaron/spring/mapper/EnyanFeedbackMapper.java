package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanFeedback;
import com.aaron.spring.model.EnyanFeedbackExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanFeedbackMapper {
    long countByExample(EnyanFeedbackExample example);

    int deleteByExample(EnyanFeedbackExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(EnyanFeedback record);

    int insertSelective(EnyanFeedback record);

    List<EnyanFeedback> selectByExample(EnyanFeedbackExample example);

    EnyanFeedback selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") EnyanFeedback record, @Param("example") EnyanFeedbackExample example);

    int updateByExample(@Param("record") EnyanFeedback record, @Param("example") EnyanFeedbackExample example);

    int updateByPrimaryKeySelective(EnyanFeedback record);

    int updateByPrimaryKey(EnyanFeedback record);
}