package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanOrder;
import com.aaron.spring.model.EnyanOrderExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.LinkedHashMap;
import java.util.List;

@Repository
public interface EnyanOrderCustomMapper extends BaseCustomMapper<EnyanOrder>{
    long countByExample(EnyanOrderExample example);

    int deleteByExample(EnyanOrderExample example);

    int deleteByPrimaryKey(Long orderId);

    int insert(EnyanOrder record);

    int insertSelective(EnyanOrder record);

    List<EnyanOrder> selectByExample(EnyanOrderExample example);

    List<EnyanOrder> selectByExampleWithBLOBs(EnyanOrderExample example);

    EnyanOrder selectByPrimaryKey(Long orderId);

    int updateByExampleSelective(@Param("record") EnyanOrder record, @Param("example") EnyanOrderExample example);

    int updateByExample(@Param("record") EnyanOrder record, @Param("example") EnyanOrderExample example);

    int updateByPrimaryKeySelective(EnyanOrder record);

    int updateByPrimaryKey(EnyanOrder record);

    int updateOrderCounted(Long orderId);

    int updateOrderExpired();

    /**
     * <p>已经购买 <code>bookId</code> 的订单</p>
     * @param bookCondition
     * @param email
     * @return: java.util.List<com.aaron.spring.model.EnyanOrder>
     * @since : 2020-07-22
     */
    List<EnyanOrder> selectByBookId(@Param("bookCondition")String bookCondition, @Param("email")String email);

    @Select("select * from enyan_order where user_email = #{email} and is_paid = 1")
    List<EnyanOrder> selectByBookIdAndEmail(@Param("bookId")Long bookId, @Param("email")String email);

    @Select("select * from enyan_order where user_email = #{email} and is_valid = 1 and order_detail->'$.couponCode' = #{couponCode}")
    List<EnyanOrder> selectByCouponCodeAndEmail(@Param("couponCode")String couponCode, @Param("email")String email);

    @Select("select COUNT(1) from enyan_order where user_email = #{email} and is_valid = 1 and order_detail->'$.couponCode' = #{couponCode}")
    long selectCountByCouponCodeAndEmail(@Param("couponCode")String couponCode, @Param("email")String email);

    @Update({"update enyan_order set is_deleted = 1 where order_id = #{orderId}"})
    int updateOrderDeletedByBookId( @Param("orderId") Long orderId);

    @Update({"update enyan_order set is_paid = 1 where order_id = #{orderId}"})
    int updateOrderToPaid( @Param("orderId") Long orderId);

    @Update({"update enyan_order set user_email = #{revokedEmail} where user_email = #{email}"})
    int revokeUser(@Param("email")String email, @Param("revokedEmail")String revokedEmail);

    /**
     * <p>昨天的统计信息（订单数，销售额,下单用户数）</p>
     * @param
     * @return long
     * @since : 2021/9/26
     **/
    @Select("select count(1) as count, sum(order_total) as order_total,count(distinct user_email) as count2  from enyan_order " +
                    "where is_deleted = 0 and is_paid = 1 and TO_DAYS(NOW())-TO_DAYS(purchased_at) = 1")
    List<EnyanOrder> countOfOrderYesterday();

    /**
     * <p>统计信息（下单用户数）</p>
     * @param
     * @return long
     * @since : 2021/9/26
     **/
    @Select("select count(distinct user_email) as count2  from enyan_order where is_deleted = 0 and is_paid = 1 ")
    List<EnyanOrder> countOfOrderUser();

    /**
     * <p>Day的统计信息（订单数，销售额,下单用户数）(不包含兑换码兑换订单)</p>
     * @param
     * @return long
     * @since : 2021/9/26
     **/
    @Select("select count(1) as count, sum(order_total) as order_total,count(distinct user_email) as count2  from enyan_order " +
                    "where is_deleted = 0 and is_paid = 1 and order_type <> 2 and DATE_FORMAT(purchased_at,'%Y%m%d') = #{yyyyMMdd}")
    List<EnyanOrder> countOfOrderByDay(@Param("yyyyMMdd")String yyyyMMdd);

    /**
     * <p>昨天的付费订单数</p>
     * @param
     * @return long
     * @since : 2021/9/26
     **/
    @Select("select count(1) from enyan_order where is_deleted = 0 and is_paid = 1 and TO_DAYS(NOW())-TO_DAYS(purchased_at) = 1 and order_total > 0")
    long countOfOrderFeeYesterday();

    /**
     * <p>昨天的付费订单数(不包含兑换码兑换订单)</p>
     * @param
     * @return long
     * @since : 2021/9/26
     **/
    @Select("select count(1) from enyan_order where is_deleted = 0 and is_paid = 1 and order_type <> 2 and DATE_FORMAT(purchased_at,'%Y%m%d') = #{yyyyMMdd} and order_total > 0")
    long countOfOrderFeeByDay(@Param("yyyyMMdd")String yyyyMMdd);
}