package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBanner;
import com.aaron.spring.model.EnyanDailyWords;
import com.aaron.spring.model.EnyanDailyWordsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanDailyWordsCustomMapper {
    @Select("select * from enyan_daily_words where data_at = #{dataAt}")
    List<EnyanDailyWords> findRecordByDateAt(@Param("dataAt") Integer dataAt);

    @Update({"update enyan_daily_words set like_count = like_count+1 where data_id = #{dataId}"})
    int updateRecordToLikeById(@Param("dataId") Long dataId);


    @Select("select * from enyan_daily_words where data_at <= #{dataAt} order by data_at desc limit 20")
    List<EnyanDailyWords> findRecordLessAndEqualDateTop20(@Param("dataAt") Integer dataAt);

    @Select("select * from enyan_daily_words where data_at <= #{dataAt} order by data_at desc limit 10")
    List<EnyanDailyWords> findRecordLessAndEqualDateTop10(@Param("dataAt") Integer dataAt);

    @Select("select COUNT(1) from enyan_daily_words where data_at <= #{dataAt}")
    long countOfLessAndEqualDate(@Param("dataAt") Integer dataAt);


    @Select("select max(data_at) from enyan_daily_words")
    long maxDate();

    /**
     * <p>根据日期修改相关信息</p>
     * @param dailyWords
     * @return int
     * @since : 2022/8/10
     **/
    @Update({"update enyan_daily_words set data_content = #{record.dataContent},book_id=#{record.bookId},book_title=#{record.bookTitle},book_author=#{record.bookAuthor} where data_at = #{record.dataAt}"})
    int updateRecordByDate(@Param("record") EnyanDailyWords dailyWords);
}