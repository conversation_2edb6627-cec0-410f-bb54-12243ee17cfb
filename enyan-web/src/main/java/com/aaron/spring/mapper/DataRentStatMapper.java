package com.aaron.spring.mapper;

import com.aaron.spring.model.DataRentStat;
import com.aaron.spring.model.DataRentStatExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DataRentStatMapper {
    long countByExample(DataRentStatExample example);

    int deleteByExample(DataRentStatExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(DataRentStat record);

    int insertSelective(DataRentStat record);

    List<DataRentStat> selectByExample(DataRentStatExample example);

    DataRentStat selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") DataRentStat record, @Param("example") DataRentStatExample example);

    int updateByExample(@Param("record") DataRentStat record, @Param("example") DataRentStatExample example);

    int updateByPrimaryKeySelective(DataRentStat record);

    int updateByPrimaryKey(DataRentStat record);
}