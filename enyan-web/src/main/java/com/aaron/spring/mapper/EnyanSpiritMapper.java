package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanSpirit;
import com.aaron.spring.model.EnyanSpiritExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanSpiritMapper {
    long countByExample(EnyanSpiritExample example);

    int deleteByExample(EnyanSpiritExample example);

    int deleteByPrimaryKey(Long spiritId);

    int insert(EnyanSpirit record);

    int insertSelective(EnyanSpirit record);

    List<EnyanSpirit> selectByExampleWithBLOBs(EnyanSpiritExample example);

    List<EnyanSpirit> selectByExample(EnyanSpiritExample example);

    EnyanSpirit selectByPrimaryKey(Long spiritId);

    int updateByExampleSelective(@Param("record") EnyanSpirit record, @Param("example") EnyanSpiritExample example);

    int updateByExampleWithBLOBs(@Param("record") EnyanSpirit record, @Param("example") EnyanSpiritExample example);

    int updateByExample(@Param("record") EnyanSpirit record, @Param("example") EnyanSpiritExample example);

    int updateByPrimaryKeySelective(EnyanSpirit record);

    int updateByPrimaryKeyWithBLOBs(EnyanSpirit record);

    int updateByPrimaryKey(EnyanSpirit record);
}