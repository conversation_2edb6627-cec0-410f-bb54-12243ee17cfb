package com.aaron.spring.mapper;

import com.aaron.spring.model.LicenseView;
import com.aaron.spring.model.LicenseViewExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface LicenseViewMapper {
    long countByExample(LicenseViewExample example);

    int deleteByExample(LicenseViewExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(LicenseView record);

    int insertSelective(LicenseView record);

    List<LicenseView> selectByExample(LicenseViewExample example);

    LicenseView selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") LicenseView record, @Param("example") LicenseViewExample example);

    int updateByExample(@Param("record") LicenseView record, @Param("example") LicenseViewExample example);

    int updateByPrimaryKeySelective(LicenseView record);

    int updateByPrimaryKey(LicenseView record);
}