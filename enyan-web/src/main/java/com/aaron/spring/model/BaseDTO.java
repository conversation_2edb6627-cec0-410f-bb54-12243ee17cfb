package com.aaron.spring.model;

import com.aaron.common.NameAndValue;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.io.Serializable;
import java.util.*;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/6
 * @Modified By:
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class BaseDTO implements Serializable{
    /**
     * 页面用到的翻页文件名称
     * */
    @JsonIgnore
    @JSONField(serialize = false)
    String pageFileName = "";

    @JsonIgnore
    @JSONField(serialize = false)
    private int firstPage;// 显示的第一页

    @JsonIgnore
    @JSONField(serialize = false)
    private String pageLand="";

    protected Page page;

    @JsonIgnore
    @JSONField(serialize = false)
    private List<NameAndValue> searchList;

    private Integer searchType;

    private String searchText;

    private Integer searchOption;

    private Integer searchSelect;

    private String startDate;

    private String endDate;

    private String rangeDate;

    private List<OrderObj> orderObjList;

    @JsonIgnore
    @JSONField(serialize = false)
    private Integer downloadType;

    @JsonIgnore
    @JSONField(serialize = false)
    protected String orderByClause;

    @JsonIgnore
    @JSONField(serialize = false)
    private Integer valueInt;

    @JsonIgnore
    @JSONField(serialize = false)
    private String valueString;

    public void addOrder(OrderObj orderObj){
        if (null == orderObjList){
            orderObjList = new ArrayList<>();
        }
        orderObjList.add(orderObj);
    }

    public List<OrderObj> getOrderObjList() {
        return orderObjList;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public String getPageLand() {
        return pageLand;
    }

    public void setPageLand(String pageLand) {
        this.pageLand = pageLand;
    }

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

    public Integer getSearchOption() {
        return searchOption;
    }

    public void setSearchOption(Integer searchOption) {
        this.searchOption = searchOption;
    }

    public String getSearchText() {
        return searchText;
    }

    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    public Integer getSearchSelect() {
        return searchSelect;
    }

    public void setSearchSelect(Integer searchSelect) {
        this.searchSelect = searchSelect;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getRangeDate() {
        return rangeDate;
    }

    public void setRangeDate(String rangeDate) {
        this.rangeDate = rangeDate;
    }

    public List<NameAndValue> getSearchList() {

        if (this instanceof EnyanBook){
            return Constant.bookSearchList;
        }else if(this instanceof EnyanCategory){
            return Constant.categorySearchList;
        }else if (this instanceof EnyanPublisher){
            return Constant.publishSearchList;
        }else if (this instanceof EnyanDiscount){
            return Constant.discountSearchList;
        }else if (this instanceof EnyanConfig){
            return Constant.configSearchList;
        }else if (this instanceof EnyanImg){
            return Constant.imgSearchList;
        }else if (this instanceof Publication){
            return Constant.publicationSearchList;
        }else if (this instanceof EnyanRedeemCode){
            return Constant.redeemSearchList;
        }else if (this instanceof EnyanSpirit){
            return Constant.spiritSearchList;
        }else if (this instanceof EnyanRefund){
            return Constant.refundSearchList;
        }else if (this instanceof EnyanCoupon){
            return Constant.couponSearchList;
        }
        return searchList;
    }

    public void setSearchList(List<NameAndValue> searchList) {
        this.searchList = searchList;
    }

    public String getPageFileName() {
        return pageFileName;
    }

    public void setPageFileName(String pageFileName) {
        this.pageFileName = pageFileName;
    }

    public Integer getDownloadType() {
        return downloadType;
    }

    public void setDownloadType(Integer downloadType) {
        this.downloadType = downloadType;
    }

    public String getOrderByClause() {
        if (null != this.getOrderObjList()){
            StringBuffer buffer = new StringBuffer();
            for (int i = 0; i < this.getOrderObjList().size(); i++) {
                OrderObj orderObj = this.getOrderObjList().get(i);
                if (i!=0){
                    buffer.append(",");
                }
                buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
            }
            return buffer.toString();
        }
        return orderByClause;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public Integer getValueInt() {
        return valueInt;
    }

    public void setValueInt(Integer valueInt) {
        this.valueInt = valueInt;
    }

    public String getValueString() {
        return valueString;
    }

    public void setValueString(String valueString) {
        this.valueString = valueString;
    }

    public void excutePageLand(Map<String, Object> queryParams){

        if (this.getPage() == null){
            this.setPage(new Page());
        }
        int currentPage = page.getCurrentPage();
        int maxPage ;
        int totalPages = page.getTotalPage();
        long totalRows =  page.getTotalRecord();

        StringBuffer buffer = new StringBuffer(100);
        /*buffer.append("共[<B>" + totalRows + "</B>]条记录 ");
        buffer.append("[<B>" + totalPages + "</B>]页 ");
        buffer.append("当前是[<B>" + currentPage + "</B>]页 ");*/

        /* 20240521
        buffer.append("<ul class=\"pagination\">");
        buffer.append("<li><p>共<code>"+ totalRows + "</code> 条记录 ");
        buffer.append("<code>"+ totalPages + "</code>页 ");
        buffer.append("当前是<code>"+ currentPage + "</code>页 ");
        buffer.append("</p></li>");
        buffer.append("</ul>");
        */
        buffer.append("<ul class=\"pagination\">");
        buffer.append("<B>Total " + totalRows + " record(s)</B>");
        //buffer.append("<code>"+ totalPages + "</code>页 ");
        //buffer.append("当前是<code>"+ currentPage + "</code>页 ");
        buffer.append("</p></li>");
        buffer.append("</ul>");

        if (queryParams != null) {
            Set<String> keys = queryParams.keySet();
            Object value = null;
            StringBuffer sb = new StringBuffer();
            boolean first = true;
            if(keys.size()>0){
                for (String key : keys) {
                    value = queryParams.get(key);
                    if (first) {
                        if (StringUtils.isNotBlank(pageFileName)){
                            sb.append(pageFileName);
                        }
                        sb.append("?").append(key).append("=").append(value);
                        first = !first;
                    } else {
                        sb.append("&").append(key).append("=").append(value);
                    }
                }
            }else{
                sb.append("?");
            }
            pageFileName = sb.toString();
        }else{
            pageFileName = "?";
        }
        if (page.getCurrentPage() > 2) {
            firstPage = currentPage - 2;
        } else {
            firstPage = 1;
        }
        maxPage = firstPage + 10;
        if ((totalPages - firstPage) < 10){//如果有10页以上的数据，则直接重新计算
            firstPage = totalPages - 10;
        }
        if (firstPage < 1){
            firstPage = 1;
        }

        buffer.append("<ul class=\"pagination pull-right\">");

        if (totalPages <= 1) {
            //buffer.append("<li><a href=\"#\">«</a></li>");
            buffer.append("<li class=\"active\"><a href=\"#\">1</a></li>");
            //buffer.append("<li><a href=\"#\">»</a></li>");
        } else {
            if (currentPage == 1) {
                //buffer.append("<li><a href=\"#\">1</a></li>");
            }
            if (1 < firstPage){
                buffer.append(" <li><a href='" + pageFileName + "&total="
                        + totalRows + "&pageNo=1' >1</a></li> ");
                buffer.append("<li><a href='#'>...</a></li>");
            }
            for (int i = firstPage; i < maxPage && i < totalPages; i++) {
                if (i != currentPage) {
                    buffer.append(" <li><a href='" + pageFileName + "&total="
                            + totalRows + "&pageNo=" + i + "' >" + i + "</a></li> ");
                } else {
                    buffer.append("<li class=\"active\"><a href=\"#\">"+currentPage+"</a></li>");
                }
            }
            if (maxPage < totalPages){
                buffer.append("<li><a href='#'>...</a></li>");
            }
            if (currentPage != totalPages) {
                buffer.append("<li><a href='" + pageFileName + "&total="
                        + totalRows + "&pageNo=" + totalPages + "'  > "+ totalPages +"</a></li>");
            } else {
                buffer.append(" <li class=\"active\"><a href='#'>" + totalPages + " </a></li>");
            }
        }
        buffer.append("</ul>");
        this.pageLand = buffer.toString();
    }

    public void excuteFrontPageLand(Map<String, Object> queryParams){

        if (this.getPage() == null){
            this.setPage(new Page());
        }
        int currentPage = page.getCurrentPage();
        int maxPage ;
        int totalPages = page.getTotalPage();
        long totalRows =  page.getTotalRecord();

        if (totalRows == 0){
            this.pageLand = "";
            return;
        }

        StringBuffer buffer = new StringBuffer(100);

        if (queryParams != null) {
            Set<String> keys = queryParams.keySet();
            Object value = null;
            StringBuffer sb = new StringBuffer();
            boolean first = true;
            if(keys.size()>0){
                for (String key : keys) {
                    value = queryParams.get(key);
                    if (first) {
                        sb.append("?").append(key).append("=").append(value);
                        first = !first;
                    } else {
                        sb.append("&").append(key).append("=").append(value);
                    }
                }
            }else{
                sb.append("?");
            }
            pageFileName = sb.toString();
        }else{
            pageFileName = "?";
        }
        if (page.getCurrentPage() > 2) {
            firstPage = currentPage - 2;
        } else {
            firstPage = 1;
        }
        maxPage = firstPage + 10;
        if ((totalPages - firstPage) < 10){//如果有10页以上的数据，则直接重新计算
            firstPage = totalPages - 10;
        }
        if (firstPage < 1){
            firstPage = 1;
        }


        if (totalPages <= 1) {
            buffer.append("<div class=\"column\"><ul class=\"pages\">");
            buffer.append("<li class=\"active\"><a href=\"#\">1</a></li>");
            buffer.append("</ul></div>");
        } else {
            if (currentPage > 1){
                buffer.append("<div class=\"column text-left hidden-xs-down\">");
                buffer.append("<a class=\"btn btn-outline-secondary btn-sm\" ");
                buffer.append(" href='" + pageFileName + "&total="
                        + totalRows + "&pageNo="+(currentPage-1)+"' ><i class=\"icon-arrow-left\"></i>&nbsp;"+this.getPrevString()+"</a></div>");
            }

            buffer.append("<div class=\"column\"><ul class=\"pages\">");
            if (currentPage < 1) {
                //buffer.append("<li class=\"active\"><a href=\"#\">1</a></li>");
                currentPage = 1;
            }
            if (1 < firstPage){
                buffer.append(" <li><a href='" + pageFileName + "&total="
                        + totalRows + "&pageNo=1' >1</a></li> ");
                buffer.append("<li>...</li>");
            }
            for (int i = firstPage; i < maxPage && i < totalPages; i++) {
                if (i != currentPage) {
                    buffer.append(" <li><a href='" + pageFileName + "&total="
                            + totalRows + "&pageNo=" + i + "' >" + i + "</a></li> ");
                } else {
                    buffer.append("<li class=\"active\"><a href=\"#\">"+currentPage+"</a></li>");
                }
            }
            if (maxPage < totalPages){
                buffer.append("<li>...</li>");
            }
            if (currentPage != totalPages) {
                buffer.append("<li><a href='" + pageFileName + "&total="
                        + totalRows + "&pageNo=" + totalPages + "'  > "+totalPages+"</a></li>");
            } else {
                buffer.append(" <li class=\"active\"><a href='#'>" + totalPages + " </a></li>");
            }
            buffer.append("</ul></div>");

            if (currentPage<totalPages){
                buffer.append("<div class=\"column text-right hidden-xs-down\">");
                buffer.append("<a class=\"btn btn-outline-secondary btn-sm\" ");
                buffer.append(" href='" + pageFileName + "&total="
                        + totalRows + "&pageNo="+(currentPage+1)+"' >&nbsp;"+this.getNextString()+"<i class=\"icon-arrow-right\"></i></a></div>");
            }
        }
        this.pageLand = buffer.toString();
    }
    private String getNextString(){
        if ("zh_CN".equals(LocaleContextHolder.getLocale().toString())){
            return "下一页";
        }
        if ("en_US".equals(LocaleContextHolder.getLocale().toString())){
            return "Next Page";
        }
        return "下一頁";
    }
    private String getPrevString(){
        if ("zh_CN".equals(LocaleContextHolder.getLocale().toString())){
            return "上一页";
        }
        if ("en_US".equals(LocaleContextHolder.getLocale().toString())){
            return "Previous Page";
        }
        return "上一頁";
    }



}
