package com.aaron.spring.model;

import com.aaron.spring.common.EBookConstant;
import com.stripe.model.Charge;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/12/13
 * @Modified By:
 */
public class OrderPayInfo implements Serializable{
    private static final long serialVersionUID = -1874052548654013789L;

    /**
     * 付费
     * */
    private PayDetail charge;//付费
    /**
     * 退款
     * */
    private PayDetail refund;//退款
    /**
     *
     *
     * @param charge
     * @param isPay -true：付费；false：退款
     * @Date: 2017/12/15
     * @update stripe pay-country: 2020/6/10
     */
    public void addStripe(Charge charge, boolean isPay){
        PayDetail payDetail = new PayDetail();

        payDetail.setPayID(charge.getId());
        payDetail.setPayType(EBookConstant.PayType.STRIPE_PAY);
        payDetail.setTradeNO(charge.getId());
        payDetail.setAmount(charge.getAmount()/100 +"");//Stripe信用卡支付会乘100，在这里重新恢复一下
        payDetail.setCurrency(charge.getCurrency());
        Charge.PaymentMethodDetails.Card card = charge.getPaymentMethodDetails().getCard();
        payDetail.setCountry(card.getCountry());

        if ("HK".equals(card.getCountry())){
            payDetail.setPayType(EBookConstant.PayType.STRIPE_PAY_IN_HK);
        }else{
            payDetail.setPayType(EBookConstant.PayType.STRIPE_PAY_OUTSIDE_HK);
        }
        if ("succeeded".equals(charge.getStatus())){
            payDetail.setTradeStatus("成功");
        }else {
            payDetail.setTradeStatus("失败");
        }
        if (isPay){
            this.charge = payDetail;
        }else {
            this.refund = payDetail;
        }
    }

    /**
     * <p>Stripe 的测试</p>
     * @param
     * @return void
     * @since : 2022/11/9
     **/
    public void addStripeTest(){
        Charge charge = new Charge();
        charge.setId("chargeTestID");
        charge.setAmount(100L);
        charge.setCurrency("HK");
        Charge.PaymentMethodDetails.Card card = new Charge.PaymentMethodDetails.Card();
        card.setCountry("Test");

        Charge.PaymentMethodDetails paymentMethodDetails = new Charge.PaymentMethodDetails();
        paymentMethodDetails.setCard(card);

        charge.setPaymentMethodDetails(paymentMethodDetails);

        charge.setStatus("succeeded");
        this.addStripe(charge, true);
    }

    public void addAlipay(Map<String,String> payParams, int payType,boolean isPay){
        PayDetail payDetail = new PayDetail();

        payDetail.setPayType(payType);
        payDetail.setTradeNO(payParams.get("trade_no"));
        payDetail.setAmount(payParams.get("total_fee"));
        payDetail.setCurrency(payParams.get("currency"));
        payDetail.setCountry("CN");
        if ("TRADE_FINISHED".equals(payParams.get("trade_status"))
                ||"TRADE_SUCCESS".equals(payParams.get("trade_status"))){
            payDetail.setTradeStatus("成功");
        }else {
            payDetail.setTradeStatus("失败");
        }

        if (isPay){
            this.charge = payDetail;
        }else {
            this.refund = payDetail;
        }
    }

    /**
     * <p>Alipay测试</p>
     * @param
     * @return void
     * @since : 2022/11/9
     **/
    public void addAlipayTest(){
        Map<String,String> params = new HashMap<>();
        params.put("total_fee","100");
        params.put("currency","HKD");
        params.put("trade_no","tradeNO测试的");
        params.put("trade_status","TRADE_FINISHED");
        this.addAlipay(params, EBookConstant.PayType.ALI_PAY_HK, true);
    }

    /**
     * <p>添加Alipay测试数据</p>
     * @param fee
     * @return void
     * @since : 2022/12/7
     **/
    public void addAlipayTestWithFee(BigDecimal fee){
        Map<String,String> params = new HashMap<>();
        params.put("total_fee", fee.toPlainString());
        params.put("currency","HKD");
        params.put("trade_no","tradeNO测试的");
        params.put("trade_status","TRADE_FINISHED");
        this.addAlipay(params, EBookConstant.PayType.ALI_PAY_HK, true);
    }

    public void addCreditTestWithFee(BigDecimal fee){
        Map<String,String> params = new HashMap<>();
        params.put("total_fee", fee.toPlainString());
        params.put("currency","HKD");
        params.put("trade_no","tradeNO测试的");
        params.put("trade_status","TRADE_FINISHED");
        this.addAlipay(params, EBookConstant.PayType.STRIPE_PAY_OUTSIDE_HK, true);
    }

    /**
     *
     * 免费的书籍
     * @param
     * @Date: 2019-11-13
     */
    public void addFree(){
        PayDetail payDetail = new PayDetail();

        payDetail.setPayType(EBookConstant.PayType.FREE_PAY);
        payDetail.setTradeNO("免费");
        payDetail.setAmount("0");
        payDetail.setCurrency("");
        payDetail.setCountry("All");
        payDetail.setTradeStatus("成功");
        this.charge = payDetail;
    }

    /**
     * <p>直接支付，没有支付的手续费</p>
     * @param
     * @return void
     * @since : 2022/5/25
     **/
    public void addDirect(){
        PayDetail payDetail = new PayDetail();

        payDetail.setPayType(EBookConstant.PayType.DIRECT_PAY);
        payDetail.setTradeNO("直接支付");
        payDetail.setAmount("0");
        payDetail.setCurrency("");
        payDetail.setCountry("All");
        payDetail.setTradeStatus("成功");
        this.charge = payDetail;
    }

    /**
     * <p>直接支付，使用Alipay的手续费</p>
     * @param
     * @return void
     * @since : 2022/5/25
     **/
    public void addDirectWithAlipayFee(){
        PayDetail payDetail = new PayDetail();

        payDetail.setPayType(EBookConstant.PayType.DIRECT_PAY_ALIPAY);
        payDetail.setTradeNO("直接支付用Alipay手续费");
        payDetail.setAmount("0");
        payDetail.setCurrency("");
        payDetail.setCountry("All");
        payDetail.setTradeStatus("成功");
        this.charge = payDetail;
    }

    /**
     *
     * 兑换码支付
     * @param
     * @Date: 2019-11-13
     */
    public void addRedeemCode(EnyanRedeemCode enyanRedeemCode){
        PayDetail payDetail = new PayDetail();

        payDetail.setPayType(EBookConstant.PayType.REDEEM_CODE_PAY);
        payDetail.setTradeNO(enyanRedeemCode.getCode());
        payDetail.setAmount("0");
        payDetail.setCurrency("");
        payDetail.setCountry("All");
        payDetail.setTradeStatus("成功");
        this.charge = payDetail;
    }

    public PayDetail getCharge() {
        return charge;
    }

    public void setCharge(PayDetail charge) {
        this.charge = charge;
    }

    public PayDetail getRefund() {
        return refund;
    }

    public void setRefund(PayDetail refund) {
        this.refund = refund;
    }

    @Override
    public String toString() {
        StringBuffer buffer = new StringBuffer();
        //buffer.append("支付明细：");
        //buffer.append("</br>");
        /*if (null != charge){
            buffer.append("付款：");
            buffer.append("</br>");
            buffer.append("交易号:");
            buffer.append(charge.getTradeNO());
            buffer.append("</br>");
            buffer.append("金额：");
            buffer.append(charge.getAmount());
            buffer.append("</br>");
            buffer.append("货币：");
            buffer.append("</br>");
            buffer.append(charge.getCurrency());
        }*/
        if (null != charge){
            //buffer.append("付款：");
            //buffer.append("</br>");
            if (charge.getPayType() == EBookConstant.PayType.REDEEM_CODE_PAY){
                buffer.append("兑换码:");
                buffer.append("<a href=\"/adminRedeemCode/getCode-");
                buffer.append(charge.getTradeNO());
                buffer.append("\" target=\"_blank\">");
                buffer.append(charge.getTradeNO());
                buffer.append("</a>");
            }else{
                buffer.append("交易号:");
                buffer.append(charge.getTradeNO());
            }
            buffer.append("</br>");
            buffer.append("支付方式:");
            buffer.append(charge.payTypeDescription());
            buffer.append("</br>");
            buffer.append("金额：");
            buffer.append(charge.getAmount());
            buffer.append("</br>");
            buffer.append("货币：");
            buffer.append(charge.getCurrency());
            buffer.append("</br>");
            buffer.append("国家：");
            buffer.append(charge.getCountry());
            buffer.append("</br>");
        }
        return buffer.toString();
    }
}
