package com.aaron.spring.model;

import com.aaron.common.Money;
import com.aaron.spring.common.APIConstant;
import com.aaron.spring.common.Constant;
import com.alibaba.fastjson2.annotation.JSONField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
/**
 * <p>CartInfo（购物车信息）- List<CartDiscountInfo>（折扣信息列表）- productInfoList</p>
 * <PERSON>
 * @since : 2020-08-06
 */
public class CartInfo  implements Serializable {


	private static final long serialVersionUID = -5142606658796155784L;
	private int orderNum;
	
	//private CustomerInfo customerInfo;
	private List<CartDiscountInfo> cartDiscountInfoList = new ArrayList<>();
	
	private BigDecimal quantity;
	
	private BigDecimal fareTotal;
	
	private BigDecimal total;

	/**
	 * 在使用优惠码之前的价钱，主要用于判断是否满足满减
	 * */
	@JSONField(serialize = false)
	private BigDecimal totalFeeBeforeCoupon;//在使用优惠码之前的价钱，主要用于判断是否满足满减
	
	private String note;
	
	private int orderStatus;
	
	//private boolean isEmpty;
	
	private boolean isValidCustomer;
	
	private boolean isPayFlag = false;
	
	private String loginName;
	
	private String expressName;
	private double expressCost;

	private String currentSysUpdate;

	private boolean empty;

	private boolean validInfo;

	private double amountCny;

	private double amountUsd;

	private double amountHkd;

	private double amountHkdFix;

	@JSONField(serialize = false)
	private String language;

    public CartInfo(String currentSysUpdate, String language) {
        this.currentSysUpdate = currentSysUpdate;
        this.language = language;
    }

    public boolean isPayFlag() {
		return isPayFlag;
	}
	public void setPayFlag(boolean flag) {
		this.isPayFlag = flag;
	}
	
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String userName) {
		this.loginName = userName;
	}
	
	public BigDecimal getQuantity() {
	    quantity = new BigDecimal("0");
        for(CartDiscountInfo info : this.cartDiscountInfoList) {
            quantity = quantity.add(info.getQuantity());
        }
        return quantity;
	}
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	
	public BigDecimal getTotal() {
		return total;
	}
	public void setTotal(BigDecimal total) {
		this.total = total;
	}
	
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	
	public int getOrderStatus() {
		return orderStatus;
	}
	public void setOrderStatus(int orderStatus) {
		this.orderStatus = orderStatus;
	}
	
	public int getOrderNum() {
		return orderNum;
	}
	public void setOrderNum(int orderNum) {
		this.orderNum = orderNum;
	}
	
	public double getExpressCost() {
        return expressCost;
    }
    public void setExpressCost(double expressCost) {
        this.expressCost = expressCost;
    }
    
    public String getExpressName() {
        return expressName;
    }
    public void setExpressName(String expressName) {
        this.expressName = expressName;
    }


	public List<CartDiscountInfo> getCartDiscountInfoList() {
		return cartDiscountInfoList;
	}

	private CartDiscountInfo findCartDiscountByProduct(ProductInfo productInfo){
		for (CartDiscountInfo cartDiscountInfo:cartDiscountInfoList){
			if (cartDiscountInfo.getDiscountId().equals(productInfo.getDiscountId())){
				return cartDiscountInfo;
			}
		}
		return null;
	}

	/*private CartLineInfo findLineByCode(long code) {
		for(CartLineInfo line : this.cartLines) {
			if(line.getProductInfo().getCode() == code)
				return line;
		}
		return null;
	}*/
    /**
     *
     * 添加产品到购物车
     * @param productInfo 要添加的产品
     * @param quantity 可以为负值
     * @Date: 2017/12/9
     */
	public void addProduct(ProductInfo productInfo, int quantity) {
	    CartDiscountInfo cartDiscountInfo = this.findCartDiscountByProduct(productInfo);
	    if (null == cartDiscountInfo){
	        cartDiscountInfo = new CartDiscountInfo(productInfo.getDiscountId());
	        cartDiscountInfo.setLanguage(this.getLanguage());
            /*if (!cartDiscountInfo.isValid()){
                return;
            }*/
            if (cartDiscountInfo.getDiscountId() > 0){
            	this.cartDiscountInfoList.add(0,cartDiscountInfo);
			}else {
				this.cartDiscountInfoList.add(cartDiscountInfo);
			}
        }
        cartDiscountInfo.addProduct(productInfo,quantity);
	    if (cartDiscountInfo.isEmpty()){
            cartDiscountInfoList.remove(cartDiscountInfo);
        }
	}
    public void updateProduct(Long code, BigDecimal quantity){
        for (CartDiscountInfo cartDiscountInfo:cartDiscountInfoList){
            if (cartDiscountInfo.updateProduct(code,quantity)){
                break;
            }
        }
    }

	private void resetProducts(CartDiscountInfo info){
        for (CartDiscountInfo cartDiscountInfo:cartDiscountInfoList){
            if (info.getDiscountId().equals(cartDiscountInfo.getDiscountId())){
                cartDiscountInfo.resetProducts(info);
                break;
            }
        }
    }
	
	public void removeProduct(Long code) {
        for (CartDiscountInfo cartDiscountInfo:cartDiscountInfoList){
        	if (cartDiscountInfo.removeProduct(code)){
				if (cartDiscountInfo.isEmpty()){
					cartDiscountInfoList.remove(cartDiscountInfo);
				}
        		break;
			}
        }
	}

    /**
     *
     * 获取购物车所有产品信息
     * @param
     * @Date: 2017/12/11
     */
	public List<ProductInfo> findProducts(){
	    List<ProductInfo> list = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:cartDiscountInfoList){
            for (ProductInfo info:cartDiscountInfo.getProductInfoList()){
                list.add(info);
            }
        }
	    return list;
    }

	/**
	 * <p>获取适用于优惠码的产品</p>
	 * @param enyanCoupon
	 * @return java.util.List<com.aaron.spring.model.ProductInfo>
	 * @since : 2021/9/13
	 **/
	public List<ProductInfo> findProductsWithCoupon(EnyanCoupon enyanCoupon){
		List<ProductInfo> list = new ArrayList<>();
		for (CartDiscountInfo cartDiscountInfo:cartDiscountInfoList){
			for (ProductInfo info:cartDiscountInfo.getProductInfoList()){
				if (enyanCoupon.getCouponType() == APIConstant.CouponType.ALL){//所有书籍都可以
					list.add(info);
					continue;
				}
				if (null != enyanCoupon.getBookSet() && enyanCoupon.getBookSet().contains(info.getCode().longValue())){
					list.add(info);
				}
			}
		}
		return list;
	}

	/**
	 * <p>重新获取使用优惠码后的所有产品信息, 使用clone</p>
	 * @param amountCoupon
	 * @return: java.util.List<com.aaron.spring.model.ProductInfo>
	 * @since : 2020-07-23
	 */
	@Deprecated
    public List<ProductInfo> findCouponProducts(BigDecimal amountCoupon) throws CloneNotSupportedException {
		List<ProductInfo> productInfoList = this.findProducts();
		if (null != amountCoupon && amountCoupon.doubleValue() > 0){
			List<ProductInfo> productInfoCopyList = new ArrayList<>();
			BigDecimal totalFee = this.getAmountHkd();//总共的价格
			BigDecimal newFee = totalFee.subtract(amountCoupon);
			if (newFee.doubleValue() < 0){
				newFee = new BigDecimal("0");
			}
			for (ProductInfo p:productInfoList){//List 是浅copy，所以需要单独clone对象，这样可以直接修改clone对象，不影响原始数据
				productInfoCopyList.add((ProductInfo) p.clone());
			}
			BigDecimal pastFee = new BigDecimal("0");
			for (int i = 0; i < productInfoCopyList.size(); i++) {
				ProductInfo productInfo = productInfoCopyList.get(i);
				productInfo.setDiscountCouponIsValid(true);
				if (i == (productInfoCopyList.size() - 1)){
					productInfo.setPriceHKDDiscount(newFee.subtract(pastFee));
				}else {
					BigDecimal newPrice = productInfo.getRealPriceHKD().multiply(newFee).divide(totalFee, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
					pastFee = pastFee.add(newPrice);

					productInfo.setPriceHKDDiscount(newPrice);
				}
			}
			return productInfoCopyList;
		}

		return productInfoList;
	}
	/**
	 * <p>重置优惠码后的价格</p>
	 * @param amountCoupon
	 * @return: java.util.List<com.aaron.spring.model.ProductInfo>
	 * @since : 2020-08-12
	 */
	@Deprecated
	public List<ProductInfo> resetCouponProducts(BigDecimal amountCoupon)  {
		List<ProductInfo> productInfoList = this.findProducts();
		if (null != amountCoupon && amountCoupon.doubleValue() > 0){
			//List<ProductInfo> productInfoCopyList = new ArrayList<>();
			BigDecimal totalFee = this.getAmountHkdDiscount();//总共的价格
			BigDecimal newFee = totalFee.subtract(amountCoupon);
			if (newFee.doubleValue() < 0){
				newFee = new BigDecimal("0");
			}

			BigDecimal pastFee = new BigDecimal("0");
			for (int i = 0; i < productInfoList.size(); i++) {
				ProductInfo productInfo = productInfoList.get(i);
				productInfo.setDiscountCouponIsValid(true);
				if (i == (productInfoList.size() - 1)){
					productInfo.setPriceHKDDiscount(newFee.subtract(pastFee));
				}else {
					BigDecimal newPrice = productInfo.getRealPriceHKD().multiply(newFee).divide(totalFee, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
					pastFee = pastFee.add(newPrice);

					productInfo.setPriceHKDDiscount(newPrice);
				}
				BigDecimal realPrice = productInfo.getPriceHKDDiscount();
				if (productInfo.getQuantity().intValue()>1){//套装书可能会有多本
					realPrice = realPrice.divide(productInfo.getQuantity(), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
					productInfo.setPriceHKDDiscount(realPrice);
				}
			}
			return productInfoList;
		}

		return productInfoList;
	}

	/**
	 * <p>根据优惠码重置当前产品信息</p>
	 * @param enyanCoupon
	 * @return java.util.List<com.aaron.spring.model.ProductInfo>
	 * @since : 2021/9/13
	 **/
	public List<ProductInfo> resetCouponProducts(EnyanCoupon enyanCoupon)  {
		List<ProductInfo> productInfoList = this.findProductsWithCoupon(enyanCoupon);
		if (null != enyanCoupon.getCouponValue() && enyanCoupon.getCouponValue() > 0){
			BigDecimal couponValue = new BigDecimal(enyanCoupon.getCouponValue()+"");
			BigDecimal totalFee = new BigDecimal("0");//总共的价格
			for (ProductInfo productInfo:productInfoList){//重新遍历获取总共的价格
				totalFee = totalFee.add(productInfo.getRealPriceHKD().multiply(productInfo.getQuantity()));
			}
			this.totalFeeBeforeCoupon = totalFee;
			if (totalFee.compareTo(new BigDecimal(enyanCoupon.getMinLimitValue()+""))  == -1){//不满足满减最低限，则返回
				return productInfoList;
			}
			BigDecimal newFee = totalFee.subtract(couponValue);
			if (newFee.doubleValue() < 0){
				newFee = new BigDecimal("0");
			}

			BigDecimal pastFee = new BigDecimal("0");
			for (int i = 0; i < productInfoList.size(); i++) {
				ProductInfo productInfo = productInfoList.get(i);
				productInfo.setDiscountCouponIsValid(true);
				if (i == (productInfoList.size() - 1)){
					productInfo.setPriceHKDDiscount(newFee.subtract(pastFee));
				}else {
					BigDecimal newPrice = productInfo.getRealPriceHKD().multiply(newFee).divide(totalFee, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
					pastFee = pastFee.add(newPrice);

					productInfo.setPriceHKDDiscount(newPrice);
				}
				BigDecimal realPrice = productInfo.getPriceHKDDiscount();
				if (productInfo.getQuantity().intValue()>1){//套装书可能会有多本
					realPrice = realPrice.divide(productInfo.getQuantity(), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
					productInfo.setPriceHKDDiscount(realPrice);
				}
			}
			return productInfoList;
		}

		return productInfoList;
	}

    public void resetProducts(CartInfo cartForm) {
        if(cartForm != null) {
            List<CartDiscountInfo> infos = cartForm.getCartDiscountInfoList();
            for(CartDiscountInfo info : infos) {
                this.resetProducts(info);
            }
        }
    }

    /*public BigDecimal getAmountCny() {
        BigDecimal total = new BigDecimal("0");
        for(CartDiscountInfo info : this.cartDiscountInfoList) {
            total = total.add(info.getAmountCny());
        }
        return total;
    }

    public BigDecimal getAmountUsd() {
        BigDecimal total = new BigDecimal("0");
        for(CartDiscountInfo info : this.cartDiscountInfoList) {
            total = total.add(info.getAmountUsd());
        }
        return total;
    }*/

    public BigDecimal getAmountHkd() {
        BigDecimal total = new BigDecimal("0");
        for(CartDiscountInfo info : this.cartDiscountInfoList) {
            total = total.add(info.getAmountHkd());
        }
        return total;
    }

	public BigDecimal getAmountHkdMiddle() {
		BigDecimal total = new BigDecimal("0");
		for(CartDiscountInfo info : this.cartDiscountInfoList) {
			total = total.add(info.getAmountHkdMiddle());
		}
		return total;
	}

	public BigDecimal getAmountHkdFix() {
		BigDecimal total = new BigDecimal("0");
		for(CartDiscountInfo info : this.cartDiscountInfoList) {
			total = total.add(info.getAmountHkdFix());
		}
		return total;
	}

    /**
     * <p>真实的最终价格，折扣后、优惠码后，或者原价</p>
     * @param
     * @return: java.math.BigDecimal
     * @since : 2020-08-14
     */
    public BigDecimal getAmountHkdDiscount() {
        BigDecimal total = new BigDecimal("0");
        for(CartDiscountInfo info : this.cartDiscountInfoList) {
            total = total.add(info.getAmountHkdDiscount());
        }
        return total;
    }
	/**
	 *
	 * 购物车是否合法
	 * @param
	 * @Date: 2017/12/10
	 */
	public boolean isValidInfo() {
		return Constant.SYS_UPDATE.equals(this.currentSysUpdate);
	}

	public boolean isEmpty() {
		return this.cartDiscountInfoList.isEmpty();
	}
	/*public void setEmpty(boolean isEmpty) {
		this.isEmpty = isEmpty;
	}*/
	
    public boolean isValidCustomer() {
    	//isValidCustomer = this.customerInfo != null && this.customerInfo.isValid();
        return isValidCustomer;
    }
    public void setValidCustomer(boolean isValidation) {
    	this.isValidCustomer = isValidation;
    }
	
	public BigDecimal getQuantityTotal(){
		BigDecimal quantity = new BigDecimal("0");
		for(CartDiscountInfo info : this.cartDiscountInfoList) {
			quantity = quantity.add(info.getQuantity());
		}
		return quantity;
	}
	public void setQuantityTotal(BigDecimal quantity) {
		this.quantity = quantity;
	}
	

	//get total with fare
	public BigDecimal getFareTotal() {
		return fareTotal;
	}
	
	public void setFareTotal(BigDecimal fareTotal) {
		this.fareTotal = fareTotal;
	}


    public String getCurrentSysUpdate() {
        return currentSysUpdate;
    }

    public void setCurrentSysUpdate(String currentSysUpdate) {
        this.currentSysUpdate = currentSysUpdate;
    }

    public void setCartInfo(CartInfo cartInfo) {
		
		this.orderNum = cartInfo.orderNum;
		//this.cartLines = cartInfo.cartLines;
		//this.customerInfo = cartInfo.customerInfo;
		this.quantity = cartInfo.quantity;
		this.total = cartInfo.total;
		//this.isEmpty = cartInfo.isEmpty;
		//this.isValidCustomer = cartInfo.isValidCustomer;
	}

	public void setCartDiscountInfoList(List<CartDiscountInfo> cartDiscountInfoList) {
		this.cartDiscountInfoList = cartDiscountInfoList;
	}

	public void setEmpty(boolean empty) {
		this.empty = empty;
	}

	public void setValidInfo(boolean validInfo) {
		this.validInfo = validInfo;
	}

	public void setAmountCny(double amountCny) {
		this.amountCny = amountCny;
	}

	public void setAmountUsd(double amountUsd) {
		this.amountUsd = amountUsd;
	}

    public void setAmountHkd(double amountHkd) {
        this.amountHkd = amountHkd;
    }

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public BigDecimal getTotalFeeBeforeCoupon() {
		return totalFeeBeforeCoupon;
	}

	public void setTotalFeeBeforeCoupon(BigDecimal totalFeeBeforeCoupon) {
		this.totalFeeBeforeCoupon = totalFeeBeforeCoupon;
	}
}
