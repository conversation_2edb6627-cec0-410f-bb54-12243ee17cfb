package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EnyanRentExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanRentExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andRentIdIsNull() {
            addCriterion("rent_id is null");
            return (Criteria) this;
        }

        public Criteria andRentIdIsNotNull() {
            addCriterion("rent_id is not null");
            return (Criteria) this;
        }

        public Criteria andRentIdEqualTo(Long value) {
            addCriterion("rent_id =", value, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdNotEqualTo(Long value) {
            addCriterion("rent_id <>", value, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdGreaterThan(Long value) {
            addCriterion("rent_id >", value, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rent_id >=", value, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdLessThan(Long value) {
            addCriterion("rent_id <", value, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdLessThanOrEqualTo(Long value) {
            addCriterion("rent_id <=", value, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdIn(List<Long> values) {
            addCriterion("rent_id in", values, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdNotIn(List<Long> values) {
            addCriterion("rent_id not in", values, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdBetween(Long value1, Long value2) {
            addCriterion("rent_id between", value1, value2, "rentId");
            return (Criteria) this;
        }

        public Criteria andRentIdNotBetween(Long value1, Long value2) {
            addCriterion("rent_id not between", value1, value2, "rentId");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNull() {
            addCriterion("order_num is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNotNull() {
            addCriterion("order_num is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumEqualTo(String value) {
            addCriterion("order_num =", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotEqualTo(String value) {
            addCriterion("order_num <>", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThan(String value) {
            addCriterion("order_num >", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("order_num >=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThan(String value) {
            addCriterion("order_num <", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThanOrEqualTo(String value) {
            addCriterion("order_num <=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLike(String value) {
            addCriterion("order_num like", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotLike(String value) {
            addCriterion("order_num not like", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumIn(List<String> values) {
            addCriterion("order_num in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotIn(List<String> values) {
            addCriterion("order_num not in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumBetween(String value1, String value2) {
            addCriterion("order_num between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotBetween(String value1, String value2) {
            addCriterion("order_num not between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andUserEmailIsNull() {
            addCriterion("user_email is null");
            return (Criteria) this;
        }

        public Criteria andUserEmailIsNotNull() {
            addCriterion("user_email is not null");
            return (Criteria) this;
        }

        public Criteria andUserEmailEqualTo(String value) {
            addCriterion("user_email =", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotEqualTo(String value) {
            addCriterion("user_email <>", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailGreaterThan(String value) {
            addCriterion("user_email >", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("user_email >=", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLessThan(String value) {
            addCriterion("user_email <", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLessThanOrEqualTo(String value) {
            addCriterion("user_email <=", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLike(String value) {
            addCriterion("user_email like", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotLike(String value) {
            addCriterion("user_email not like", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailIn(List<String> values) {
            addCriterion("user_email in", values, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotIn(List<String> values) {
            addCriterion("user_email not in", values, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailBetween(String value1, String value2) {
            addCriterion("user_email between", value1, value2, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotBetween(String value1, String value2) {
            addCriterion("user_email not between", value1, value2, "userEmail");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNull() {
            addCriterion("publisher_id is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNotNull() {
            addCriterion("publisher_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdEqualTo(Long value) {
            addCriterion("publisher_id =", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotEqualTo(Long value) {
            addCriterion("publisher_id <>", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThan(Long value) {
            addCriterion("publisher_id >", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThanOrEqualTo(Long value) {
            addCriterion("publisher_id >=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThan(Long value) {
            addCriterion("publisher_id <", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThanOrEqualTo(Long value) {
            addCriterion("publisher_id <=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIn(List<Long> values) {
            addCriterion("publisher_id in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotIn(List<Long> values) {
            addCriterion("publisher_id not in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdBetween(Long value1, Long value2) {
            addCriterion("publisher_id between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotBetween(Long value1, Long value2) {
            addCriterion("publisher_id not between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andRentTypeIsNull() {
            addCriterion("rent_type is null");
            return (Criteria) this;
        }

        public Criteria andRentTypeIsNotNull() {
            addCriterion("rent_type is not null");
            return (Criteria) this;
        }

        public Criteria andRentTypeEqualTo(Integer value) {
            addCriterion("rent_type =", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeNotEqualTo(Integer value) {
            addCriterion("rent_type <>", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeGreaterThan(Integer value) {
            addCriterion("rent_type >", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_type >=", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeLessThan(Integer value) {
            addCriterion("rent_type <", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeLessThanOrEqualTo(Integer value) {
            addCriterion("rent_type <=", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeIn(List<Integer> values) {
            addCriterion("rent_type in", values, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeNotIn(List<Integer> values) {
            addCriterion("rent_type not in", values, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeBetween(Integer value1, Integer value2) {
            addCriterion("rent_type between", value1, value2, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_type not between", value1, value2, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentLangIsNull() {
            addCriterion("rent_lang is null");
            return (Criteria) this;
        }

        public Criteria andRentLangIsNotNull() {
            addCriterion("rent_lang is not null");
            return (Criteria) this;
        }

        public Criteria andRentLangEqualTo(Integer value) {
            addCriterion("rent_lang =", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangNotEqualTo(Integer value) {
            addCriterion("rent_lang <>", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangGreaterThan(Integer value) {
            addCriterion("rent_lang >", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_lang >=", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangLessThan(Integer value) {
            addCriterion("rent_lang <", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangLessThanOrEqualTo(Integer value) {
            addCriterion("rent_lang <=", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangIn(List<Integer> values) {
            addCriterion("rent_lang in", values, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangNotIn(List<Integer> values) {
            addCriterion("rent_lang not in", values, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangBetween(Integer value1, Integer value2) {
            addCriterion("rent_lang between", value1, value2, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_lang not between", value1, value2, "rentLang");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNull() {
            addCriterion("is_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNotNull() {
            addCriterion("is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsValidEqualTo(Integer value) {
            addCriterion("is_valid =", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotEqualTo(Integer value) {
            addCriterion("is_valid <>", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThan(Integer value) {
            addCriterion("is_valid >", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_valid >=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThan(Integer value) {
            addCriterion("is_valid <", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThanOrEqualTo(Integer value) {
            addCriterion("is_valid <=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidIn(List<Integer> values) {
            addCriterion("is_valid in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotIn(List<Integer> values) {
            addCriterion("is_valid not in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidBetween(Integer value1, Integer value2) {
            addCriterion("is_valid between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotBetween(Integer value1, Integer value2) {
            addCriterion("is_valid not between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andRentStatusIsNull() {
            addCriterion("rent_status is null");
            return (Criteria) this;
        }

        public Criteria andRentStatusIsNotNull() {
            addCriterion("rent_status is not null");
            return (Criteria) this;
        }

        public Criteria andRentStatusEqualTo(Integer value) {
            addCriterion("rent_status =", value, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusNotEqualTo(Integer value) {
            addCriterion("rent_status <>", value, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusGreaterThan(Integer value) {
            addCriterion("rent_status >", value, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_status >=", value, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusLessThan(Integer value) {
            addCriterion("rent_status <", value, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusLessThanOrEqualTo(Integer value) {
            addCriterion("rent_status <=", value, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusIn(List<Integer> values) {
            addCriterion("rent_status in", values, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusNotIn(List<Integer> values) {
            addCriterion("rent_status not in", values, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusBetween(Integer value1, Integer value2) {
            addCriterion("rent_status between", value1, value2, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andRentStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_status not between", value1, value2, "rentStatus");
            return (Criteria) this;
        }

        public Criteria andIsAutoIsNull() {
            addCriterion("is_auto is null");
            return (Criteria) this;
        }

        public Criteria andIsAutoIsNotNull() {
            addCriterion("is_auto is not null");
            return (Criteria) this;
        }

        public Criteria andIsAutoEqualTo(Integer value) {
            addCriterion("is_auto =", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoNotEqualTo(Integer value) {
            addCriterion("is_auto <>", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoGreaterThan(Integer value) {
            addCriterion("is_auto >", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_auto >=", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoLessThan(Integer value) {
            addCriterion("is_auto <", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoLessThanOrEqualTo(Integer value) {
            addCriterion("is_auto <=", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoIn(List<Integer> values) {
            addCriterion("is_auto in", values, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoNotIn(List<Integer> values) {
            addCriterion("is_auto not in", values, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoBetween(Integer value1, Integer value2) {
            addCriterion("is_auto between", value1, value2, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoNotBetween(Integer value1, Integer value2) {
            addCriterion("is_auto not between", value1, value2, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsPaidIsNull() {
            addCriterion("is_paid is null");
            return (Criteria) this;
        }

        public Criteria andIsPaidIsNotNull() {
            addCriterion("is_paid is not null");
            return (Criteria) this;
        }

        public Criteria andIsPaidEqualTo(Integer value) {
            addCriterion("is_paid =", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidNotEqualTo(Integer value) {
            addCriterion("is_paid <>", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidGreaterThan(Integer value) {
            addCriterion("is_paid >", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_paid >=", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidLessThan(Integer value) {
            addCriterion("is_paid <", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidLessThanOrEqualTo(Integer value) {
            addCriterion("is_paid <=", value, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidIn(List<Integer> values) {
            addCriterion("is_paid in", values, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidNotIn(List<Integer> values) {
            addCriterion("is_paid not in", values, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidBetween(Integer value1, Integer value2) {
            addCriterion("is_paid between", value1, value2, "isPaid");
            return (Criteria) this;
        }

        public Criteria andIsPaidNotBetween(Integer value1, Integer value2) {
            addCriterion("is_paid not between", value1, value2, "isPaid");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyIsNull() {
            addCriterion("leave_buy is null");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyIsNotNull() {
            addCriterion("leave_buy is not null");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyEqualTo(Integer value) {
            addCriterion("leave_buy =", value, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyNotEqualTo(Integer value) {
            addCriterion("leave_buy <>", value, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyGreaterThan(Integer value) {
            addCriterion("leave_buy >", value, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyGreaterThanOrEqualTo(Integer value) {
            addCriterion("leave_buy >=", value, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyLessThan(Integer value) {
            addCriterion("leave_buy <", value, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyLessThanOrEqualTo(Integer value) {
            addCriterion("leave_buy <=", value, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyIn(List<Integer> values) {
            addCriterion("leave_buy in", values, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyNotIn(List<Integer> values) {
            addCriterion("leave_buy not in", values, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyBetween(Integer value1, Integer value2) {
            addCriterion("leave_buy between", value1, value2, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyNotBetween(Integer value1, Integer value2) {
            addCriterion("leave_buy not between", value1, value2, "leaveBuy");
            return (Criteria) this;
        }

        public Criteria andToOrderNumIsNull() {
            addCriterion("to_order_num is null");
            return (Criteria) this;
        }

        public Criteria andToOrderNumIsNotNull() {
            addCriterion("to_order_num is not null");
            return (Criteria) this;
        }

        public Criteria andToOrderNumEqualTo(String value) {
            addCriterion("to_order_num =", value, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumNotEqualTo(String value) {
            addCriterion("to_order_num <>", value, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumGreaterThan(String value) {
            addCriterion("to_order_num >", value, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("to_order_num >=", value, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumLessThan(String value) {
            addCriterion("to_order_num <", value, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumLessThanOrEqualTo(String value) {
            addCriterion("to_order_num <=", value, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumLike(String value) {
            addCriterion("to_order_num like", value, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumNotLike(String value) {
            addCriterion("to_order_num not like", value, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumIn(List<String> values) {
            addCriterion("to_order_num in", values, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumNotIn(List<String> values) {
            addCriterion("to_order_num not in", values, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumBetween(String value1, String value2) {
            addCriterion("to_order_num between", value1, value2, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andToOrderNumNotBetween(String value1, String value2) {
            addCriterion("to_order_num not between", value1, value2, "toOrderNum");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseIsNull() {
            addCriterion("base_license is null");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseIsNotNull() {
            addCriterion("base_license is not null");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseEqualTo(String value) {
            addCriterion("base_license =", value, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseNotEqualTo(String value) {
            addCriterion("base_license <>", value, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseGreaterThan(String value) {
            addCriterion("base_license >", value, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("base_license >=", value, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseLessThan(String value) {
            addCriterion("base_license <", value, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseLessThanOrEqualTo(String value) {
            addCriterion("base_license <=", value, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseLike(String value) {
            addCriterion("base_license like", value, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseNotLike(String value) {
            addCriterion("base_license not like", value, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseIn(List<String> values) {
            addCriterion("base_license in", values, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseNotIn(List<String> values) {
            addCriterion("base_license not in", values, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseBetween(String value1, String value2) {
            addCriterion("base_license between", value1, value2, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andBaseLicenseNotBetween(String value1, String value2) {
            addCriterion("base_license not between", value1, value2, "baseLicense");
            return (Criteria) this;
        }

        public Criteria andRentPriceIsNull() {
            addCriterion("rent_price is null");
            return (Criteria) this;
        }

        public Criteria andRentPriceIsNotNull() {
            addCriterion("rent_price is not null");
            return (Criteria) this;
        }

        public Criteria andRentPriceEqualTo(BigDecimal value) {
            addCriterion("rent_price =", value, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceNotEqualTo(BigDecimal value) {
            addCriterion("rent_price <>", value, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceGreaterThan(BigDecimal value) {
            addCriterion("rent_price >", value, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("rent_price >=", value, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceLessThan(BigDecimal value) {
            addCriterion("rent_price <", value, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("rent_price <=", value, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceIn(List<BigDecimal> values) {
            addCriterion("rent_price in", values, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceNotIn(List<BigDecimal> values) {
            addCriterion("rent_price not in", values, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("rent_price between", value1, value2, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andRentPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("rent_price not between", value1, value2, "rentPrice");
            return (Criteria) this;
        }

        public Criteria andTotalFeeIsNull() {
            addCriterion("total_fee is null");
            return (Criteria) this;
        }

        public Criteria andTotalFeeIsNotNull() {
            addCriterion("total_fee is not null");
            return (Criteria) this;
        }

        public Criteria andTotalFeeEqualTo(BigDecimal value) {
            addCriterion("total_fee =", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeNotEqualTo(BigDecimal value) {
            addCriterion("total_fee <>", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeGreaterThan(BigDecimal value) {
            addCriterion("total_fee >", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_fee >=", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeLessThan(BigDecimal value) {
            addCriterion("total_fee <", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_fee <=", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeIn(List<BigDecimal> values) {
            addCriterion("total_fee in", values, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeNotIn(List<BigDecimal> values) {
            addCriterion("total_fee not in", values, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_fee between", value1, value2, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_fee not between", value1, value2, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsIsNull() {
            addCriterion("total_months is null");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsIsNotNull() {
            addCriterion("total_months is not null");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsEqualTo(Integer value) {
            addCriterion("total_months =", value, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsNotEqualTo(Integer value) {
            addCriterion("total_months <>", value, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsGreaterThan(Integer value) {
            addCriterion("total_months >", value, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_months >=", value, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsLessThan(Integer value) {
            addCriterion("total_months <", value, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsLessThanOrEqualTo(Integer value) {
            addCriterion("total_months <=", value, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsIn(List<Integer> values) {
            addCriterion("total_months in", values, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsNotIn(List<Integer> values) {
            addCriterion("total_months not in", values, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsBetween(Integer value1, Integer value2) {
            addCriterion("total_months between", value1, value2, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andTotalMonthsNotBetween(Integer value1, Integer value2) {
            addCriterion("total_months not between", value1, value2, "totalMonths");
            return (Criteria) this;
        }

        public Criteria andOrderFromIsNull() {
            addCriterion("order_from is null");
            return (Criteria) this;
        }

        public Criteria andOrderFromIsNotNull() {
            addCriterion("order_from is not null");
            return (Criteria) this;
        }

        public Criteria andOrderFromEqualTo(Integer value) {
            addCriterion("order_from =", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromNotEqualTo(Integer value) {
            addCriterion("order_from <>", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromGreaterThan(Integer value) {
            addCriterion("order_from >", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_from >=", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromLessThan(Integer value) {
            addCriterion("order_from <", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromLessThanOrEqualTo(Integer value) {
            addCriterion("order_from <=", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromIn(List<Integer> values) {
            addCriterion("order_from in", values, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromNotIn(List<Integer> values) {
            addCriterion("order_from not in", values, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromBetween(Integer value1, Integer value2) {
            addCriterion("order_from between", value1, value2, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromNotBetween(Integer value1, Integer value2) {
            addCriterion("order_from not between", value1, value2, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andBeginAtIsNull() {
            addCriterion("begin_at is null");
            return (Criteria) this;
        }

        public Criteria andBeginAtIsNotNull() {
            addCriterion("begin_at is not null");
            return (Criteria) this;
        }

        public Criteria andBeginAtEqualTo(Date value) {
            addCriterion("begin_at =", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtNotEqualTo(Date value) {
            addCriterion("begin_at <>", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtGreaterThan(Date value) {
            addCriterion("begin_at >", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtGreaterThanOrEqualTo(Date value) {
            addCriterion("begin_at >=", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtLessThan(Date value) {
            addCriterion("begin_at <", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtLessThanOrEqualTo(Date value) {
            addCriterion("begin_at <=", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtIn(List<Date> values) {
            addCriterion("begin_at in", values, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtNotIn(List<Date> values) {
            addCriterion("begin_at not in", values, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtBetween(Date value1, Date value2) {
            addCriterion("begin_at between", value1, value2, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtNotBetween(Date value1, Date value2) {
            addCriterion("begin_at not between", value1, value2, "beginAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtIsNull() {
            addCriterion("leave_at is null");
            return (Criteria) this;
        }

        public Criteria andLeaveAtIsNotNull() {
            addCriterion("leave_at is not null");
            return (Criteria) this;
        }

        public Criteria andLeaveAtEqualTo(Date value) {
            addCriterion("leave_at =", value, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtNotEqualTo(Date value) {
            addCriterion("leave_at <>", value, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtGreaterThan(Date value) {
            addCriterion("leave_at >", value, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtGreaterThanOrEqualTo(Date value) {
            addCriterion("leave_at >=", value, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtLessThan(Date value) {
            addCriterion("leave_at <", value, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtLessThanOrEqualTo(Date value) {
            addCriterion("leave_at <=", value, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtIn(List<Date> values) {
            addCriterion("leave_at in", values, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtNotIn(List<Date> values) {
            addCriterion("leave_at not in", values, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtBetween(Date value1, Date value2) {
            addCriterion("leave_at between", value1, value2, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveAtNotBetween(Date value1, Date value2) {
            addCriterion("leave_at not between", value1, value2, "leaveAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtIsNull() {
            addCriterion("leave_buy_at is null");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtIsNotNull() {
            addCriterion("leave_buy_at is not null");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtEqualTo(Date value) {
            addCriterion("leave_buy_at =", value, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtNotEqualTo(Date value) {
            addCriterion("leave_buy_at <>", value, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtGreaterThan(Date value) {
            addCriterion("leave_buy_at >", value, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtGreaterThanOrEqualTo(Date value) {
            addCriterion("leave_buy_at >=", value, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtLessThan(Date value) {
            addCriterion("leave_buy_at <", value, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtLessThanOrEqualTo(Date value) {
            addCriterion("leave_buy_at <=", value, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtIn(List<Date> values) {
            addCriterion("leave_buy_at in", values, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtNotIn(List<Date> values) {
            addCriterion("leave_buy_at not in", values, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtBetween(Date value1, Date value2) {
            addCriterion("leave_buy_at between", value1, value2, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andLeaveBuyAtNotBetween(Date value1, Date value2) {
            addCriterion("leave_buy_at not between", value1, value2, "leaveBuyAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtIsNull() {
            addCriterion("expired_at is null");
            return (Criteria) this;
        }

        public Criteria andExpiredAtIsNotNull() {
            addCriterion("expired_at is not null");
            return (Criteria) this;
        }

        public Criteria andExpiredAtEqualTo(Date value) {
            addCriterion("expired_at =", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtNotEqualTo(Date value) {
            addCriterion("expired_at <>", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtGreaterThan(Date value) {
            addCriterion("expired_at >", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtGreaterThanOrEqualTo(Date value) {
            addCriterion("expired_at >=", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtLessThan(Date value) {
            addCriterion("expired_at <", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtLessThanOrEqualTo(Date value) {
            addCriterion("expired_at <=", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtIn(List<Date> values) {
            addCriterion("expired_at in", values, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtNotIn(List<Date> values) {
            addCriterion("expired_at not in", values, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtBetween(Date value1, Date value2) {
            addCriterion("expired_at between", value1, value2, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtNotBetween(Date value1, Date value2) {
            addCriterion("expired_at not between", value1, value2, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}