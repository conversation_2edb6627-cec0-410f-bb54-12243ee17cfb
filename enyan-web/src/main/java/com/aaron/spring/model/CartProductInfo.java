package com.aaron.spring.model;

import com.aaron.spring.common.Constant;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *
 * 购物车产品明细
 * @Author: <PERSON>
 * @Date: 2018/2/23
 */
public class CartProductInfo implements Serializable {
	private static final long serialVersionUID = 8598048709083934011L;
	private ProductInfo productInfo;
	private BigDecimal quantity;
	private BigDecimal amount;

	private BigDecimal amountCny;
	private BigDecimal amountUsd;
    private BigDecimal amountHkd;

	public CartProductInfo() {
		this.quantity = new BigDecimal("0");
	}

	public ProductInfo getProductInfo() {
		return productInfo;
	}
	public void setProductInfo(ProductInfo productInfo) {
		this.productInfo = productInfo;
	}
	/**
	 * <p>设置产品的满几件折扣,使用真是的Middle值乘以折扣</p>
	 * @param discountValue
	 * @return: void
	 * @since : 2020-08-05
	 */
	public void resetProductCumulateDiscount(Integer discountValue){
		if (discountValue >= 100){
			productInfo.setPriceHKDDiscount(productInfo.getPriceHKDMiddle());
			return;
		}
		BigDecimal price = productInfo.getPriceHKDMiddle().multiply(new BigDecimal(discountValue)).divide(Constant.VALUE_100, Constant.NUM_SCALE_2, RoundingMode.HALF_UP);
		productInfo.setPriceHKDDiscount(price);
	}

	public BigDecimal getAmount() {
		amount = this.productInfo.getPrice().multiply(quantity);
		return amount;
	}
	public BigDecimal getAmountCny() {
		if (this.productInfo.isDiscountSingleIsValid()){
			return this.productInfo.getPriceCnyDiscount().multiply(quantity);
		}
		return this.productInfo.getPriceCny().multiply(quantity);
	}

	public BigDecimal getAmountUsd() {
		if (this.productInfo.isDiscountSingleIsValid()){
			return this.productInfo.getPriceUSDDiscount().multiply(quantity);
		}
		return this.productInfo.getPriceUsd().multiply(quantity);
	}

	public BigDecimal getAmountHkd() {
		if (this.productInfo.isDiscountSingleIsValid()){
			return this.productInfo.getPriceHKDDiscount().multiply(quantity);
		}
		return this.productInfo.getPriceHkd().multiply(quantity);
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public void setAmountCny(BigDecimal amountCny) {
		this.amountCny = amountCny;
	}

	public void setAmountUsd(BigDecimal amountUsd) {
		this.amountUsd = amountUsd;
	}

    public void setAmountHkd(BigDecimal amountHkd) {
        this.amountHkd = amountHkd;
    }
}
