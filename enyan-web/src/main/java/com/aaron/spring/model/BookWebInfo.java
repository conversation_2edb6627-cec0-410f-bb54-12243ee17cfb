package com.aaron.spring.model;

import com.aaron.common.NameAndValue;
import com.aaron.common.NameAndValueDTO;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/5/16
 * @Modified By:
 */
public class BookWebInfo implements Serializable {
	@Serial
	private static final long serialVersionUID = 5691145823216840945L;
	/**
	 * 纸板书链接
	 * */
	@JSONField(name = "values")
	private List<NameAndValue> salePapers;

	/**
	 * 电子书版本
	 * */
	@JSONField(name = "v")
	private List<NameAndValue> versions;

	@JSONField(serialize = false)
	private String salePapersJson;

	@JSONField(serialize = false)
	private String versionsJson;

	public List<NameAndValue> getSalePapers() {
		return salePapers;
	}

	public void setSalePapers(List<NameAndValue> salePapers) {
		this.salePapers = salePapers;
	}

	public List<NameAndValue> getVersions() {
		return versions;
	}

	public void setVersions(List<NameAndValue> versions) {
		this.versions = versions;
	}

	public String getSalePapersJson() {
		if (null == this.salePapers || this.salePapers.isEmpty() == true){
			return null;
		}
		NameAndValueDTO obj = new NameAndValueDTO();
		obj.setValues(this.salePapers);
		return JSON.toJSONString(obj);
	}

	public void setSalePapersJson(String salePapersJson) {
		this.salePapersJson = salePapersJson;
	}

	public String getVersionsJson() {
		if (null == this.versions || this.versions.isEmpty() == true){
			return null;
		}
		NameAndValueDTO obj = new NameAndValueDTO();
		obj.setValues(this.versions);
		return JSON.toJSONString(obj);
	}

	public void setVersionsJson(String versionsJson) {
		this.versionsJson = versionsJson;
	}
}
