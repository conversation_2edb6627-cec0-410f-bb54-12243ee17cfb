package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Date;

public class EnyanSubscription extends BaseDTO{
    private static final long serialVersionUID = -8210085001208078316L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long dataId;

    private String email;

    private Date createAt;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }
}