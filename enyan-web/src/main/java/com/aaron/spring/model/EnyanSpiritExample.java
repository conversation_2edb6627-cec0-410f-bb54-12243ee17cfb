package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.List;

public class EnyanSpiritExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanSpiritExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSpiritIdIsNull() {
            addCriterion("spirit_id is null");
            return (Criteria) this;
        }

        public Criteria andSpiritIdIsNotNull() {
            addCriterion("spirit_id is not null");
            return (Criteria) this;
        }

        public Criteria andSpiritIdEqualTo(Long value) {
            addCriterion("spirit_id =", value, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdNotEqualTo(Long value) {
            addCriterion("spirit_id <>", value, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdGreaterThan(Long value) {
            addCriterion("spirit_id >", value, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdGreaterThanOrEqualTo(Long value) {
            addCriterion("spirit_id >=", value, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdLessThan(Long value) {
            addCriterion("spirit_id <", value, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdLessThanOrEqualTo(Long value) {
            addCriterion("spirit_id <=", value, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdIn(List<Long> values) {
            addCriterion("spirit_id in", values, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdNotIn(List<Long> values) {
            addCriterion("spirit_id not in", values, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdBetween(Long value1, Long value2) {
            addCriterion("spirit_id between", value1, value2, "spiritId");
            return (Criteria) this;
        }

        public Criteria andSpiritIdNotBetween(Long value1, Long value2) {
            addCriterion("spirit_id not between", value1, value2, "spiritId");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNull() {
            addCriterion("book_id is null");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNotNull() {
            addCriterion("book_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookIdEqualTo(Long value) {
            addCriterion("book_id =", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotEqualTo(Long value) {
            addCriterion("book_id <>", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThan(Long value) {
            addCriterion("book_id >", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThanOrEqualTo(Long value) {
            addCriterion("book_id >=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThan(Long value) {
            addCriterion("book_id <", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThanOrEqualTo(Long value) {
            addCriterion("book_id <=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdIn(List<Long> values) {
            addCriterion("book_id in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotIn(List<Long> values) {
            addCriterion("book_id not in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdBetween(Long value1, Long value2) {
            addCriterion("book_id between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotBetween(Long value1, Long value2) {
            addCriterion("book_id not between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andAuthorIsNull() {
            addCriterion("author is null");
            return (Criteria) this;
        }

        public Criteria andAuthorIsNotNull() {
            addCriterion("author is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorEqualTo(String value) {
            addCriterion("author =", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotEqualTo(String value) {
            addCriterion("author <>", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorGreaterThan(String value) {
            addCriterion("author >", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorGreaterThanOrEqualTo(String value) {
            addCriterion("author >=", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLessThan(String value) {
            addCriterion("author <", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLessThanOrEqualTo(String value) {
            addCriterion("author <=", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLike(String value) {
            addCriterion("author like", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotLike(String value) {
            addCriterion("author not like", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorIn(List<String> values) {
            addCriterion("author in", values, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotIn(List<String> values) {
            addCriterion("author not in", values, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorBetween(String value1, String value2) {
            addCriterion("author between", value1, value2, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotBetween(String value1, String value2) {
            addCriterion("author not between", value1, value2, "author");
            return (Criteria) this;
        }

        public Criteria andSloganIsNull() {
            addCriterion("slogan is null");
            return (Criteria) this;
        }

        public Criteria andSloganIsNotNull() {
            addCriterion("slogan is not null");
            return (Criteria) this;
        }

        public Criteria andSloganEqualTo(String value) {
            addCriterion("slogan =", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganNotEqualTo(String value) {
            addCriterion("slogan <>", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganGreaterThan(String value) {
            addCriterion("slogan >", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganGreaterThanOrEqualTo(String value) {
            addCriterion("slogan >=", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganLessThan(String value) {
            addCriterion("slogan <", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganLessThanOrEqualTo(String value) {
            addCriterion("slogan <=", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganLike(String value) {
            addCriterion("slogan like", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganNotLike(String value) {
            addCriterion("slogan not like", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganIn(List<String> values) {
            addCriterion("slogan in", values, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganNotIn(List<String> values) {
            addCriterion("slogan not in", values, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganBetween(String value1, String value2) {
            addCriterion("slogan between", value1, value2, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganNotBetween(String value1, String value2) {
            addCriterion("slogan not between", value1, value2, "slogan");
            return (Criteria) this;
        }

        public Criteria andBookVersionIsNull() {
            addCriterion("book_version is null");
            return (Criteria) this;
        }

        public Criteria andBookVersionIsNotNull() {
            addCriterion("book_version is not null");
            return (Criteria) this;
        }

        public Criteria andBookVersionEqualTo(String value) {
            addCriterion("book_version =", value, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionNotEqualTo(String value) {
            addCriterion("book_version <>", value, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionGreaterThan(String value) {
            addCriterion("book_version >", value, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionGreaterThanOrEqualTo(String value) {
            addCriterion("book_version >=", value, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionLessThan(String value) {
            addCriterion("book_version <", value, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionLessThanOrEqualTo(String value) {
            addCriterion("book_version <=", value, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionLike(String value) {
            addCriterion("book_version like", value, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionNotLike(String value) {
            addCriterion("book_version not like", value, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionIn(List<String> values) {
            addCriterion("book_version in", values, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionNotIn(List<String> values) {
            addCriterion("book_version not in", values, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionBetween(String value1, String value2) {
            addCriterion("book_version between", value1, value2, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andBookVersionNotBetween(String value1, String value2) {
            addCriterion("book_version not between", value1, value2, "bookVersion");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionIsNull() {
            addCriterion("author_description is null");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionIsNotNull() {
            addCriterion("author_description is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionEqualTo(String value) {
            addCriterion("author_description =", value, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionNotEqualTo(String value) {
            addCriterion("author_description <>", value, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionGreaterThan(String value) {
            addCriterion("author_description >", value, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("author_description >=", value, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionLessThan(String value) {
            addCriterion("author_description <", value, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionLessThanOrEqualTo(String value) {
            addCriterion("author_description <=", value, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionLike(String value) {
            addCriterion("author_description like", value, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionNotLike(String value) {
            addCriterion("author_description not like", value, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionIn(List<String> values) {
            addCriterion("author_description in", values, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionNotIn(List<String> values) {
            addCriterion("author_description not in", values, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionBetween(String value1, String value2) {
            addCriterion("author_description between", value1, value2, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andAuthorDescriptionNotBetween(String value1, String value2) {
            addCriterion("author_description not between", value1, value2, "authorDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionIsNull() {
            addCriterion("book_description is null");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionIsNotNull() {
            addCriterion("book_description is not null");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionEqualTo(String value) {
            addCriterion("book_description =", value, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionNotEqualTo(String value) {
            addCriterion("book_description <>", value, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionGreaterThan(String value) {
            addCriterion("book_description >", value, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("book_description >=", value, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionLessThan(String value) {
            addCriterion("book_description <", value, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionLessThanOrEqualTo(String value) {
            addCriterion("book_description <=", value, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionLike(String value) {
            addCriterion("book_description like", value, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionNotLike(String value) {
            addCriterion("book_description not like", value, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionIn(List<String> values) {
            addCriterion("book_description in", values, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionNotIn(List<String> values) {
            addCriterion("book_description not in", values, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionBetween(String value1, String value2) {
            addCriterion("book_description between", value1, value2, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andBookDescriptionNotBetween(String value1, String value2) {
            addCriterion("book_description not between", value1, value2, "bookDescription");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeIsNull() {
            addCriterion("language_type is null");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeIsNotNull() {
            addCriterion("language_type is not null");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeEqualTo(Integer value) {
            addCriterion("language_type =", value, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeNotEqualTo(Integer value) {
            addCriterion("language_type <>", value, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeGreaterThan(Integer value) {
            addCriterion("language_type >", value, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("language_type >=", value, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeLessThan(Integer value) {
            addCriterion("language_type <", value, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeLessThanOrEqualTo(Integer value) {
            addCriterion("language_type <=", value, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeIn(List<Integer> values) {
            addCriterion("language_type in", values, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeNotIn(List<Integer> values) {
            addCriterion("language_type not in", values, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeBetween(Integer value1, Integer value2) {
            addCriterion("language_type between", value1, value2, "languageType");
            return (Criteria) this;
        }

        public Criteria andLanguageTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("language_type not between", value1, value2, "languageType");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIsNull() {
            addCriterion("recommended_order is null");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIsNotNull() {
            addCriterion("recommended_order is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderEqualTo(Integer value) {
            addCriterion("recommended_order =", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotEqualTo(Integer value) {
            addCriterion("recommended_order <>", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderGreaterThan(Integer value) {
            addCriterion("recommended_order >", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("recommended_order >=", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderLessThan(Integer value) {
            addCriterion("recommended_order <", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderLessThanOrEqualTo(Integer value) {
            addCriterion("recommended_order <=", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIn(List<Integer> values) {
            addCriterion("recommended_order in", values, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotIn(List<Integer> values) {
            addCriterion("recommended_order not in", values, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderBetween(Integer value1, Integer value2) {
            addCriterion("recommended_order between", value1, value2, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("recommended_order not between", value1, value2, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andShelfStatusIsNull() {
            addCriterion("shelf_status is null");
            return (Criteria) this;
        }

        public Criteria andShelfStatusIsNotNull() {
            addCriterion("shelf_status is not null");
            return (Criteria) this;
        }

        public Criteria andShelfStatusEqualTo(Byte value) {
            addCriterion("shelf_status =", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusNotEqualTo(Byte value) {
            addCriterion("shelf_status <>", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusGreaterThan(Byte value) {
            addCriterion("shelf_status >", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("shelf_status >=", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusLessThan(Byte value) {
            addCriterion("shelf_status <", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusLessThanOrEqualTo(Byte value) {
            addCriterion("shelf_status <=", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusIn(List<Byte> values) {
            addCriterion("shelf_status in", values, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusNotIn(List<Byte> values) {
            addCriterion("shelf_status not in", values, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusBetween(Byte value1, Byte value2) {
            addCriterion("shelf_status between", value1, value2, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("shelf_status not between", value1, value2, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andDaysIsNull() {
            addCriterion("days is null");
            return (Criteria) this;
        }

        public Criteria andDaysIsNotNull() {
            addCriterion("days is not null");
            return (Criteria) this;
        }

        public Criteria andDaysEqualTo(Integer value) {
            addCriterion("days =", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotEqualTo(Integer value) {
            addCriterion("days <>", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysGreaterThan(Integer value) {
            addCriterion("days >", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("days >=", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLessThan(Integer value) {
            addCriterion("days <", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLessThanOrEqualTo(Integer value) {
            addCriterion("days <=", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysIn(List<Integer> values) {
            addCriterion("days in", values, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotIn(List<Integer> values) {
            addCriterion("days not in", values, "days");
            return (Criteria) this;
        }

        public Criteria andDaysBetween(Integer value1, Integer value2) {
            addCriterion("days between", value1, value2, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("days not between", value1, value2, "days");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlIsNull() {
            addCriterion("book_img_url is null");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlIsNotNull() {
            addCriterion("book_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlEqualTo(String value) {
            addCriterion("book_img_url =", value, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlNotEqualTo(String value) {
            addCriterion("book_img_url <>", value, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlGreaterThan(String value) {
            addCriterion("book_img_url >", value, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("book_img_url >=", value, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlLessThan(String value) {
            addCriterion("book_img_url <", value, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlLessThanOrEqualTo(String value) {
            addCriterion("book_img_url <=", value, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlLike(String value) {
            addCriterion("book_img_url like", value, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlNotLike(String value) {
            addCriterion("book_img_url not like", value, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlIn(List<String> values) {
            addCriterion("book_img_url in", values, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlNotIn(List<String> values) {
            addCriterion("book_img_url not in", values, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlBetween(String value1, String value2) {
            addCriterion("book_img_url between", value1, value2, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andBookImgUrlNotBetween(String value1, String value2) {
            addCriterion("book_img_url not between", value1, value2, "bookImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlIsNull() {
            addCriterion("info_img_url is null");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlIsNotNull() {
            addCriterion("info_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlEqualTo(String value) {
            addCriterion("info_img_url =", value, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlNotEqualTo(String value) {
            addCriterion("info_img_url <>", value, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlGreaterThan(String value) {
            addCriterion("info_img_url >", value, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("info_img_url >=", value, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlLessThan(String value) {
            addCriterion("info_img_url <", value, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlLessThanOrEqualTo(String value) {
            addCriterion("info_img_url <=", value, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlLike(String value) {
            addCriterion("info_img_url like", value, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlNotLike(String value) {
            addCriterion("info_img_url not like", value, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlIn(List<String> values) {
            addCriterion("info_img_url in", values, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlNotIn(List<String> values) {
            addCriterion("info_img_url not in", values, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlBetween(String value1, String value2) {
            addCriterion("info_img_url between", value1, value2, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andInfoImgUrlNotBetween(String value1, String value2) {
            addCriterion("info_img_url not between", value1, value2, "infoImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlIsNull() {
            addCriterion("to_buy_img_url is null");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlIsNotNull() {
            addCriterion("to_buy_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlEqualTo(String value) {
            addCriterion("to_buy_img_url =", value, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlNotEqualTo(String value) {
            addCriterion("to_buy_img_url <>", value, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlGreaterThan(String value) {
            addCriterion("to_buy_img_url >", value, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("to_buy_img_url >=", value, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlLessThan(String value) {
            addCriterion("to_buy_img_url <", value, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlLessThanOrEqualTo(String value) {
            addCriterion("to_buy_img_url <=", value, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlLike(String value) {
            addCriterion("to_buy_img_url like", value, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlNotLike(String value) {
            addCriterion("to_buy_img_url not like", value, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlIn(List<String> values) {
            addCriterion("to_buy_img_url in", values, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlNotIn(List<String> values) {
            addCriterion("to_buy_img_url not in", values, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlBetween(String value1, String value2) {
            addCriterion("to_buy_img_url between", value1, value2, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andToBuyImgUrlNotBetween(String value1, String value2) {
            addCriterion("to_buy_img_url not between", value1, value2, "toBuyImgUrl");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNull() {
            addCriterion("file_name is null");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNotNull() {
            addCriterion("file_name is not null");
            return (Criteria) this;
        }

        public Criteria andFileNameEqualTo(String value) {
            addCriterion("file_name =", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotEqualTo(String value) {
            addCriterion("file_name <>", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThan(String value) {
            addCriterion("file_name >", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThanOrEqualTo(String value) {
            addCriterion("file_name >=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThan(String value) {
            addCriterion("file_name <", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThanOrEqualTo(String value) {
            addCriterion("file_name <=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLike(String value) {
            addCriterion("file_name like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotLike(String value) {
            addCriterion("file_name not like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameIn(List<String> values) {
            addCriterion("file_name in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotIn(List<String> values) {
            addCriterion("file_name not in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameBetween(String value1, String value2) {
            addCriterion("file_name between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotBetween(String value1, String value2) {
            addCriterion("file_name not between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andCopyrightIsNull() {
            addCriterion("copyright is null");
            return (Criteria) this;
        }

        public Criteria andCopyrightIsNotNull() {
            addCriterion("copyright is not null");
            return (Criteria) this;
        }

        public Criteria andCopyrightEqualTo(String value) {
            addCriterion("copyright =", value, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightNotEqualTo(String value) {
            addCriterion("copyright <>", value, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightGreaterThan(String value) {
            addCriterion("copyright >", value, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightGreaterThanOrEqualTo(String value) {
            addCriterion("copyright >=", value, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightLessThan(String value) {
            addCriterion("copyright <", value, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightLessThanOrEqualTo(String value) {
            addCriterion("copyright <=", value, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightLike(String value) {
            addCriterion("copyright like", value, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightNotLike(String value) {
            addCriterion("copyright not like", value, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightIn(List<String> values) {
            addCriterion("copyright in", values, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightNotIn(List<String> values) {
            addCriterion("copyright not in", values, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightBetween(String value1, String value2) {
            addCriterion("copyright between", value1, value2, "copyright");
            return (Criteria) this;
        }

        public Criteria andCopyrightNotBetween(String value1, String value2) {
            addCriterion("copyright not between", value1, value2, "copyright");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}