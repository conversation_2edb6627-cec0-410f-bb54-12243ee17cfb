package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

public class EnyanRent extends BaseDTO{
    @Serial
    private static final long serialVersionUID = 1115126836338702346L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long rentId;

    private String orderNum;

    private String userEmail;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long publisherId;

    private Integer rentType;

    private Integer rentLang;

    private Integer isValid;

    private Integer rentStatus;

    private Integer isAuto;

    private Integer isPaid;

    private Integer leaveBuy;

    private String toOrderNum;

    private String baseLicense;

    private BigDecimal rentPrice;

    private BigDecimal totalFee;

    private Integer totalMonths;

    private Integer orderFrom;

    private Date beginAt;

    private Date leaveAt;

    private Date leaveBuyAt;

    private Date expiredAt;

    private Date createAt;

    private Integer isDeleted;

    private String rentDetail;

    /**
     * 新的过期时间，用于临时存储
     * */
    private Date newExpiredAt;

    /**
     * detail转成RentInfo
     * */
    private RentInfo rentInfo; //detail转成RentInfo

    public Long getRentId() {
        return rentId;
    }

    public void setRentId(Long rentId) {
        this.rentId = rentId;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum == null ? null : orderNum.trim();
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail == null ? null : userEmail.trim();
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public Integer getRentType() {
        return rentType;
    }

    public void setRentType(Integer rentType) {
        this.rentType = rentType;
    }

    public Integer getRentLang() {
        return rentLang;
    }

    public void setRentLang(Integer rentLang) {
        this.rentLang = rentLang;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getRentStatus() {
        return rentStatus;
    }

    public void setRentStatus(Integer rentStatus) {
        this.rentStatus = rentStatus;
    }

    public Integer getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }

    public Integer getIsPaid() {
        return isPaid;
    }

    public void setIsPaid(Integer isPaid) {
        this.isPaid = isPaid;
    }

    public Integer getLeaveBuy() {
        return leaveBuy;
    }

    public void setLeaveBuy(Integer leaveBuy) {
        this.leaveBuy = leaveBuy;
    }

    public String getToOrderNum() {
        return toOrderNum;
    }

    public void setToOrderNum(String toOrderNum) {
        this.toOrderNum = toOrderNum == null ? null : toOrderNum.trim();
    }

    public String getBaseLicense() {
        return baseLicense;
    }

    public void setBaseLicense(String baseLicense) {
        this.baseLicense = baseLicense == null ? null : baseLicense.trim();
    }

    public BigDecimal getRentPrice() {
        return rentPrice;
    }

    public void setRentPrice(BigDecimal rentPrice) {
        this.rentPrice = rentPrice;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getTotalMonths() {
        return totalMonths;
    }

    public void setTotalMonths(Integer totalMonths) {
        this.totalMonths = totalMonths;
    }

    public Integer getOrderFrom() {
        return orderFrom;
    }

    public void setOrderFrom(Integer orderFrom) {
        this.orderFrom = orderFrom;
    }

    public Date getBeginAt() {
        return beginAt;
    }

    public void setBeginAt(Date beginAt) {
        this.beginAt = beginAt;
    }

    public Date getLeaveAt() {
        return leaveAt;
    }

    public void setLeaveAt(Date leaveAt) {
        this.leaveAt = leaveAt;
    }

    public Date getLeaveBuyAt() {
        return leaveBuyAt;
    }

    public void setLeaveBuyAt(Date leaveBuyAt) {
        this.leaveBuyAt = leaveBuyAt;
    }

    public Date getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(Date expiredAt) {
        this.expiredAt = expiredAt;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getRentDetail() {
        return rentDetail;
    }

    public void setRentDetail(String rentDetail) {
        this.rentDetail = rentDetail == null ? null : rentDetail.trim();
    }

    public Date getNewExpiredAt() {
        return newExpiredAt;
    }

    public void setNewExpiredAt(Date newExpiredAt) {
        this.newExpiredAt = newExpiredAt;
    }

    public RentInfo getRentInfo() {
        return rentInfo;
    }

    public void setRentInfo(RentInfo rentInfo) {
        this.rentInfo = rentInfo;
    }
}