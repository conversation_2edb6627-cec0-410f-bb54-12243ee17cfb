package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Date;

public class EnyanBookCost extends BaseDTO{

    private static final long serialVersionUID = 321439330512299164L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookCostId;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookId;

    private String bookTitle;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long publisherId;

    private Integer bookCost;

    private Date purchasedAt;

    private Byte isCounted;

    public Long getBookCostId() {
        return bookCostId;
    }

    public void setBookCostId(Long bookCostId) {
        this.bookCostId = bookCostId;
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public void setBookTitle(String bookTitle) {
        this.bookTitle = bookTitle == null ? null : bookTitle.trim();
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public Integer getBookCost() {
        return bookCost;
    }

    public void setBookCost(Integer bookCost) {
        this.bookCost = bookCost;
    }

    public Date getPurchasedAt() {
        return purchasedAt;
    }

    public void setPurchasedAt(Date purchasedAt) {
        this.purchasedAt = purchasedAt;
    }

    public Byte getIsCounted() {
        return isCounted;
    }

    public void setIsCounted(Byte isCounted) {
        this.isCounted = isCounted;
    }
}