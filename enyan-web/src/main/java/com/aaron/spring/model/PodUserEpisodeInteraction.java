package com.aaron.spring.model;

import com.aaron.common.OrderObj;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PodUserEpisodeInteraction {
    private Long interactionId;
    private Long userId;
    private String userEmail;
    private Long episodeId;
    /**
     * 是否点赞(当前不用了，需要变为匿名点赞)
     */
    private Integer isLiked;
    private Integer playbackProgressSeconds;  // 播放进度 (秒) (当前不用了，只在前端记录播放进度)
    /**
     * 累计播放时长 (秒) （这里不用了，数据要累积在单集上面）
     */
    private Integer cumulativePlaybackSeconds;
    private Date lastPlayedAt;
    private Integer isCompleted;
    private Date downloadedAt;
    private Date createdAt;
    private List<OrderObj> orderObjList = new ArrayList<>();

    // Getters and Setters
    public Long getInteractionId() {
        return interactionId;
    }

    public void setInteractionId(Long interactionId) {
        this.interactionId = interactionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail == null ? null : userEmail.trim();
    }

    public Long getEpisodeId() {
        return episodeId;
    }

    public void setEpisodeId(Long episodeId) {
        this.episodeId = episodeId;
    }

    public Integer getIsLiked() {
        return isLiked;
    }

    public void setIsLiked(Integer isLiked) {
        this.isLiked = isLiked;
    }

    public Integer getPlaybackProgressSeconds() {
        return playbackProgressSeconds;
    }

    public void setPlaybackProgressSeconds(Integer playbackProgressSeconds) {
        this.playbackProgressSeconds = playbackProgressSeconds;
    }

    public Integer getCumulativePlaybackSeconds() {
        return cumulativePlaybackSeconds;
    }

    public void setCumulativePlaybackSeconds(Integer cumulativePlaybackSeconds) {
        this.cumulativePlaybackSeconds = cumulativePlaybackSeconds;
    }

    public Date getLastPlayedAt() {
        return lastPlayedAt;
    }

    public void setLastPlayedAt(Date lastPlayedAt) {
        this.lastPlayedAt = lastPlayedAt;
    }

    public Integer getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Integer isCompleted) {
        this.isCompleted = isCompleted;
    }

    public Date getDownloadedAt() {
        return downloadedAt;
    }

    public void setDownloadedAt(Date downloadedAt) {
        this.downloadedAt = downloadedAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public List<OrderObj> getOrderObjList() {
        return orderObjList;
    }

    public void setOrderObjList(List<OrderObj> orderObjList) {
        this.orderObjList = orderObjList;
    }

    public void addOrder(OrderObj orderObj) {
        if (this.orderObjList == null) {
            this.orderObjList = new ArrayList<>();
        }
        this.orderObjList.add(orderObj);
    }
}
