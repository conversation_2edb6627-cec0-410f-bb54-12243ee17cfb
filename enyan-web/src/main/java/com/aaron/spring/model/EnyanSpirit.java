package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.aaron.spring.common.BitSetUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.apache.commons.lang3.StringUtils;

import java.util.BitSet;

public class EnyanSpirit extends BaseDTO{
    private static final long serialVersionUID = -2260307007549221604L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long spiritId;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookId;

    private String name;

    private String author;

    private String slogan;

    private String bookVersion;

    private String authorDescription;

    private String bookDescription;

    private Integer languageType;

    private Integer recommendedOrder;

    private Byte shelfStatus;

    private Integer days;

    private String bookImgUrl;

    private String infoImgUrl;

    private String toBuyImgUrl;

    private String fileName;

    private String copyright;

    private byte[] dataBitsBitSet;

    //不包括的天数(以 , 区隔，用于前端)
    private String daysExclude;

    private String[] bookIDs;

    private String dataBits;//灵修计划中排除的时间


    public Long getSpiritId() {
        return spiritId;
    }

    public void setSpiritId(Long spiritId) {
        this.spiritId = spiritId;
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author == null ? null : author.trim();
    }

    public String getSlogan() {
        return slogan;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan == null ? null : slogan.trim();
    }

    public String getBookVersion() {
        return bookVersion;
    }

    public void setBookVersion(String bookVersion) {
        this.bookVersion = bookVersion == null ? null : bookVersion.trim();
    }

    public String getAuthorDescription() {
        return authorDescription;
    }

    public void setAuthorDescription(String authorDescription) {
        this.authorDescription = authorDescription == null ? null : authorDescription.trim();
    }

    public String getBookDescription() {
        return bookDescription;
    }

    public void setBookDescription(String bookDescription) {
        this.bookDescription = bookDescription == null ? null : bookDescription.trim();
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public Integer getRecommendedOrder() {
        return recommendedOrder;
    }

    public void setRecommendedOrder(Integer recommendedOrder) {
        this.recommendedOrder = recommendedOrder;
    }

    public Byte getShelfStatus() {
        return shelfStatus;
    }

    public void setShelfStatus(Byte shelfStatus) {
        this.shelfStatus = shelfStatus;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public String getBookImgUrl() {
        return bookImgUrl;
    }

    public void setBookImgUrl(String bookImgUrl) {
        this.bookImgUrl = bookImgUrl == null ? null : bookImgUrl.trim();
    }

    public String getInfoImgUrl() {
        return infoImgUrl;
    }

    public void setInfoImgUrl(String infoImgUrl) {
        this.infoImgUrl = infoImgUrl == null ? null : infoImgUrl.trim();
    }

    public String getToBuyImgUrl() {
        return toBuyImgUrl;
    }

    public void setToBuyImgUrl(String toBuyImgUrl) {
        this.toBuyImgUrl = toBuyImgUrl == null ? null : toBuyImgUrl.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public String getCopyright() {
        return copyright;
    }

    public void setCopyright(String copyright) {
        this.copyright = copyright == null ? null : copyright.trim();
    }

    public byte[] getDataBitsBitSet() {
        return dataBitsBitSet;
    }

    public void setDataBitsBitSet(byte[] dataBitsBitSet) {
        this.dataBitsBitSet = dataBitsBitSet;
        if (null != dataBitsBitSet){
            BitSet bitSet = BitSet.valueOf(dataBitsBitSet);
            dataBits = BitSetUtil.encode(dataBitsBitSet);
            daysExclude = BitSetUtil.toJoinString(bitSet,",");
            //long[] array = bitSet.toLongArray();
            //String[] arrayString = Arrays.toString(bitSet.toLongArray());
            //int[] array = Arrays.asList(strings).stream().mapToInt(Integer::parseInt).toArray();
            //int[] array = Arrays.stream(strings).mapToInt(Integer::parseInt).toArray();
            //String[] arrayString = Arrays.stream(array).map(Long::toString).toArray();

            /*ArrayUtils.toStringArray(bitSet.toLongArray());
            String s = String.join(",",array);
            for (int i = 0; i < bitSet.length(); i++) {
                bitSet.get(i);
            }*/
        }
    }

    public String[] getBookIDs() {
        return bookIDs;
    }

    public void setBookIDs(String[] bookIDs) {
        this.bookIDs = bookIDs;
    }

    public String getDaysExclude() {
        return daysExclude;
    }

    public void setDaysExclude(String daysExclude) {
        this.daysExclude = daysExclude;
        if (StringUtils.isNotBlank(daysExclude)){
            String[] days = daysExclude.split(",");
            BitSet bitSet = new BitSet();
            for (String day: days){
                int newDay = Integer.parseInt(day);
                bitSet.set(newDay);
            }
            dataBitsBitSet = bitSet.toByteArray();
        }else {
            dataBitsBitSet = new byte[0];
        }
    }

    public String getDataBits() {
        return dataBits;
    }

    public void setDataBits(String dataBits) {
        this.dataBits = dataBits;
    }
}