package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serial;
import java.math.BigDecimal;

public class EnyanBookSet extends BaseDTO{
    @Serial
    private static final long serialVersionUID = 3102113873545370162L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long setId;

    private String setName;

    private String setNameTc;

    private String setNameEn;

    private String bannerUrl;

    private String setAbstract;

    private String bookWeb;

    private Integer discountValue;

    private Integer isDiscountValid;

    private BigDecimal price;

    private BigDecimal priceDiscount;

    private Integer isIndex;

    private Integer showOrder;

    private String bookCover;

    private Integer isValid;

    private Integer isShow;

    private Integer canAllBuy;

    private String[] bookIDs;

    private String[] bookIDsOld;

    private BookWebInfo bookWebInfo;

    private String bookWebSalePapers;

    private String bookWebVersions;

    public Long getSetId() {
        return setId;
    }

    public void setSetId(Long setId) {
        this.setId = setId;
    }

    public String getSetName() {
        return setName;
    }

    public void setSetName(String setName) {
        this.setName = setName == null ? null : setName.trim();
    }

    public String getSetNameTc() {
        return setNameTc;
    }

    public void setSetNameTc(String setNameTc) {
        this.setNameTc = setNameTc == null ? null : setNameTc.trim();
    }

    public String getSetNameEn() {
        return setNameEn;
    }

    public void setSetNameEn(String setNameEn) {
        this.setNameEn = setNameEn == null ? null : setNameEn.trim();
    }

    public String getBannerUrl() {
        return bannerUrl;
    }

    public void setBannerUrl(String bannerUrl) {
        this.bannerUrl = bannerUrl == null ? null : bannerUrl.trim();
    }

    public String getSetAbstract() {
        return setAbstract;
    }

    public void setSetAbstract(String setAbstract) {
        this.setAbstract = setAbstract == null ? null : setAbstract.trim();
    }

    public String getBookWeb() {
        return bookWeb;
    }

    public void setBookWeb(String bookWeb) {
        this.bookWeb = bookWeb == null ? null : bookWeb.trim();
    }

    public Integer getDiscountValue() {
        return discountValue;
    }

    public void setDiscountValue(Integer discountValue) {
        this.discountValue = discountValue;
    }

    public Integer getIsDiscountValid() {
        return isDiscountValid;
    }

    public void setIsDiscountValid(Integer isDiscountValid) {
        this.isDiscountValid = isDiscountValid;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPriceDiscount() {
        return priceDiscount;
    }

    public void setPriceDiscount(BigDecimal priceDiscount) {
        this.priceDiscount = priceDiscount;
    }

    public Integer getIsIndex() {
        return isIndex;
    }

    public void setIsIndex(Integer isIndex) {
        this.isIndex = isIndex;
    }

    public Integer getShowOrder() {
        return showOrder;
    }

    public void setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
    }

    public String getBookCover() {
        return bookCover;
    }

    public void setBookCover(String bookCover) {
        this.bookCover = bookCover == null ? null : bookCover.trim();
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    public Integer getCanAllBuy() {
        return canAllBuy;
    }

    public void setCanAllBuy(Integer canAllBuy) {
        this.canAllBuy = canAllBuy;
    }

    public String[] getBookIDs() {
        return bookIDs;
    }

    public void setBookIDs(String[] bookIDs) {
        this.bookIDs = bookIDs;
    }

    public String[] getBookIDsOld() {
        return bookIDsOld;
    }

    public void setBookIDsOld(String[] bookIDsOld) {
        this.bookIDsOld = bookIDsOld;
    }

    public BookWebInfo getBookWebInfo() {
        return bookWebInfo;
    }

    public void setBookWebInfo(BookWebInfo bookWebInfo) {
        this.bookWebInfo = bookWebInfo;
    }

    public String getBookWebSalePapers() {
        return bookWebSalePapers;
    }

    public void setBookWebSalePapers(String bookWebSalePapers) {
        this.bookWebSalePapers = bookWebSalePapers;
    }

    public String getBookWebVersions() {
        return bookWebVersions;
    }

    public void setBookWebVersions(String bookWebVersions) {
        this.bookWebVersions = bookWebVersions;
    }
}