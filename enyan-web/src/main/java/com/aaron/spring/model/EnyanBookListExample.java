package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class EnyanBookListExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanBookListExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSetIdIsNull() {
            addCriterion("set_id is null");
            return (Criteria) this;
        }

        public Criteria andSetIdIsNotNull() {
            addCriterion("set_id is not null");
            return (Criteria) this;
        }

        public Criteria andSetIdEqualTo(Long value) {
            addCriterion("set_id =", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdNotEqualTo(Long value) {
            addCriterion("set_id <>", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdGreaterThan(Long value) {
            addCriterion("set_id >", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("set_id >=", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdLessThan(Long value) {
            addCriterion("set_id <", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdLessThanOrEqualTo(Long value) {
            addCriterion("set_id <=", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdIn(List<Long> values) {
            addCriterion("set_id in", values, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdNotIn(List<Long> values) {
            addCriterion("set_id not in", values, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdBetween(Long value1, Long value2) {
            addCriterion("set_id between", value1, value2, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdNotBetween(Long value1, Long value2) {
            addCriterion("set_id not between", value1, value2, "setId");
            return (Criteria) this;
        }

        public Criteria andSetNameIsNull() {
            addCriterion("set_name is null");
            return (Criteria) this;
        }

        public Criteria andSetNameIsNotNull() {
            addCriterion("set_name is not null");
            return (Criteria) this;
        }

        public Criteria andSetNameEqualTo(String value) {
            addCriterion("set_name =", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameNotEqualTo(String value) {
            addCriterion("set_name <>", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameGreaterThan(String value) {
            addCriterion("set_name >", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameGreaterThanOrEqualTo(String value) {
            addCriterion("set_name >=", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameLessThan(String value) {
            addCriterion("set_name <", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameLessThanOrEqualTo(String value) {
            addCriterion("set_name <=", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameLike(String value) {
            addCriterion("set_name like", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameNotLike(String value) {
            addCriterion("set_name not like", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameIn(List<String> values) {
            addCriterion("set_name in", values, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameNotIn(List<String> values) {
            addCriterion("set_name not in", values, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameBetween(String value1, String value2) {
            addCriterion("set_name between", value1, value2, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameNotBetween(String value1, String value2) {
            addCriterion("set_name not between", value1, value2, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameTcIsNull() {
            addCriterion("set_name_tc is null");
            return (Criteria) this;
        }

        public Criteria andSetNameTcIsNotNull() {
            addCriterion("set_name_tc is not null");
            return (Criteria) this;
        }

        public Criteria andSetNameTcEqualTo(String value) {
            addCriterion("set_name_tc =", value, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcNotEqualTo(String value) {
            addCriterion("set_name_tc <>", value, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcGreaterThan(String value) {
            addCriterion("set_name_tc >", value, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcGreaterThanOrEqualTo(String value) {
            addCriterion("set_name_tc >=", value, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcLessThan(String value) {
            addCriterion("set_name_tc <", value, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcLessThanOrEqualTo(String value) {
            addCriterion("set_name_tc <=", value, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcLike(String value) {
            addCriterion("set_name_tc like", value, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcNotLike(String value) {
            addCriterion("set_name_tc not like", value, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcIn(List<String> values) {
            addCriterion("set_name_tc in", values, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcNotIn(List<String> values) {
            addCriterion("set_name_tc not in", values, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcBetween(String value1, String value2) {
            addCriterion("set_name_tc between", value1, value2, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameTcNotBetween(String value1, String value2) {
            addCriterion("set_name_tc not between", value1, value2, "setNameTc");
            return (Criteria) this;
        }

        public Criteria andSetNameEnIsNull() {
            addCriterion("set_name_en is null");
            return (Criteria) this;
        }

        public Criteria andSetNameEnIsNotNull() {
            addCriterion("set_name_en is not null");
            return (Criteria) this;
        }

        public Criteria andSetNameEnEqualTo(String value) {
            addCriterion("set_name_en =", value, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnNotEqualTo(String value) {
            addCriterion("set_name_en <>", value, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnGreaterThan(String value) {
            addCriterion("set_name_en >", value, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnGreaterThanOrEqualTo(String value) {
            addCriterion("set_name_en >=", value, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnLessThan(String value) {
            addCriterion("set_name_en <", value, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnLessThanOrEqualTo(String value) {
            addCriterion("set_name_en <=", value, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnLike(String value) {
            addCriterion("set_name_en like", value, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnNotLike(String value) {
            addCriterion("set_name_en not like", value, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnIn(List<String> values) {
            addCriterion("set_name_en in", values, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnNotIn(List<String> values) {
            addCriterion("set_name_en not in", values, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnBetween(String value1, String value2) {
            addCriterion("set_name_en between", value1, value2, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andSetNameEnNotBetween(String value1, String value2) {
            addCriterion("set_name_en not between", value1, value2, "setNameEn");
            return (Criteria) this;
        }

        public Criteria andBannerUrlIsNull() {
            addCriterion("banner_url is null");
            return (Criteria) this;
        }

        public Criteria andBannerUrlIsNotNull() {
            addCriterion("banner_url is not null");
            return (Criteria) this;
        }

        public Criteria andBannerUrlEqualTo(String value) {
            addCriterion("banner_url =", value, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlNotEqualTo(String value) {
            addCriterion("banner_url <>", value, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlGreaterThan(String value) {
            addCriterion("banner_url >", value, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlGreaterThanOrEqualTo(String value) {
            addCriterion("banner_url >=", value, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlLessThan(String value) {
            addCriterion("banner_url <", value, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlLessThanOrEqualTo(String value) {
            addCriterion("banner_url <=", value, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlLike(String value) {
            addCriterion("banner_url like", value, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlNotLike(String value) {
            addCriterion("banner_url not like", value, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlIn(List<String> values) {
            addCriterion("banner_url in", values, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlNotIn(List<String> values) {
            addCriterion("banner_url not in", values, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlBetween(String value1, String value2) {
            addCriterion("banner_url between", value1, value2, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andBannerUrlNotBetween(String value1, String value2) {
            addCriterion("banner_url not between", value1, value2, "bannerUrl");
            return (Criteria) this;
        }

        public Criteria andSetAbstractIsNull() {
            addCriterion("set_abstract is null");
            return (Criteria) this;
        }

        public Criteria andSetAbstractIsNotNull() {
            addCriterion("set_abstract is not null");
            return (Criteria) this;
        }

        public Criteria andSetAbstractEqualTo(String value) {
            addCriterion("set_abstract =", value, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractNotEqualTo(String value) {
            addCriterion("set_abstract <>", value, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractGreaterThan(String value) {
            addCriterion("set_abstract >", value, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractGreaterThanOrEqualTo(String value) {
            addCriterion("set_abstract >=", value, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractLessThan(String value) {
            addCriterion("set_abstract <", value, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractLessThanOrEqualTo(String value) {
            addCriterion("set_abstract <=", value, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractLike(String value) {
            addCriterion("set_abstract like", value, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractNotLike(String value) {
            addCriterion("set_abstract not like", value, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractIn(List<String> values) {
            addCriterion("set_abstract in", values, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractNotIn(List<String> values) {
            addCriterion("set_abstract not in", values, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractBetween(String value1, String value2) {
            addCriterion("set_abstract between", value1, value2, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andSetAbstractNotBetween(String value1, String value2) {
            addCriterion("set_abstract not between", value1, value2, "setAbstract");
            return (Criteria) this;
        }

        public Criteria andBookWebIsNull() {
            addCriterion("book_web is null");
            return (Criteria) this;
        }

        public Criteria andBookWebIsNotNull() {
            addCriterion("book_web is not null");
            return (Criteria) this;
        }

        public Criteria andBookWebEqualTo(String value) {
            addCriterion("book_web =", value, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebNotEqualTo(String value) {
            addCriterion("book_web <>", value, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebGreaterThan(String value) {
            addCriterion("book_web >", value, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebGreaterThanOrEqualTo(String value) {
            addCriterion("book_web >=", value, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebLessThan(String value) {
            addCriterion("book_web <", value, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebLessThanOrEqualTo(String value) {
            addCriterion("book_web <=", value, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebLike(String value) {
            addCriterion("book_web like", value, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebNotLike(String value) {
            addCriterion("book_web not like", value, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebIn(List<String> values) {
            addCriterion("book_web in", values, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebNotIn(List<String> values) {
            addCriterion("book_web not in", values, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebBetween(String value1, String value2) {
            addCriterion("book_web between", value1, value2, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookWebNotBetween(String value1, String value2) {
            addCriterion("book_web not between", value1, value2, "bookWeb");
            return (Criteria) this;
        }

        public Criteria andBookIdTextIsNull() {
            addCriterion("book_id_text is null");
            return (Criteria) this;
        }

        public Criteria andBookIdTextIsNotNull() {
            addCriterion("book_id_text is not null");
            return (Criteria) this;
        }

        public Criteria andBookIdTextEqualTo(String value) {
            addCriterion("book_id_text =", value, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextNotEqualTo(String value) {
            addCriterion("book_id_text <>", value, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextGreaterThan(String value) {
            addCriterion("book_id_text >", value, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextGreaterThanOrEqualTo(String value) {
            addCriterion("book_id_text >=", value, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextLessThan(String value) {
            addCriterion("book_id_text <", value, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextLessThanOrEqualTo(String value) {
            addCriterion("book_id_text <=", value, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextLike(String value) {
            addCriterion("book_id_text like", value, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextNotLike(String value) {
            addCriterion("book_id_text not like", value, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextIn(List<String> values) {
            addCriterion("book_id_text in", values, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextNotIn(List<String> values) {
            addCriterion("book_id_text not in", values, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextBetween(String value1, String value2) {
            addCriterion("book_id_text between", value1, value2, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andBookIdTextNotBetween(String value1, String value2) {
            addCriterion("book_id_text not between", value1, value2, "bookIdText");
            return (Criteria) this;
        }

        public Criteria andDiscountValueIsNull() {
            addCriterion("discount_value is null");
            return (Criteria) this;
        }

        public Criteria andDiscountValueIsNotNull() {
            addCriterion("discount_value is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountValueEqualTo(Integer value) {
            addCriterion("discount_value =", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueNotEqualTo(Integer value) {
            addCriterion("discount_value <>", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueGreaterThan(Integer value) {
            addCriterion("discount_value >", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("discount_value >=", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueLessThan(Integer value) {
            addCriterion("discount_value <", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueLessThanOrEqualTo(Integer value) {
            addCriterion("discount_value <=", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueIn(List<Integer> values) {
            addCriterion("discount_value in", values, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueNotIn(List<Integer> values) {
            addCriterion("discount_value not in", values, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueBetween(Integer value1, Integer value2) {
            addCriterion("discount_value between", value1, value2, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueNotBetween(Integer value1, Integer value2) {
            addCriterion("discount_value not between", value1, value2, "discountValue");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidIsNull() {
            addCriterion("is_discount_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidIsNotNull() {
            addCriterion("is_discount_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidEqualTo(Integer value) {
            addCriterion("is_discount_valid =", value, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidNotEqualTo(Integer value) {
            addCriterion("is_discount_valid <>", value, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidGreaterThan(Integer value) {
            addCriterion("is_discount_valid >", value, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_discount_valid >=", value, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidLessThan(Integer value) {
            addCriterion("is_discount_valid <", value, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidLessThanOrEqualTo(Integer value) {
            addCriterion("is_discount_valid <=", value, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidIn(List<Integer> values) {
            addCriterion("is_discount_valid in", values, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidNotIn(List<Integer> values) {
            addCriterion("is_discount_valid not in", values, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidBetween(Integer value1, Integer value2) {
            addCriterion("is_discount_valid between", value1, value2, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andIsDiscountValidNotBetween(Integer value1, Integer value2) {
            addCriterion("is_discount_valid not between", value1, value2, "isDiscountValid");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(BigDecimal value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(BigDecimal value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(BigDecimal value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(BigDecimal value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<BigDecimal> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<BigDecimal> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountIsNull() {
            addCriterion("price_discount is null");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountIsNotNull() {
            addCriterion("price_discount is not null");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountEqualTo(BigDecimal value) {
            addCriterion("price_discount =", value, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountNotEqualTo(BigDecimal value) {
            addCriterion("price_discount <>", value, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountGreaterThan(BigDecimal value) {
            addCriterion("price_discount >", value, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price_discount >=", value, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountLessThan(BigDecimal value) {
            addCriterion("price_discount <", value, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price_discount <=", value, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountIn(List<BigDecimal> values) {
            addCriterion("price_discount in", values, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountNotIn(List<BigDecimal> values) {
            addCriterion("price_discount not in", values, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_discount between", value1, value2, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andPriceDiscountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_discount not between", value1, value2, "priceDiscount");
            return (Criteria) this;
        }

        public Criteria andIsIndexIsNull() {
            addCriterion("is_index is null");
            return (Criteria) this;
        }

        public Criteria andIsIndexIsNotNull() {
            addCriterion("is_index is not null");
            return (Criteria) this;
        }

        public Criteria andIsIndexEqualTo(Integer value) {
            addCriterion("is_index =", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexNotEqualTo(Integer value) {
            addCriterion("is_index <>", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexGreaterThan(Integer value) {
            addCriterion("is_index >", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_index >=", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexLessThan(Integer value) {
            addCriterion("is_index <", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexLessThanOrEqualTo(Integer value) {
            addCriterion("is_index <=", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexIn(List<Integer> values) {
            addCriterion("is_index in", values, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexNotIn(List<Integer> values) {
            addCriterion("is_index not in", values, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexBetween(Integer value1, Integer value2) {
            addCriterion("is_index between", value1, value2, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexNotBetween(Integer value1, Integer value2) {
            addCriterion("is_index not between", value1, value2, "isIndex");
            return (Criteria) this;
        }

        public Criteria andShowOrderIsNull() {
            addCriterion("show_order is null");
            return (Criteria) this;
        }

        public Criteria andShowOrderIsNotNull() {
            addCriterion("show_order is not null");
            return (Criteria) this;
        }

        public Criteria andShowOrderEqualTo(Integer value) {
            addCriterion("show_order =", value, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderNotEqualTo(Integer value) {
            addCriterion("show_order <>", value, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderGreaterThan(Integer value) {
            addCriterion("show_order >", value, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("show_order >=", value, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderLessThan(Integer value) {
            addCriterion("show_order <", value, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderLessThanOrEqualTo(Integer value) {
            addCriterion("show_order <=", value, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderIn(List<Integer> values) {
            addCriterion("show_order in", values, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderNotIn(List<Integer> values) {
            addCriterion("show_order not in", values, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderBetween(Integer value1, Integer value2) {
            addCriterion("show_order between", value1, value2, "showOrder");
            return (Criteria) this;
        }

        public Criteria andShowOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("show_order not between", value1, value2, "showOrder");
            return (Criteria) this;
        }

        public Criteria andBookCoverIsNull() {
            addCriterion("book_cover is null");
            return (Criteria) this;
        }

        public Criteria andBookCoverIsNotNull() {
            addCriterion("book_cover is not null");
            return (Criteria) this;
        }

        public Criteria andBookCoverEqualTo(String value) {
            addCriterion("book_cover =", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverNotEqualTo(String value) {
            addCriterion("book_cover <>", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverGreaterThan(String value) {
            addCriterion("book_cover >", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverGreaterThanOrEqualTo(String value) {
            addCriterion("book_cover >=", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverLessThan(String value) {
            addCriterion("book_cover <", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverLessThanOrEqualTo(String value) {
            addCriterion("book_cover <=", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverLike(String value) {
            addCriterion("book_cover like", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverNotLike(String value) {
            addCriterion("book_cover not like", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverIn(List<String> values) {
            addCriterion("book_cover in", values, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverNotIn(List<String> values) {
            addCriterion("book_cover not in", values, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverBetween(String value1, String value2) {
            addCriterion("book_cover between", value1, value2, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverNotBetween(String value1, String value2) {
            addCriterion("book_cover not between", value1, value2, "bookCover");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNull() {
            addCriterion("is_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNotNull() {
            addCriterion("is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsValidEqualTo(Integer value) {
            addCriterion("is_valid =", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotEqualTo(Integer value) {
            addCriterion("is_valid <>", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThan(Integer value) {
            addCriterion("is_valid >", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_valid >=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThan(Integer value) {
            addCriterion("is_valid <", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThanOrEqualTo(Integer value) {
            addCriterion("is_valid <=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidIn(List<Integer> values) {
            addCriterion("is_valid in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotIn(List<Integer> values) {
            addCriterion("is_valid not in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidBetween(Integer value1, Integer value2) {
            addCriterion("is_valid between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotBetween(Integer value1, Integer value2) {
            addCriterion("is_valid not between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsShowIsNull() {
            addCriterion("is_show is null");
            return (Criteria) this;
        }

        public Criteria andIsShowIsNotNull() {
            addCriterion("is_show is not null");
            return (Criteria) this;
        }

        public Criteria andIsShowEqualTo(Integer value) {
            addCriterion("is_show =", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotEqualTo(Integer value) {
            addCriterion("is_show <>", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowGreaterThan(Integer value) {
            addCriterion("is_show >", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_show >=", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowLessThan(Integer value) {
            addCriterion("is_show <", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowLessThanOrEqualTo(Integer value) {
            addCriterion("is_show <=", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowIn(List<Integer> values) {
            addCriterion("is_show in", values, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotIn(List<Integer> values) {
            addCriterion("is_show not in", values, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowBetween(Integer value1, Integer value2) {
            addCriterion("is_show between", value1, value2, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotBetween(Integer value1, Integer value2) {
            addCriterion("is_show not between", value1, value2, "isShow");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyIsNull() {
            addCriterion("can_all_buy is null");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyIsNotNull() {
            addCriterion("can_all_buy is not null");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyEqualTo(Integer value) {
            addCriterion("can_all_buy =", value, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyNotEqualTo(Integer value) {
            addCriterion("can_all_buy <>", value, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyGreaterThan(Integer value) {
            addCriterion("can_all_buy >", value, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyGreaterThanOrEqualTo(Integer value) {
            addCriterion("can_all_buy >=", value, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyLessThan(Integer value) {
            addCriterion("can_all_buy <", value, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyLessThanOrEqualTo(Integer value) {
            addCriterion("can_all_buy <=", value, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyIn(List<Integer> values) {
            addCriterion("can_all_buy in", values, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyNotIn(List<Integer> values) {
            addCriterion("can_all_buy not in", values, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyBetween(Integer value1, Integer value2) {
            addCriterion("can_all_buy between", value1, value2, "canAllBuy");
            return (Criteria) this;
        }

        public Criteria andCanAllBuyNotBetween(Integer value1, Integer value2) {
            addCriterion("can_all_buy not between", value1, value2, "canAllBuy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}