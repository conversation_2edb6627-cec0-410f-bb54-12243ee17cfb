package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.Date;

public class DataStat extends BaseDTO{
    private static final long serialVersionUID = 3608387860957981578L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long dataId;

    private Integer salesVolume;

    private BigDecimal incomeTotal;

    private Integer orderCount;

    private Integer orderFeeCount;

    private Integer orderFreeCount;

    private Integer userBuyCount;

    private Integer userNewCount;

    private Date createTime;

    private String dateString;

    private Long count;

    private Integer userAllCount;

    private Integer userActiveCount;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public Integer getSalesVolume() {
        return salesVolume;
    }

    public void setSalesVolume(Integer salesVolume) {
        this.salesVolume = salesVolume;
    }

    public BigDecimal getIncomeTotal() {
        return incomeTotal;
    }

    public void setIncomeTotal(BigDecimal incomeTotal) {
        this.incomeTotal = incomeTotal;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getOrderFeeCount() {
        return orderFeeCount;
    }

    public void setOrderFeeCount(Integer orderFeeCount) {
        this.orderFeeCount = orderFeeCount;
    }

    public Integer getOrderFreeCount() {
        return orderFreeCount;
    }

    public void setOrderFreeCount(Integer orderFreeCount) {
        this.orderFreeCount = orderFreeCount;
    }

    public Integer getUserBuyCount() {
        return userBuyCount;
    }

    public void setUserBuyCount(Integer userBuyCount) {
        this.userBuyCount = userBuyCount;
    }

    public Integer getUserNewCount() {
        return userNewCount;
    }

    public void setUserNewCount(Integer userNewCount) {
        this.userNewCount = userNewCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public Integer getUserAllCount() {
        return userAllCount;
    }

    public void setUserAllCount(Integer userAllCount) {
        this.userAllCount = userAllCount;
    }

    public Integer getUserActiveCount() {
        return userActiveCount;
    }

    public void setUserActiveCount(Integer userActiveCount) {
        this.userActiveCount = userActiveCount;
    }

    @Override
    public String toString() {
        return "数据统计{" +
                       "数据Id=" + dataId +
                       ", 销量=" + salesVolume +
                       ", 销售额=" + incomeTotal +
                       ", 订单数=" + orderCount +
                       ", 付费订单数=" + orderFeeCount +
                       ", 免费订单数=" + orderFreeCount +
                       ", 购买用户数=" + userBuyCount +
                       ", 新注册用户数=" + userNewCount +
                       ", 创建时间=" + createTime +
                       '}';
    }
}