package com.aaron.spring.model;

import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.common.Constant;
import com.alibaba.fastjson2.annotation.JSONField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/12/9
 * @Modified By:
 */
public class CartDiscountInfo implements Serializable{
    private static final long serialVersionUID = 5040794087389414261L;

    /**
     * 1:满300减30
     * */
    private static final Byte DISCOUNT_FULL = Constant.BYTE_VALUE_1;

    /**
     * 0:累计折扣 n件n折
     * */
    private static final Byte DISCOUNT_CUMULATE = Constant.BYTE_VALUE_0;

    private Long discountId;

    private String discountTitle;

    /**
     * 0:累计折扣 n件n折;1:满300减30
     * */
    private Byte discountType;//

    /**
     * 累计件数
     * */
    private Integer cumulatePackage;//

    private Integer cumulateDiscount;

    private Integer cumulatePackageMuti;//

    private Integer cumulateDiscountMuti;

    private Integer fullBase;

    private Integer fullMinus;

    private List<ProductInfo> productInfoList = new ArrayList<>();

    @JSONField(serialize = false)
    private BigDecimal amountCny = Constant.VALUE_0;

    @JSONField(serialize = false)
    private BigDecimal amountUsd = Constant.VALUE_0;

    private BigDecimal amountHkd = Constant.VALUE_0;

    //原价
    private BigDecimal amountHkdFix = Constant.VALUE_0;

    private boolean empty;

    @JSONField(serialize = false)
    private BigDecimal quantity;

    private boolean valid = false; // 是否有折扣

    @JSONField(serialize = false)
    private BigDecimal amountCnyNoDiscount;

    @JSONField(serialize = false)
    private BigDecimal amountUsdNoDiscount;

    @JSONField(serialize = false)
    private BigDecimal amountHkdNoDiscount;

    @JSONField(serialize = false)
    private BigDecimal amountHkdAfterCoupon;

    @JSONField(serialize = false)
    private BigDecimal amountCnyFull;

    @JSONField(serialize = false)
    private BigDecimal amountUsdFull;

    @JSONField(serialize = false)
    private BigDecimal amountHkdFull;

    @JSONField(serialize = false)
    private BigDecimal amountCnyCumulate;

    @JSONField(serialize = false)
    private BigDecimal amountUsdCumulate;

    @JSONField(serialize = false)
    private String language;

    public CartDiscountInfo() {
    }

    public CartDiscountInfo(Long discountId) {
        this.discountId = discountId;
        this.init();
    }
    private void init(){
        if (discountId.equals(Constant.DEFAULT_DISCOUNT_ID)){
            return;
        }
        EnyanDiscount discount = this.findDiscountById(this.discountId);
        if (null != discount){
            valid = true;
            cumulatePackage = discount.getCumulatePackage();
            cumulateDiscount = discount.getCumulateDiscount();
            cumulatePackageMuti = discount.getCumulatePackageMuti();
            cumulateDiscountMuti = discount.getCumulateDiscountMuti();
            discountType = discount.getDiscountType();
        }
    }

    /**
     * <p>根据<code>discountId</code>获取折扣信息</p>
     * @param discountId
     * @return: com.aaron.spring.model.EnyanDiscount
     * @since : 2020-08-05
     */
    private EnyanDiscount findDiscountById(Long discountId){
        return Constant.discountMap.get(discountId);
    }

    /**
     * <p>根据product code获取ProductInfo</p>
     * @param code
     * @return: com.aaron.spring.model.ProductInfo
     * @since : 2020-08-07
     */
    public ProductInfo getProductInfoByCode(Long code) {
        for(ProductInfo productInfo : this.productInfoList) {
            if(productInfo.getCode() == code)
                return productInfo;
        }
        return null;
    }

    public void addProduct(ProductInfo productInfo, int quantity) {
        /*if (null == this.findDiscountById(productInfo.getDiscountId()) && !this.discountId.equals(Constant.DEFAULT_DISCOUNT_ID)){//折扣不存在
            this.productInfoList.clear();
            return;
        }*/

        ProductInfo tmpProductInfo = this.getProductInfoByCode(productInfo.getCode());

        if(tmpProductInfo == null) {
            productInfo.setQuantity(new BigDecimal("0"));
            this.productInfoList.add(productInfo);
        }
        BigDecimal newQuantity = productInfo.getQuantity().add(new BigDecimal(quantity));
        if(newQuantity.intValue() <=0) {
            this.productInfoList.remove(productInfo);
        } else {
            if (EBookConstant.OrderType.ORDER_EBOOK_SET_BUY == productInfo.getOrderType()
                    || EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY == productInfo.getOrderType()){
                newQuantity = new BigDecimal("1");//电子书只需要添加一次即可
            }
            productInfo.setQuantity(newQuantity);
        }
        this.resetDiscountTitle();
    }

    public boolean updateProduct(Long code, BigDecimal quantity) {
        ProductInfo productInfo = this.getProductInfoByCode(code);
        if(productInfo != null) {
            BigDecimal newQuantity = quantity;
            if(newQuantity.intValue() <=0) {
                this.productInfoList.remove(productInfo);
            } else {
                productInfo.setQuantity(newQuantity);
            }
            return true;
        }
        return false;
    }

    /**
     * <p>重置产品信息</p>
     * @param info
     * @return: void
     * @since : 2020-08-07
     */
    public void resetProducts(CartDiscountInfo info){
        for (ProductInfo productInfo:info.getProductInfoList()){
            this.updateProduct(productInfo.getCode(),productInfo.getQuantity());
        }
    }

    public boolean removeProduct(Long code) {
        ProductInfo productInfo = this.getProductInfoByCode(code);
        if(productInfo != null){
            this.productInfoList.remove(productInfo);
            return true;
        }
        return false;
    }

    public boolean isEmpty() {
        //isEmpty = this.productInfoList.isEmpty();
        return this.productInfoList.isEmpty();
    }

    public BigDecimal getQuantity() {
        BigDecimal quantity = new BigDecimal("0");
        for(ProductInfo productInfo : this.productInfoList) {
            if (productInfo.getRealPriceHKD().doubleValue() > 0){//免费的书籍不计入书籍的购买
                quantity = quantity.add(productInfo.getQuantity());
            }
        }
        return quantity;
    }

    /*public BigDecimal getAmountCny() {
        if (Constant.DEFAULT_DISCOUNT_ID.equals(discountId)){
            return this.getAmountCnyNoDiscount();
        }
        if (DISCOUNT_CUMULATE.equals(discountType)){
            return this.getAmountCnyCumulate();
        }
        if (DISCOUNT_FULL.equals(discountType)){
            return this.getAmountCnyFull();
        }
        return amountCny;
    }*/


    /*public BigDecimal getAmountUsd() {
        if (Constant.DEFAULT_DISCOUNT_ID.equals(discountId)){
            return this.getAmountUsdNoDiscount();
        }
        if (DISCOUNT_CUMULATE.equals(discountType)){
            return this.getAmountUsdCumulate();
        }
        if (DISCOUNT_FULL.equals(discountType)){
            return this.getAmountUsdFull();
        }
        return amountUsd;
    }*/

    public BigDecimal getAmountHkd() {
        if (Constant.DEFAULT_DISCOUNT_ID.equals(discountId) ){//非使用N件折扣
            return this.getAmountHkdReal();
        }
        if (DISCOUNT_CUMULATE.equals(discountType)){
            return this.getAmountHkdCumulate();
        }
        if (DISCOUNT_FULL.equals(discountType)){
            return this.getAmountHkdFull();
        }
        return amountHkd;
    }
    /**
     * <p>有单一折扣或没有的时候，可以使用的价格</p>
     * @param
     * @return: java.math.BigDecimal
     * @since : 2020-08-11
     */
    public BigDecimal getAmountHkdMiddle(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo productInfo : this.productInfoList) {
            total = total.add(productInfo.getRealPriceHKD().multiply(productInfo.getQuantity()));
        }
        return total;
    }

    /**
     * <p>真实的最终价格，折扣后、优惠码后，或者原价</p>
     * @param
     * @return: java.math.BigDecimal
     * @since : 2020-08-14
     */
    public BigDecimal getAmountHkdDiscount(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo productInfo : this.productInfoList) {
            total = total.add(productInfo.getRealPriceHKD().multiply(productInfo.getQuantity()));
        }
        return total;
    }

    private BigDecimal getAmountCnyNoDiscount(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo productInfo : this.productInfoList) {
            total = total.add(productInfo.getPriceCny().multiply(productInfo.getQuantity()));
        }
        return total;
    }

    private BigDecimal getAmountUsdNoDiscount(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getPriceUsd().multiply(info.getQuantity()));
        }
        return total;
    }

    private BigDecimal getAmountHkdNoDiscount(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getPriceHkd().multiply(info.getQuantity()));
        }
        return total;
    }

    /**
     * <p>单个折扣或真实价格</p>
     * @param
     * @return: java.math.BigDecimal
     * @since : 2020-08-12
     */
    public BigDecimal getAmountHkdReal(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getRealPriceHKD().multiply(info.getQuantity()));
        }
        return total;
    }

    /**
     * <p>原价总和</p>
     * @param
     * @return: java.math.BigDecimal
     * @since : 2020-09-16
     */
    public BigDecimal getAmountHkdFix() {
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getPriceHkd().multiply(info.getQuantity()));
        }
        return total;
    }

    /**
     * <p>使用优惠码后的费用合计</p>
     * @param
     * @return: java.math.BigDecimal
     * @since : 2020-08-14
     */
    public BigDecimal getAmountHkdAfterCoupon(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getPriceHKDDiscount().multiply(info.getQuantity()));
        }
        return total;
    }

    /**
     * 1:满300减30
     * */
    private BigDecimal getAmountCnyFull(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getPriceCny().multiply(info.getQuantity()));
        }
        if (total.doubleValue()>fullBase){
            total = total.subtract(new BigDecimal(fullMinus));
        }
        return total;
    }

    /**
     * 1:满300减30
     * */
    private BigDecimal getAmountUsdFull(){
        //TODO 满减 美元 是个问题

        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getPriceUsd().multiply(info.getQuantity()));
        }
        if (total.doubleValue()>fullBase){
            total = total.subtract(new BigDecimal(fullMinus));
        }
        return total;
    }

    private BigDecimal getAmountHkdFull(){
        //TODO 满减 美元 是个问题

        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getPriceHkd().multiply(info.getQuantity()));
        }
        if (total.doubleValue()>fullBase){
            total = total.subtract(new BigDecimal(fullMinus));
        }
        return total;
    }

    /**
     * 0:累计折扣 n件n折;
     * */
    private BigDecimal getAmountCnyCumulate(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getPriceCnyDiscount().multiply(info.getQuantity()));
        }
        return total;
    }

    /**
     * 0:累计折扣 n件n折;
     * */
    private BigDecimal getAmountUsdCumulate(){
        BigDecimal total = new BigDecimal("0");
        for(ProductInfo info : this.productInfoList) {
            total = total.add(info.getRealPriceHKD().multiply(info.getQuantity()));
        }
        return total;
    }

    /**
     * <p>根据折扣产品的个数进行折扣的重算</p>
     * @param
     * @return: void
     * @since : 2020-08-05
     */
    public void resetCumulateDiscount(){
        if (discountId < 0){
            return;
        }
        Integer cumulateDiscountNew = 100;
        BigDecimal quatity = this.getQuantity();
        if (quatity.intValue() >= cumulatePackageMuti){
            cumulateDiscountNew = cumulateDiscountMuti;
        }else if (quatity.intValue() >= cumulatePackage){
            cumulateDiscountNew = cumulateDiscount;
        }
        if (cumulateDiscountNew == 100){
            return;
        }
        for(ProductInfo info : this.productInfoList) {
            info.resetProductCumulateDiscount(cumulateDiscountNew);
        }
    }

    /**
     * <p>使用Middle计算N件折信息</p>
     * <p>使用原价计算N件折信息 2020/09/16</p>
     * @param
     * @return: java.math.BigDecimal
     * @since : 2020-08-14
     */
    private BigDecimal getAmountHkdCumulate(){
        Integer count = this.getQuantity().intValue();//商品件数
        BigDecimal total = new BigDecimal("0");
        Integer discount = 100;
        if (count >= this.getCumulatePackageMuti()){
            discount = this.getCumulateDiscountMuti();
        }else if (count >= this.getCumulatePackage()){
            discount = this.getCumulateDiscount();
        }
        for(ProductInfo info : this.productInfoList) {
            if (discount == 100){//虽然设置N件折，但因没有启用，所以直接加和
                total = total.add(info.getRealPriceHKD().multiply(info.getQuantity()));
                continue;
            }
            BigDecimal fee = info.getPriceHkd().multiply(info.getQuantity()).multiply(new BigDecimal(discount)).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP);
            total = total.add(fee);
        }
        return total;
        /*if (!valid){
            for(CartLineInfo info : this.productInfoList) {
                total = total.add(info.getAmountHkd());

            }
            return total;
        }

        Integer cumulateDiscountNew = 100;
        Integer cumulatePackageNew = Integer.MAX_VALUE;
        if (this.productInfoList.size() > cumulatePackageMuti){
            cumulateDiscountNew = cumulateDiscountMuti;
            cumulatePackageNew = cumulatePackageMuti;
        }else if (this.productInfoList.size() > cumulatePackage){
            cumulateDiscountNew = cumulateDiscount;
            cumulatePackageNew = cumulatePackage;
        }

        if (null != cumulateDiscountMuti && null != cumulatePackageMuti){
            if (this.productInfoList.size() > cumulatePackageMuti){
                total = total.multiply(new BigDecimal(cumulateDiscountMuti)).divide(new BigDecimal(100),2, RoundingMode.HALF_EVEN);
            }
            return total;
        }
        if (this.productInfoList.size() > cumulatePackage){
            total = total.multiply(new BigDecimal(cumulateDiscount)).divide(new BigDecimal(100),2, RoundingMode.HALF_EVEN);
        }
        return total;*/
    }

    /**
     * <p>重新设置享受的折扣信息</p>
     * @param
     * @return: void
     * @since : 2020-08-11
     */
    private void resetDiscountTitle(){
        if (discountId.equals(Constant.DEFAULT_DISCOUNT_ID)){
            return;
        }
        Integer count = this.getQuantity().intValue();
        if (count >= this.getCumulatePackageMuti()){
            this.setDiscountTitle(this.getDiscountInfo(this.getCumulatePackageMuti(),this.getCumulateDiscountMuti()));
        }else if(count >= this.getCumulatePackage()){
            this.setDiscountTitle(this.getDiscountInfo(this.getCumulatePackage(),this.getCumulateDiscount()));
        }
    }

    private String getDiscountInfo(Integer packageValue, Integer discountValue){
        if (Constant.BYTE_VALUE_0.equals(this.getDiscountType())){
            if ("en_US".equals(this.getLanguage())){
                return packageValue + " for "+(100-discountValue)+"% off";
            }
            if (discountValue < 10){
                return packageValue + "件0."+discountValue+"折";
            }
            if (discountValue%10 == 0){
                return packageValue + "件"+discountValue/10+"折";
            }
            if (discountValue<100){
                return packageValue + "件"+discountValue+"折";
            }
        }
        return "";
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    public String getDiscountTitle() {
        return discountTitle;
    }

    public void setDiscountTitle(String discountTitle) {
        this.discountTitle = discountTitle;
    }

    public Byte getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Byte discountType) {
        this.discountType = discountType;
    }

    public Integer getCumulatePackage() {
        return cumulatePackage;
    }

    public void setCumulatePackage(Integer cumulatePackage) {
        this.cumulatePackage = cumulatePackage;
    }

    public Integer getCumulateDiscount() {
        return cumulateDiscount;
    }

    public void setCumulateDiscount(Integer cumulateDiscount) {
        this.cumulateDiscount = cumulateDiscount;
    }

    public Integer getCumulatePackageMuti() {
        return cumulatePackageMuti;
    }

    public void setCumulatePackageMuti(Integer cumulatePackageMuti) {
        this.cumulatePackageMuti = cumulatePackageMuti;
    }

    public Integer getCumulateDiscountMuti() {
        return cumulateDiscountMuti;
    }

    public void setCumulateDiscountMuti(Integer cumulateDiscountMuti) {
        this.cumulateDiscountMuti = cumulateDiscountMuti;
    }

    public Integer getFullBase() {
        return fullBase;
    }

    public void setFullBase(Integer fullBase) {
        this.fullBase = fullBase;
    }

    public Integer getFullMinus() {
        return fullMinus;
    }

    public void setFullMinus(Integer fullMinus) {
        this.fullMinus = fullMinus;
    }

    public List<ProductInfo> getProductInfoList() {
        return productInfoList;
    }

    public boolean isValid() {
        return valid;
    }

    public void setProductInfoList(List<ProductInfo> productInfoList) {
        this.productInfoList = productInfoList;
    }

    public void setAmountCny(BigDecimal amountCny) {
        this.amountCny = amountCny;
    }

    public void setAmountUsd(BigDecimal amountUsd) {
        this.amountUsd = amountUsd;
    }

    public void setAmountHkd(BigDecimal amountHkd) {
        this.amountHkd = amountHkd;
    }

    public void setEmpty(boolean empty) {
        this.empty = empty;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}
