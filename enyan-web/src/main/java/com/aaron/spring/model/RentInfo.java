package com.aaron.spring.model;

import com.aaron.common.CreditInfo;
import com.aaron.drm.model.DrmInfo;
import com.aaron.drm.model.LcpInfo;
import com.aaron.spring.common.Constant;
import com.aaron.util.AESUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/10/31
 * @Modified By:
 */
public class RentInfo implements Serializable {
	private static final long serialVersionUID = 6097034413385629868L;
	@JSONField(name = "dInfo")
	private DrmInfo drmInfo;

	@JSONField(name = "lInfoM")
	private Map<String,LcpInfo> lcpInfoMap;

	/**
	 * 主要用于在Rent里存储
	 **/
	@JSONField(name = "uId")
	private Integer userId; //主要用于在Rent里存储

	/**
	 * <p>加密的卡信息</p>
	 **/
	@JSONField(name = "cc")
	private String creditCard;

	@JSONField(serialize = false)
	private CreditInfo creditInfo;

	/**
	 * <p>支付信息</p>
	 **/
	@JSONField(name = "pay")
	private OrderPayInfo orderPayInfo;
	/**
	* 加密卡
	* */
	public void encryptCredit(){
		if (null == creditInfo){
			return;
		}
		try {
			String text = JSONObject.toJSONString(creditInfo );
			String encryptText = AESUtil.encrypt(text, Constant.SAVE_CARD_API_AES_KEY,Constant.SAVE_CARD_API_AES_IV);
			this.creditCard = encryptText;
		}catch (Exception e){
			e.printStackTrace();
		}
	}
	/**
	 * 解密卡
	 * */
	public void decryptCredit(){
		if (StringUtils.isBlank(this.creditCard)){
			return;
		}
		try {
			String text = AESUtil.decrypt(this.creditCard,Constant.SAVE_CARD_API_AES_KEY,Constant.SAVE_CARD_API_AES_IV);
			this.creditInfo = JSONObject.parseObject(text, CreditInfo.class);
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	public DrmInfo getDrmInfo() {
		return drmInfo;
	}

	public void setDrmInfo(DrmInfo drmInfo) {
		this.drmInfo = drmInfo;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getCreditCard() {
		return creditCard;
	}

	public void setCreditCard(String creditCard) {
		this.creditCard = creditCard;
	}

	public CreditInfo getCreditInfo() {
		return creditInfo;
	}

	public void setCreditInfo(CreditInfo creditInfo) {
		this.creditInfo = creditInfo;
	}

	public OrderPayInfo getOrderPayInfo() {
		return orderPayInfo;
	}

	public void setOrderPayInfo(OrderPayInfo orderPayInfo) {
		this.orderPayInfo = orderPayInfo;
	}

	public Map<String, LcpInfo> getLcpInfoMap() {
		return lcpInfoMap;
	}

	public void setLcpInfoMap(Map<String, LcpInfo> lcpInfoMap) {
		this.lcpInfoMap = lcpInfoMap;
	}
}
