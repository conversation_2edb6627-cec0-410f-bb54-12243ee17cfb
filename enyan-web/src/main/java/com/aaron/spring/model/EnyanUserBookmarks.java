package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

public class EnyanUserBookmarks {
    private String bookmarkId;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long userId;

    private String userEmail;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookId;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long createTime;

    private String chapterName;

    private Integer pageChapter;

    private Integer currentPage;

    private Integer totalPage;

    private Integer pageProcess;

    private Integer isDeleted;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long updateTime;

    public String getBookmarkId() {
        return bookmarkId;
    }

    public void setBookmarkId(String bookmarkId) {
        this.bookmarkId = bookmarkId == null ? null : bookmarkId.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail == null ? null : userEmail.trim();
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName == null ? null : chapterName.trim();
    }

    public Integer getPageChapter() {
        return pageChapter;
    }

    public void setPageChapter(Integer pageChapter) {
        this.pageChapter = pageChapter;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getPageProcess() {
        return pageProcess;
    }

    public void setPageProcess(Integer pageProcess) {
        this.pageProcess = pageProcess;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}