package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.List;

public class StuInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public StuInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDataIdIsNull() {
            addCriterion("data_id is null");
            return (Criteria) this;
        }

        public Criteria andDataIdIsNotNull() {
            addCriterion("data_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataIdEqualTo(Long value) {
            addCriterion("data_id =", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotEqualTo(Long value) {
            addCriterion("data_id <>", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThan(Long value) {
            addCriterion("data_id >", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_id >=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThan(Long value) {
            addCriterion("data_id <", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThanOrEqualTo(Long value) {
            addCriterion("data_id <=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdIn(List<Long> values) {
            addCriterion("data_id in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotIn(List<Long> values) {
            addCriterion("data_id not in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdBetween(Long value1, Long value2) {
            addCriterion("data_id between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotBetween(Long value1, Long value2) {
            addCriterion("data_id not between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andStuIdIsNull() {
            addCriterion("stu_id is null");
            return (Criteria) this;
        }

        public Criteria andStuIdIsNotNull() {
            addCriterion("stu_id is not null");
            return (Criteria) this;
        }

        public Criteria andStuIdEqualTo(Integer value) {
            addCriterion("stu_id =", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdNotEqualTo(Integer value) {
            addCriterion("stu_id <>", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdGreaterThan(Integer value) {
            addCriterion("stu_id >", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("stu_id >=", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdLessThan(Integer value) {
            addCriterion("stu_id <", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdLessThanOrEqualTo(Integer value) {
            addCriterion("stu_id <=", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdIn(List<Integer> values) {
            addCriterion("stu_id in", values, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdNotIn(List<Integer> values) {
            addCriterion("stu_id not in", values, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdBetween(Integer value1, Integer value2) {
            addCriterion("stu_id between", value1, value2, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("stu_id not between", value1, value2, "stuId");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNull() {
            addCriterion("last_name is null");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNotNull() {
            addCriterion("last_name is not null");
            return (Criteria) this;
        }

        public Criteria andLastNameEqualTo(String value) {
            addCriterion("last_name =", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotEqualTo(String value) {
            addCriterion("last_name <>", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThan(String value) {
            addCriterion("last_name >", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("last_name >=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThan(String value) {
            addCriterion("last_name <", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThanOrEqualTo(String value) {
            addCriterion("last_name <=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLike(String value) {
            addCriterion("last_name like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotLike(String value) {
            addCriterion("last_name not like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameIn(List<String> values) {
            addCriterion("last_name in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotIn(List<String> values) {
            addCriterion("last_name not in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameBetween(String value1, String value2) {
            addCriterion("last_name between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotBetween(String value1, String value2) {
            addCriterion("last_name not between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNull() {
            addCriterion("first_name is null");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNotNull() {
            addCriterion("first_name is not null");
            return (Criteria) this;
        }

        public Criteria andFirstNameEqualTo(String value) {
            addCriterion("first_name =", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotEqualTo(String value) {
            addCriterion("first_name <>", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThan(String value) {
            addCriterion("first_name >", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThanOrEqualTo(String value) {
            addCriterion("first_name >=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThan(String value) {
            addCriterion("first_name <", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThanOrEqualTo(String value) {
            addCriterion("first_name <=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLike(String value) {
            addCriterion("first_name like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotLike(String value) {
            addCriterion("first_name not like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIn(List<String> values) {
            addCriterion("first_name in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotIn(List<String> values) {
            addCriterion("first_name not in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameBetween(String value1, String value2) {
            addCriterion("first_name between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotBetween(String value1, String value2) {
            addCriterion("first_name not between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andPasswordIsNull() {
            addCriterion("password is null");
            return (Criteria) this;
        }

        public Criteria andPasswordIsNotNull() {
            addCriterion("password is not null");
            return (Criteria) this;
        }

        public Criteria andPasswordEqualTo(String value) {
            addCriterion("password =", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotEqualTo(String value) {
            addCriterion("password <>", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordGreaterThan(String value) {
            addCriterion("password >", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordGreaterThanOrEqualTo(String value) {
            addCriterion("password >=", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLessThan(String value) {
            addCriterion("password <", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLessThanOrEqualTo(String value) {
            addCriterion("password <=", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLike(String value) {
            addCriterion("password like", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotLike(String value) {
            addCriterion("password not like", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordIn(List<String> values) {
            addCriterion("password in", values, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotIn(List<String> values) {
            addCriterion("password not in", values, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordBetween(String value1, String value2) {
            addCriterion("password between", value1, value2, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotBetween(String value1, String value2) {
            addCriterion("password not between", value1, value2, "password");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeIsNull() {
            addCriterion("enroll_code is null");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeIsNotNull() {
            addCriterion("enroll_code is not null");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeEqualTo(String value) {
            addCriterion("enroll_code =", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeNotEqualTo(String value) {
            addCriterion("enroll_code <>", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeGreaterThan(String value) {
            addCriterion("enroll_code >", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeGreaterThanOrEqualTo(String value) {
            addCriterion("enroll_code >=", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeLessThan(String value) {
            addCriterion("enroll_code <", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeLessThanOrEqualTo(String value) {
            addCriterion("enroll_code <=", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeLike(String value) {
            addCriterion("enroll_code like", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeNotLike(String value) {
            addCriterion("enroll_code not like", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeIn(List<String> values) {
            addCriterion("enroll_code in", values, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeNotIn(List<String> values) {
            addCriterion("enroll_code not in", values, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeBetween(String value1, String value2) {
            addCriterion("enroll_code between", value1, value2, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeNotBetween(String value1, String value2) {
            addCriterion("enroll_code not between", value1, value2, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andStuClassIsNull() {
            addCriterion("stu_class is null");
            return (Criteria) this;
        }

        public Criteria andStuClassIsNotNull() {
            addCriterion("stu_class is not null");
            return (Criteria) this;
        }

        public Criteria andStuClassEqualTo(String value) {
            addCriterion("stu_class =", value, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassNotEqualTo(String value) {
            addCriterion("stu_class <>", value, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassGreaterThan(String value) {
            addCriterion("stu_class >", value, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassGreaterThanOrEqualTo(String value) {
            addCriterion("stu_class >=", value, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassLessThan(String value) {
            addCriterion("stu_class <", value, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassLessThanOrEqualTo(String value) {
            addCriterion("stu_class <=", value, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassLike(String value) {
            addCriterion("stu_class like", value, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassNotLike(String value) {
            addCriterion("stu_class not like", value, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassIn(List<String> values) {
            addCriterion("stu_class in", values, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassNotIn(List<String> values) {
            addCriterion("stu_class not in", values, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassBetween(String value1, String value2) {
            addCriterion("stu_class between", value1, value2, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuClassNotBetween(String value1, String value2) {
            addCriterion("stu_class not between", value1, value2, "stuClass");
            return (Criteria) this;
        }

        public Criteria andStuHousingIsNull() {
            addCriterion("stu_housing is null");
            return (Criteria) this;
        }

        public Criteria andStuHousingIsNotNull() {
            addCriterion("stu_housing is not null");
            return (Criteria) this;
        }

        public Criteria andStuHousingEqualTo(String value) {
            addCriterion("stu_housing =", value, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingNotEqualTo(String value) {
            addCriterion("stu_housing <>", value, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingGreaterThan(String value) {
            addCriterion("stu_housing >", value, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingGreaterThanOrEqualTo(String value) {
            addCriterion("stu_housing >=", value, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingLessThan(String value) {
            addCriterion("stu_housing <", value, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingLessThanOrEqualTo(String value) {
            addCriterion("stu_housing <=", value, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingLike(String value) {
            addCriterion("stu_housing like", value, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingNotLike(String value) {
            addCriterion("stu_housing not like", value, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingIn(List<String> values) {
            addCriterion("stu_housing in", values, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingNotIn(List<String> values) {
            addCriterion("stu_housing not in", values, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingBetween(String value1, String value2) {
            addCriterion("stu_housing between", value1, value2, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuHousingNotBetween(String value1, String value2) {
            addCriterion("stu_housing not between", value1, value2, "stuHousing");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameIsNull() {
            addCriterion("stu_team_name is null");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameIsNotNull() {
            addCriterion("stu_team_name is not null");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameEqualTo(String value) {
            addCriterion("stu_team_name =", value, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameNotEqualTo(String value) {
            addCriterion("stu_team_name <>", value, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameGreaterThan(String value) {
            addCriterion("stu_team_name >", value, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameGreaterThanOrEqualTo(String value) {
            addCriterion("stu_team_name >=", value, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameLessThan(String value) {
            addCriterion("stu_team_name <", value, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameLessThanOrEqualTo(String value) {
            addCriterion("stu_team_name <=", value, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameLike(String value) {
            addCriterion("stu_team_name like", value, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameNotLike(String value) {
            addCriterion("stu_team_name not like", value, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameIn(List<String> values) {
            addCriterion("stu_team_name in", values, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameNotIn(List<String> values) {
            addCriterion("stu_team_name not in", values, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameBetween(String value1, String value2) {
            addCriterion("stu_team_name between", value1, value2, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andStuTeamNameNotBetween(String value1, String value2) {
            addCriterion("stu_team_name not between", value1, value2, "stuTeamName");
            return (Criteria) this;
        }

        public Criteria andTeamRoleIsNull() {
            addCriterion("team_role is null");
            return (Criteria) this;
        }

        public Criteria andTeamRoleIsNotNull() {
            addCriterion("team_role is not null");
            return (Criteria) this;
        }

        public Criteria andTeamRoleEqualTo(Integer value) {
            addCriterion("team_role =", value, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleNotEqualTo(Integer value) {
            addCriterion("team_role <>", value, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleGreaterThan(Integer value) {
            addCriterion("team_role >", value, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleGreaterThanOrEqualTo(Integer value) {
            addCriterion("team_role >=", value, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleLessThan(Integer value) {
            addCriterion("team_role <", value, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleLessThanOrEqualTo(Integer value) {
            addCriterion("team_role <=", value, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleIn(List<Integer> values) {
            addCriterion("team_role in", values, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleNotIn(List<Integer> values) {
            addCriterion("team_role not in", values, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleBetween(Integer value1, Integer value2) {
            addCriterion("team_role between", value1, value2, "teamRole");
            return (Criteria) this;
        }

        public Criteria andTeamRoleNotBetween(Integer value1, Integer value2) {
            addCriterion("team_role not between", value1, value2, "teamRole");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomIsNull() {
            addCriterion("discussion_room is null");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomIsNotNull() {
            addCriterion("discussion_room is not null");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomEqualTo(String value) {
            addCriterion("discussion_room =", value, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomNotEqualTo(String value) {
            addCriterion("discussion_room <>", value, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomGreaterThan(String value) {
            addCriterion("discussion_room >", value, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomGreaterThanOrEqualTo(String value) {
            addCriterion("discussion_room >=", value, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomLessThan(String value) {
            addCriterion("discussion_room <", value, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomLessThanOrEqualTo(String value) {
            addCriterion("discussion_room <=", value, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomLike(String value) {
            addCriterion("discussion_room like", value, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomNotLike(String value) {
            addCriterion("discussion_room not like", value, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomIn(List<String> values) {
            addCriterion("discussion_room in", values, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomNotIn(List<String> values) {
            addCriterion("discussion_room not in", values, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomBetween(String value1, String value2) {
            addCriterion("discussion_room between", value1, value2, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andDiscussionRoomNotBetween(String value1, String value2) {
            addCriterion("discussion_room not between", value1, value2, "discussionRoom");
            return (Criteria) this;
        }

        public Criteria andTermsIsNull() {
            addCriterion("terms is null");
            return (Criteria) this;
        }

        public Criteria andTermsIsNotNull() {
            addCriterion("terms is not null");
            return (Criteria) this;
        }

        public Criteria andTermsEqualTo(Integer value) {
            addCriterion("terms =", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsNotEqualTo(Integer value) {
            addCriterion("terms <>", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsGreaterThan(Integer value) {
            addCriterion("terms >", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsGreaterThanOrEqualTo(Integer value) {
            addCriterion("terms >=", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsLessThan(Integer value) {
            addCriterion("terms <", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsLessThanOrEqualTo(Integer value) {
            addCriterion("terms <=", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsIn(List<Integer> values) {
            addCriterion("terms in", values, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsNotIn(List<Integer> values) {
            addCriterion("terms not in", values, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsBetween(Integer value1, Integer value2) {
            addCriterion("terms between", value1, value2, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsNotBetween(Integer value1, Integer value2) {
            addCriterion("terms not between", value1, value2, "terms");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}