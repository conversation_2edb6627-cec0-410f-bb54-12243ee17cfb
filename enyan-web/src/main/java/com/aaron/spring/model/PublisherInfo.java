package com.aaron.spring.model;

import com.alibaba.fastjson2.annotation.JSONField;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/1
 * @Modified By:
 */
public class PublisherInfo implements Serializable {
    private static final long serialVersionUID = -3272034258815554352L;
    @JSONField(name = "pub")
    private long publisherId;

    public long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(long publisherId) {
        this.publisherId = publisherId;
    }
}
