package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

public class EnyanOrder extends BaseDTO{
    private static final long serialVersionUID = -4986320091744966155L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long orderId;

    private String orderNum;

    private String orderBookHash;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long userId;

    private String userEmail;

    private String orderTitle;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date purchasedAt;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date expiredAt;

    private BigDecimal orderDiscount;

    private BigDecimal orderTotal;

    private Byte orderCurrency;

    private Byte isValid;

    private Byte isPaid;

    private String payInfo;

    private Integer orderType;

    private Byte isCounted;

    private Integer orderFrom;

    private Integer isDeleted;

    private String orderDetail;

    private OrderTitleInfo orderTitleInfo;

    private OrderDetailInfo orderDetailInfo;

    private OrderPayInfo orderPayInfo;

    ///订单里的书籍列表
    private Map<Long,EnyanBook> bookMap;

    private String dateString;

    private Long count;

    private Long count2;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum == null ? null : orderNum.trim();
    }

    public String getOrderBookHash() {
        return orderBookHash;
    }

    public void setOrderBookHash(String orderBookHash) {
        this.orderBookHash = orderBookHash == null ? null : orderBookHash.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail == null ? null : userEmail.trim();
    }

    public String getOrderTitle() {
        return orderTitle;
    }

    public void setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle == null ? null : orderTitle.trim();
    }

    public Date getPurchasedAt() {
        return purchasedAt;
    }

    public void setPurchasedAt(Date purchasedAt) {
        this.purchasedAt = purchasedAt;
    }

    public Date getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(Date expiredAt) {
        this.expiredAt = expiredAt;
    }

    public BigDecimal getOrderDiscount() {
        return orderDiscount;
    }

    public void setOrderDiscount(BigDecimal orderDiscount) {
        this.orderDiscount = orderDiscount;
    }

    public BigDecimal getOrderTotal() {
        return orderTotal;
    }

    public void setOrderTotal(BigDecimal orderTotal) {
        this.orderTotal = orderTotal;
    }

    public Byte getOrderCurrency() {
        return orderCurrency;
    }

    public void setOrderCurrency(Byte orderCurrency) {
        this.orderCurrency = orderCurrency;
    }

    public Byte getIsValid() {
        return isValid;
    }

    public void setIsValid(Byte isValid) {
        this.isValid = isValid;
    }

    public Byte getIsPaid() {
        return isPaid;
    }

    public void setIsPaid(Byte isPaid) {
        this.isPaid = isPaid;
    }

    public String getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(String payInfo) {
        this.payInfo = payInfo == null ? null : payInfo.trim();
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Byte getIsCounted() {
        return isCounted;
    }

    public void setIsCounted(Byte isCounted) {
        this.isCounted = isCounted;
    }

    public Integer getOrderFrom() {
        return orderFrom;
    }

    public void setOrderFrom(Integer orderFrom) {
        this.orderFrom = orderFrom;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getOrderDetail() {
        return orderDetail;
    }

    public void setOrderDetail(String orderDetail) {
        this.orderDetail = orderDetail == null ? null : orderDetail.trim();
    }

    public OrderTitleInfo getOrderTitleInfo() {
        return orderTitleInfo;
    }

    public void setOrderTitleInfo(OrderTitleInfo orderTitleInfo) {
        this.orderTitleInfo = orderTitleInfo;
    }

    public OrderDetailInfo getOrderDetailInfo() {
        return orderDetailInfo;
    }

    public void setOrderDetailInfo(OrderDetailInfo orderDetailInfo) {
        this.orderDetailInfo = orderDetailInfo;
    }

    public OrderPayInfo getOrderPayInfo() {
        return orderPayInfo;
    }

    public void setOrderPayInfo(OrderPayInfo orderPayInfo) {
        this.orderPayInfo = orderPayInfo;
    }

    public Map<Long, EnyanBook> getBookMap() {
        return bookMap;
    }

    public void setBookMap(Map<Long, EnyanBook> bookMap) {
        this.bookMap = bookMap;
    }

    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public Long getCount2() {
        return count2;
    }

    public void setCount2(Long count2) {
        this.count2 = count2;
    }
}