package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serial;

public class StuInfo extends BaseDTO{
    @Serial
    private static final long serialVersionUID = 2044498998628381736L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long dataId;

    private Integer stuId;

    private String lastName;

    private String firstName;

    private String email;

    private String password;

    private String enrollCode;

    private String stuClass;

    private String stuHousing;

    private String stuTeamName;

    private Integer teamRole;

    private String discussionRoom;

    private Integer terms;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public Integer getStuId() {
        return stuId;
    }

    public void setStuId(Integer stuId) {
        this.stuId = stuId;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName == null ? null : lastName.trim();
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName == null ? null : firstName.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public String getEnrollCode() {
        return enrollCode;
    }

    public void setEnrollCode(String enrollCode) {
        this.enrollCode = enrollCode == null ? null : enrollCode.trim();
    }

    public String getStuClass() {
        return stuClass;
    }

    public void setStuClass(String stuClass) {
        this.stuClass = stuClass == null ? null : stuClass.trim();
    }

    public String getStuHousing() {
        return stuHousing;
    }

    public void setStuHousing(String stuHousing) {
        this.stuHousing = stuHousing == null ? null : stuHousing.trim();
    }

    public String getStuTeamName() {
        return stuTeamName;
    }

    public void setStuTeamName(String stuTeamName) {
        this.stuTeamName = stuTeamName == null ? null : stuTeamName.trim();
    }

    public Integer getTeamRole() {
        return teamRole;
    }

    public void setTeamRole(Integer teamRole) {
        this.teamRole = teamRole;
    }

    public String getDiscussionRoom() {
        return discussionRoom;
    }

    public void setDiscussionRoom(String discussionRoom) {
        this.discussionRoom = discussionRoom == null ? null : discussionRoom.trim();
    }

    public Integer getTerms() {
        return terms;
    }

    public void setTerms(Integer terms) {
        this.terms = terms;
    }
}