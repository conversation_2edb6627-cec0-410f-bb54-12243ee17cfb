package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

public class EnyanCategory extends BaseDTO{
    private static final long serialVersionUID = 8072528359706107589L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long categoryId;

    private String categoryName;

    private String categoryNameTc;

    private String categoryNameEn;

    private Byte isIndex;

    private Integer isHidden;

    private Integer categoryOrder;

    private String bookRecommended;

    private List<MutiSelectModel> bookRecommendList;

    private String[] bookIDs;

    private String bookRecommendedOld;

    private List<EnyanBook> records ;

    /**
     * 长banner
     * */
    private String bannerUrl;

    /**
     * 封面的URL
     * */
    private String bookCoverUrl;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    public String getCategoryNameTc() {
        return categoryNameTc;
    }

    public void setCategoryNameTc(String categoryNameTc) {
        this.categoryNameTc = categoryNameTc == null ? null : categoryNameTc.trim();
    }

    public String getCategoryNameEn() {
        return categoryNameEn;
    }

    public void setCategoryNameEn(String categoryNameEn) {
        this.categoryNameEn = categoryNameEn == null ? null : categoryNameEn.trim();
    }

    public Byte getIsIndex() {
        return isIndex;
    }

    public void setIsIndex(Byte isIndex) {
        this.isIndex = isIndex;
    }

    public Integer getIsHidden() {
        return isHidden;
    }

    public void setIsHidden(Integer isHidden) {
        this.isHidden = isHidden;
    }

    public Integer getCategoryOrder() {
        return categoryOrder;
    }

    public void setCategoryOrder(Integer categoryOrder) {
        this.categoryOrder = categoryOrder;
    }

    public String getBookRecommended() {
        return bookRecommended;
    }

    public void setBookRecommended(String bookRecommended) {
        this.bookRecommended = bookRecommended == null ? null : bookRecommended.trim();
    }

    public List<MutiSelectModel> getBookRecommendList() {
        return bookRecommendList;
    }

    public void setBookRecommendList(List<MutiSelectModel> bookRecommendList) {
        this.bookRecommendList = bookRecommendList;
    }

    public String[] getBookIDs() {
        return bookIDs;
    }

    public void setBookIDs(String[] bookIDs) {
        this.bookIDs = bookIDs;
    }

    public String getBookRecommendedOld() {
        return bookRecommendedOld;
    }

    public void setBookRecommendedOld(String bookRecommendedOld) {
        this.bookRecommendedOld = bookRecommendedOld;
    }

    public List<EnyanBook> getRecords() {
        return records;
    }

    public void setRecords(List<EnyanBook> records) {
        this.records = records;
    }

    public String getBannerUrl() {
        return bannerUrl;
    }

    public void setBannerUrl(String bannerUrl) {
        this.bannerUrl = bannerUrl;
    }

    public String getBookCoverUrl() {
        return bookCoverUrl;
    }

    public void setBookCoverUrl(String bookCoverUrl) {
        this.bookCoverUrl = bookCoverUrl;
    }
}