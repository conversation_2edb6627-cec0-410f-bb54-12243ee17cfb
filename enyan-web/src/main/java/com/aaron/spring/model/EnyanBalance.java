package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;

public class EnyanBalance extends BaseDTO{

    private static final long serialVersionUID = 2896772827334827081L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long balanceId;

    private Integer purchasedMonth;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long publisherId;

    private Integer quantity;

    private BigDecimal priceFixed;

    private BigDecimal priceSelling;

    private BigDecimal incomeVendor;

    private BigDecimal incomePlat;

    private BigDecimal incomeTotal;

    private BigDecimal incomeReal;

    private Integer orderType;

    private Byte isCounted;

    public Long getBalanceId() {
        return balanceId;
    }

    public void setBalanceId(Long balanceId) {
        this.balanceId = balanceId;
    }

    public Integer getPurchasedMonth() {
        return purchasedMonth;
    }

    public void setPurchasedMonth(Integer purchasedMonth) {
        this.purchasedMonth = purchasedMonth;
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPriceFixed() {
        return priceFixed;
    }

    public void setPriceFixed(BigDecimal priceFixed) {
        this.priceFixed = priceFixed;
    }

    public BigDecimal getPriceSelling() {
        return priceSelling;
    }

    public void setPriceSelling(BigDecimal priceSelling) {
        this.priceSelling = priceSelling;
    }

    public BigDecimal getIncomeVendor() {
        return incomeVendor;
    }

    public void setIncomeVendor(BigDecimal incomeVendor) {
        this.incomeVendor = incomeVendor;
    }

    public BigDecimal getIncomePlat() {
        return incomePlat;
    }

    public void setIncomePlat(BigDecimal incomePlat) {
        this.incomePlat = incomePlat;
    }

    public BigDecimal getIncomeTotal() {
        return incomeTotal;
    }

    public void setIncomeTotal(BigDecimal incomeTotal) {
        this.incomeTotal = incomeTotal;
    }

    public BigDecimal getIncomeReal() {
        return incomeReal;
    }

    public void setIncomeReal(BigDecimal incomeReal) {
        this.incomeReal = incomeReal;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Byte getIsCounted() {
        return isCounted;
    }

    public void setIsCounted(Byte isCounted) {
        this.isCounted = isCounted;
    }
}