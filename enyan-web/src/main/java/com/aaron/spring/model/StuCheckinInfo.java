package com.aaron.spring.model;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/5/10
 * @Modified By:
 */
public class StuCheckinInfo implements Serializable {
	@Serial
	private static final long serialVersionUID = 446982969196628613L;

	private List<StuCheckin> checkinList;

	public void resetData(Long current){
		if (null == checkinList || checkinList.isEmpty()){
			return;
		}
		//Date current = new Date();
		for (StuCheckin obj : checkinList) {
			obj.resetData(current);
		}
	}

	public List<StuCheckin> getCheckinList() {
		return checkinList;
	}

	public void setCheckinList(List<StuCheckin> checkinList) {
		this.checkinList = checkinList;
	}
}