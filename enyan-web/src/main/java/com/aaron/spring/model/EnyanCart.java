package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Date;

public class EnyanCart extends BaseDTO{
    private static final long serialVersionUID = 2730005301845072505L;
    private String cartId;

    private String userEmail;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookId;

    private Integer quantity;

    private Date addAt;

    public String getCartId() {
        return cartId;
    }

    public void setCartId(String cartId) {
        this.cartId = cartId == null ? null : cartId.trim();
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail == null ? null : userEmail.trim();
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Date getAddAt() {
        return addAt;
    }

    public void setAddAt(Date addAt) {
        this.addAt = addAt;
    }
}