package com.aaron.spring.model;



import java.util.Date;

/**
 * 包含播客信息的收藏记录
 */
public class PodFavoriteWithPodcast extends com.aaron.spring.model.PodFavorite {
    
    // 播客标题
    private String podcastTitle;
    
    // 播客描述
    private String podcastDescription;
    
    // 播客封面图URL
    private String podcastCoverImageUrl;
    
    // 播客详情页长条图URL
    private String podcastCoverImageUrl2;
    
    // 作者名称
    private String authorName;
    
    // 剧集数量
    private Integer episodeCount;
    
    // 显示顺序
    private Integer displayOrder;
    
    // 发布日期
    private Date publicationDate;

    public String getPodcastTitle() {
        return podcastTitle;
    }

    public void setPodcastTitle(String podcastTitle) {
        this.podcastTitle = podcastTitle;
    }

    public String getPodcastDescription() {
        return podcastDescription;
    }

    public void setPodcastDescription(String podcastDescription) {
        this.podcastDescription = podcastDescription;
    }

    public String getPodcastCoverImageUrl() {
        return podcastCoverImageUrl;
    }

    public void setPodcastCoverImageUrl(String podcastCoverImageUrl) {
        this.podcastCoverImageUrl = podcastCoverImageUrl;
    }

    public String getPodcastCoverImageUrl2() {
        return podcastCoverImageUrl2;
    }

    public void setPodcastCoverImageUrl2(String podcastCoverImageUrl2) {
        this.podcastCoverImageUrl2 = podcastCoverImageUrl2;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }
    
    public Integer getEpisodeCount() {
        return episodeCount;
    }

    public void setEpisodeCount(Integer episodeCount) {
        this.episodeCount = episodeCount;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public Date getPublicationDate() {
        return publicationDate;
    }

    public void setPublicationDate(Date publicationDate) {
        this.publicationDate = publicationDate;
    }
}
