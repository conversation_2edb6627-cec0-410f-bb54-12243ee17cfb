package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DataRentStatExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public DataRentStatExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDataIdIsNull() {
            addCriterion("data_id is null");
            return (Criteria) this;
        }

        public Criteria andDataIdIsNotNull() {
            addCriterion("data_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataIdEqualTo(Long value) {
            addCriterion("data_id =", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotEqualTo(Long value) {
            addCriterion("data_id <>", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThan(Long value) {
            addCriterion("data_id >", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_id >=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThan(Long value) {
            addCriterion("data_id <", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThanOrEqualTo(Long value) {
            addCriterion("data_id <=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdIn(List<Long> values) {
            addCriterion("data_id in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotIn(List<Long> values) {
            addCriterion("data_id not in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdBetween(Long value1, Long value2) {
            addCriterion("data_id between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotBetween(Long value1, Long value2) {
            addCriterion("data_id not between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumIsNull() {
            addCriterion("rent_sc_all_sum is null");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumIsNotNull() {
            addCriterion("rent_sc_all_sum is not null");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumEqualTo(Integer value) {
            addCriterion("rent_sc_all_sum =", value, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumNotEqualTo(Integer value) {
            addCriterion("rent_sc_all_sum <>", value, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumGreaterThan(Integer value) {
            addCriterion("rent_sc_all_sum >", value, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_sum >=", value, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumLessThan(Integer value) {
            addCriterion("rent_sc_all_sum <", value, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_sum <=", value, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumIn(List<Integer> values) {
            addCriterion("rent_sc_all_sum in", values, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumNotIn(List<Integer> values) {
            addCriterion("rent_sc_all_sum not in", values, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_sum between", value1, value2, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllSumNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_sum not between", value1, value2, "rentScAllSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumIsNull() {
            addCriterion("rent_sc_ot_sum is null");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumIsNotNull() {
            addCriterion("rent_sc_ot_sum is not null");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumEqualTo(Integer value) {
            addCriterion("rent_sc_ot_sum =", value, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumNotEqualTo(Integer value) {
            addCriterion("rent_sc_ot_sum <>", value, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumGreaterThan(Integer value) {
            addCriterion("rent_sc_ot_sum >", value, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_sum >=", value, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumLessThan(Integer value) {
            addCriterion("rent_sc_ot_sum <", value, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_sum <=", value, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumIn(List<Integer> values) {
            addCriterion("rent_sc_ot_sum in", values, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumNotIn(List<Integer> values) {
            addCriterion("rent_sc_ot_sum not in", values, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_sum between", value1, value2, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScOtSumNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_sum not between", value1, value2, "rentScOtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumIsNull() {
            addCriterion("rent_sc_nt_sum is null");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumIsNotNull() {
            addCriterion("rent_sc_nt_sum is not null");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumEqualTo(Integer value) {
            addCriterion("rent_sc_nt_sum =", value, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumNotEqualTo(Integer value) {
            addCriterion("rent_sc_nt_sum <>", value, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumGreaterThan(Integer value) {
            addCriterion("rent_sc_nt_sum >", value, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_sum >=", value, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumLessThan(Integer value) {
            addCriterion("rent_sc_nt_sum <", value, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_sum <=", value, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumIn(List<Integer> values) {
            addCriterion("rent_sc_nt_sum in", values, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumNotIn(List<Integer> values) {
            addCriterion("rent_sc_nt_sum not in", values, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_sum between", value1, value2, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScNtSumNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_sum not between", value1, value2, "rentScNtSum");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewIsNull() {
            addCriterion("rent_sc_all_new is null");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewIsNotNull() {
            addCriterion("rent_sc_all_new is not null");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewEqualTo(Integer value) {
            addCriterion("rent_sc_all_new =", value, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewNotEqualTo(Integer value) {
            addCriterion("rent_sc_all_new <>", value, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewGreaterThan(Integer value) {
            addCriterion("rent_sc_all_new >", value, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_new >=", value, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewLessThan(Integer value) {
            addCriterion("rent_sc_all_new <", value, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_new <=", value, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewIn(List<Integer> values) {
            addCriterion("rent_sc_all_new in", values, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewNotIn(List<Integer> values) {
            addCriterion("rent_sc_all_new not in", values, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_new between", value1, value2, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllNewNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_new not between", value1, value2, "rentScAllNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewIsNull() {
            addCriterion("rent_sc_ot_new is null");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewIsNotNull() {
            addCriterion("rent_sc_ot_new is not null");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewEqualTo(Integer value) {
            addCriterion("rent_sc_ot_new =", value, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewNotEqualTo(Integer value) {
            addCriterion("rent_sc_ot_new <>", value, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewGreaterThan(Integer value) {
            addCriterion("rent_sc_ot_new >", value, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_new >=", value, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewLessThan(Integer value) {
            addCriterion("rent_sc_ot_new <", value, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_new <=", value, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewIn(List<Integer> values) {
            addCriterion("rent_sc_ot_new in", values, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewNotIn(List<Integer> values) {
            addCriterion("rent_sc_ot_new not in", values, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_new between", value1, value2, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScOtNewNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_new not between", value1, value2, "rentScOtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewIsNull() {
            addCriterion("rent_sc_nt_new is null");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewIsNotNull() {
            addCriterion("rent_sc_nt_new is not null");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewEqualTo(Integer value) {
            addCriterion("rent_sc_nt_new =", value, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewNotEqualTo(Integer value) {
            addCriterion("rent_sc_nt_new <>", value, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewGreaterThan(Integer value) {
            addCriterion("rent_sc_nt_new >", value, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_new >=", value, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewLessThan(Integer value) {
            addCriterion("rent_sc_nt_new <", value, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_new <=", value, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewIn(List<Integer> values) {
            addCriterion("rent_sc_nt_new in", values, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewNotIn(List<Integer> values) {
            addCriterion("rent_sc_nt_new not in", values, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_new between", value1, value2, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScNtNewNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_new not between", value1, value2, "rentScNtNew");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveIsNull() {
            addCriterion("rent_sc_all_leave is null");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveIsNotNull() {
            addCriterion("rent_sc_all_leave is not null");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveEqualTo(Integer value) {
            addCriterion("rent_sc_all_leave =", value, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveNotEqualTo(Integer value) {
            addCriterion("rent_sc_all_leave <>", value, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveGreaterThan(Integer value) {
            addCriterion("rent_sc_all_leave >", value, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_leave >=", value, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveLessThan(Integer value) {
            addCriterion("rent_sc_all_leave <", value, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_leave <=", value, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveIn(List<Integer> values) {
            addCriterion("rent_sc_all_leave in", values, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveNotIn(List<Integer> values) {
            addCriterion("rent_sc_all_leave not in", values, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_leave between", value1, value2, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllLeaveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_leave not between", value1, value2, "rentScAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveIsNull() {
            addCriterion("rent_sc_ot_leave is null");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveIsNotNull() {
            addCriterion("rent_sc_ot_leave is not null");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveEqualTo(Integer value) {
            addCriterion("rent_sc_ot_leave =", value, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveNotEqualTo(Integer value) {
            addCriterion("rent_sc_ot_leave <>", value, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveGreaterThan(Integer value) {
            addCriterion("rent_sc_ot_leave >", value, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_leave >=", value, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveLessThan(Integer value) {
            addCriterion("rent_sc_ot_leave <", value, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_leave <=", value, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveIn(List<Integer> values) {
            addCriterion("rent_sc_ot_leave in", values, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveNotIn(List<Integer> values) {
            addCriterion("rent_sc_ot_leave not in", values, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_leave between", value1, value2, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScOtLeaveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_leave not between", value1, value2, "rentScOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveIsNull() {
            addCriterion("rent_sc_nt_leave is null");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveIsNotNull() {
            addCriterion("rent_sc_nt_leave is not null");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveEqualTo(Integer value) {
            addCriterion("rent_sc_nt_leave =", value, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveNotEqualTo(Integer value) {
            addCriterion("rent_sc_nt_leave <>", value, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveGreaterThan(Integer value) {
            addCriterion("rent_sc_nt_leave >", value, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_leave >=", value, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveLessThan(Integer value) {
            addCriterion("rent_sc_nt_leave <", value, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_leave <=", value, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveIn(List<Integer> values) {
            addCriterion("rent_sc_nt_leave in", values, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveNotIn(List<Integer> values) {
            addCriterion("rent_sc_nt_leave not in", values, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_leave between", value1, value2, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScNtLeaveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_leave not between", value1, value2, "rentScNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyIsNull() {
            addCriterion("rent_sc_all_buy is null");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyIsNotNull() {
            addCriterion("rent_sc_all_buy is not null");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyEqualTo(Integer value) {
            addCriterion("rent_sc_all_buy =", value, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyNotEqualTo(Integer value) {
            addCriterion("rent_sc_all_buy <>", value, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyGreaterThan(Integer value) {
            addCriterion("rent_sc_all_buy >", value, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_buy >=", value, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyLessThan(Integer value) {
            addCriterion("rent_sc_all_buy <", value, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_buy <=", value, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyIn(List<Integer> values) {
            addCriterion("rent_sc_all_buy in", values, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyNotIn(List<Integer> values) {
            addCriterion("rent_sc_all_buy not in", values, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_buy between", value1, value2, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllBuyNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_buy not between", value1, value2, "rentScAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyIsNull() {
            addCriterion("rent_sc_ot_buy is null");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyIsNotNull() {
            addCriterion("rent_sc_ot_buy is not null");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyEqualTo(Integer value) {
            addCriterion("rent_sc_ot_buy =", value, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyNotEqualTo(Integer value) {
            addCriterion("rent_sc_ot_buy <>", value, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyGreaterThan(Integer value) {
            addCriterion("rent_sc_ot_buy >", value, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_buy >=", value, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyLessThan(Integer value) {
            addCriterion("rent_sc_ot_buy <", value, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_buy <=", value, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyIn(List<Integer> values) {
            addCriterion("rent_sc_ot_buy in", values, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyNotIn(List<Integer> values) {
            addCriterion("rent_sc_ot_buy not in", values, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_buy between", value1, value2, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScOtBuyNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_buy not between", value1, value2, "rentScOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyIsNull() {
            addCriterion("rent_sc_nt_buy is null");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyIsNotNull() {
            addCriterion("rent_sc_nt_buy is not null");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyEqualTo(Integer value) {
            addCriterion("rent_sc_nt_buy =", value, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyNotEqualTo(Integer value) {
            addCriterion("rent_sc_nt_buy <>", value, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyGreaterThan(Integer value) {
            addCriterion("rent_sc_nt_buy >", value, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_buy >=", value, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyLessThan(Integer value) {
            addCriterion("rent_sc_nt_buy <", value, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_buy <=", value, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyIn(List<Integer> values) {
            addCriterion("rent_sc_nt_buy in", values, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyNotIn(List<Integer> values) {
            addCriterion("rent_sc_nt_buy not in", values, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_buy between", value1, value2, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScNtBuyNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_buy not between", value1, value2, "rentScNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveIsNull() {
            addCriterion("rent_sc_all_active is null");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveIsNotNull() {
            addCriterion("rent_sc_all_active is not null");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveEqualTo(Integer value) {
            addCriterion("rent_sc_all_active =", value, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveNotEqualTo(Integer value) {
            addCriterion("rent_sc_all_active <>", value, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveGreaterThan(Integer value) {
            addCriterion("rent_sc_all_active >", value, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_active >=", value, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveLessThan(Integer value) {
            addCriterion("rent_sc_all_active <", value, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_all_active <=", value, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveIn(List<Integer> values) {
            addCriterion("rent_sc_all_active in", values, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveNotIn(List<Integer> values) {
            addCriterion("rent_sc_all_active not in", values, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_active between", value1, value2, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllActiveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_all_active not between", value1, value2, "rentScAllActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveIsNull() {
            addCriterion("rent_sc_ot_active is null");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveIsNotNull() {
            addCriterion("rent_sc_ot_active is not null");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveEqualTo(Integer value) {
            addCriterion("rent_sc_ot_active =", value, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveNotEqualTo(Integer value) {
            addCriterion("rent_sc_ot_active <>", value, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveGreaterThan(Integer value) {
            addCriterion("rent_sc_ot_active >", value, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_active >=", value, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveLessThan(Integer value) {
            addCriterion("rent_sc_ot_active <", value, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_ot_active <=", value, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveIn(List<Integer> values) {
            addCriterion("rent_sc_ot_active in", values, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveNotIn(List<Integer> values) {
            addCriterion("rent_sc_ot_active not in", values, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_active between", value1, value2, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScOtActiveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_ot_active not between", value1, value2, "rentScOtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveIsNull() {
            addCriterion("rent_sc_nt_active is null");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveIsNotNull() {
            addCriterion("rent_sc_nt_active is not null");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveEqualTo(Integer value) {
            addCriterion("rent_sc_nt_active =", value, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveNotEqualTo(Integer value) {
            addCriterion("rent_sc_nt_active <>", value, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveGreaterThan(Integer value) {
            addCriterion("rent_sc_nt_active >", value, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_active >=", value, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveLessThan(Integer value) {
            addCriterion("rent_sc_nt_active <", value, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_sc_nt_active <=", value, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveIn(List<Integer> values) {
            addCriterion("rent_sc_nt_active in", values, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveNotIn(List<Integer> values) {
            addCriterion("rent_sc_nt_active not in", values, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_active between", value1, value2, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScNtActiveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_sc_nt_active not between", value1, value2, "rentScNtActive");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeIsNull() {
            addCriterion("rent_sc_all_time is null");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeIsNotNull() {
            addCriterion("rent_sc_all_time is not null");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeEqualTo(String value) {
            addCriterion("rent_sc_all_time =", value, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeNotEqualTo(String value) {
            addCriterion("rent_sc_all_time <>", value, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeGreaterThan(String value) {
            addCriterion("rent_sc_all_time >", value, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeGreaterThanOrEqualTo(String value) {
            addCriterion("rent_sc_all_time >=", value, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeLessThan(String value) {
            addCriterion("rent_sc_all_time <", value, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeLessThanOrEqualTo(String value) {
            addCriterion("rent_sc_all_time <=", value, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeLike(String value) {
            addCriterion("rent_sc_all_time like", value, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeNotLike(String value) {
            addCriterion("rent_sc_all_time not like", value, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeIn(List<String> values) {
            addCriterion("rent_sc_all_time in", values, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeNotIn(List<String> values) {
            addCriterion("rent_sc_all_time not in", values, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeBetween(String value1, String value2) {
            addCriterion("rent_sc_all_time between", value1, value2, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScAllTimeNotBetween(String value1, String value2) {
            addCriterion("rent_sc_all_time not between", value1, value2, "rentScAllTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeIsNull() {
            addCriterion("rent_sc_ot_time is null");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeIsNotNull() {
            addCriterion("rent_sc_ot_time is not null");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeEqualTo(String value) {
            addCriterion("rent_sc_ot_time =", value, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeNotEqualTo(String value) {
            addCriterion("rent_sc_ot_time <>", value, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeGreaterThan(String value) {
            addCriterion("rent_sc_ot_time >", value, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeGreaterThanOrEqualTo(String value) {
            addCriterion("rent_sc_ot_time >=", value, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeLessThan(String value) {
            addCriterion("rent_sc_ot_time <", value, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeLessThanOrEqualTo(String value) {
            addCriterion("rent_sc_ot_time <=", value, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeLike(String value) {
            addCriterion("rent_sc_ot_time like", value, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeNotLike(String value) {
            addCriterion("rent_sc_ot_time not like", value, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeIn(List<String> values) {
            addCriterion("rent_sc_ot_time in", values, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeNotIn(List<String> values) {
            addCriterion("rent_sc_ot_time not in", values, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeBetween(String value1, String value2) {
            addCriterion("rent_sc_ot_time between", value1, value2, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScOtTimeNotBetween(String value1, String value2) {
            addCriterion("rent_sc_ot_time not between", value1, value2, "rentScOtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeIsNull() {
            addCriterion("rent_sc_nt_time is null");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeIsNotNull() {
            addCriterion("rent_sc_nt_time is not null");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeEqualTo(String value) {
            addCriterion("rent_sc_nt_time =", value, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeNotEqualTo(String value) {
            addCriterion("rent_sc_nt_time <>", value, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeGreaterThan(String value) {
            addCriterion("rent_sc_nt_time >", value, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeGreaterThanOrEqualTo(String value) {
            addCriterion("rent_sc_nt_time >=", value, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeLessThan(String value) {
            addCriterion("rent_sc_nt_time <", value, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeLessThanOrEqualTo(String value) {
            addCriterion("rent_sc_nt_time <=", value, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeLike(String value) {
            addCriterion("rent_sc_nt_time like", value, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeNotLike(String value) {
            addCriterion("rent_sc_nt_time not like", value, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeIn(List<String> values) {
            addCriterion("rent_sc_nt_time in", values, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeNotIn(List<String> values) {
            addCriterion("rent_sc_nt_time not in", values, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeBetween(String value1, String value2) {
            addCriterion("rent_sc_nt_time between", value1, value2, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentScNtTimeNotBetween(String value1, String value2) {
            addCriterion("rent_sc_nt_time not between", value1, value2, "rentScNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumIsNull() {
            addCriterion("rent_tc_all_sum is null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumIsNotNull() {
            addCriterion("rent_tc_all_sum is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumEqualTo(Integer value) {
            addCriterion("rent_tc_all_sum =", value, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumNotEqualTo(Integer value) {
            addCriterion("rent_tc_all_sum <>", value, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumGreaterThan(Integer value) {
            addCriterion("rent_tc_all_sum >", value, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_sum >=", value, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumLessThan(Integer value) {
            addCriterion("rent_tc_all_sum <", value, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_sum <=", value, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumIn(List<Integer> values) {
            addCriterion("rent_tc_all_sum in", values, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumNotIn(List<Integer> values) {
            addCriterion("rent_tc_all_sum not in", values, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_sum between", value1, value2, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllSumNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_sum not between", value1, value2, "rentTcAllSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumIsNull() {
            addCriterion("rent_tc_ot_sum is null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumIsNotNull() {
            addCriterion("rent_tc_ot_sum is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumEqualTo(Integer value) {
            addCriterion("rent_tc_ot_sum =", value, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumNotEqualTo(Integer value) {
            addCriterion("rent_tc_ot_sum <>", value, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumGreaterThan(Integer value) {
            addCriterion("rent_tc_ot_sum >", value, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_sum >=", value, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumLessThan(Integer value) {
            addCriterion("rent_tc_ot_sum <", value, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_sum <=", value, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumIn(List<Integer> values) {
            addCriterion("rent_tc_ot_sum in", values, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumNotIn(List<Integer> values) {
            addCriterion("rent_tc_ot_sum not in", values, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_sum between", value1, value2, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcOtSumNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_sum not between", value1, value2, "rentTcOtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumIsNull() {
            addCriterion("rent_tc_nt_sum is null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumIsNotNull() {
            addCriterion("rent_tc_nt_sum is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumEqualTo(Integer value) {
            addCriterion("rent_tc_nt_sum =", value, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumNotEqualTo(Integer value) {
            addCriterion("rent_tc_nt_sum <>", value, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumGreaterThan(Integer value) {
            addCriterion("rent_tc_nt_sum >", value, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_sum >=", value, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumLessThan(Integer value) {
            addCriterion("rent_tc_nt_sum <", value, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_sum <=", value, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumIn(List<Integer> values) {
            addCriterion("rent_tc_nt_sum in", values, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumNotIn(List<Integer> values) {
            addCriterion("rent_tc_nt_sum not in", values, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_sum between", value1, value2, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcNtSumNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_sum not between", value1, value2, "rentTcNtSum");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewIsNull() {
            addCriterion("rent_tc_all_new is null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewIsNotNull() {
            addCriterion("rent_tc_all_new is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewEqualTo(Integer value) {
            addCriterion("rent_tc_all_new =", value, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewNotEqualTo(Integer value) {
            addCriterion("rent_tc_all_new <>", value, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewGreaterThan(Integer value) {
            addCriterion("rent_tc_all_new >", value, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_new >=", value, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewLessThan(Integer value) {
            addCriterion("rent_tc_all_new <", value, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_new <=", value, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewIn(List<Integer> values) {
            addCriterion("rent_tc_all_new in", values, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewNotIn(List<Integer> values) {
            addCriterion("rent_tc_all_new not in", values, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_new between", value1, value2, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllNewNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_new not between", value1, value2, "rentTcAllNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewIsNull() {
            addCriterion("rent_tc_ot_new is null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewIsNotNull() {
            addCriterion("rent_tc_ot_new is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewEqualTo(Integer value) {
            addCriterion("rent_tc_ot_new =", value, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewNotEqualTo(Integer value) {
            addCriterion("rent_tc_ot_new <>", value, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewGreaterThan(Integer value) {
            addCriterion("rent_tc_ot_new >", value, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_new >=", value, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewLessThan(Integer value) {
            addCriterion("rent_tc_ot_new <", value, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_new <=", value, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewIn(List<Integer> values) {
            addCriterion("rent_tc_ot_new in", values, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewNotIn(List<Integer> values) {
            addCriterion("rent_tc_ot_new not in", values, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_new between", value1, value2, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcOtNewNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_new not between", value1, value2, "rentTcOtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewIsNull() {
            addCriterion("rent_tc_nt_new is null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewIsNotNull() {
            addCriterion("rent_tc_nt_new is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewEqualTo(Integer value) {
            addCriterion("rent_tc_nt_new =", value, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewNotEqualTo(Integer value) {
            addCriterion("rent_tc_nt_new <>", value, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewGreaterThan(Integer value) {
            addCriterion("rent_tc_nt_new >", value, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_new >=", value, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewLessThan(Integer value) {
            addCriterion("rent_tc_nt_new <", value, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_new <=", value, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewIn(List<Integer> values) {
            addCriterion("rent_tc_nt_new in", values, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewNotIn(List<Integer> values) {
            addCriterion("rent_tc_nt_new not in", values, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_new between", value1, value2, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcNtNewNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_new not between", value1, value2, "rentTcNtNew");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveIsNull() {
            addCriterion("rent_tc_all_leave is null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveIsNotNull() {
            addCriterion("rent_tc_all_leave is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveEqualTo(Integer value) {
            addCriterion("rent_tc_all_leave =", value, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveNotEqualTo(Integer value) {
            addCriterion("rent_tc_all_leave <>", value, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveGreaterThan(Integer value) {
            addCriterion("rent_tc_all_leave >", value, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_leave >=", value, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveLessThan(Integer value) {
            addCriterion("rent_tc_all_leave <", value, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_leave <=", value, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveIn(List<Integer> values) {
            addCriterion("rent_tc_all_leave in", values, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveNotIn(List<Integer> values) {
            addCriterion("rent_tc_all_leave not in", values, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_leave between", value1, value2, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllLeaveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_leave not between", value1, value2, "rentTcAllLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveIsNull() {
            addCriterion("rent_tc_ot_leave is null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveIsNotNull() {
            addCriterion("rent_tc_ot_leave is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveEqualTo(Integer value) {
            addCriterion("rent_tc_ot_leave =", value, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveNotEqualTo(Integer value) {
            addCriterion("rent_tc_ot_leave <>", value, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveGreaterThan(Integer value) {
            addCriterion("rent_tc_ot_leave >", value, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_leave >=", value, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveLessThan(Integer value) {
            addCriterion("rent_tc_ot_leave <", value, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_leave <=", value, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveIn(List<Integer> values) {
            addCriterion("rent_tc_ot_leave in", values, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveNotIn(List<Integer> values) {
            addCriterion("rent_tc_ot_leave not in", values, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_leave between", value1, value2, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcOtLeaveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_leave not between", value1, value2, "rentTcOtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveIsNull() {
            addCriterion("rent_tc_nt_leave is null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveIsNotNull() {
            addCriterion("rent_tc_nt_leave is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveEqualTo(Integer value) {
            addCriterion("rent_tc_nt_leave =", value, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveNotEqualTo(Integer value) {
            addCriterion("rent_tc_nt_leave <>", value, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveGreaterThan(Integer value) {
            addCriterion("rent_tc_nt_leave >", value, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_leave >=", value, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveLessThan(Integer value) {
            addCriterion("rent_tc_nt_leave <", value, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_leave <=", value, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveIn(List<Integer> values) {
            addCriterion("rent_tc_nt_leave in", values, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveNotIn(List<Integer> values) {
            addCriterion("rent_tc_nt_leave not in", values, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_leave between", value1, value2, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcNtLeaveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_leave not between", value1, value2, "rentTcNtLeave");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyIsNull() {
            addCriterion("rent_tc_all_buy is null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyIsNotNull() {
            addCriterion("rent_tc_all_buy is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyEqualTo(Integer value) {
            addCriterion("rent_tc_all_buy =", value, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyNotEqualTo(Integer value) {
            addCriterion("rent_tc_all_buy <>", value, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyGreaterThan(Integer value) {
            addCriterion("rent_tc_all_buy >", value, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_buy >=", value, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyLessThan(Integer value) {
            addCriterion("rent_tc_all_buy <", value, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_buy <=", value, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyIn(List<Integer> values) {
            addCriterion("rent_tc_all_buy in", values, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyNotIn(List<Integer> values) {
            addCriterion("rent_tc_all_buy not in", values, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_buy between", value1, value2, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllBuyNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_buy not between", value1, value2, "rentTcAllBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyIsNull() {
            addCriterion("rent_tc_ot_buy is null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyIsNotNull() {
            addCriterion("rent_tc_ot_buy is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyEqualTo(Integer value) {
            addCriterion("rent_tc_ot_buy =", value, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyNotEqualTo(Integer value) {
            addCriterion("rent_tc_ot_buy <>", value, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyGreaterThan(Integer value) {
            addCriterion("rent_tc_ot_buy >", value, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_buy >=", value, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyLessThan(Integer value) {
            addCriterion("rent_tc_ot_buy <", value, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_buy <=", value, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyIn(List<Integer> values) {
            addCriterion("rent_tc_ot_buy in", values, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyNotIn(List<Integer> values) {
            addCriterion("rent_tc_ot_buy not in", values, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_buy between", value1, value2, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcOtBuyNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_buy not between", value1, value2, "rentTcOtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyIsNull() {
            addCriterion("rent_tc_nt_buy is null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyIsNotNull() {
            addCriterion("rent_tc_nt_buy is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyEqualTo(Integer value) {
            addCriterion("rent_tc_nt_buy =", value, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyNotEqualTo(Integer value) {
            addCriterion("rent_tc_nt_buy <>", value, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyGreaterThan(Integer value) {
            addCriterion("rent_tc_nt_buy >", value, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_buy >=", value, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyLessThan(Integer value) {
            addCriterion("rent_tc_nt_buy <", value, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_buy <=", value, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyIn(List<Integer> values) {
            addCriterion("rent_tc_nt_buy in", values, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyNotIn(List<Integer> values) {
            addCriterion("rent_tc_nt_buy not in", values, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_buy between", value1, value2, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcNtBuyNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_buy not between", value1, value2, "rentTcNtBuy");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveIsNull() {
            addCriterion("rent_tc_all_active is null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveIsNotNull() {
            addCriterion("rent_tc_all_active is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveEqualTo(Integer value) {
            addCriterion("rent_tc_all_active =", value, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveNotEqualTo(Integer value) {
            addCriterion("rent_tc_all_active <>", value, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveGreaterThan(Integer value) {
            addCriterion("rent_tc_all_active >", value, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_active >=", value, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveLessThan(Integer value) {
            addCriterion("rent_tc_all_active <", value, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_all_active <=", value, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveIn(List<Integer> values) {
            addCriterion("rent_tc_all_active in", values, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveNotIn(List<Integer> values) {
            addCriterion("rent_tc_all_active not in", values, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_active between", value1, value2, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllActiveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_all_active not between", value1, value2, "rentTcAllActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveIsNull() {
            addCriterion("rent_tc_ot_active is null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveIsNotNull() {
            addCriterion("rent_tc_ot_active is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveEqualTo(Integer value) {
            addCriterion("rent_tc_ot_active =", value, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveNotEqualTo(Integer value) {
            addCriterion("rent_tc_ot_active <>", value, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveGreaterThan(Integer value) {
            addCriterion("rent_tc_ot_active >", value, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_active >=", value, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveLessThan(Integer value) {
            addCriterion("rent_tc_ot_active <", value, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_ot_active <=", value, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveIn(List<Integer> values) {
            addCriterion("rent_tc_ot_active in", values, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveNotIn(List<Integer> values) {
            addCriterion("rent_tc_ot_active not in", values, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_active between", value1, value2, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcOtActiveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_ot_active not between", value1, value2, "rentTcOtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveIsNull() {
            addCriterion("rent_tc_nt_active is null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveIsNotNull() {
            addCriterion("rent_tc_nt_active is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveEqualTo(Integer value) {
            addCriterion("rent_tc_nt_active =", value, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveNotEqualTo(Integer value) {
            addCriterion("rent_tc_nt_active <>", value, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveGreaterThan(Integer value) {
            addCriterion("rent_tc_nt_active >", value, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_active >=", value, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveLessThan(Integer value) {
            addCriterion("rent_tc_nt_active <", value, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveLessThanOrEqualTo(Integer value) {
            addCriterion("rent_tc_nt_active <=", value, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveIn(List<Integer> values) {
            addCriterion("rent_tc_nt_active in", values, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveNotIn(List<Integer> values) {
            addCriterion("rent_tc_nt_active not in", values, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_active between", value1, value2, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcNtActiveNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_tc_nt_active not between", value1, value2, "rentTcNtActive");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeIsNull() {
            addCriterion("rent_tc_all_time is null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeIsNotNull() {
            addCriterion("rent_tc_all_time is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeEqualTo(String value) {
            addCriterion("rent_tc_all_time =", value, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeNotEqualTo(String value) {
            addCriterion("rent_tc_all_time <>", value, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeGreaterThan(String value) {
            addCriterion("rent_tc_all_time >", value, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeGreaterThanOrEqualTo(String value) {
            addCriterion("rent_tc_all_time >=", value, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeLessThan(String value) {
            addCriterion("rent_tc_all_time <", value, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeLessThanOrEqualTo(String value) {
            addCriterion("rent_tc_all_time <=", value, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeLike(String value) {
            addCriterion("rent_tc_all_time like", value, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeNotLike(String value) {
            addCriterion("rent_tc_all_time not like", value, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeIn(List<String> values) {
            addCriterion("rent_tc_all_time in", values, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeNotIn(List<String> values) {
            addCriterion("rent_tc_all_time not in", values, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeBetween(String value1, String value2) {
            addCriterion("rent_tc_all_time between", value1, value2, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcAllTimeNotBetween(String value1, String value2) {
            addCriterion("rent_tc_all_time not between", value1, value2, "rentTcAllTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeIsNull() {
            addCriterion("rent_tc_ot_time is null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeIsNotNull() {
            addCriterion("rent_tc_ot_time is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeEqualTo(String value) {
            addCriterion("rent_tc_ot_time =", value, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeNotEqualTo(String value) {
            addCriterion("rent_tc_ot_time <>", value, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeGreaterThan(String value) {
            addCriterion("rent_tc_ot_time >", value, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeGreaterThanOrEqualTo(String value) {
            addCriterion("rent_tc_ot_time >=", value, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeLessThan(String value) {
            addCriterion("rent_tc_ot_time <", value, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeLessThanOrEqualTo(String value) {
            addCriterion("rent_tc_ot_time <=", value, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeLike(String value) {
            addCriterion("rent_tc_ot_time like", value, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeNotLike(String value) {
            addCriterion("rent_tc_ot_time not like", value, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeIn(List<String> values) {
            addCriterion("rent_tc_ot_time in", values, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeNotIn(List<String> values) {
            addCriterion("rent_tc_ot_time not in", values, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeBetween(String value1, String value2) {
            addCriterion("rent_tc_ot_time between", value1, value2, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcOtTimeNotBetween(String value1, String value2) {
            addCriterion("rent_tc_ot_time not between", value1, value2, "rentTcOtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeIsNull() {
            addCriterion("rent_tc_nt_time is null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeIsNotNull() {
            addCriterion("rent_tc_nt_time is not null");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeEqualTo(String value) {
            addCriterion("rent_tc_nt_time =", value, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeNotEqualTo(String value) {
            addCriterion("rent_tc_nt_time <>", value, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeGreaterThan(String value) {
            addCriterion("rent_tc_nt_time >", value, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeGreaterThanOrEqualTo(String value) {
            addCriterion("rent_tc_nt_time >=", value, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeLessThan(String value) {
            addCriterion("rent_tc_nt_time <", value, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeLessThanOrEqualTo(String value) {
            addCriterion("rent_tc_nt_time <=", value, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeLike(String value) {
            addCriterion("rent_tc_nt_time like", value, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeNotLike(String value) {
            addCriterion("rent_tc_nt_time not like", value, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeIn(List<String> values) {
            addCriterion("rent_tc_nt_time in", values, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeNotIn(List<String> values) {
            addCriterion("rent_tc_nt_time not in", values, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeBetween(String value1, String value2) {
            addCriterion("rent_tc_nt_time between", value1, value2, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andRentTcNtTimeNotBetween(String value1, String value2) {
            addCriterion("rent_tc_nt_time not between", value1, value2, "rentTcNtTime");
            return (Criteria) this;
        }

        public Criteria andDataAtIsNull() {
            addCriterion("data_at is null");
            return (Criteria) this;
        }

        public Criteria andDataAtIsNotNull() {
            addCriterion("data_at is not null");
            return (Criteria) this;
        }

        public Criteria andDataAtEqualTo(Date value) {
            addCriterion("data_at =", value, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtNotEqualTo(Date value) {
            addCriterion("data_at <>", value, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtGreaterThan(Date value) {
            addCriterion("data_at >", value, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtGreaterThanOrEqualTo(Date value) {
            addCriterion("data_at >=", value, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtLessThan(Date value) {
            addCriterion("data_at <", value, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtLessThanOrEqualTo(Date value) {
            addCriterion("data_at <=", value, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtIn(List<Date> values) {
            addCriterion("data_at in", values, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtNotIn(List<Date> values) {
            addCriterion("data_at not in", values, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtBetween(Date value1, Date value2) {
            addCriterion("data_at between", value1, value2, "dataAt");
            return (Criteria) this;
        }

        public Criteria andDataAtNotBetween(Date value1, Date value2) {
            addCriterion("data_at not between", value1, value2, "dataAt");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}