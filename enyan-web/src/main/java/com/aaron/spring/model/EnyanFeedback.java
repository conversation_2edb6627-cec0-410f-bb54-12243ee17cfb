package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serial;
import java.util.Date;

public class EnyanFeedback extends BaseDTO{
    @Serial
    private static final long serialVersionUID = -9102127663861385632L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long dataId;

    private Integer type;

    private String feedEmail;

    private String deviceFrom;

    private String deviceVersion;

    private String content;

    private Integer doneType;

    private Date createAt;

    private Integer isDeleted;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getFeedEmail() {
        return feedEmail;
    }

    public void setFeedEmail(String feedEmail) {
        this.feedEmail = feedEmail == null ? null : feedEmail.trim();
    }

    public String getDeviceFrom() {
        return deviceFrom;
    }

    public void setDeviceFrom(String deviceFrom) {
        this.deviceFrom = deviceFrom == null ? null : deviceFrom.trim();
    }

    public String getDeviceVersion() {
        return deviceVersion;
    }

    public void setDeviceVersion(String deviceVersion) {
        this.deviceVersion = deviceVersion == null ? null : deviceVersion.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Integer getDoneType() {
        return doneType;
    }

    public void setDoneType(Integer doneType) {
        this.doneType = doneType;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}