package com.aaron.spring.model;

import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2020-07-22
 * @Modified By:
 */
public class Coupon {
    private String code="EDT";
    private BigDecimal value;
    private Long beginDate = -1L;
    private Long endDate = -1L;
    private BigDecimal minLimitValue;//满减最低限额

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public Long getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Long beginDate) {
        this.beginDate = beginDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getMinLimitValue() {
        return minLimitValue;
    }

    public void setMinLimitValue(BigDecimal minLimitValue) {
        this.minLimitValue = minLimitValue;
    }

    @Override
    public String toString() {
        return "Coupon{" +
                "code='" + code + '\'' +
                ", value=" + value +
                ", beginDate=" + beginDate +
                ", endDate=" + endDate +
                ", minLimitValue=" + minLimitValue +
                '}';
    }
}
