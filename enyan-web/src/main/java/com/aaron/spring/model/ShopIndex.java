package com.aaron.spring.model;

import com.aaron.spring.api.RestBanner;
import com.aaron.spring.api.v4.model.RestBook;
import com.aaron.spring.api.v4.model.RestShopCategory;
import com.alibaba.fastjson2.annotation.JSONField;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2024-09-17
 * @Modified By:
 */
public class ShopIndex implements Serializable {
	@Serial
	private static final long serialVersionUID = -8946490023709158079L;

	private List<EnyanBook> editorList;//推荐
	private List<EnyanCategory> shopTopCategories;//一些促销的分类（首页展示）
	//private List<EnyanCategory> shopCategories;//需要在首页展示的分类
	private List<EnyanCategory> shopTopMenu;//需要在首页展示的在套装书上方的书单书籍
	private List<EnyanCategory> shopMenu;//需要在首页展示套装书的书单书籍
	/**
	*需要在首页展示套装书的书系书籍
	* */
	private List<EnyanCategory> shopSet;//需要在首页展示套装书的书系书籍
	private List<EnyanCategory> shopBottomMenu;//需要在首页展示的在套装书下方的书单书籍
	private List<RestBanner> topBanners;//最上边的banner
	/**
	 * 右侧展示的文字blog
	 * */
	private List<RestBanner> topBlogs;//右侧展示的文字blog
	private List<RestBanner> shopBanners;//其他展示的banner（一次展示一张）

	@JSONField(serialize = false)
	private boolean isInit = false; //是否已经被初始化，如果没有初始化则需要重新处理

	public List<EnyanBook> getEditorList() {
		return editorList;
	}

	public void setEditorList(List<EnyanBook> editorList) {
		this.editorList = editorList;
	}

	public List<EnyanCategory> getShopTopCategories() {
		return shopTopCategories;
	}

	public void setShopTopCategories(List<EnyanCategory> shopTopCategories) {
		this.shopTopCategories = shopTopCategories;
	}

	public List<EnyanCategory> getShopTopMenu() {
		return shopTopMenu;
	}

	public void setShopTopMenu(List<EnyanCategory> shopTopMenu) {
		this.shopTopMenu = shopTopMenu;
	}

	public List<EnyanCategory> getShopMenu() {
		return shopMenu;
	}

	public void setShopMenu(List<EnyanCategory> shopMenu) {
		this.shopMenu = shopMenu;
	}

	public List<EnyanCategory> getShopSet() {
		return shopSet;
	}

	public void setShopSet(List<EnyanCategory> shopSet) {
		this.shopSet = shopSet;
	}

	public List<RestBanner> getTopBanners() {
		return topBanners;
	}

	public void setTopBanners(List<RestBanner> topBanners) {
		this.topBanners = topBanners;
	}

	public List<RestBanner> getShopBanners() {
		return shopBanners;
	}

	public void setShopBanners(List<RestBanner> shopBanners) {
		this.shopBanners = shopBanners;
	}

	public boolean isInit() {
		return isInit;
	}

	public void setInit(boolean init) {
		isInit = init;
	}

	public List<EnyanCategory> getShopBottomMenu() {
		return shopBottomMenu;
	}

	public void setShopBottomMenu(List<EnyanCategory> shopBottomMenu) {
		this.shopBottomMenu = shopBottomMenu;
	}

	public List<RestBanner> getTopBlogs() {
		return topBlogs;
	}

	public void setTopBlogs(List<RestBanner> topBlogs) {
		this.topBlogs = topBlogs;
	}
}
