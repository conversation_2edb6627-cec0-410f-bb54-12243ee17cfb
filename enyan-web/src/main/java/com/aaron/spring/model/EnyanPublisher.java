package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.Date;

public class EnyanPublisher extends BaseDTO{
    private static final long serialVersionUID = 8966639048849287995L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long publisherId;

    private String publisherName;

    private String publisherNameTc;

    private String publisherNameEn;

    private String publisherAvatar;

    private Date startTime;

    private Integer vendorPercent;

    private Integer bankNational;

    private String bankName;

    private String bankAddress;

    private String bankCode;

    private String bankTitle;

    private String bankNum;

    private BigDecimal fundTotal;

    private BigDecimal fundWithdrawn;

    private String miscConfig;

    private PublisherInfo publisherInfo;

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public String getPublisherName() {
        return publisherName;
    }

    public void setPublisherName(String publisherName) {
        this.publisherName = publisherName == null ? null : publisherName.trim();
    }

    public String getPublisherNameTc() {
        return publisherNameTc;
    }

    public void setPublisherNameTc(String publisherNameTc) {
        this.publisherNameTc = publisherNameTc == null ? null : publisherNameTc.trim();
    }

    public String getPublisherNameEn() {
        return publisherNameEn;
    }

    public void setPublisherNameEn(String publisherNameEn) {
        this.publisherNameEn = publisherNameEn == null ? null : publisherNameEn.trim();
    }

    public String getPublisherAvatar() {
        return publisherAvatar;
    }

    public void setPublisherAvatar(String publisherAvatar) {
        this.publisherAvatar = publisherAvatar == null ? null : publisherAvatar.trim();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Integer getVendorPercent() {
        return vendorPercent;
    }

    public void setVendorPercent(Integer vendorPercent) {
        this.vendorPercent = vendorPercent;
    }

    public Integer getBankNational() {
        return bankNational;
    }

    public void setBankNational(Integer bankNational) {
        this.bankNational = bankNational;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName == null ? null : bankName.trim();
    }

    public String getBankAddress() {
        return bankAddress;
    }

    public void setBankAddress(String bankAddress) {
        this.bankAddress = bankAddress == null ? null : bankAddress.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }

    public String getBankTitle() {
        return bankTitle;
    }

    public void setBankTitle(String bankTitle) {
        this.bankTitle = bankTitle == null ? null : bankTitle.trim();
    }

    public String getBankNum() {
        return bankNum;
    }

    public void setBankNum(String bankNum) {
        this.bankNum = bankNum == null ? null : bankNum.trim();
    }

    public BigDecimal getFundTotal() {
        return fundTotal;
    }

    public void setFundTotal(BigDecimal fundTotal) {
        this.fundTotal = fundTotal;
    }

    public BigDecimal getFundWithdrawn() {
        return fundWithdrawn;
    }

    public void setFundWithdrawn(BigDecimal fundWithdrawn) {
        this.fundWithdrawn = fundWithdrawn;
    }

    public String getMiscConfig() {
        return miscConfig;
    }

    public void setMiscConfig(String miscConfig) {
        this.miscConfig = miscConfig == null ? null : miscConfig.trim();
    }

    public PublisherInfo getPublisherInfo() {
        return publisherInfo;
    }

    public void setPublisherInfo(PublisherInfo publisherInfo) {
        this.publisherInfo = publisherInfo;
    }
}