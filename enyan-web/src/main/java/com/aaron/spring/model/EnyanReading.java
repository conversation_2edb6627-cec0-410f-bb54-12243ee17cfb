package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Date;

public class EnyanReading extends BaseDTO{
    private static final long serialVersionUID = -2501672798690297394L;
    @JsonSerialize(using = Long2String.class)
    @JsonDeserialize(using = String2Long.class)
    private Long dataId;

    private String dataName;

    private String dataImgUrl;

    private String dataToUrl;

    private String dataBuyUrl;

    private Integer dataReadShow;

    private Integer dataPriority;

    private Integer dataStatus;

    private Integer isDeleted;

    private Date beginAt;

    private Date endAt;

    private Date createAt;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public String getDataName() {
        return dataName;
    }

    public void setDataName(String dataName) {
        this.dataName = dataName == null ? null : dataName.trim();
    }

    public String getDataImgUrl() {
        return dataImgUrl;
    }

    public void setDataImgUrl(String dataImgUrl) {
        this.dataImgUrl = dataImgUrl == null ? null : dataImgUrl.trim();
    }

    public String getDataToUrl() {
        return dataToUrl;
    }

    public void setDataToUrl(String dataToUrl) {
        this.dataToUrl = dataToUrl == null ? null : dataToUrl.trim();
    }

    public String getDataBuyUrl() {
        return dataBuyUrl;
    }

    public void setDataBuyUrl(String dataBuyUrl) {
        this.dataBuyUrl = dataBuyUrl == null ? null : dataBuyUrl.trim();
    }

    public Integer getDataReadShow() {
        return dataReadShow;
    }

    public void setDataReadShow(Integer dataReadShow) {
        this.dataReadShow = dataReadShow;
    }

    public Integer getDataPriority() {
        return dataPriority;
    }

    public void setDataPriority(Integer dataPriority) {
        this.dataPriority = dataPriority;
    }

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getBeginAt() {
        return beginAt;
    }

    public void setBeginAt(Date beginAt) {
        this.beginAt = beginAt;
    }

    public Date getEndAt() {
        return endAt;
    }

    public void setEndAt(Date endAt) {
        this.endAt = endAt;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }
}