package com.aaron.spring.model;

import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单详细信息类，用于存储和处理订单的详细信息
 * 主要功能：
 * 1. 存储订单中的商品列表和折扣信息
 * 2. 计算订单的各种货币金额（港币、人民币、美元）
 * 3. 处理优惠码和折扣的应用逻辑
 * 4. 支持从购物车信息(CartInfo)初始化订单详情
 * 5. 提供订单金额计算和调整的方法
 * 
 * @Author: Aaron Hao
 * @Date: Created in  2017/12/11
 * @Modified By:
 */
public class OrderDetailInfo implements Serializable{

    /** 序列化ID */
    private static final long serialVersionUID = 356956084500175516L;
    
    /**
     * 产品信息列表
     * @deprecated  <p>之后使用<code>cartDiscountInfoList</code>替代这个属性，如果兼容旧的数据，在从json中重新获取的时候，使用<code>resetFromJson</code>方法</p>
     * @since : 2020-09-16
     */
    @Deprecated
    private List<ProductInfo> productInfoList;
    
    /** 购物车折扣信息列表，包含商品和对应的折扣 */
    private List<CartDiscountInfo> cartDiscountInfoList = new ArrayList<>();
    
    /** 订单人民币金额 */
    private BigDecimal amountCny;
    
    /** 订单美元金额 */
    private BigDecimal amountUsd;
    
    /** 订单港币金额（最终支付金额） */
    private BigDecimal amountHkd;
    
    /** 订单港币中间价格，主要用于未支付订单的展示或者总价 */
    private BigDecimal amountHkdMiddle;
    
    /** 优惠码折扣金额 */
    private BigDecimal amountCoupon = new BigDecimal("0");
    
    /** 其他折扣金额 */
    private BigDecimal amountDiscount = new BigDecimal("0");
    
    /** 使用的优惠码 */
    private String couponCode;
    
    /** 折扣标题描述 */
    private String discountTitle;
    
    /** 购物车信息，不序列化 */
    @JSONField(serialize = false)
    private CartInfo cartInfo;

    /** 语言设置，不序列化 */
    @JSONField(serialize = false)
    private String language;

    /**
     * 在使用优惠码之前的价钱，主要用于判断是否满足满减条件
     * 不序列化到JSON
     */
    @JSONField(serialize = false)
    private BigDecimal totalFeeBeforeCoupon;

    /**
     * 默认构造函数
     */
    public OrderDetailInfo() {
    }

    /**
     * 使用购物车信息初始化订单详情
     * 
     * @param cartInfo 购物车信息对象，包含商品列表和折扣信息
     */
    public OrderDetailInfo(CartInfo cartInfo) {
        this.cartInfo = cartInfo;
        this.cartDiscountInfoList = cartInfo.getCartDiscountInfoList();
        this.setLanguage(cartInfo.getLanguage());
        //this.productInfoList = cartInfo.findProducts();
        //this.amountCny = cartInfo.getAmountCny();
        //this.amountUsd = cartInfo.getAmountUsd();
        this.amountHkd = cartInfo.getAmountHkd();
        this.amountHkdMiddle = cartInfo.getAmountHkdFix();
        for (CartDiscountInfo cartDiscountInfo:this.cartDiscountInfoList){
            cartDiscountInfo.resetCumulateDiscount();
            if (StringUtils.isNotBlank(cartDiscountInfo.getDiscountTitle())){
                this.discountTitle = cartDiscountInfo.getDiscountTitle();
                break;
            }
        }
        this.amountDiscount = this.amountHkdMiddle.subtract(this.amountHkd);
    }

    /**
     * 获取产品信息列表
     * 
     * @return 产品信息列表
     * @deprecated 使用getCartDiscountInfoList代替
     */
    public List<ProductInfo> getProductInfoList() {
        return productInfoList;
    }

    /**
     * 设置产品信息列表
     * 
     * @param productInfoList 产品信息列表
     * @deprecated 使用setCartDiscountInfoList代替
     */
    public void setProductInfoList(List<ProductInfo> productInfoList) {
        this.productInfoList = productInfoList;
    }

    /**
     * 获取订单人民币金额
     * 
     * @return 订单人民币金额
     */
    public BigDecimal getAmountCny() {
        return amountCny;
    }

    /**
     * 设置订单人民币金额
     * 
     * @param amountCny 订单人民币金额
     */
    public void setAmountCny(BigDecimal amountCny) {
        this.amountCny = amountCny;
    }

    /**
     * 获取订单美元金额
     * 
     * @return 订单美元金额
     */
    public BigDecimal getAmountUsd() {
        return amountUsd;
    }

    /**
     * 设置订单美元金额
     * 
     * @param amountUsd 订单美元金额
     */
    public void setAmountUsd(BigDecimal amountUsd) {
        this.amountUsd = amountUsd;
    }

    /**
     * 获取订单港币金额（最终支付金额）
     * 
     * @return 订单港币金额
     */
    public BigDecimal getAmountHkd() {
        return amountHkd;
    }

    /**
     * 设置订单港币金额
     * 
     * @param amountHkd 订单港币金额
     */
    public void setAmountHkd(BigDecimal amountHkd) {
        this.amountHkd = amountHkd;
    }

    /**
     * 获取优惠码折扣金额
     * 
     * @return 优惠码折扣金额
     */
    public BigDecimal getAmountCoupon() {
        return amountCoupon;
    }

    /**
     * 设置优惠码折扣金额
     * <p>设置优惠码后，会同步设置新的产品列表</p>
     * 
     * @param amountCoupon 优惠码折扣金额
     * @since : 2020-07-23
     */
    public void setAmountCoupon(BigDecimal amountCoupon) {
        this.amountCoupon = amountCoupon;
    }

    /**
     * 获取使用的优惠码
     * 
     * @return 优惠码
     */
    public String getCouponCode() {
        return couponCode;
    }

    /**
     * 设置使用的优惠码
     * 
     * @param couponCode 优惠码
     */
    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    /**
     * 获取订单港币中间价格
     * 
     * @return 订单港币中间价格
     */
    public BigDecimal getAmountHkdMiddle() {
        return amountHkdMiddle;
    }

    /**
     * 设置订单港币中间价格
     * 
     * @param amountHkdMiddle 订单港币中间价格
     */
    public void setAmountHkdMiddle(BigDecimal amountHkdMiddle) {
        this.amountHkdMiddle = amountHkdMiddle;
    }

    /**
     * 获取购物车折扣信息列表
     * 
     * @return 购物车折扣信息列表
     */
    public List<CartDiscountInfo> getCartDiscountInfoList() {
        return cartDiscountInfoList;
    }

    /**
     * 设置购物车折扣信息列表
     * 
     * @param cartDiscountInfoList 购物车折扣信息列表
     */
    public void setCartDiscountInfoList(List<CartDiscountInfo> cartDiscountInfoList) {
        this.cartDiscountInfoList = cartDiscountInfoList;
    }

    /**
     * 获取折扣标题描述
     * 
     * @return 折扣标题描述
     */
    public String getDiscountTitle() {
        return discountTitle;
    }

    /**
     * 设置折扣标题描述
     * 
     * @param discountTitle 折扣标题描述
     */
    public void setDiscountTitle(String discountTitle) {
        this.discountTitle = discountTitle;
    }

    /**
     * 获取其他折扣金额
     * 
     * @return 其他折扣金额
     */
    public BigDecimal getAmountDiscount() {
        return amountDiscount;
    }

    /**
     * 设置其他折扣金额
     * 
     * @param amountDiscount 其他折扣金额
     */
    public void setAmountDiscount(BigDecimal amountDiscount) {
        this.amountDiscount = amountDiscount;
    }

    /**
     * 获取语言设置
     * 
     * @return 语言设置
     */
    public String getLanguage() {
        return language;
    }

    /**
     * 设置语言设置
     * 
     * @param language 语言设置
     */
    public void setLanguage(String language) {
        this.language = language;
    }

    /**
     * 重置使用优惠码后的产品列表和价格
     * 应用优惠码折扣并重新计算总价
     * 
     * @param enyanCoupon 优惠码对象
     * @since : 2020-07-23
     */
    public void resetProductListByCoupon(EnyanCoupon enyanCoupon){
        //this.productInfoList = cartInfo.findCouponProducts(amountCoupon);
//        cartInfo.resetCouponProducts(amountCoupon);
        cartInfo.resetCouponProducts(enyanCoupon);
        this.totalFeeBeforeCoupon = cartInfo.getTotalFeeBeforeCoupon();
        this.resetAmountHKD();
    }
    
    /**
     * 重新计算订单港币总价
     * 根据折扣信息列表中的各项折扣金额重新计算总价
     * 
     * @since : 2020-07-23
     */
    private void resetAmountHKD(){
        BigDecimal totalFee = new BigDecimal("0");
        for(CartDiscountInfo info : this.cartDiscountInfoList) {
            totalFee = totalFee.add(info.getAmountHkdDiscount());
        }
        this.amountHkd = totalFee;
    }
    
    /**
     * 从JSON反序列化后重置数据
     * 为兼容之前的数据格式，从JSON中重新加载时需要重置一些字段
     * 
     * @since : 2020-08-11
     */
    public void resetFromJson(){
        if (cartDiscountInfoList.isEmpty()){
            CartDiscountInfo cartDiscountInfo = new CartDiscountInfo();
            cartDiscountInfo.setProductInfoList(this.getProductInfoList());
            cartDiscountInfoList.add(cartDiscountInfo);
        }
        if (null == productInfoList || productInfoList.isEmpty()){
            productInfoList = new ArrayList<>();
            for (CartDiscountInfo cartDiscountInfo: cartDiscountInfoList){
                if (null == cartDiscountInfo.getProductInfoList()){
                    continue;
                }
                for (ProductInfo productInfo : cartDiscountInfo.getProductInfoList()){
                    productInfoList.add(productInfo);
                }
            }
        }
    }

    /**
     * 获取使用优惠码前的订单总价
     * 用于判断是否满足满减条件
     * 
     * @return 使用优惠码前的订单总价
     */
    public BigDecimal getTotalFeeBeforeCoupon() {
        return totalFeeBeforeCoupon;
    }

    /**
     * 设置使用优惠码前的订单总价
     * 
     * @param totalFeeBeforeCoupon 使用优惠码前的订单总价
     */
    public void setTotalFeeBeforeCoupon(BigDecimal totalFeeBeforeCoupon) {
        this.totalFeeBeforeCoupon = totalFeeBeforeCoupon;
    }
}
