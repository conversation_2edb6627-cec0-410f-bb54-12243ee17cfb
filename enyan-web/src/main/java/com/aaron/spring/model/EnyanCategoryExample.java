package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.List;

public class EnyanCategoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanCategoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Long value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Long value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Long value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Long value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Long> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Long> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Long value1, Long value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcIsNull() {
            addCriterion("category_name_tc is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcIsNotNull() {
            addCriterion("category_name_tc is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcEqualTo(String value) {
            addCriterion("category_name_tc =", value, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcNotEqualTo(String value) {
            addCriterion("category_name_tc <>", value, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcGreaterThan(String value) {
            addCriterion("category_name_tc >", value, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcGreaterThanOrEqualTo(String value) {
            addCriterion("category_name_tc >=", value, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcLessThan(String value) {
            addCriterion("category_name_tc <", value, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcLessThanOrEqualTo(String value) {
            addCriterion("category_name_tc <=", value, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcLike(String value) {
            addCriterion("category_name_tc like", value, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcNotLike(String value) {
            addCriterion("category_name_tc not like", value, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcIn(List<String> values) {
            addCriterion("category_name_tc in", values, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcNotIn(List<String> values) {
            addCriterion("category_name_tc not in", values, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcBetween(String value1, String value2) {
            addCriterion("category_name_tc between", value1, value2, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameTcNotBetween(String value1, String value2) {
            addCriterion("category_name_tc not between", value1, value2, "categoryNameTc");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnIsNull() {
            addCriterion("category_name_en is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnIsNotNull() {
            addCriterion("category_name_en is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnEqualTo(String value) {
            addCriterion("category_name_en =", value, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnNotEqualTo(String value) {
            addCriterion("category_name_en <>", value, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnGreaterThan(String value) {
            addCriterion("category_name_en >", value, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnGreaterThanOrEqualTo(String value) {
            addCriterion("category_name_en >=", value, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnLessThan(String value) {
            addCriterion("category_name_en <", value, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnLessThanOrEqualTo(String value) {
            addCriterion("category_name_en <=", value, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnLike(String value) {
            addCriterion("category_name_en like", value, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnNotLike(String value) {
            addCriterion("category_name_en not like", value, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnIn(List<String> values) {
            addCriterion("category_name_en in", values, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnNotIn(List<String> values) {
            addCriterion("category_name_en not in", values, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnBetween(String value1, String value2) {
            addCriterion("category_name_en between", value1, value2, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEnNotBetween(String value1, String value2) {
            addCriterion("category_name_en not between", value1, value2, "categoryNameEn");
            return (Criteria) this;
        }

        public Criteria andIsIndexIsNull() {
            addCriterion("is_index is null");
            return (Criteria) this;
        }

        public Criteria andIsIndexIsNotNull() {
            addCriterion("is_index is not null");
            return (Criteria) this;
        }

        public Criteria andIsIndexEqualTo(Byte value) {
            addCriterion("is_index =", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexNotEqualTo(Byte value) {
            addCriterion("is_index <>", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexGreaterThan(Byte value) {
            addCriterion("is_index >", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_index >=", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexLessThan(Byte value) {
            addCriterion("is_index <", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexLessThanOrEqualTo(Byte value) {
            addCriterion("is_index <=", value, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexIn(List<Byte> values) {
            addCriterion("is_index in", values, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexNotIn(List<Byte> values) {
            addCriterion("is_index not in", values, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexBetween(Byte value1, Byte value2) {
            addCriterion("is_index between", value1, value2, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsIndexNotBetween(Byte value1, Byte value2) {
            addCriterion("is_index not between", value1, value2, "isIndex");
            return (Criteria) this;
        }

        public Criteria andIsHiddenIsNull() {
            addCriterion("is_hidden is null");
            return (Criteria) this;
        }

        public Criteria andIsHiddenIsNotNull() {
            addCriterion("is_hidden is not null");
            return (Criteria) this;
        }

        public Criteria andIsHiddenEqualTo(Integer value) {
            addCriterion("is_hidden =", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenNotEqualTo(Integer value) {
            addCriterion("is_hidden <>", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenGreaterThan(Integer value) {
            addCriterion("is_hidden >", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_hidden >=", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenLessThan(Integer value) {
            addCriterion("is_hidden <", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenLessThanOrEqualTo(Integer value) {
            addCriterion("is_hidden <=", value, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenIn(List<Integer> values) {
            addCriterion("is_hidden in", values, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenNotIn(List<Integer> values) {
            addCriterion("is_hidden not in", values, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenBetween(Integer value1, Integer value2) {
            addCriterion("is_hidden between", value1, value2, "isHidden");
            return (Criteria) this;
        }

        public Criteria andIsHiddenNotBetween(Integer value1, Integer value2) {
            addCriterion("is_hidden not between", value1, value2, "isHidden");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderIsNull() {
            addCriterion("category_order is null");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderIsNotNull() {
            addCriterion("category_order is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderEqualTo(Integer value) {
            addCriterion("category_order =", value, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderNotEqualTo(Integer value) {
            addCriterion("category_order <>", value, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderGreaterThan(Integer value) {
            addCriterion("category_order >", value, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_order >=", value, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderLessThan(Integer value) {
            addCriterion("category_order <", value, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderLessThanOrEqualTo(Integer value) {
            addCriterion("category_order <=", value, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderIn(List<Integer> values) {
            addCriterion("category_order in", values, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderNotIn(List<Integer> values) {
            addCriterion("category_order not in", values, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderBetween(Integer value1, Integer value2) {
            addCriterion("category_order between", value1, value2, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andCategoryOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("category_order not between", value1, value2, "categoryOrder");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedIsNull() {
            addCriterion("book_recommended is null");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedIsNotNull() {
            addCriterion("book_recommended is not null");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedEqualTo(String value) {
            addCriterion("book_recommended =", value, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedNotEqualTo(String value) {
            addCriterion("book_recommended <>", value, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedGreaterThan(String value) {
            addCriterion("book_recommended >", value, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedGreaterThanOrEqualTo(String value) {
            addCriterion("book_recommended >=", value, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedLessThan(String value) {
            addCriterion("book_recommended <", value, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedLessThanOrEqualTo(String value) {
            addCriterion("book_recommended <=", value, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedLike(String value) {
            addCriterion("book_recommended like", value, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedNotLike(String value) {
            addCriterion("book_recommended not like", value, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedIn(List<String> values) {
            addCriterion("book_recommended in", values, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedNotIn(List<String> values) {
            addCriterion("book_recommended not in", values, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedBetween(String value1, String value2) {
            addCriterion("book_recommended between", value1, value2, "bookRecommended");
            return (Criteria) this;
        }

        public Criteria andBookRecommendedNotBetween(String value1, String value2) {
            addCriterion("book_recommended not between", value1, value2, "bookRecommended");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}