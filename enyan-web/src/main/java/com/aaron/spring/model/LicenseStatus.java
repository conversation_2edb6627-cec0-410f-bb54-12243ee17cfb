package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Date;

public class LicenseStatus {
    private Integer id;

    private Integer status;

    private Date licenseUpdated;

    private Date statusUpdated;

    private Integer deviceCount;

    private Date potentialRightsEnd;

    private String licenseRef;

    private Date rightsEnd;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getLicenseUpdated() {
        return licenseUpdated;
    }

    public void setLicenseUpdated(Date licenseUpdated) {
        this.licenseUpdated = licenseUpdated;
    }

    public Date getStatusUpdated() {
        return statusUpdated;
    }

    public void setStatusUpdated(Date statusUpdated) {
        this.statusUpdated = statusUpdated;
    }

    public Integer getDeviceCount() {
        return deviceCount;
    }

    public void setDeviceCount(Integer deviceCount) {
        this.deviceCount = deviceCount;
    }

    public Date getPotentialRightsEnd() {
        return potentialRightsEnd;
    }

    public void setPotentialRightsEnd(Date potentialRightsEnd) {
        this.potentialRightsEnd = potentialRightsEnd;
    }

    public String getLicenseRef() {
        return licenseRef;
    }

    public void setLicenseRef(String licenseRef) {
        this.licenseRef = licenseRef == null ? null : licenseRef.trim();
    }

    public Date getRightsEnd() {
        return rightsEnd;
    }

    public void setRightsEnd(Date rightsEnd) {
        this.rightsEnd = rightsEnd;
    }
}