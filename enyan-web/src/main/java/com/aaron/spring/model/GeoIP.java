package com.aaron.spring.model;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/11/18
 * @Modified By:
 */
public class GeoIP extends BaseDTO{
	private static final long serialVersionUID = 3502026608344539818L;
	private String country;
	private String ipAddress;
	private String city;
	private String latitude;
	private String longitude;

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}
}
