package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EnyanRentDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanRentDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDetailIdIsNull() {
            addCriterion("detail_id is null");
            return (Criteria) this;
        }

        public Criteria andDetailIdIsNotNull() {
            addCriterion("detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andDetailIdEqualTo(Long value) {
            addCriterion("detail_id =", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdNotEqualTo(Long value) {
            addCriterion("detail_id <>", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdGreaterThan(Long value) {
            addCriterion("detail_id >", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("detail_id >=", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdLessThan(Long value) {
            addCriterion("detail_id <", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("detail_id <=", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdIn(List<Long> values) {
            addCriterion("detail_id in", values, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdNotIn(List<Long> values) {
            addCriterion("detail_id not in", values, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdBetween(Long value1, Long value2) {
            addCriterion("detail_id between", value1, value2, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("detail_id not between", value1, value2, "detailId");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNull() {
            addCriterion("order_num is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNotNull() {
            addCriterion("order_num is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumEqualTo(String value) {
            addCriterion("order_num =", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotEqualTo(String value) {
            addCriterion("order_num <>", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThan(String value) {
            addCriterion("order_num >", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("order_num >=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThan(String value) {
            addCriterion("order_num <", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThanOrEqualTo(String value) {
            addCriterion("order_num <=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLike(String value) {
            addCriterion("order_num like", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotLike(String value) {
            addCriterion("order_num not like", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumIn(List<String> values) {
            addCriterion("order_num in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotIn(List<String> values) {
            addCriterion("order_num not in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumBetween(String value1, String value2) {
            addCriterion("order_num between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotBetween(String value1, String value2) {
            addCriterion("order_num not between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andTradeNoIsNull() {
            addCriterion("trade_no is null");
            return (Criteria) this;
        }

        public Criteria andTradeNoIsNotNull() {
            addCriterion("trade_no is not null");
            return (Criteria) this;
        }

        public Criteria andTradeNoEqualTo(String value) {
            addCriterion("trade_no =", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotEqualTo(String value) {
            addCriterion("trade_no <>", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThan(String value) {
            addCriterion("trade_no >", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThanOrEqualTo(String value) {
            addCriterion("trade_no >=", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThan(String value) {
            addCriterion("trade_no <", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThanOrEqualTo(String value) {
            addCriterion("trade_no <=", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoLike(String value) {
            addCriterion("trade_no like", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotLike(String value) {
            addCriterion("trade_no not like", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoIn(List<String> values) {
            addCriterion("trade_no in", values, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotIn(List<String> values) {
            addCriterion("trade_no not in", values, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoBetween(String value1, String value2) {
            addCriterion("trade_no between", value1, value2, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotBetween(String value1, String value2) {
            addCriterion("trade_no not between", value1, value2, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andUserEmailIsNull() {
            addCriterion("user_email is null");
            return (Criteria) this;
        }

        public Criteria andUserEmailIsNotNull() {
            addCriterion("user_email is not null");
            return (Criteria) this;
        }

        public Criteria andUserEmailEqualTo(String value) {
            addCriterion("user_email =", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotEqualTo(String value) {
            addCriterion("user_email <>", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailGreaterThan(String value) {
            addCriterion("user_email >", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("user_email >=", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLessThan(String value) {
            addCriterion("user_email <", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLessThanOrEqualTo(String value) {
            addCriterion("user_email <=", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLike(String value) {
            addCriterion("user_email like", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotLike(String value) {
            addCriterion("user_email not like", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailIn(List<String> values) {
            addCriterion("user_email in", values, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotIn(List<String> values) {
            addCriterion("user_email not in", values, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailBetween(String value1, String value2) {
            addCriterion("user_email between", value1, value2, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotBetween(String value1, String value2) {
            addCriterion("user_email not between", value1, value2, "userEmail");
            return (Criteria) this;
        }

        public Criteria andRentTypeIsNull() {
            addCriterion("rent_type is null");
            return (Criteria) this;
        }

        public Criteria andRentTypeIsNotNull() {
            addCriterion("rent_type is not null");
            return (Criteria) this;
        }

        public Criteria andRentTypeEqualTo(Integer value) {
            addCriterion("rent_type =", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeNotEqualTo(Integer value) {
            addCriterion("rent_type <>", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeGreaterThan(Integer value) {
            addCriterion("rent_type >", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_type >=", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeLessThan(Integer value) {
            addCriterion("rent_type <", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeLessThanOrEqualTo(Integer value) {
            addCriterion("rent_type <=", value, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeIn(List<Integer> values) {
            addCriterion("rent_type in", values, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeNotIn(List<Integer> values) {
            addCriterion("rent_type not in", values, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeBetween(Integer value1, Integer value2) {
            addCriterion("rent_type between", value1, value2, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_type not between", value1, value2, "rentType");
            return (Criteria) this;
        }

        public Criteria andRentLangIsNull() {
            addCriterion("rent_lang is null");
            return (Criteria) this;
        }

        public Criteria andRentLangIsNotNull() {
            addCriterion("rent_lang is not null");
            return (Criteria) this;
        }

        public Criteria andRentLangEqualTo(Integer value) {
            addCriterion("rent_lang =", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangNotEqualTo(Integer value) {
            addCriterion("rent_lang <>", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangGreaterThan(Integer value) {
            addCriterion("rent_lang >", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_lang >=", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangLessThan(Integer value) {
            addCriterion("rent_lang <", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangLessThanOrEqualTo(Integer value) {
            addCriterion("rent_lang <=", value, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangIn(List<Integer> values) {
            addCriterion("rent_lang in", values, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangNotIn(List<Integer> values) {
            addCriterion("rent_lang not in", values, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangBetween(Integer value1, Integer value2) {
            addCriterion("rent_lang between", value1, value2, "rentLang");
            return (Criteria) this;
        }

        public Criteria andRentLangNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_lang not between", value1, value2, "rentLang");
            return (Criteria) this;
        }

        public Criteria andIsAutoIsNull() {
            addCriterion("is_auto is null");
            return (Criteria) this;
        }

        public Criteria andIsAutoIsNotNull() {
            addCriterion("is_auto is not null");
            return (Criteria) this;
        }

        public Criteria andIsAutoEqualTo(Integer value) {
            addCriterion("is_auto =", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoNotEqualTo(Integer value) {
            addCriterion("is_auto <>", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoGreaterThan(Integer value) {
            addCriterion("is_auto >", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_auto >=", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoLessThan(Integer value) {
            addCriterion("is_auto <", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoLessThanOrEqualTo(Integer value) {
            addCriterion("is_auto <=", value, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoIn(List<Integer> values) {
            addCriterion("is_auto in", values, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoNotIn(List<Integer> values) {
            addCriterion("is_auto not in", values, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoBetween(Integer value1, Integer value2) {
            addCriterion("is_auto between", value1, value2, "isAuto");
            return (Criteria) this;
        }

        public Criteria andIsAutoNotBetween(Integer value1, Integer value2) {
            addCriterion("is_auto not between", value1, value2, "isAuto");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNull() {
            addCriterion("publisher_id is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNotNull() {
            addCriterion("publisher_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdEqualTo(Long value) {
            addCriterion("publisher_id =", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotEqualTo(Long value) {
            addCriterion("publisher_id <>", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThan(Long value) {
            addCriterion("publisher_id >", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThanOrEqualTo(Long value) {
            addCriterion("publisher_id >=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThan(Long value) {
            addCriterion("publisher_id <", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThanOrEqualTo(Long value) {
            addCriterion("publisher_id <=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIn(List<Long> values) {
            addCriterion("publisher_id in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotIn(List<Long> values) {
            addCriterion("publisher_id not in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdBetween(Long value1, Long value2) {
            addCriterion("publisher_id between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotBetween(Long value1, Long value2) {
            addCriterion("publisher_id not between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andRentMonthsIsNull() {
            addCriterion("rent_months is null");
            return (Criteria) this;
        }

        public Criteria andRentMonthsIsNotNull() {
            addCriterion("rent_months is not null");
            return (Criteria) this;
        }

        public Criteria andRentMonthsEqualTo(Integer value) {
            addCriterion("rent_months =", value, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsNotEqualTo(Integer value) {
            addCriterion("rent_months <>", value, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsGreaterThan(Integer value) {
            addCriterion("rent_months >", value, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_months >=", value, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsLessThan(Integer value) {
            addCriterion("rent_months <", value, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsLessThanOrEqualTo(Integer value) {
            addCriterion("rent_months <=", value, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsIn(List<Integer> values) {
            addCriterion("rent_months in", values, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsNotIn(List<Integer> values) {
            addCriterion("rent_months not in", values, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsBetween(Integer value1, Integer value2) {
            addCriterion("rent_months between", value1, value2, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andRentMonthsNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_months not between", value1, value2, "rentMonths");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIsNull() {
            addCriterion("vendor_percent is null");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIsNotNull() {
            addCriterion("vendor_percent is not null");
            return (Criteria) this;
        }

        public Criteria andVendorPercentEqualTo(Integer value) {
            addCriterion("vendor_percent =", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotEqualTo(Integer value) {
            addCriterion("vendor_percent <>", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentGreaterThan(Integer value) {
            addCriterion("vendor_percent >", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentGreaterThanOrEqualTo(Integer value) {
            addCriterion("vendor_percent >=", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentLessThan(Integer value) {
            addCriterion("vendor_percent <", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentLessThanOrEqualTo(Integer value) {
            addCriterion("vendor_percent <=", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIn(List<Integer> values) {
            addCriterion("vendor_percent in", values, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotIn(List<Integer> values) {
            addCriterion("vendor_percent not in", values, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentBetween(Integer value1, Integer value2) {
            addCriterion("vendor_percent between", value1, value2, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotBetween(Integer value1, Integer value2) {
            addCriterion("vendor_percent not between", value1, value2, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorIsNull() {
            addCriterion("income_vendor is null");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorIsNotNull() {
            addCriterion("income_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorEqualTo(BigDecimal value) {
            addCriterion("income_vendor =", value, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorNotEqualTo(BigDecimal value) {
            addCriterion("income_vendor <>", value, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorGreaterThan(BigDecimal value) {
            addCriterion("income_vendor >", value, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_vendor >=", value, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorLessThan(BigDecimal value) {
            addCriterion("income_vendor <", value, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_vendor <=", value, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorIn(List<BigDecimal> values) {
            addCriterion("income_vendor in", values, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorNotIn(List<BigDecimal> values) {
            addCriterion("income_vendor not in", values, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_vendor between", value1, value2, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_vendor not between", value1, value2, "incomeVendor");
            return (Criteria) this;
        }

        public Criteria andIncomePlatIsNull() {
            addCriterion("income_plat is null");
            return (Criteria) this;
        }

        public Criteria andIncomePlatIsNotNull() {
            addCriterion("income_plat is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePlatEqualTo(BigDecimal value) {
            addCriterion("income_plat =", value, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatNotEqualTo(BigDecimal value) {
            addCriterion("income_plat <>", value, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatGreaterThan(BigDecimal value) {
            addCriterion("income_plat >", value, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_plat >=", value, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatLessThan(BigDecimal value) {
            addCriterion("income_plat <", value, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_plat <=", value, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatIn(List<BigDecimal> values) {
            addCriterion("income_plat in", values, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatNotIn(List<BigDecimal> values) {
            addCriterion("income_plat not in", values, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_plat between", value1, value2, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomePlatNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_plat not between", value1, value2, "incomePlat");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalIsNull() {
            addCriterion("income_total is null");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalIsNotNull() {
            addCriterion("income_total is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalEqualTo(BigDecimal value) {
            addCriterion("income_total =", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalNotEqualTo(BigDecimal value) {
            addCriterion("income_total <>", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalGreaterThan(BigDecimal value) {
            addCriterion("income_total >", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_total >=", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalLessThan(BigDecimal value) {
            addCriterion("income_total <", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_total <=", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalIn(List<BigDecimal> values) {
            addCriterion("income_total in", values, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalNotIn(List<BigDecimal> values) {
            addCriterion("income_total not in", values, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_total between", value1, value2, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_total not between", value1, value2, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealIsNull() {
            addCriterion("income_real is null");
            return (Criteria) this;
        }

        public Criteria andIncomeRealIsNotNull() {
            addCriterion("income_real is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeRealEqualTo(BigDecimal value) {
            addCriterion("income_real =", value, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealNotEqualTo(BigDecimal value) {
            addCriterion("income_real <>", value, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealGreaterThan(BigDecimal value) {
            addCriterion("income_real >", value, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_real >=", value, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealLessThan(BigDecimal value) {
            addCriterion("income_real <", value, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_real <=", value, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealIn(List<BigDecimal> values) {
            addCriterion("income_real in", values, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealNotIn(List<BigDecimal> values) {
            addCriterion("income_real not in", values, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_real between", value1, value2, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andIncomeRealNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_real not between", value1, value2, "incomeReal");
            return (Criteria) this;
        }

        public Criteria andPayFeeIsNull() {
            addCriterion("pay_fee is null");
            return (Criteria) this;
        }

        public Criteria andPayFeeIsNotNull() {
            addCriterion("pay_fee is not null");
            return (Criteria) this;
        }

        public Criteria andPayFeeEqualTo(BigDecimal value) {
            addCriterion("pay_fee =", value, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeNotEqualTo(BigDecimal value) {
            addCriterion("pay_fee <>", value, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeGreaterThan(BigDecimal value) {
            addCriterion("pay_fee >", value, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pay_fee >=", value, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeLessThan(BigDecimal value) {
            addCriterion("pay_fee <", value, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pay_fee <=", value, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeIn(List<BigDecimal> values) {
            addCriterion("pay_fee in", values, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeNotIn(List<BigDecimal> values) {
            addCriterion("pay_fee not in", values, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pay_fee between", value1, value2, "payFee");
            return (Criteria) this;
        }

        public Criteria andPayFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pay_fee not between", value1, value2, "payFee");
            return (Criteria) this;
        }

        public Criteria andNetSalesIsNull() {
            addCriterion("net_sales is null");
            return (Criteria) this;
        }

        public Criteria andNetSalesIsNotNull() {
            addCriterion("net_sales is not null");
            return (Criteria) this;
        }

        public Criteria andNetSalesEqualTo(BigDecimal value) {
            addCriterion("net_sales =", value, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesNotEqualTo(BigDecimal value) {
            addCriterion("net_sales <>", value, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesGreaterThan(BigDecimal value) {
            addCriterion("net_sales >", value, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("net_sales >=", value, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesLessThan(BigDecimal value) {
            addCriterion("net_sales <", value, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("net_sales <=", value, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesIn(List<BigDecimal> values) {
            addCriterion("net_sales in", values, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesNotIn(List<BigDecimal> values) {
            addCriterion("net_sales not in", values, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("net_sales between", value1, value2, "netSales");
            return (Criteria) this;
        }

        public Criteria andNetSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("net_sales not between", value1, value2, "netSales");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyIsNull() {
            addCriterion("order_currency is null");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyIsNotNull() {
            addCriterion("order_currency is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyEqualTo(Integer value) {
            addCriterion("order_currency =", value, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyNotEqualTo(Integer value) {
            addCriterion("order_currency <>", value, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyGreaterThan(Integer value) {
            addCriterion("order_currency >", value, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_currency >=", value, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyLessThan(Integer value) {
            addCriterion("order_currency <", value, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyLessThanOrEqualTo(Integer value) {
            addCriterion("order_currency <=", value, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyIn(List<Integer> values) {
            addCriterion("order_currency in", values, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyNotIn(List<Integer> values) {
            addCriterion("order_currency not in", values, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyBetween(Integer value1, Integer value2) {
            addCriterion("order_currency between", value1, value2, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andOrderCurrencyNotBetween(Integer value1, Integer value2) {
            addCriterion("order_currency not between", value1, value2, "orderCurrency");
            return (Criteria) this;
        }

        public Criteria andPayTypeIsNull() {
            addCriterion("pay_type is null");
            return (Criteria) this;
        }

        public Criteria andPayTypeIsNotNull() {
            addCriterion("pay_type is not null");
            return (Criteria) this;
        }

        public Criteria andPayTypeEqualTo(Integer value) {
            addCriterion("pay_type =", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotEqualTo(Integer value) {
            addCriterion("pay_type <>", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeGreaterThan(Integer value) {
            addCriterion("pay_type >", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("pay_type >=", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeLessThan(Integer value) {
            addCriterion("pay_type <", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeLessThanOrEqualTo(Integer value) {
            addCriterion("pay_type <=", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeIn(List<Integer> values) {
            addCriterion("pay_type in", values, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotIn(List<Integer> values) {
            addCriterion("pay_type not in", values, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeBetween(Integer value1, Integer value2) {
            addCriterion("pay_type between", value1, value2, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("pay_type not between", value1, value2, "payType");
            return (Criteria) this;
        }

        public Criteria andPayCountryIsNull() {
            addCriterion("pay_country is null");
            return (Criteria) this;
        }

        public Criteria andPayCountryIsNotNull() {
            addCriterion("pay_country is not null");
            return (Criteria) this;
        }

        public Criteria andPayCountryEqualTo(String value) {
            addCriterion("pay_country =", value, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryNotEqualTo(String value) {
            addCriterion("pay_country <>", value, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryGreaterThan(String value) {
            addCriterion("pay_country >", value, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryGreaterThanOrEqualTo(String value) {
            addCriterion("pay_country >=", value, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryLessThan(String value) {
            addCriterion("pay_country <", value, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryLessThanOrEqualTo(String value) {
            addCriterion("pay_country <=", value, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryLike(String value) {
            addCriterion("pay_country like", value, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryNotLike(String value) {
            addCriterion("pay_country not like", value, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryIn(List<String> values) {
            addCriterion("pay_country in", values, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryNotIn(List<String> values) {
            addCriterion("pay_country not in", values, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryBetween(String value1, String value2) {
            addCriterion("pay_country between", value1, value2, "payCountry");
            return (Criteria) this;
        }

        public Criteria andPayCountryNotBetween(String value1, String value2) {
            addCriterion("pay_country not between", value1, value2, "payCountry");
            return (Criteria) this;
        }

        public Criteria andOrderFromIsNull() {
            addCriterion("order_from is null");
            return (Criteria) this;
        }

        public Criteria andOrderFromIsNotNull() {
            addCriterion("order_from is not null");
            return (Criteria) this;
        }

        public Criteria andOrderFromEqualTo(Integer value) {
            addCriterion("order_from =", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromNotEqualTo(Integer value) {
            addCriterion("order_from <>", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromGreaterThan(Integer value) {
            addCriterion("order_from >", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_from >=", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromLessThan(Integer value) {
            addCriterion("order_from <", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromLessThanOrEqualTo(Integer value) {
            addCriterion("order_from <=", value, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromIn(List<Integer> values) {
            addCriterion("order_from in", values, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromNotIn(List<Integer> values) {
            addCriterion("order_from not in", values, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromBetween(Integer value1, Integer value2) {
            addCriterion("order_from between", value1, value2, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andOrderFromNotBetween(Integer value1, Integer value2) {
            addCriterion("order_from not between", value1, value2, "orderFrom");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtIsNull() {
            addCriterion("purchased_at is null");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtIsNotNull() {
            addCriterion("purchased_at is not null");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtEqualTo(Date value) {
            addCriterion("purchased_at =", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtNotEqualTo(Date value) {
            addCriterion("purchased_at <>", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtGreaterThan(Date value) {
            addCriterion("purchased_at >", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("purchased_at >=", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtLessThan(Date value) {
            addCriterion("purchased_at <", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtLessThanOrEqualTo(Date value) {
            addCriterion("purchased_at <=", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtIn(List<Date> values) {
            addCriterion("purchased_at in", values, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtNotIn(List<Date> values) {
            addCriterion("purchased_at not in", values, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtBetween(Date value1, Date value2) {
            addCriterion("purchased_at between", value1, value2, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtNotBetween(Date value1, Date value2) {
            addCriterion("purchased_at not between", value1, value2, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andFromAtIsNull() {
            addCriterion("from_at is null");
            return (Criteria) this;
        }

        public Criteria andFromAtIsNotNull() {
            addCriterion("from_at is not null");
            return (Criteria) this;
        }

        public Criteria andFromAtEqualTo(Date value) {
            addCriterion("from_at =", value, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtNotEqualTo(Date value) {
            addCriterion("from_at <>", value, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtGreaterThan(Date value) {
            addCriterion("from_at >", value, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtGreaterThanOrEqualTo(Date value) {
            addCriterion("from_at >=", value, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtLessThan(Date value) {
            addCriterion("from_at <", value, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtLessThanOrEqualTo(Date value) {
            addCriterion("from_at <=", value, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtIn(List<Date> values) {
            addCriterion("from_at in", values, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtNotIn(List<Date> values) {
            addCriterion("from_at not in", values, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtBetween(Date value1, Date value2) {
            addCriterion("from_at between", value1, value2, "fromAt");
            return (Criteria) this;
        }

        public Criteria andFromAtNotBetween(Date value1, Date value2) {
            addCriterion("from_at not between", value1, value2, "fromAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtIsNull() {
            addCriterion("expired_at is null");
            return (Criteria) this;
        }

        public Criteria andExpiredAtIsNotNull() {
            addCriterion("expired_at is not null");
            return (Criteria) this;
        }

        public Criteria andExpiredAtEqualTo(Date value) {
            addCriterion("expired_at =", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtNotEqualTo(Date value) {
            addCriterion("expired_at <>", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtGreaterThan(Date value) {
            addCriterion("expired_at >", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtGreaterThanOrEqualTo(Date value) {
            addCriterion("expired_at >=", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtLessThan(Date value) {
            addCriterion("expired_at <", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtLessThanOrEqualTo(Date value) {
            addCriterion("expired_at <=", value, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtIn(List<Date> values) {
            addCriterion("expired_at in", values, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtNotIn(List<Date> values) {
            addCriterion("expired_at not in", values, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtBetween(Date value1, Date value2) {
            addCriterion("expired_at between", value1, value2, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andExpiredAtNotBetween(Date value1, Date value2) {
            addCriterion("expired_at not between", value1, value2, "expiredAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}