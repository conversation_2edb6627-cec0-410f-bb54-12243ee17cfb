package com.aaron.spring.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.aaron.common.OrderObj;

public class PodFavorite extends BaseDTO{
    private Long favoriteId;
    private Long userId;
    private String userEmail;
    private Long podcastId;
    private Date favoritedAt;
    private List<OrderObj> orderObjList;

    // Getters and Setters
    public Long getFavoriteId() {
        return favoriteId;
    }

    public void setFavoriteId(Long favoriteId) {
        this.favoriteId = favoriteId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail == null ? null : userEmail.trim();
    }

    public Long getPodcastId() {
        return podcastId;
    }

    public void setPodcastId(Long podcastId) {
        this.podcastId = podcastId;
    }

    public Date getFavoritedAt() {
        return favoritedAt;
    }

    public void setFavoritedAt(Date favoritedAt) {
        this.favoritedAt = favoritedAt;
    }

    public List<OrderObj> getOrderObjList() {
        return orderObjList;
    }

    public void setOrderObjList(List<OrderObj> orderObjList) {
        this.orderObjList = orderObjList;
    }

    public void addOrder(OrderObj orderObj) {
        if (this.orderObjList == null) {
            this.orderObjList = new ArrayList<>();
        }
        this.orderObjList.add(orderObj);
    }
}
