package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.Date;

public class EnyanOrderDetail extends BaseDTO{
    private static final long serialVersionUID = -5418996103256581145L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long orderDetailId;

    private String orderNum;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long userId;

    private String userEmail;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookId;

    private String bookTitle;

    private String bookPubCode;

    private String bookEsin;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long publisherId;

    private String rateValue;

    private BigDecimal priceFixed;

    private BigDecimal priceSelling;

    private Integer quantity;

    private Integer vendorPercent;

    private BigDecimal incomeVendor;

    private BigDecimal incomePlat;

    private BigDecimal incomeTotal;

    private BigDecimal incomeReal;

    private BigDecimal payFee;

    private BigDecimal netSales;

    private Byte orderCurrency;

    private Date purchasedAt;

    private Integer purchasedDay;

    private Integer orderType;

    private Integer payType;

    private String payCountry;

    private Byte isCounted;

    private Integer orderFrom;

    private Integer salesModel;

    private String drminfo;

    private Integer isDeleted;

    private Integer purchasedDayStart;

    private Integer purchasedDayEnd;

    private BigDecimal grossProfit;//毛利

    private BigDecimal grossProfitMargin;//毛利率

    public Long getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Long orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum == null ? null : orderNum.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail == null ? null : userEmail.trim();
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public void setBookTitle(String bookTitle) {
        this.bookTitle = bookTitle == null ? null : bookTitle.trim();
    }

    public String getBookPubCode() {
        return bookPubCode;
    }

    public void setBookPubCode(String bookPubCode) {
        this.bookPubCode = bookPubCode == null ? null : bookPubCode.trim();
    }

    public String getBookEsin() {
        return bookEsin;
    }

    public void setBookEsin(String bookEsin) {
        this.bookEsin = bookEsin == null ? null : bookEsin.trim();
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public String getRateValue() {
        return rateValue;
    }

    public void setRateValue(String rateValue) {
        this.rateValue = rateValue == null ? null : rateValue.trim();
    }

    public BigDecimal getPriceFixed() {
        return priceFixed;
    }

    public void setPriceFixed(BigDecimal priceFixed) {
        this.priceFixed = priceFixed;
    }

    public BigDecimal getPriceSelling() {
        return priceSelling;
    }

    public void setPriceSelling(BigDecimal priceSelling) {
        this.priceSelling = priceSelling;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getVendorPercent() {
        return vendorPercent;
    }

    public void setVendorPercent(Integer vendorPercent) {
        this.vendorPercent = vendorPercent;
    }

    public BigDecimal getIncomeVendor() {
        return incomeVendor;
    }

    public void setIncomeVendor(BigDecimal incomeVendor) {
        this.incomeVendor = incomeVendor;
    }

    public BigDecimal getIncomePlat() {
        return incomePlat;
    }

    public void setIncomePlat(BigDecimal incomePlat) {
        this.incomePlat = incomePlat;
    }

    public BigDecimal getIncomeTotal() {
        return incomeTotal;
    }

    public void setIncomeTotal(BigDecimal incomeTotal) {
        this.incomeTotal = incomeTotal;
    }

    public BigDecimal getIncomeReal() {
        return incomeReal;
    }

    public void setIncomeReal(BigDecimal incomeReal) {
        this.incomeReal = incomeReal;
    }

    public BigDecimal getPayFee() {
        return payFee;
    }

    public void setPayFee(BigDecimal payFee) {
        this.payFee = payFee;
    }

    public BigDecimal getNetSales() {
        return netSales;
    }

    public void setNetSales(BigDecimal netSales) {
        this.netSales = netSales;
    }

    public Byte getOrderCurrency() {
        return orderCurrency;
    }

    public void setOrderCurrency(Byte orderCurrency) {
        this.orderCurrency = orderCurrency;
    }

    public Date getPurchasedAt() {
        return purchasedAt;
    }

    public void setPurchasedAt(Date purchasedAt) {
        this.purchasedAt = purchasedAt;
    }

    public Integer getPurchasedDay() {
        return purchasedDay;
    }

    public void setPurchasedDay(Integer purchasedDay) {
        this.purchasedDay = purchasedDay;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getPayCountry() {
        return payCountry;
    }

    public void setPayCountry(String payCountry) {
        this.payCountry = payCountry == null ? null : payCountry.trim();
    }

    public Byte getIsCounted() {
        return isCounted;
    }

    public void setIsCounted(Byte isCounted) {
        this.isCounted = isCounted;
    }

    public Integer getOrderFrom() {
        return orderFrom;
    }

    public void setOrderFrom(Integer orderFrom) {
        this.orderFrom = orderFrom;
    }

    public Integer getSalesModel() {
        return salesModel;
    }

    public void setSalesModel(Integer salesModel) {
        this.salesModel = salesModel;
    }

    public String getDrminfo() {
        return drminfo;
    }

    public void setDrminfo(String drminfo) {
        this.drminfo = drminfo == null ? null : drminfo.trim();
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getPurchasedDayStart() {
        return purchasedDayStart;
    }

    public void setPurchasedDayStart(Integer purchasedDayStart) {
        this.purchasedDayStart = purchasedDayStart;
    }

    public Integer getPurchasedDayEnd() {
        return purchasedDayEnd;
    }

    public void setPurchasedDayEnd(Integer purchasedDayEnd) {
        this.purchasedDayEnd = purchasedDayEnd;
    }

    public BigDecimal getGrossProfit() {
        return grossProfit;
    }

    public void setGrossProfit(BigDecimal grossProfit) {
        this.grossProfit = grossProfit;
    }

    public BigDecimal getGrossProfitMargin() {
        return grossProfitMargin;
    }

    public void setGrossProfitMargin(BigDecimal grossProfitMargin) {
        this.grossProfitMargin = grossProfitMargin;
    }
}