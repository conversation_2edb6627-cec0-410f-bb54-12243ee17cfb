package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.aaron.spring.common.APIConstant;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

public class EnyanBook extends BaseDTO implements Cloneable{
    @Serial
    private static final long serialVersionUID = -5500508768027355897L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookId;

    private String bookTitle;

    private String bookPinyin;

    private String author;

    private String authorBio;

    private String translator;

    private String wordCount;

    private String wordCountShow;

    private String productWeb;

    private BigDecimal price;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long salesVolume;

    private BigDecimal priceCny;

    private BigDecimal priceUsd;

    private BigDecimal priceHkd;

    private Integer bookCost;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long categoryId;

    private String categoryName;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long publisherId;

    private String publisherName;

    private String publishedAt;

    private String bookCatalogue;

    private String bookAbstract;

    private String bookCover;

    private String bookCoverApp;

    private String bookSample;

    private String bookFull;

    private String bookHash;

    private String bookIsbn;

    private String ebookIsbn;

    private String bookEsin;

    private Byte hasBookPagination;

    private String bookPubCode;

    private String bookKeywords;

    private Integer bookType;

    private Integer specialOffer;

    private Integer areaDiscount;

    private Integer canTts;

    private String size;

    private String bookDrmRef;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long discountSingleId;

    private Byte discountSingleType;

    private Integer discountSingleValue;

    private Date discountSingleStartTime;

    private Date discountSingleEndTime;

    private Byte discountSingleIsValid;

    private String discountSingleDescription;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long discountId;

    private String discountDescription;

    private Byte discountIsValid;

    private String setName;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long setId;

    private Byte isInCn;

    private Integer bibleVersion;

    private Integer canMember;

    private Byte showPublisher;

    private Integer recommendedOrder;

    private String recommendedCaption;

    private Byte printPermission;

    private Byte copyPermission;

    private Byte ebookFormat;

    private Byte shelfStatus;

    private Integer vendorPercent;

    private Integer salesModel;

    private String star;

    private Integer starCount;

    private Date opensaleAt;

    private String bookDescription;

    private String bookWeb;

    private BigDecimal priceCnyDiscount;

    private BigDecimal priceUSDDiscount;

    private BigDecimal priceHKDDiscount;

    private BookSetInfo bookSetInfo;

    private String[] bookIDs;

    private String cartId;//在我的收藏里用到了这个字段，用于区分是否已加入购物车

    @JSONField(serialize = false)
    private boolean notShowDiscount;//是否需要显示折扣信息


    private BookWebInfo bookWebInfo;

    private String bookWebSalePapers;

    private String bookWebVersions;

    /**
     * <p>获取书籍的折扣描述</p>
     * @return: java.lang.String
     * @since : 2020-07-30
     */
    @JsonIgnore
    public String getBookDiscountDescription(){
        if (notShowDiscount){
            return "";
        }
        if (!Constant.BYTE_VALUE_1.equals(this.getDiscountSingleIsValid())){
            return "";
        }
        if (Constant.BYTE_VALUE_0.equals(this.getDiscountSingleType())){
            if (this.getDiscountSingleValue() < 10){
                return "0."+this.getDiscountSingleValue()+"折";
            }
            if (this.getDiscountSingleValue()%10 == 0){
                return this.getDiscountSingleValue()/10+"折";
            }
            if (this.getDiscountSingleValue()<100){
                return this.getDiscountSingleValue()+"折";
            }
        }
        return "";
    }

    /**
     * <p>根据area重置书籍价格信息</p>
     * 这个折扣与N件折的冲突问题
     * @param area
     * @return void
     * @since : 2021/11/22
     **/
    public void resetByArea(String area){
        //System.out.println("resetArea:"+area);
        switch (areaDiscount){
            case 1://大陆免费
                if (APIConstant.BookDiscountArea.CN.equals(area)){
//                    priceHkd = Constant.DEFAULT_ZERO;
                    //priceHkd = Constant.VALUE_0;//只是折扣价格为0，定价还是非0
                    priceHKDDiscount = Constant.VALUE_0;
                    notShowDiscount = true;
                }
                break;
            default:
                break;
        }
    }

    @JsonIgnore
    public boolean getIsCBCS(){
        if (this.getPublisherId() != 1){//本身就应该是恩道的书籍
            return false;
        }
        if (Constant.BYTE_VALUE_1.equals(this.isInCn)){//简体
            if (EBookConstant.RentConstant.CBCS_OT_BOOKS_Hans.contains(this.bookId.longValue())
                        ||EBookConstant.RentConstant.CBCS_NT_BOOKS_Hans.contains(this.bookId.longValue())){
                return true;
            }
            return false;
        }
        if (Constant.BYTE_VALUE_2.equals(this.isInCn)){//繁体
            if (EBookConstant.RentConstant.CBCS_OT_BOOKS_Hant.contains(this.bookId.longValue())
                        ||EBookConstant.RentConstant.CBCS_NT_BOOKS_Hant.contains(this.bookId.longValue())){
                return true;
            }
            return false;
        }
        return false;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public void setBookTitle(String bookTitle) {
        this.bookTitle = bookTitle == null ? null : bookTitle.trim();
    }

    public String getBookPinyin() {
        return bookPinyin;
    }

    public void setBookPinyin(String bookPinyin) {
        this.bookPinyin = bookPinyin == null ? null : bookPinyin.trim();
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author == null ? null : author.trim();
    }

    public String getAuthorBio() {
        return authorBio;
    }

    public void setAuthorBio(String authorBio) {
        this.authorBio = authorBio == null ? null : authorBio.trim();
    }

    public String getTranslator() {
        return translator;
    }

    public void setTranslator(String translator) {
        this.translator = translator == null ? null : translator.trim();
    }

    public String getWordCount() {
        return wordCount;
    }

    public void setWordCount(String wordCount) {
        this.wordCount = wordCount == null ? null : wordCount.trim();
    }

    public String getWordCountShow() {
        return wordCountShow;
    }

    public void setWordCountShow(String wordCountShow) {
        this.wordCountShow = wordCountShow == null ? null : wordCountShow.trim();
    }

    public String getProductWeb() {
        return productWeb;
    }

    public void setProductWeb(String productWeb) {
        this.productWeb = productWeb == null ? null : productWeb.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getSalesVolume() {
        return salesVolume;
    }

    public void setSalesVolume(Long salesVolume) {
        this.salesVolume = salesVolume;
    }

    public BigDecimal getPriceCny() {
        return priceCny;
    }

    public void setPriceCny(BigDecimal priceCny) {
        this.priceCny = priceCny;
    }

    public BigDecimal getPriceUsd() {
        return priceUsd;
    }

    public void setPriceUsd(BigDecimal priceUsd) {
        this.priceUsd = priceUsd;
    }

    public BigDecimal getPriceHkd() {
        return priceHkd;
    }

    public void setPriceHkd(BigDecimal priceHkd) {
        this.priceHkd = priceHkd;
    }

    public Integer getBookCost() {
        return bookCost;
    }

    public void setBookCost(Integer bookCost) {
        this.bookCost = bookCost;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public String getPublisherName() {
        return publisherName;
    }

    public void setPublisherName(String publisherName) {
        this.publisherName = publisherName == null ? null : publisherName.trim();
    }

    public String getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(String publishedAt) {
        this.publishedAt = publishedAt == null ? null : publishedAt.trim();
    }

    public String getBookCatalogue() {
        return bookCatalogue;
    }

    public void setBookCatalogue(String bookCatalogue) {
        this.bookCatalogue = bookCatalogue == null ? null : bookCatalogue.trim();
    }

    public String getBookAbstract() {
        return bookAbstract;
    }

    public void setBookAbstract(String bookAbstract) {
        this.bookAbstract = bookAbstract == null ? null : bookAbstract.trim();
    }

    public String getBookCover() {
        return bookCover;
    }

    public void setBookCover(String bookCover) {
        this.bookCover = bookCover == null ? null : bookCover.trim();
    }

    public String getBookCoverApp() {
        return bookCoverApp;
    }

    public void setBookCoverApp(String bookCoverApp) {
        this.bookCoverApp = bookCoverApp == null ? null : bookCoverApp.trim();
    }

    public String getBookSample() {
        return bookSample;
    }

    public void setBookSample(String bookSample) {
        this.bookSample = bookSample == null ? null : bookSample.trim();
    }

    public String getBookFull() {
        return bookFull;
    }

    public void setBookFull(String bookFull) {
        this.bookFull = bookFull == null ? null : bookFull.trim();
    }

    public String getBookHash() {
        return bookHash;
    }

    public void setBookHash(String bookHash) {
        this.bookHash = bookHash == null ? null : bookHash.trim();
    }

    public String getBookIsbn() {
        return bookIsbn;
    }

    public void setBookIsbn(String bookIsbn) {
        this.bookIsbn = bookIsbn == null ? null : bookIsbn.trim();
    }

    public String getEbookIsbn() {
        return ebookIsbn;
    }

    public void setEbookIsbn(String ebookIsbn) {
        this.ebookIsbn = ebookIsbn == null ? null : ebookIsbn.trim();
    }

    public String getBookEsin() {
        return bookEsin;
    }

    public void setBookEsin(String bookEsin) {
        this.bookEsin = bookEsin == null ? null : bookEsin.trim();
    }

    public Byte getHasBookPagination() {
        return hasBookPagination;
    }

    public void setHasBookPagination(Byte hasBookPagination) {
        this.hasBookPagination = hasBookPagination;
    }

    public String getBookPubCode() {
        return bookPubCode;
    }

    public void setBookPubCode(String bookPubCode) {
        this.bookPubCode = bookPubCode == null ? null : bookPubCode.trim();
    }

    public String getBookKeywords() {
        return bookKeywords;
    }

    public void setBookKeywords(String bookKeywords) {
        this.bookKeywords = bookKeywords == null ? null : bookKeywords.trim();
    }

    public Integer getBookType() {
        return bookType;
    }

    public void setBookType(Integer bookType) {
        this.bookType = bookType;
    }

    public Integer getSpecialOffer() {
        return specialOffer;
    }

    public void setSpecialOffer(Integer specialOffer) {
        this.specialOffer = specialOffer;
    }

    public Integer getAreaDiscount() {
        return areaDiscount;
    }

    public void setAreaDiscount(Integer areaDiscount) {
        this.areaDiscount = areaDiscount;
    }

    public Integer getCanTts() {
        return canTts;
    }

    public void setCanTts(Integer canTts) {
        this.canTts = canTts;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size == null ? null : size.trim();
    }

    public String getBookDrmRef() {
        return bookDrmRef;
    }

    public void setBookDrmRef(String bookDrmRef) {
        this.bookDrmRef = bookDrmRef == null ? null : bookDrmRef.trim();
    }

    public Long getDiscountSingleId() {
        return discountSingleId;
    }

    public void setDiscountSingleId(Long discountSingleId) {
        this.discountSingleId = discountSingleId;
    }

    public Byte getDiscountSingleType() {
        return discountSingleType;
    }

    public void setDiscountSingleType(Byte discountSingleType) {
        this.discountSingleType = discountSingleType;
    }

    public Integer getDiscountSingleValue() {
        return discountSingleValue;
    }

    public void setDiscountSingleValue(Integer discountSingleValue) {
        this.discountSingleValue = discountSingleValue;
    }

    public Date getDiscountSingleStartTime() {
        return discountSingleStartTime;
    }

    public void setDiscountSingleStartTime(Date discountSingleStartTime) {
        this.discountSingleStartTime = discountSingleStartTime;
    }

    public Date getDiscountSingleEndTime() {
        return discountSingleEndTime;
    }

    public void setDiscountSingleEndTime(Date discountSingleEndTime) {
        this.discountSingleEndTime = discountSingleEndTime;
    }

    public Byte getDiscountSingleIsValid() {
        return discountSingleIsValid;
    }

    public void setDiscountSingleIsValid(Byte discountSingleIsValid) {
        this.discountSingleIsValid = discountSingleIsValid;
    }

    public String getDiscountSingleDescription() {
        return discountSingleDescription;
    }

    public void setDiscountSingleDescription(String discountSingleDescription) {
        this.discountSingleDescription = discountSingleDescription == null ? null : discountSingleDescription.trim();
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    public String getDiscountDescription() {
        return discountDescription;
    }

    public void setDiscountDescription(String discountDescription) {
        this.discountDescription = discountDescription == null ? null : discountDescription.trim();
    }

    public Byte getDiscountIsValid() {
        return discountIsValid;
    }

    public void setDiscountIsValid(Byte discountIsValid) {
        this.discountIsValid = discountIsValid;
    }

    public String getSetName() {
        return setName;
    }

    public void setSetName(String setName) {
        this.setName = setName == null ? null : setName.trim();
    }

    public Long getSetId() {
        return setId;
    }

    public void setSetId(Long setId) {
        this.setId = setId;
    }

    public Byte getIsInCn() {
        return isInCn;
    }

    public void setIsInCn(Byte isInCn) {
        this.isInCn = isInCn;
    }

    public Integer getBibleVersion() {
        return bibleVersion;
    }

    public void setBibleVersion(Integer bibleVersion) {
        this.bibleVersion = bibleVersion;
    }

    public Integer getCanMember() {
        return canMember;
    }

    public void setCanMember(Integer canMember) {
        this.canMember = canMember;
    }

    public Byte getShowPublisher() {
        return showPublisher;
    }

    public void setShowPublisher(Byte showPublisher) {
        this.showPublisher = showPublisher;
    }

    public Integer getRecommendedOrder() {
        return recommendedOrder;
    }

    public void setRecommendedOrder(Integer recommendedOrder) {
        this.recommendedOrder = recommendedOrder;
    }

    public String getRecommendedCaption() {
        return recommendedCaption;
    }

    public void setRecommendedCaption(String recommendedCaption) {
        this.recommendedCaption = recommendedCaption == null ? null : recommendedCaption.trim();
    }

    public Byte getPrintPermission() {
        return printPermission;
    }

    public void setPrintPermission(Byte printPermission) {
        this.printPermission = printPermission;
    }

    public Byte getCopyPermission() {
        return copyPermission;
    }

    public void setCopyPermission(Byte copyPermission) {
        this.copyPermission = copyPermission;
    }

    public Byte getEbookFormat() {
        return ebookFormat;
    }

    public void setEbookFormat(Byte ebookFormat) {
        this.ebookFormat = ebookFormat;
    }

    public Byte getShelfStatus() {
        return shelfStatus;
    }

    public void setShelfStatus(Byte shelfStatus) {
        this.shelfStatus = shelfStatus;
    }

    public Integer getVendorPercent() {
        return vendorPercent;
    }

    public void setVendorPercent(Integer vendorPercent) {
        this.vendorPercent = vendorPercent;
    }

    public Integer getSalesModel() {
        return salesModel;
    }

    public void setSalesModel(Integer salesModel) {
        this.salesModel = salesModel;
    }

    public String getStar() {
        return star;
    }

    public void setStar(String star) {
        this.star = star == null ? null : star.trim();
    }

    public Integer getStarCount() {
        return starCount;
    }

    public void setStarCount(Integer starCount) {
        this.starCount = starCount;
    }

    public Date getOpensaleAt() {
        return opensaleAt;
    }

    public void setOpensaleAt(Date opensaleAt) {
        this.opensaleAt = opensaleAt;
    }

    public String getBookDescription() {
        return bookDescription;
    }

    public void setBookDescription(String bookDescription) {
        this.bookDescription = bookDescription == null ? null : bookDescription.trim();
    }

    public String getBookWeb() {
        return bookWeb;
    }

    public void setBookWeb(String bookWeb) {
        this.bookWeb = bookWeb == null ? null : bookWeb.trim();
    }

    public BigDecimal getPriceCnyDiscount() {
        return priceCnyDiscount;
    }

    public void setPriceCnyDiscount(BigDecimal priceCnyDiscount) {
        this.priceCnyDiscount = priceCnyDiscount;
    }

    public BigDecimal getPriceUSDDiscount() {
        return priceUSDDiscount;
    }

    public void setPriceUSDDiscount(BigDecimal priceUSDDiscount) {
        this.priceUSDDiscount = priceUSDDiscount;
    }

    public BigDecimal getPriceHKDDiscount() {
        return priceHKDDiscount;
    }

    public void setPriceHKDDiscount(BigDecimal priceHKDDiscount) {
        this.priceHKDDiscount = priceHKDDiscount;
    }

    public BookSetInfo getBookSetInfo() {
        return bookSetInfo;
    }

    public void setBookSetInfo(BookSetInfo bookSetInfo) {
        this.bookSetInfo = bookSetInfo;
    }

    public String[] getBookIDs() {
        return bookIDs;
    }

    public void setBookIDs(String[] bookIDs) {
        this.bookIDs = bookIDs;
    }

    public String getCartId() {
        return cartId;
    }

    public void setCartId(String cartId) {
        this.cartId = cartId;
    }

    public boolean isNotShowDiscount() {
        return notShowDiscount;
    }

    public void setNotShowDiscount(boolean notShowDiscount) {
        this.notShowDiscount = notShowDiscount;
    }

    public BookWebInfo getBookWebInfo() {
        return bookWebInfo;
    }

    public void setBookWebInfo(BookWebInfo bookWebInfo) {
        this.bookWebInfo = bookWebInfo;
    }

    public String getBookWebSalePapers() {
        return bookWebSalePapers;
    }

    public void setBookWebSalePapers(String bookWebSalePapers) {
        this.bookWebSalePapers = bookWebSalePapers;
    }

    public String getBookWebVersions() {
        return bookWebVersions;
    }

    public void setBookWebVersions(String bookWebVersions) {
        this.bookWebVersions = bookWebVersions;
    }
}