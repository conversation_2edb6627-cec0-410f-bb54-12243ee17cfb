package com.aaron.spring.model;

import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PodEpisodeExample {
    protected String orderByClause;
    protected boolean distinct;
    protected List<Criteria> oredCriteria;
    protected Page page;
    protected List<OrderObj> orderObjList;

    public PodEpisodeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public List<OrderObj> getOrderObjList() {
        return orderObjList;
    }

    public void setOrderObjList(List<OrderObj> orderObjList) {
        this.orderObjList = orderObjList;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2, property));
        }

        public Criteria andEpisodeIdIsNull() {
            addCriterion("episode_id is null");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdIsNotNull() {
            addCriterion("episode_id is not null");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdEqualTo(Long value) {
            addCriterion("episode_id =", value, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdNotEqualTo(Long value) {
            addCriterion("episode_id <>", value, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdGreaterThan(Long value) {
            addCriterion("episode_id >", value, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("episode_id >=", value, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdLessThan(Long value) {
            addCriterion("episode_id <", value, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdLessThanOrEqualTo(Long value) {
            addCriterion("episode_id <=", value, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdIn(List<Long> values) {
            addCriterion("episode_id in", values, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdNotIn(List<Long> values) {
            addCriterion("episode_id not in", values, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdBetween(Long value1, Long value2) {
            addCriterion("episode_id between", value1, value2, "episodeId");
            return (Criteria) this;
        }

        public Criteria andEpisodeIdNotBetween(Long value1, Long value2) {
            addCriterion("episode_id not between", value1, value2, "episodeId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdIsNull() {
            addCriterion("podcast_id is null");
            return (Criteria) this;
        }

        public Criteria andPodcastIdIsNotNull() {
            addCriterion("podcast_id is not null");
            return (Criteria) this;
        }

        public Criteria andPodcastIdEqualTo(Long value) {
            addCriterion("podcast_id =", value, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdNotEqualTo(Long value) {
            addCriterion("podcast_id <>", value, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdGreaterThan(Long value) {
            addCriterion("podcast_id >", value, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdGreaterThanOrEqualTo(Long value) {
            addCriterion("podcast_id >=", value, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdLessThan(Long value) {
            addCriterion("podcast_id <", value, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdLessThanOrEqualTo(Long value) {
            addCriterion("podcast_id <=", value, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdIn(List<Long> values) {
            addCriterion("podcast_id in", values, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdNotIn(List<Long> values) {
            addCriterion("podcast_id not in", values, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdBetween(Long value1, Long value2) {
            addCriterion("podcast_id between", value1, value2, "podcastId");
            return (Criteria) this;
        }

        public Criteria andPodcastIdNotBetween(Long value1, Long value2) {
            addCriterion("podcast_id not between", value1, value2, "podcastId");
            return (Criteria) this;
        }

        public Criteria andTopicIdIsNull() {
            addCriterion("topic_id is null");
            return (Criteria) this;
        }

        public Criteria andTopicIdIsNotNull() {
            addCriterion("topic_id is not null");
            return (Criteria) this;
        }

        public Criteria andTopicIdEqualTo(Long value) {
            addCriterion("topic_id =", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdNotEqualTo(Long value) {
            addCriterion("topic_id <>", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdGreaterThan(Long value) {
            addCriterion("topic_id >", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdGreaterThanOrEqualTo(Long value) {
            addCriterion("topic_id >=", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdLessThan(Long value) {
            addCriterion("topic_id <", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdLessThanOrEqualTo(Long value) {
            addCriterion("topic_id <=", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdIn(List<Long> values) {
            addCriterion("topic_id in", values, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdNotIn(List<Long> values) {
            addCriterion("topic_id not in", values, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdBetween(Long value1, Long value2) {
            addCriterion("topic_id between", value1, value2, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdNotBetween(Long value1, Long value2) {
            addCriterion("topic_id not between", value1, value2, "topicId");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlIsNull() {
            addCriterion("audio_file_url is null");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlIsNotNull() {
            addCriterion("audio_file_url is not null");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlEqualTo(String value) {
            addCriterion("audio_file_url =", value, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlNotEqualTo(String value) {
            addCriterion("audio_file_url <>", value, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlGreaterThan(String value) {
            addCriterion("audio_file_url >", value, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlGreaterThanOrEqualTo(String value) {
            addCriterion("audio_file_url >=", value, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlLessThan(String value) {
            addCriterion("audio_file_url <", value, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlLessThanOrEqualTo(String value) {
            addCriterion("audio_file_url <=", value, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlLike(String value) {
            addCriterion("audio_file_url like", value, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlNotLike(String value) {
            addCriterion("audio_file_url not like", value, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlIn(List<String> values) {
            addCriterion("audio_file_url in", values, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlNotIn(List<String> values) {
            addCriterion("audio_file_url not in", values, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlBetween(String value1, String value2) {
            addCriterion("audio_file_url between", value1, value2, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andAudioFileUrlNotBetween(String value1, String value2) {
            addCriterion("audio_file_url not between", value1, value2, "audioFileUrl");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsIsNull() {
            addCriterion("duration_seconds is null");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsIsNotNull() {
            addCriterion("duration_seconds is not null");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsEqualTo(Integer value) {
            addCriterion("duration_seconds =", value, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsNotEqualTo(Integer value) {
            addCriterion("duration_seconds <>", value, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsGreaterThan(Integer value) {
            addCriterion("duration_seconds >", value, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsGreaterThanOrEqualTo(Integer value) {
            addCriterion("duration_seconds >=", value, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsLessThan(Integer value) {
            addCriterion("duration_seconds <", value, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsLessThanOrEqualTo(Integer value) {
            addCriterion("duration_seconds <=", value, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsIn(List<Integer> values) {
            addCriterion("duration_seconds in", values, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsNotIn(List<Integer> values) {
            addCriterion("duration_seconds not in", values, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsBetween(Integer value1, Integer value2) {
            addCriterion("duration_seconds between", value1, value2, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andDurationSecondsNotBetween(Integer value1, Integer value2) {
            addCriterion("duration_seconds not between", value1, value2, "durationSeconds");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIsNull() {
            addCriterion("is_published is null");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIsNotNull() {
            addCriterion("is_published is not null");
            return (Criteria) this;
        }

        public Criteria andIsPublishedEqualTo(Integer value) {
            addCriterion("is_published =", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotEqualTo(Integer value) {
            addCriterion("is_published <>", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedGreaterThan(Integer value) {
            addCriterion("is_published >", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_published >=", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedLessThan(Integer value) {
            addCriterion("is_published <", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedLessThanOrEqualTo(Integer value) {
            addCriterion("is_published <=", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIn(List<Integer> values) {
            addCriterion("is_published in", values, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotIn(List<Integer> values) {
            addCriterion("is_published not in", values, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedBetween(Integer value1, Integer value2) {
            addCriterion("is_published between", value1, value2, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_published not between", value1, value2, "isPublished");
            return (Criteria) this;
        }

        public Criteria andPublicationDateIsNull() {
            addCriterion("publication_date is null");
            return (Criteria) this;
        }

        public Criteria andPublicationDateIsNotNull() {
            addCriterion("publication_date is not null");
            return (Criteria) this;
        }

        public Criteria andPublicationDateEqualTo(Date value) {
            addCriterion("publication_date =", value, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateNotEqualTo(Date value) {
            addCriterion("publication_date <>", value, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateGreaterThan(Date value) {
            addCriterion("publication_date >", value, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateGreaterThanOrEqualTo(Date value) {
            addCriterion("publication_date >=", value, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateLessThan(Date value) {
            addCriterion("publication_date <", value, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateLessThanOrEqualTo(Date value) {
            addCriterion("publication_date <=", value, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateIn(List<Date> values) {
            addCriterion("publication_date in", values, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateNotIn(List<Date> values) {
            addCriterion("publication_date not in", values, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateBetween(Date value1, Date value2) {
            addCriterion("publication_date between", value1, value2, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andPublicationDateNotBetween(Date value1, Date value2) {
            addCriterion("publication_date not between", value1, value2, "publicationDate");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberIsNull() {
            addCriterion("episode_number is null");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberIsNotNull() {
            addCriterion("episode_number is not null");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberEqualTo(Integer value) {
            addCriterion("episode_number =", value, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberNotEqualTo(Integer value) {
            addCriterion("episode_number <>", value, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberGreaterThan(Integer value) {
            addCriterion("episode_number >", value, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("episode_number >=", value, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberLessThan(Integer value) {
            addCriterion("episode_number <", value, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberLessThanOrEqualTo(Integer value) {
            addCriterion("episode_number <=", value, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberIn(List<Integer> values) {
            addCriterion("episode_number in", values, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberNotIn(List<Integer> values) {
            addCriterion("episode_number not in", values, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberBetween(Integer value1, Integer value2) {
            addCriterion("episode_number between", value1, value2, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andEpisodeNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("episode_number not between", value1, value2, "episodeNumber");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;
        private Object value;
        private Object secondValue;
        private boolean noValue;
        private boolean singleValue;
        private boolean betweenValue;
        private boolean listValue;
        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value1, Object value2, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value1;
            this.secondValue = value2;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }
    }
}
