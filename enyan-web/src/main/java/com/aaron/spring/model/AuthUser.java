package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

public class AuthUser extends BaseDTO {

    @Serial
    private static final long serialVersionUID = 5954744390090571455L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long userId;

    private String userPasswordOriginal;

    private String userPassword;

    private String userPasswordAgain;

    private String salt;

    private int roleType;

    private String userName;

    private String nickName;

    private Integer sex;

    private String birthday;

    private String email;

    private Byte isStaff;

    private Byte isActive;

    private Date dateJoined;

    private Date lastLogin;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserPasswordOriginal() {
        return userPasswordOriginal;
    }

    public void setUserPasswordOriginal(String userPasswordOriginal) {
        this.userPasswordOriginal = userPasswordOriginal;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword == null ? null : userPassword.trim();
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt == null ? null : salt.trim();
    }

    public int getRoleType() {
        return roleType;
    }

    public void setRoleType(int roleType) {
        this.roleType = roleType;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName == null ? null : nickName.trim();
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public Byte getIsStaff() {
        return isStaff;
    }

    public void setIsStaff(Byte isStaff) {
        this.isStaff = isStaff;
    }

    public Byte getIsActive() {
        return isActive;
    }

    public void setIsActive(Byte isActive) {
        this.isActive = isActive;
    }

    public Date getDateJoined() {
        return dateJoined;
    }

    public void setDateJoined(Date dateJoined) {
        this.dateJoined = dateJoined;
    }

    public Date getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(Date lastLogin) {
        this.lastLogin = lastLogin;
    }

    public String getUserPasswordAgain() {
        return userPasswordAgain;
    }

    public void setUserPasswordAgain(String userPasswordAgain) {
        this.userPasswordAgain = userPasswordAgain;
    }
}