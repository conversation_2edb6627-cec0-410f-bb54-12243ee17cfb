package com.aaron.spring.interceptor;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.spring.common.Constant;
import com.aaron.spring.service.GeoIPLocationService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * @Author: Aaron <PERSON>
 * @Description:
 * @Date: Created in  2025/2/24
 * @Modified By:
 */
@Slf4j
public class IPInterceptor implements HandlerInterceptor {

    @Resource
    private GeoIPLocationService geoIPLocationService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        //log.debug("IPInterceptor:{}",request.getRequestURI());

        //log.debug("locale:{}",response.getLocale().toString());//zh_CN,en_US
        /*
        String email = UserUtils.getCurrentLoginUser().getEmail();

        String lang = response.getLocale().toString();

        log.debug("email:[{}] is login()",email);*/

        //log.debug("IP:[{}] is login()",geoIPLocationService.getClientIpAddress(request));

        if (Constant.FORBIDDEN_IP.size() == 0){
            return true;
        }
        String ip = geoIPLocationService.getClientIpAddress(request);
        if (Constant.FORBIDDEN_IP.contains(ip) == true) {//
            this.sendRequestErrorResponse(response);
            return false;
        }
        return true;
    }

    /**
     * 非法请求
     *
     * @param response
     * @Date: 2020-02-26
     */
    private void sendRequestErrorResponse(HttpServletResponse response) throws Exception {
        ExecuteResult<String> result = new ExecuteResult<>();
        result.addErrorMessage(InterfaceContant.ApiErrorConfig.ILLEGAL_REQUEST_PARAMETERS_CODE);

        response.setContentType(APPLICATION_JSON_VALUE);
        PrintWriter writer = response.getWriter();
        writer.print("HelloWorld");
        writer.close();
        response.flushBuffer();
    }

}
