package com.aaron.excel.util;

import com.aaron.common.Money;
import com.aaron.common.NameAndValue;
import com.aaron.excel.ExcelRowReaderBatch;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.tomcat.util.bcel.Const;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * 从教务导入数据
 * @Author: <PERSON>
 * @Date: Created in  2019-11-12
 * @Modified By:
 */
@Slf4j
public class ExcelReaderMultiFromBook extends ExcelRowReaderBatch {
    //private String currentDate = DateFormatUtils.format(new Date(),"yyyyMMddHHmmss");
    private Date current = new Date();
    public static final String MSG_YES = "是";
    public static final String MSG_NO = "否";
    public static final String MSG_HAVE = "有";
    public static final String MSG_HAVE_NOT = "无";
    public static final String MSG_SHOW = "显示";
    public static final String MSG_SHOW_NOT = "隐藏";
    public static final String MSG_SUPPORT = "支持";
    public static final String MSG_SUPPORT_NOT = "不支持";
    public static final String MSG_FIXED_EPUB = "固定ePub";
    public static final String MSG_PDF = "PDF";
    public static final String MSG_FLOW_EPUB = "流式ePub";
    public static final String MSG_LANG_SC = "简体中文";
    public static final String MSG_LANG_TC = "繁体中文";
    public static final String MSG_BIBLE_SC = "简体和合本";
    public static final String MSG_BIBLE_TC = "繁体和合本";
    public static final String MSG_SALE_COMMON = "正常销售";
    public static final String MSG_AREA_CHINA_FREE = "大陆免费";
    public static final String BOOK_IMG_WEB_URL = "https://dl.edhub.cc/root/images/book_img/${azureDir}/${bookPubCode}_web.jpg";
    public static final String BOOK_IMG_APP_URL = "https://dl.edhub.cc/root/images/book_img/${azureDir}/${bookPubCode}_app.jpg";
    public static final String EPUB_PREVIEW_URL = "https://dl.edhub.cc/root/preview/${azureDir}/SD_${bookPubCode}.epub";

    private EnyanBookService enyanBookService;
    @Override
    public void getRows(int sheetIndex, int curRow, List<String> rowlist) {
        /*
        if (curRow<this.getBatchReadBeginRow()){
            System.out.println("--------------getRows--------------");
            System.out.println(rowlist);
        }
        if (curRow == 0){
            //
            //canInsert = false;
        }*/
    }
    @Override
    public void batchRead(List<List<String>> rowlists) {
        /*---------需要copy start-----------*/
        if (this.isInterrupted()){
            return;
        }
        super.batchRead(rowlists);
        /*---------需要copy end-----------*/
        //System.out.println("--------------batchRead--------------");
        /*
        for (List<String> rowlist : rowlists){
            if (this.getTotalRows() == 2){

                this.interruptImport();//实现中断

                this.saveRow(rowlist);//保存特定行
            }
            System.out.println(rowlist);
        }*/
        Map valuesMap = new HashMap();
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        for (List<String> rowList : rowlists){
            log.debug(rowList.toString());
            if (rowList.size() >= 30){
                String publicationName = rowList.get(1);
                if (StringUtils.isBlank(publicationName)){
                    continue;
                }
                List<NameAndValue> publisherList = Constant.publishersList.stream().filter(obj -> obj.getName().equals(publicationName)).toList();
                if (null == publisherList || publisherList.isEmpty() == true || StringUtils.isBlank(publisherList.get(0).getValue())){
                    continue;
                }
                Long publisherId = Long.parseLong(publisherList.get(0).getValue());

                String name = rowList.get(3);
                String price = rowList.get(4);
                String vendorPercent = rowList.get(5);
                //String discountSingle = rowList.get(6);
                String categoryName = rowList.get(9);
                if (StringUtils.isBlank(categoryName)){
                    continue;
                }
                List<NameAndValue> categoryList = Constant.categoriesAllList.stream().filter(obj -> obj.getName().equals(categoryName)).toList();
                if (null == categoryList || categoryList.isEmpty() == true || StringUtils.isBlank(categoryList.get(0).getValue())){
                    continue;
                }
                Long categoryId = Long.parseLong(categoryList.get(0).getValue());

                String author = rowList.get(11);
                String translator = rowList.get(12);
                String lang = rowList.get(14);
                /*
                if (StringUtils.isNotBlank(lang)){
                    lang = lang.trim();
                }*/
                String bibleVersion = rowList.get(15);
                String wordCountShow  = rowList.get(16);
                String wordCount  = rowList.get(17);
                String isbnBook  = rowList.get(20);
                String isbnEBook  = rowList.get(21);
                String esin = IdUtil.getEDBookId();
                String bookPubCode  = rowList.get(23);//出版编码
                String publishedAt  = rowList.get(24);
                String keyword  = rowList.get(25);
                String tts  = rowList.get(28);
                String pagination  = rowList.get(29);
                String priority  = rowList.get(30);
                String productWeb  = rowList.get(33);//产品网站
                String publisherShow  = rowList.get(34);
                String areaDiscount  = rowList.get(35);//区域折扣
                String ebookFormat  = rowList.get(36);//ePub
                String size  = rowList.get(37);
                String saleModel  = rowList.get(38);//"正常销售"

                String azureDir  = rowList.get(46);//azureDir

                valuesMap.put("bookPubCode", bookPubCode);
                valuesMap.put("azureDir", azureDir);

                String bookCover  = sub.replace(BOOK_IMG_WEB_URL);//
                String bookCoverApp  = sub.replace(BOOK_IMG_APP_URL);//
                String bookSample  = sub.replace(EPUB_PREVIEW_URL);//

                String bookAbstract  = rowList.get(42);//

                String recommendedCaption  = rowList.get(43);//
                String bookCatalogue  = rowList.get(44);//目录
                String bookDescription  = rowList.get(45);//简介

                EnyanBook enyanBook = new EnyanBook();
                enyanBook.setAreaDiscount(MSG_AREA_CHINA_FREE.equals(areaDiscount) ? 1 : 0);
                enyanBook.setBookTitle(name);
                enyanBook.setCanMember(0);
                enyanBook.setAuthor(author);
                enyanBook.setBibleVersion(MSG_BIBLE_SC.equals(bibleVersion) ? 0 : 1);//0:简体；1：繁体
                enyanBook.setBookIsbn(isbnBook);
                enyanBook.setBookPubCode(bookPubCode);
                enyanBook.setBookKeywords(keyword);
                enyanBook.setBookCover(bookCover);
                enyanBook.setBookCoverApp(bookCoverApp);
                enyanBook.setBookSample(bookSample);
                enyanBook.setBookAbstract(bookAbstract);
                enyanBook.setBookCatalogue(bookCatalogue);
                enyanBook.setBookDescription(bookDescription);
                enyanBook.setBookType(0);
                enyanBook.setBookDrmRef("-1");
                enyanBook.setStarCount(0);
                enyanBook.setSetId(0L);
                enyanBook.setCanTts(MSG_SUPPORT.equals(tts) ? 1 : 0);
                enyanBook.setCategoryId(categoryId);
                enyanBook.setCategoryName(categoryName);
                //enyanBook.setCopyPermission(Constant.BYTE_VALUE_1);
                if (MSG_FLOW_EPUB.equals(ebookFormat)){
                    enyanBook.setEbookFormat(Constant.BYTE_VALUE_1);
                }else if (MSG_FIXED_EPUB.equals(ebookFormat)){
                    enyanBook.setEbookFormat(Constant.BYTE_VALUE_3);
                }else{
                    enyanBook.setEbookFormat(Constant.BYTE_VALUE_2);
                }
                //enyanBook.setEbookFormat(MSG_EPUB.equals(ebookFormat) ? Constant.BYTE_VALUE_1 : Constant.BYTE_VALUE_2);//载体格式 (1 流式ePUB;2 PDF;3 固定ePUB)
                enyanBook.setEbookIsbn(isbnEBook);
                enyanBook.setBookEsin(esin);
                enyanBook.setHasBookPagination(MSG_HAVE.equals(pagination) ? Constant.BYTE_VALUE_1 : Constant.BYTE_VALUE_0);//
                if (MSG_LANG_SC.equals(lang)){//书籍语言(1 简体sc;2 繁体tc;3 英文eng)
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_1);
                } else if (MSG_LANG_TC.equals(lang)) {
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_2);
                }else{
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_3);
                }

                enyanBook.setNotShowDiscount(false);
                enyanBook.setOpensaleAt(current);

                enyanBook.setSalesVolume(0L);
                enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_0);
                enyanBook.setDiscountSingleType(Constant.BYTE_VALUE_0);
                enyanBook.setDiscountSingleDescription("");
                enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_0);

                enyanBook.setDiscountIsValid(Constant.BYTE_VALUE_0);
                enyanBook.setDiscountId(-1L);

                enyanBook.setBookCost(0);
                enyanBook.setBookHash("");

                BigDecimal priceHkd = new BigDecimal(price);
                enyanBook.setPrice(priceHkd);
                enyanBook.setPriceHkd(priceHkd);
                enyanBook.setPriceHKDDiscount(priceHkd);

                int priorityInt = 0;
                if (StringUtils.isNotBlank(priority)){
                    priorityInt  = Integer.parseInt(priority);
                }

                int vendorPercentInt = 0;
                if (StringUtils.isNotBlank(vendorPercent)){
                    vendorPercentInt = Integer.parseInt(vendorPercent);
                }

                enyanBook.setProductWeb(productWeb);
                enyanBook.setPublishedAt(publishedAt);
                enyanBook.setPublisherId(publisherId);
                enyanBook.setPublisherName(publicationName);
                enyanBook.setRecommendedCaption(recommendedCaption);
                enyanBook.setRecommendedOrder(priorityInt);
                enyanBook.setSalesModel(MSG_SALE_COMMON.equals(saleModel) ? 0 : 1);//0:正式销售；1：预售
                enyanBook.setShelfStatus(Constant.BYTE_VALUE_0);//销售状态 (0 已下架;1 已上架;2 待重审)
                enyanBook.setSalesVolume(0L);
                enyanBook.setShowPublisher(MSG_SHOW.equals(publisherShow) ? Constant.BYTE_VALUE_1 : Constant.BYTE_VALUE_0);
                enyanBook.setSize(size);
                enyanBook.setSpecialOffer(0);
                enyanBook.setTranslator(translator);
                enyanBook.setVendorPercent(vendorPercentInt);
                enyanBook.setWordCount(wordCount);
                enyanBook.setWordCountShow(wordCountShow);

                enyanBook.setStar("0");
                enyanBook.setStarCount(0);
                /*
                if (!"-".equals(enyanBook.getDiscountSingleIsValid())){
                    enyanBook.setdiscountsi
                    Money money = Money.hkds(priceHkd);
                    enyanBook.setPrice(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
                }else {
                    enyanBook.setPrice(enyanBook.getPriceHkd());
                }*/
                //enyanBook.setBookWeb(productWeb);


                this.enyanBookService.addRecordOnly(enyanBook);
            }
        }
    }

    @Override
    public void readEnd() {
        /*---------需要copy start-----------*/
        super.readEnd();
        /*---------需要copy end-----------*/
        System.out.println("ExcelReaderMultiFromBook read end, 总共有"+this.getTotalRows()+"条数据。");
        System.out.println(""+this.getSaveRowLists());//获取保存的行信息
    }

    public EnyanBookService getEnyanBookService() {
        return enyanBookService;
    }

    public void setEnyanBookService(EnyanBookService enyanBookService) {
        this.enyanBookService = enyanBookService;
    }
}
