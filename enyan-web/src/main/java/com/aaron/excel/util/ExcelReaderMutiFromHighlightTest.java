package com.aaron.excel.util;

import com.aaron.excel.ExcelRowReaderBatch;
import com.aaron.spring.model.EnyanReaderHighlights;
import com.aaron.spring.service.EnyanReaderHighlightService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 *
 * 自己书籍的推广
 * @Date: Created in  2020-05-14
 * @Author: <PERSON>
 * @Modified By:
 */
public class ExcelReaderMutiFromHighlightTest extends ExcelRowReaderBatch {
    private static final Logger logger = LoggerFactory.getLogger(ExcelReaderMutiFromHighlightTest.class);
    private EnyanReaderHighlightService enyanReaderHighlightService;
    @Override
    public void getRows(int sheetIndex, int curRow, List<String> rowlist) {
        /*
        if (curRow<this.getBatchReadBeginRow()){
            logger.debug("--------------getRows--------------");
            logger.debug(rowlist);
        }
        if (curRow == 0){
            //
            //canInsert = false;
        }*/
    }
    @Override
    public void batchRead(List<List<String>> rowlists) {
        /*---------需要copy start-----------*/
        if (this.isInterrupted()){
            return;
        }
        super.batchRead(rowlists);
        /*---------需要copy end-----------*/
        //logger.debug("--------------batchRead--------------");
        /*
        for (List<String> rowlist : rowlists){
            if (this.getTotalRows() == 2){

                this.interruptImport();//实现中断

                this.saveRow(rowlist);//保存特定行
            }
            logger.debug(rowlist);
        }*/

        long now = System.currentTimeMillis();
        for (List<String> rowList : rowlists){
            logger.debug(String.valueOf(rowList));
            String oldHref = rowList.get(0);
            String newHref = rowList.get(1);
            String bookId = rowList.get(2);

            if (StringUtils.isBlank(oldHref) || StringUtils.isBlank(newHref) || StringUtils.isBlank(bookId)){
                continue;
            }

            long bookIdLong = Long.parseLong(bookId);
            enyanReaderHighlightService.updateHighlightHref(bookIdLong,oldHref,newHref,now);
        }
    }

    @Override
    public void readEnd() {
        /*---------需要copy start-----------*/
        super.readEnd();
        /*---------需要copy end-----------*/
        logger.debug("ExcelReaderMutiFromHighlight read end, 总共有"+this.getTotalRows()+"条数据。");
        logger.debug(""+this.getSaveRowLists());//获取保存的行信息
    }

    public EnyanReaderHighlightService getEnyanReaderHighlightService() {
        return enyanReaderHighlightService;
    }

    public void setEnyanReaderHighlightService(EnyanReaderHighlightService enyanReaderHighlightService) {
        this.enyanReaderHighlightService = enyanReaderHighlightService;
    }

}
