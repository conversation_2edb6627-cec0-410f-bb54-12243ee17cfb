package com.aaron.excel.util;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.excel.ExcelRowReaderBatch;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanDailyWords;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.RedeemCodeNoteInfo;
import com.aaron.spring.service.EnyanDailyWordsService;
import com.aaron.spring.service.EnyanRedeemCodeService;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * 从教务导入数据
 * @Author: <PERSON>
 * @Date: Created in  2019-11-12
 * @Modified By:
 */
public class ExcelReaderMutiFromDailyWords extends ExcelRowReaderBatch {
    //private String currentDate = DateFormatUtils.format(new Date(),"yyyyMMddHHmmss");

    private EnyanDailyWordsService enyanDailyWordsService;
    private List<EnyanBook> bookList;
    @Override
    public void getRows(int sheetIndex, int curRow, List<String> rowlist) {
        /*
        if (curRow<this.getBatchReadBeginRow()){
            System.out.println("--------------getRows--------------");
            System.out.println(rowlist);
        }
        if (curRow == 0){
            //
            //canInsert = false;
        }*/
    }
    @Override
    public void batchRead(List<List<String>> rowlists) {
        /*---------需要copy start-----------*/
        if (this.isInterrupted()){
            return;
        }
        super.batchRead(rowlists);
        /*---------需要copy end-----------*/
        //System.out.println("--------------batchRead--------------");
        /*
        for (List<String> rowlist : rowlists){
            if (this.getTotalRows() == 2){

                this.interruptImport();//实现中断

                this.saveRow(rowlist);//保存特定行
            }
            System.out.println(rowlist);
        }*/
        for (List<String> rowList : rowlists){
            if (rowList.size() >= 5){
                //日期	书籍ID	书名	作者	金句内容
                String dateString = rowList.get(0);//日期
                String bookIdString = rowList.get(1);//书籍ID
                String bookTitle = rowList.get(2);//书名
                String author = rowList.get(3);//作者
                String content = rowList.get(4);//金句内容
                if (StringUtils.isBlank(bookIdString) || StringUtils.isBlank(dateString)){
                    continue;
                }

                EnyanDailyWords record = new EnyanDailyWords();
                record.setBookId(Long.parseLong(bookIdString));
                record.setBookAuthor(author);
                record.setBookTitle(bookTitle);
                record.setDataContent(content);
                record.setDataAt(Integer.parseInt(dateString));

                this.enyanDailyWordsService.updateRecordByDate(record);
            }
        }
    }

    @Override
    public void readEnd() {
        /*---------需要copy start-----------*/
        super.readEnd();
        /*---------需要copy end-----------*/
        System.out.println("ExcelReaderMutiFromDailyWords read end, 总共有"+this.getTotalRows()+"条数据。");
        System.out.println(""+this.getSaveRowLists());//获取保存的行信息
    }

    public EnyanDailyWordsService getEnyanDailyWordsService() {
        return enyanDailyWordsService;
    }

    public void setEnyanDailyWordsService(EnyanDailyWordsService enyanDailyWordsService) {
        this.enyanDailyWordsService = enyanDailyWordsService;
    }

    public List<EnyanBook> getBookList() {
        return bookList;
    }

    public void setBookList(List<EnyanBook> bookList) {
        this.bookList = bookList;
    }
}
