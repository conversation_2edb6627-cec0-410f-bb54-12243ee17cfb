package com.aaron.excel.util;

import com.aaron.excel.ExcelRowReaderBatch;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.RedeemCodeNoteInfo;
import com.aaron.spring.service.EnyanRedeemCodeService;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * 自己书籍的推广
 * @Author: <PERSON>
 * @Date: Created in  2020-05-14
 * @Modified By:
 */
public class ExcelReaderMutiFromOwn extends ExcelRowReaderBatch {
    private static final Logger logger = LoggerFactory.getLogger(ExcelReaderMutiFromOwn.class);
    private static final String dateFormat = "yyyy-MM-dd";
    private String currentDate = DateFormatUtils.format(new Date(),"yyyyMMddHHmmss");
    private EnyanRedeemCodeService enyanRedeemCodeService;
    private List<EnyanBook> bookList;

    @Override
    public void getRows(int sheetIndex, int curRow, List<String> rowlist) {
        /*
        if (curRow<this.getBatchReadBeginRow()){
            logger.debug("--------------getRows--------------");
            logger.debug(rowlist);
        }
        if (curRow == 0){
            //
            //canInsert = false;
        }*/
    }
    @Override
    public void batchRead(List<List<String>> rowlists) {
        /*---------需要copy start-----------*/
        if (this.isInterrupted()){
            return;
        }
        super.batchRead(rowlists);
        /*---------需要copy end-----------*/
        //logger.debug("--------------batchRead--------------");
        /*
        for (List<String> rowlist : rowlists){
            if (this.getTotalRows() == 2){

                this.interruptImport();//实现中断

                this.saveRow(rowlist);//保存特定行
            }
            logger.debug(rowlist);
        }*/
        for (List<String> rowList : rowlists){
            if (rowList.size() >= 6){
                String uuid = rowList.get(0);
                String name = rowList.get(1);
                String email = rowList.get(2);
                String course = rowList.get(3);
                String courseTime = rowList.get(4);
                String endTime = rowList.get(5);

                StringBuffer buffer = new StringBuffer();
                buffer.append("姓名：");
                buffer.append(name);
                buffer.append(";email：");
                buffer.append(email);
                buffer.append(";备注：");
                buffer.append(course);
                buffer.append(";时间：");
                buffer.append(courseTime);

                List<EnyanBook> newList = new ArrayList<>();
                for (EnyanBook enyanBook : this.getBookList()){
                    EnyanBook newBook = new EnyanBook();
                    newBook.setBookId(enyanBook.getBookId());
                    newBook.setBookTitle(enyanBook.getBookTitle());

                    newList.add(newBook);
                }

                RedeemCodeNoteInfo redeemCodeNoteInfo = new RedeemCodeNoteInfo();
                redeemCodeNoteInfo.setSource(EBookConstant.RedeemSource.PROMOTION_DESCRIPTION);
                redeemCodeNoteInfo.setType(EBookConstant.RedeemType.SPECIAL);
                redeemCodeNoteInfo.setOtherThings(buffer.toString());
                redeemCodeNoteInfo.setBooksToRedeemList(newList);

                EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
                enyanRedeemCode.setCode(uuid);
                enyanRedeemCode.setCreateTime(currentDate);
                enyanRedeemCode.setStatus(EBookConstant.RedeemStatus.DEFAULT);
                enyanRedeemCode.setType(EBookConstant.RedeemType.SPECIAL);
                enyanRedeemCode.setNote(JSON.toJSONString(redeemCodeNoteInfo));
                enyanRedeemCode.setUserEmail(Constant.DEFAULT_EXCEL_IMPORT);
                if (StringUtils.isNotBlank(endTime) && endTime.length() == 10){
                    try {
                        enyanRedeemCode.setEndAt(DateUtils.parseDate(endTime, dateFormat));
                    } catch (ParseException e) {
                        logger.error(e.getMessage());
                    }
                }

                this.enyanRedeemCodeService.addRecord(enyanRedeemCode);
            }
        }
    }

    @Override
    public void readEnd() {
        /*---------需要copy start-----------*/
        super.readEnd();
        /*---------需要copy end-----------*/
        logger.debug("ExcelReaderMutiFromOwn read end, 总共有"+this.getTotalRows()+"条数据。");
        logger.debug(""+this.getSaveRowLists());//获取保存的行信息
    }

    public EnyanRedeemCodeService getEnyanRedeemCodeService() {
        return enyanRedeemCodeService;
    }

    public void setEnyanRedeemCodeService(EnyanRedeemCodeService enyanRedeemCodeService) {
        this.enyanRedeemCodeService = enyanRedeemCodeService;
    }

    public List<EnyanBook> getBookList() {
        return bookList;
    }

    public void setBookList(List<EnyanBook> bookList) {
        this.bookList = bookList;
    }

}
