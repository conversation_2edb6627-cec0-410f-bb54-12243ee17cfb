package com.aaron.excel.util;

import com.aaron.excel.ExcelRowReaderBatch;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanReaderHighlights;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.RedeemCodeNoteInfo;
import com.aaron.spring.service.EnyanReaderHighlightService;
import com.aaron.spring.service.EnyanRedeemCodeService;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * 自己书籍的推广
 * @Date: Created in  2020-05-14
 * @Author: <PERSON>
 * @Modified By:
 */
public class ExcelReaderMutiFromHighlight extends ExcelRowReaderBatch {
    private static final Logger logger = LoggerFactory.getLogger(ExcelReaderMutiFromHighlight.class);
    private EnyanReaderHighlightService enyanReaderHighlightService;
    @Override
    public void getRows(int sheetIndex, int curRow, List<String> rowlist) {
        /*
        if (curRow<this.getBatchReadBeginRow()){
            logger.debug("--------------getRows--------------");
            logger.debug(rowlist);
        }
        if (curRow == 0){
            //
            //canInsert = false;
        }*/
    }
    @Override
    public void batchRead(List<List<String>> rowlists) {
        /*---------需要copy start-----------*/
        if (this.isInterrupted()){
            return;
        }
        super.batchRead(rowlists);
        /*---------需要copy end-----------*/
        //logger.debug("--------------batchRead--------------");
        /*
        for (List<String> rowlist : rowlists){
            if (this.getTotalRows() == 2){

                this.interruptImport();//实现中断

                this.saveRow(rowlist);//保存特定行
            }
            logger.debug(rowlist);
        }*/
        for (List<String> rowList : rowlists){
            if (rowList.size() >= 5){
                String uuid = rowList.get(0);
                String highlightID = rowList.get(1);
                String userID = rowList.get(2);
                String email = rowList.get(3);
                String publicationID = rowList.get(4);

                String bookID = rowList.get(5);
                String resourceIndex = rowList.get(6);
                String resourceHref = rowList.get(7);
                String resourceType = rowList.get(8);
                String resourceTitle = rowList.get(9);

                String location = rowList.get(10);
                String locatorText = rowList.get(11);
                String color = rowList.get(12);
                String annotation = rowList.get(13);
                String creationDate = rowList.get(14);

                String isDeleted = rowList.get(15);
                String updateTime = rowList.get(16);

                if ("1".equals(isDeleted)){
                    continue;
                }

                if (StringUtils.isBlank(userID) || StringUtils.isBlank(bookID)
                        || StringUtils.isBlank(resourceIndex) || StringUtils.isBlank(color)
                        || StringUtils.isBlank(creationDate) || StringUtils.isBlank(isDeleted)
                        || StringUtils.isBlank(updateTime)|| StringUtils.isBlank(email)){
                    continue;
                }

                EnyanReaderHighlights obj = new EnyanReaderHighlights();

                obj.setId(uuid);
                obj.setHighlightId(highlightID);
                obj.setUserId(Long.parseLong(userID));
                obj.setUserEmail(email);
                obj.setPublicationId(publicationID);

                obj.setBookId(Long.parseLong(bookID));
                obj.setResourceIndex(Integer.parseInt(resourceIndex));
                obj.setResourceHref(resourceHref);
                obj.setResourceType(resourceType);
                obj.setResourceTitle(resourceTitle);

                obj.setLocation(location);
                obj.setLocatorText(locatorText);
                obj.setColor(Integer.parseInt(color));
                obj.setAnnotation(annotation);
                obj.setCreationDate(Long.parseLong(creationDate));

                obj.setIsDeleted(Integer.parseInt(isDeleted));
                obj.setUpdateTime(Long.parseLong(updateTime));

                enyanReaderHighlightService.addHighlightOnly(obj);
            }
        }
    }

    @Override
    public void readEnd() {
        /*---------需要copy start-----------*/
        super.readEnd();
        /*---------需要copy end-----------*/
        logger.debug("ExcelReaderMutiFromHighlight read end, 总共有"+this.getTotalRows()+"条数据。");
        logger.debug(""+this.getSaveRowLists());//获取保存的行信息
    }

    public EnyanReaderHighlightService getEnyanReaderHighlightService() {
        return enyanReaderHighlightService;
    }

    public void setEnyanReaderHighlightService(EnyanReaderHighlightService enyanReaderHighlightService) {
        this.enyanReaderHighlightService = enyanReaderHighlightService;
    }

}
