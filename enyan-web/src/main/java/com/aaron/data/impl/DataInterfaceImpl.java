package com.aaron.data.impl;

import com.aaron.common.NameAndValue;
import com.aaron.data.DataInterface;
import com.aaron.data.constant.InterfaceDataConstant;
import com.aaron.redis.RedisInterface;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/8
 * @Modified By:
 */
@Slf4j
@Component(value = "dataInterface")
public class DataInterfaceImpl implements DataInterface {
    public static final Long REDIS_EXPIRE_SECONDS = 600L;//秒

    public static final Long REDIS_EXPIRE_SECONDS_DAY = 24*60*60L;//秒(1天)

    //@Autowired
    //private RedisInterface<String, String> redisInterface;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    //@Autowired
    //private DatDictionaryItemClient datDictionaryItemClient;


    @Override
    public EnyanPayRate getPayRateByDate(int rateDate) {
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_PAY_RATE,rateDate+"");
        if (null == object){
            return null;
        }
        if (object instanceof EnyanPayRate){
            return (EnyanPayRate) object;
        }
        return null;
    }

    @Override
    public void savePayRate(EnyanPayRate payRate) {
        if (null == payRate){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_PAY_RATE,payRate.getPayRateId()+"",payRate);
    }

    @Override
    public void delPayRate(int rateDate) {
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_PAY_RATE,rateDate+"");
    }

    @Override
    public EnyanBook getBookByID(Long bookId) {
        if (null == bookId){
            return null;
        }
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK,bookId+"");
        if (null == object){
            return null;
        }
        if (object instanceof EnyanBook){
            return (EnyanBook) object;
        }
        return null;
    }

    @Override
    public void saveBook(EnyanBook book) {
        if (null == book){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK,book.getBookId()+"",book);
    }

    @Override
    public void delBook(Long bookId) {
        if (null == bookId){
            return;
        }
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK,bookId+"");
    }

    @Override
    public void delAllBooks() {
        this.delObjectByCode(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK);
    }

    @Override
    public EnyanPublisher getPublisherByID(Long publisherId) {
        if (null == publisherId){
            return null;
        }
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_PUBLISHER,publisherId+"");
        if (null == object){
            return null;
        }
        if (object instanceof EnyanPublisher){
            return (EnyanPublisher) object;
        }
        return null;
    }

    @Override
    public void savePublisher(EnyanPublisher publisher) {
        if (null == publisher){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_PUBLISHER,publisher.getPublisherId()+"",publisher);
    }

    @Override
    public void delPublisher(Long publiserId) {
        if (null == publiserId){
            return;
        }
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_PUBLISHER,publiserId+"");
    }

    @Override
    public EnyanSpirit getSpiritByBookId(Long bookId) {
        if (null == bookId){
            return null;
        }
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_SPIRIT,bookId+"");
        if (null == object){
            return null;
        }
        if (object instanceof EnyanSpirit){
            return (EnyanSpirit) object;
        }
        return null;
    }

    @Override
    public void saveSpiritByBook(EnyanSpirit spirit) {
        if (null == spirit){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_SPIRIT,spirit.getBookId()+"",spirit);
    }

    @Override
    public void delSpiritByBookId(Long bookId) {
        if (null == bookId){
            return;
        }
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_SPIRIT,bookId+"");
    }

    @Override
    public void delAllSpirits() {
        this.delObjectByCode(InterfaceDataConstant.CodeType.CODE_TYPE_SPIRIT);
    }

    @Override
    public UserInfo getUserInfoByEmail(String email) {
        if (StringUtils.isBlank(email)){
            return null;
        }
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_USERINFO,email.toLowerCase());
        if (null == object){
            return null;
        }
        if (object instanceof UserInfo){
            return (UserInfo) object;
        }
        return null;
    }

    @Override
    public void saveUserInfo(UserInfo userInfo) {
        if (null == userInfo){
            return;
        }
        if (StringUtils.isBlank(userInfo.getUsername())){
            return ;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_USERINFO,userInfo.getUsername().toLowerCase(),userInfo);
    }

    @Override
    public void delUserInfo(String email) {
        if (StringUtils.isBlank(email)){
            return;
        }
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_USERINFO,email.toLowerCase());
    }

    @Override
    public EnyanCoupon getCouponByCode(String code) {
        if (StringUtils.isBlank(code)){
            return null;
        }
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_COUPON,code);
        if (null == object){
            return null;
        }
        if (object instanceof EnyanCoupon){
            return (EnyanCoupon) object;
        }
        return null;
    }

    @Override
    public void saveCoupon(EnyanCoupon coupon) {
        if (null == coupon){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_COUPON,coupon.getCouponCode(),coupon);
    }

    @Override
    public void delCoupon(String code) {
        if (StringUtils.isBlank(code)){
            return ;
        }
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_COUPON,code);
    }

    @Override
    public EnyanRent getRentByID(String code) {
        if (StringUtils.isBlank(code)){
            return null;
        }
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_RENT,code);
        if (null == object){
            return null;
        }
        if (object instanceof EnyanRent){
            return (EnyanRent) object;
        }
        return null;
    }

    @Override
    public void saveRent(EnyanRent rent) {
        if (null == rent){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_RENT,rent.getOrderNum(),rent,true);
    }

    @Override
    public void delRent(String code) {
        if (StringUtils.isBlank(code)){
            return ;
        }
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_RENT,code);
    }

    @Override
    public EnyanBookSet getBookSetByID(String setId){
        if (StringUtils.isBlank(setId)){
            return null;
        }
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_SET,setId);
        if (null == object){
            return null;
        }
        if (object instanceof EnyanBookSet){
            return (EnyanBookSet) object;
        }
        return null;
    }

    @Override
    public void saveBookSet(EnyanBookSet record) {
        if (null == record){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_SET,record.getSetId()+"",record,true);
    }

    @Override
    public void delBookSetById(String setId) {
        if (StringUtils.isBlank(setId)){
            return ;
        }
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_SET,setId);
    }

    @Override
    public EnyanBookList getBookListByID(String setId){
        if (StringUtils.isBlank(setId)){
            return null;
        }
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_LIST,setId);
        if (null == object){
            return null;
        }
        if (object instanceof EnyanBookSet){
            return (EnyanBookList) object;
        }
        return null;
    }

    @Override
    public void saveBookList(EnyanBookList record) {
        if (null == record){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_LIST,record.getSetId()+"",record,true);
    }

    @Override
    public void delBookListById(String setId) {
        if (StringUtils.isBlank(setId)){
            return ;
        }
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_LIST,setId);
    }

    @Override
    public List<NameAndValue> getBookNameAndValueList() {
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_NAME_AND_VALUE,"0");
        if (null == object){
            return null;
        }
        if (object instanceof List){
            return (List<NameAndValue>) object;
        }
        return null;
    }

    @Override
    public void saveBookNameAndValueList(List<NameAndValue> record) {
        if (null == record){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_NAME_AND_VALUE,"0",record,false);
    }

    @Override
    public void delBookNameAndValueList() {
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_NAME_AND_VALUE,"0");
    }

    @Override
    public List<EnyanBook> getBookRecommendBySetId(Long setId) {
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,setId + "setId");
        if (null == object){
            return null;
        }
        if (object instanceof List){
            return (List<EnyanBook>) object;
        }
        return null;
    }

    @Override
    public void saveBookRecommendBySetId(List<EnyanBook> bookList, Long setId) {
        if (null == bookList){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,setId + "setId",bookList,true, REDIS_EXPIRE_SECONDS_DAY);
    }

    @Override
    public void delBookRecommendBySetId(Long setId) {
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,setId + "setId");
    }

    @Override
    public List<EnyanBook> getBookRecommendTopByCategory(Long categoryId) {
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,categoryId + "topCategoryId");
        if (null == object){
            return null;
        }
        if (object instanceof List){
            return (List<EnyanBook>) object;
        }
        return null;
    }

    @Override
    public void saveBookRecommendTopByCategory(List<EnyanBook> bookList, Long categoryId) {
        if (null == bookList){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,categoryId + "topCategoryId",bookList,true, REDIS_EXPIRE_SECONDS_DAY);
    }

    @Override
    public void delBookRecommendTopByCategory(Long categoryId) {
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,categoryId + "topCategoryId");
    }

    @Override
    public List<EnyanBook> getBookRecommendRandomByCategory(Long categoryId) {
        Object object = this.getObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,categoryId + "randomCategoryId");
        if (null == object){
            return null;
        }
        if (object instanceof List){
            return (List<EnyanBook>) object;
        }
        return null;
    }

    @Override
    public void saveBookRecommendRandomByCategory(List<EnyanBook> bookList, Long categoryId) {
        if (null == bookList){
            return;
        }
        this.updateObject(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,categoryId + "randomCategoryId",bookList,true, REDIS_EXPIRE_SECONDS_DAY);
    }

    @Override
    public void delBookRecommendRandomByCategory(Long categoryId) {
        this.delObjectByCodeAndHashKey(InterfaceDataConstant.CodeType.CODE_TYPE_BOOK_RECOMMEND,categoryId + "randomCategoryId");
    }

    private Object getObjectByCodeAndHashKey(String code,String hashKey) {
        if (!Constant.IS_PRODUCT){ // 非生产环境，禁用redis
            return null;
        }
        try {
            return redisTemplate.opsForHash().get(code,hashKey);
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    private void delObjectByCodeAndHashKey(String code,String hashKey) {
        if (!Constant.IS_PRODUCT){ // 非生产环境，禁用redis
            return;
        }
        try {
            redisTemplate.opsForHash().delete(code,hashKey);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void delObjectByCode(String code) {
        if (!Constant.IS_PRODUCT){ // 非生产环境，禁用redis
            return;
        }
        try {
            redisTemplate.delete(code);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    /**
     *
     *
     * @param code
     * @param hashKey
     * @param value
     * @Date: 2018/5/29
     */
    private boolean updateObject(String code,String hashKey,Object value){
        if (!Constant.IS_PRODUCT){ // 非生产环境，禁用redis
            return false;
        }
        if (StringUtils.isBlank(code)||StringUtils.isBlank(hashKey)||null == value){
            return false;
        }
        try {
            redisTemplate.opsForHash().put(code,hashKey,value);
        }catch (Exception e){
            e.printStackTrace();
        }
        return true;
    }

    private boolean updateObject(String code,String hashKey,Object value, boolean autoExpire){
        return this.updateObject(code,hashKey,value,autoExpire,REDIS_EXPIRE_SECONDS);
    }

    private boolean updateObject(String code,String hashKey,Object value, boolean autoExpire, Long seconds){
        if (!Constant.IS_PRODUCT){ // 非生产环境，禁用redis
            return false;
        }
        if (StringUtils.isBlank(code)||StringUtils.isBlank(hashKey)||null == value){
            return false;
        }
        try {
            redisTemplate.opsForHash().put(code,hashKey,value);
            if (autoExpire){
                redisTemplate.expire(hashKey, seconds, TimeUnit.SECONDS);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return true;
    }

    /**
     * @param code
     * @param fatherCode
     *
     * 丛Dubbo中获取数据字典name值
     * @Date: 2017/11/8
     */
    private String getDictNameByCodeFromDubbo(String code, String fatherCode) {
        String name = null;
/*
        //String key = InterfaceDataConstant.CodeType.CODE_TYPE_DICT+"_"+fatherCode+"_"+code;
        //redisInterface.addString(key,name);

        DatDictionaryItemDTO dto = new DatDictionaryItemDTO();
        dto.setCode(code);
        dto.setFatherCode(fatherCode);

        Map map = datDictionaryItemClient.queryName(dto);

        //System.out.println("map:"+map);

        if (null == map){
            return null;
        }
        if (map.containsKey("data")){

            Object data = map.get("data");
            if (null == data){
                return null;
            }
            if (data instanceof String){
                return  data.toString();
            }
        }*/

        return null;
    }

    /**
     * @param code
     * @param codeType
     *
     * 返回key
     * @Date: 2017/11/8
     */
    private String getKey(String code, String codeType) {
        return codeType + "_" + code;
    }

    /**
     * @param code
     * @param fatherCode
     *
     * 返回Dict key
     * @Date: 2017/11/8
     */
    private String getDictKey(String code, String fatherCode) {
        return InterfaceDataConstant.CodeType.CODE_TYPE_PAY_RATE + "_" + fatherCode + "_" + code;
    }

    /**
     * @param key
     * @param value
     *
     * 更新Redis数据
     * @Date: 2017/11/8
     */
    private void saveOrUpdateRedis(String key, String value) {
        //redisInterface.addString(key, value);
        //redisInterface.expire(key, REDIS_EXPIRE_SECONDS);
    }

    /**
     * @param key
     * @param value
     *
     * 更新Cache数据
     * @Date: 2017/11/8
     */
    private void saveOrUpdateCache(String sessionId, String key, String value) {
        /*Map<Object, Object> session = dataCacheService.getSession(sessionId);
        if (null == session) {
            session = new HashMap<>();
        }
        //System.out.println("session:" + session);
        session.put(key, value);
        dataCacheService.updateSession(sessionId, session);*/

        //System.out.println("at last....." + this.getNameByCodeFromCache(sessionId, key));

    }

}
