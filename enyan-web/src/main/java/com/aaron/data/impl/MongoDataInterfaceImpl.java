package com.aaron.data.impl;

import com.aaron.data.MongoDataInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;


/**
 * http://www.mydlq.club/article/85/#8mongodb-聚合操作
 * https://www.tpisoftware.com/tpu/articleDetails/2517
 * https://docs.spring.io/spring-data/mongodb/docs/current/reference/html/#repositories.create-instances
 * https://www.baeldung.com/spring-data-mongodb-tutorial
 *
 *
 * MySQL 评测：
 * http://howto.philippkeller.com/2005/04/24/Tags-Database-schemas/
 * http://howto.philippkeller.com/2005/06/19/Tagsystems-performance-tests/
 *
 * Redis：
 * https://redis-cookbook.readthedocs.io/en/latest/t.html
 * https://www.cnblogs.com/kenshinobiy/p/7771619.html
 *
 * Thymeleaf：
 * https://www.thymeleaf.org/doc/tutorials/2.1/usingthymeleaf.html#iteration-basics
 * https://developer.aliyun.com/article/769977
 *
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2021/12/23
 * @Modified By:
 */
@Slf4j
@Component(value = "mongoDataInterface")
public class MongoDataInterfaceImpl implements MongoDataInterface {

	@Resource
	private MongoTemplate mongoTemplate;

	/**
	 * 新增
	 * <br>------------------------------<br>
	 * @param user
	 */
	public void insert(User user) {
		// TODO Auto-generated method stub
		mongoTemplate.insert(user);
	}
	/**
	 * 批量新增
	 * <br>------------------------------<br>
	 * @param users
	 */
	public void insertAll(List<User> users) {
		// TODO Auto-generated method stub
		mongoTemplate.insertAll(users);
	}
	/**
	 * 删除,按主键id, 如果主键的值为null,删除会失败
	 * <br>------------------------------<br>
	 * @param id
	 */
	public void deleteById(String id) {
		// TODO Auto-generated method stub
		User user = new User(id, null, 0);
		mongoTemplate.remove(user);
	}
	/**
	 * 按条件删除
	 * <br>------------------------------<br>
	 * @param criteriaUser
	 */
	public void delete(User criteriaUser) {
		// TODO Auto-generated method stub
		Criteria criteria = Criteria.where("age").gt(criteriaUser.getAge());;
		Query query = new Query(criteria);
		mongoTemplate.remove(query, User.class);
	}
	/**
	 * 删除全部
	 * <br>------------------------------<br>
	 */
	public void deleteAll() {
		// TODO Auto-generated method stub
		mongoTemplate.dropCollection(User.class);
	}
	/**
	 * 按主键修改,
	 * 如果文档中没有相关key 会新增 使用$set修改器
	 * <br>------------------------------<br>
	 * @param user
	 */
	public void updateById(User user) {
		// TODO Auto-generated method stub
		Criteria criteria = Criteria.where("id").is(user.getId());
		Query query = new Query(criteria);
		Update update = Update.update("age", user.getAge()).set("name", user.getName());
		mongoTemplate.updateFirst(query, update, User.class);
	}
	/**
	 * 修改多条
	 * <br>------------------------------<br>
	 * @param criteriaUser
	 * @param user
	 */
	public void update(User criteriaUser, User user) {
		// TODO Auto-generated method stub
		Criteria criteria = Criteria.where("age").gt(criteriaUser.getAge());;
		Query query = new Query(criteria);
		Update update = Update.update("name", user.getName()).set("age", user.getAge());
		mongoTemplate.updateMulti(query, update, User.class);
	}
	/**
	 * 根据主键查询
	 * <br>------------------------------<br>
	 * @param id
	 * @return
	 */
	public User findById(String id) {
		// TODO Auto-generated method stub
		return mongoTemplate.findById(id, User.class);
	}
	/**
	 * 查询全部
	 * <br>------------------------------<br>
	 * @return
	 */
	public List<User> findAll() {
		// TODO Auto-generated method stub
		return mongoTemplate.findAll(User.class);
	}
	/**
	 * 按条件查询, 分页
	 * <br>------------------------------<br>
	 * @param criteriaUser
	 * @param skip
	 * @param limit
	 * @return
	 */
	public List<User> find(User criteriaUser, int skip, int limit) {
		// TODO Auto-generated method stub
		Query query = getQuery(criteriaUser);
		query.skip(skip);
		query.limit(limit);
		return mongoTemplate.find(query, User.class);
	}
	/**
	 * 根据条件查询出来后 再去修改
	 * <br>------------------------------<br>
	 * @param criteriaUser  查询条件
	 * @param updateUser    修改的值对象
	 * @return
	 */
	public User findAndModify(User criteriaUser, User updateUser) {
		// TODO Auto-generated method stub
		Query query = getQuery(criteriaUser);
		Update update = Update.update("age", updateUser.getAge()).set("name", updateUser.getName());
		return mongoTemplate.findAndModify(query, update, User.class);
	}
	/**
	 * 查询出来后 删除
	 * <br>------------------------------<br>
	 * @param criteriaUser
	 * @return
	 */
	public User findAndRemove(User criteriaUser) {
		// TODO Auto-generated method stub
		Query query = getQuery(criteriaUser);
		return mongoTemplate.findAndRemove(query, User.class);
	}
	/**
	 * count
	 * <br>------------------------------<br>
	 * @param criteriaUser
	 * @return
	 */
	public long count(User criteriaUser) {
		// TODO Auto-generated method stub
		Query query = getQuery(criteriaUser);
		return mongoTemplate.count(query, User.class);
	}
	/**
	 *
	 * <br>------------------------------<br>
	 * @param criteriaUser
	 * @return
	 */
	private Query getQuery(User criteriaUser) {
		if (criteriaUser == null) {
			criteriaUser = new User();
		}
		Query query = new Query();
		if (criteriaUser.getId() != null) {
			Criteria criteria = Criteria.where("id").is(criteriaUser.getId());
			query.addCriteria(criteria);
		}
		if (criteriaUser.getAge() > 0) {
			Criteria criteria = Criteria.where("age").gt(criteriaUser.getAge());
			query.addCriteria(criteria);
		}
		if (criteriaUser.getName() != null) {
			Criteria criteria = Criteria.where("name").regex("^" + criteriaUser.getName());
			query.addCriteria(criteria);
		}
		return query;
	}
}

class User implements Serializable {
	/**
	 *
	 */
	private static final long serialVersionUID = 1L;
	/**
	 *
	 */
	private String id;
	private String name;
	private int age;

	public User() {
	}

	public User(String id, String name, int age) {
		super();
		this.id = id;
		this.name = name;
		this.age = age;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getAge() {
		return age;
	}

	public void setAge(int age) {
		this.age = age;
	}
}