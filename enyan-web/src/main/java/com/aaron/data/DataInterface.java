package com.aaron.data;

import com.aaron.common.NameAndValue;
import com.aaron.spring.model.*;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/8
 * @Modified By:
 */
public interface DataInterface {

    /**
     *
     * 根据rateTime 获取PayRate
     * @param rateDate
     * @Date: 2018/5/29
     */
    EnyanPayRate getPayRateByDate(int rateDate);
    /**
     *
     *
     * @param payRate
     * @Date: 2018/5/29
     */
    void savePayRate(EnyanPayRate payRate);
    /**
     *
     *
     * @param rateDate
     * @Date: 2018/5/29
     */
    void delPayRate(int rateDate);

    /**
     *
     *
     * @param bookId
     * @Date: 2018/5/29
     */
    EnyanBook getBookByID(Long bookId);
    /**
     *
     *
     * @param book
     * @Date: 2018/5/29
     */
    void saveBook(EnyanBook book);
    /**
     *
     *
     * @param bookId
     * @Date: 2018/5/29
     */
    void delBook(Long bookId);

    /**
     * <p>删除所有书籍的cache</p>
     * @return: void
     * @since : 2020-09-08
     */
    void delAllBooks();

    EnyanPublisher getPublisherByID(Long publisherId);

    void savePublisher(EnyanPublisher publisher);

    void delPublisher(Long publiserId);


    EnyanSpirit getSpiritByBookId(Long bookId);

    void saveSpiritByBook(EnyanSpirit spirit);

    void delSpiritByBookId(Long bookId);

    /**
     * <p>删除所有spirits</p>
     * @param
     * @return: void
     * @since : 11/16/20
     */
    void delAllSpirits();

    UserInfo getUserInfoByEmail(String email);

    void saveUserInfo(UserInfo userInfo);

    void delUserInfo(String email);

    EnyanCoupon getCouponByCode(String code);

    void saveCoupon(EnyanCoupon coupon);

    void delCoupon(String code);

    /**
     * <p>后续需要check email</p>
     * @param orderNum
     * @return com.aaron.spring.model.EnyanRent
     * @since : 2022/10/31
     **/
    EnyanRent getRentByID(String orderNum);

    void saveRent(EnyanRent rent);

    /**
     * <p>根据Rent的orderNum删除</p>
     * @param orderNum
     * @return void
     * @since : 2022/10/31
     **/
    void delRent(String orderNum);


    EnyanBookSet getBookSetByID(String setId);

    void saveBookSet(EnyanBookSet record);

    void delBookSetById(String setId);


    EnyanBookList getBookListByID(String setId);

    void saveBookList(EnyanBookList record);

    void delBookListById(String setId);


    List<NameAndValue> getBookNameAndValueList();

    void saveBookNameAndValueList(List<NameAndValue> bookList);

    void delBookNameAndValueList();


    List<EnyanBook> getBookRecommendBySetId(Long setId);

    void saveBookRecommendBySetId(List<EnyanBook> bookList, Long setId);

    void delBookRecommendBySetId(Long setId);


    List<EnyanBook> getBookRecommendTopByCategory(Long categoryId);

    void saveBookRecommendTopByCategory(List<EnyanBook> bookList, Long categoryId);

    void delBookRecommendTopByCategory(Long categoryId);


    List<EnyanBook> getBookRecommendRandomByCategory(Long categoryId);

    void saveBookRecommendRandomByCategory(List<EnyanBook> bookList, Long categoryId);

    void delBookRecommendRandomByCategory(Long categoryId);
}
