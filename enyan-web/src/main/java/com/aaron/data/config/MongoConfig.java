package com.aaron.data.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import java.util.Collection;
import java.util.Collections;

import static java.util.Collections.singletonList;

/**
 * @Author: Aaron <PERSON>
 * @Description:
 * @Date: Created in  2021/12/22
 * @Modified By:
 */
@Configuration
@EnableMongoRepositories(basePackages = "com.aaron.data.repository.mongo")
public class MongoConfig extends AbstractMongoClientConfiguration {

	@Value("${mongodb.host}")
	private String host;

	@Value("${mongodb.port}")
	private Integer port;

	@Value("${mongodb.database}")
	private String mongoDB;

	@Value("${mongodb.username}")
	private String userName;

	@Value("${mongodb.password}")
	private String password;

	@Override
	protected String getDatabaseName() {
		return mongoDB;
	}

	@Override
	protected void configureClientSettings(MongoClientSettings.Builder builder) {

		builder
				.credential(MongoCredential.createCredential(userName, mongoDB, password.toCharArray()))
				.applyToClusterSettings(settings  -> {
					settings.hosts(singletonList(new ServerAddress(host, port)));
				});
	}

//	@Override
//	public MongoClient mongoClient() {
//		ConnectionString connectionString = new ConnectionString("mongodb://localhost:27017/test");
//		MongoClientSettings mongoClientSettings = MongoClientSettings.builder()
//				.applyConnectionString(connectionString)
//				.build();
//
//		return MongoClients.create(mongoClientSettings);
//	}

//	@Override
//	public Collection getMappingBasePackages() {
//		return Collections.singleton("com.baeldung");
//	}
}
