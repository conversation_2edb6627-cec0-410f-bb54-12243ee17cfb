package com.aaron.data.repository.mongo;

import com.aaron.spring.model.EnyanReaderHighlights;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * 在接口中只需要声明符合规范的方法，即拥有对应的功能，如下表所示为方法中支持的关键字(官网地址
 * https://docs.spring.io/spring-data/jpa/docs/2.1.0.M3/reference/html/#repositories.custom-implementations)：
 *
 * Keyword	Sample	JPQL snippet
 * And	findByLastnameAndFirstname	… where x.lastname = ?1 and x.firstname = ?2
 * Or	findByLastnameOrFirstname	… where x.lastname = ?1 or x.firstname = ?2
 * Is,Equals	findByFirstnameIs,findByFirstnameEquals	… where x.firstname = ?1
 * Between	findByStartDateBetween	… where x.startDate between ?1 and ?2
 * LessThan	findByAgeLessThan	… where x.age < ?1
 * LessThanEqual	findByAgeLessThanEqual	… where x.age ⇐ ?1
 * GreaterThan	findByAgeGreaterThan	… where x.age > ?1
 * GreaterThanEqual	findByAgeGreaterThanEqual	… where x.age >= ?1
 * After	findByStartDateAfter	… where x.startDate > ?1
 * Before	findByStartDateBefore	… where x.startDate < ?1
 * IsNull	findByAgeIsNull	… where x.age is null
 * IsNotNull,NotNull	findByAge(Is)NotNull	… where x.age not null
 * Like	findByFirstnameLike	… where x.firstname like ?1
 * NotLike	findByFirstnameNotLike	… where x.firstname not like ?1
 * StartingWith	findByFirstnameStartingWith	… where x.firstname like ?1 (parameter bound with appended %)
 * EndingWith	findByFirstnameEndingWith	… where x.firstname like ?1 (parameter bound with prepended %)
 * Containing	findByFirstnameContaining	… where x.firstname like ?1 (parameter bound wrapped in %)
 * OrderBy	findByAgeOrderByLastnameDesc	… where x.age = ?1 order by x.lastname desc
 * Not	findByLastnameNot	… where x.lastname <> ?1
 * In	findByAgeIn(Collection ages)	… where x.age in ?1
 * NotIn	findByAgeNotIn(Collection age)	… where x.age not in ?1
 * TRUE	findByActiveTrue()	… where x.active = true
 * FALSE	findByActiveFalse()	… where x.active = false
 * IgnoreCase	findByFirstnameIgnoreCase	… where UPPER(x.firstame) = UPPER(?1)
 * @Date: Created in  2021/12/24
 * @Modified By:
 */
public interface HighlightRepository extends MongoRepository<EnyanReaderHighlights, String> {
	Page<EnyanReaderHighlights> findAllByType(Integer type,Pageable pageable);

	Page<EnyanReaderHighlights> findAllByBookIdAndType(Long bookId,Integer type,Pageable pageable);

	Page<EnyanReaderHighlights> findByUserEmail(String userEmail, Pageable pageable);
	Page<EnyanReaderHighlights> findByUserEmailAndUpdateTimeGreaterThanEqual(String userEmail, Long updateTime, Pageable pageable);
	Page<EnyanReaderHighlights> findByUserEmailAndTypeAndUpdateTimeGreaterThanEqual(String userEmail, Integer type,Long updateTime, Pageable pageable);

	Page<EnyanReaderHighlights> findByUserEmailAndUpdateTimeGreaterThan(String userEmail, Long updateTime, Pageable pageable);
	Page<EnyanReaderHighlights> findByUserEmailAndTypeAndUpdateTimeGreaterThan(String userEmail, Integer type,Long updateTime, Pageable pageable);
}
