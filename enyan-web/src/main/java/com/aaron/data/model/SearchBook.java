package com.aaron.data.model;

import com.aaron.spring.common.AaronHtmlUtils;
import com.aaron.spring.model.EnyanBook;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/3/21
 * @Modified By:
 */
@Document(indexName="books")
public class SearchBook {
	@Id
	private Long bookId;
	@Field(type = FieldType.Text)
	private String bookTitle;
	@Field(type = FieldType.Text)
	private String author;
	@Field(type = FieldType.Text)
	private String bookDescription;
	@Field(type = FieldType.Text)
	private String publisherName;
	public void initWithEBook(EnyanBook enyanBook){
		this.bookId = enyanBook.getBookId();
		this.bookTitle = enyanBook.getBookTitle();
		this.author = enyanBook.getAuthor();
		if (StringUtils.isNotBlank(enyanBook.getBookDescription())){
			this.bookDescription = AaronHtmlUtils.getHtmlText(enyanBook.getBookDescription());
		}
		this.publisherName = enyanBook.getPublisherName();
	}

	public Long getBookId() {
		return bookId;
	}

	public void setBookId(Long bookId) {
		this.bookId = bookId;
	}

	public String getBookTitle() {
		return bookTitle;
	}

	public void setBookTitle(String bookTitle) {
		this.bookTitle = bookTitle;
	}

	public String getAuthor() {
		return author;
	}

	public void setAuthor(String author) {
		this.author = author;
	}

	public String getBookDescription() {
		return bookDescription;
	}

	public void setBookDescription(String bookDescription) {
		this.bookDescription = bookDescription;
	}

	public String getPublisherName() {
		return publisherName;
	}

	public void setPublisherName(String publisherName) {
		this.publisherName = publisherName;
	}
}
