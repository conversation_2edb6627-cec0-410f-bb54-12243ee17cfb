function loadRecommendBooks(api, bookId) {
    const langData = window.langData
	// 获取推荐书籍列表
	getRecommendBooksList()
	async function getRecommendBooksList() {
		const listRes = await getData(`${api}/inBook`, {
			bookId,
		})
        const recommendList = listRes.result
		if (listRes.success) {
            $('#mayBeInterested').text(langData?.mayBeInterested || '您可能感兴趣');
            $('#mayBeInterested').css('display', 'block');
			$.each(recommendList, function(index, book) {
				let li = `
                      <li>
                        <div class="recommend-container">
                            <a class="product-thumb" href="/book-${book.bookId}#">
                              <img src=${book.imgUrlFull} alt="${book.name}" title="${book.name}">
                            </a>
                            <h3 class="recommendBookName" title="${book.name}"> <a href="/book-${book.bookId}#">${book.name}</a></h3>
                        </div>
                      </li>
                `
				$('#recommendList-wrapper').append(li)
			})
		}
	}
}

function loadBooksetDetailsList(api, booksetId, bookId) {
    const langData = window.langData
    // 获取书系详情列表
	getBooksetDetailsList()
	async function getBooksetDetailsList() {
		const listRes = await getData(`${api}/bookListBySet`, {
            page: 1,
			setId: booksetId,
			bookId,
		})
        let booksetListRes = listRes.result.filter(item => item.bookId !== bookId);
        const listLimitLength = 8;
        if (booksetListRes.length > listLimitLength) {
            booksetListRes = booksetListRes.slice(0, listLimitLength)
        }
        if (booksetListRes.length < 5) {
            $('#booksetList-wrapper').addClass('less-booksetList')
        }
		if (listRes.success && booksetListRes.length) {
            $('#bookSet').text(`${langData?.bookSet || '书系'}：`);
            $('#booksetName').text(booksetListRes[0].setName);
            $('#booksetName').css('visibility', 'visible');
            $('#booksetName').attr('href', `/store/bookset-${booksetListRes[0].setId}`);
			$.each(booksetListRes, function(index, book) {
				let li = `
                      <li class="${booksetListRes.length - 1 === index ? 'last-booksetItem' : "booksetItem"}">
                        <div class="booksetItem-container">
                            <a class="product-thumb" href="/book-${book.bookId}#">
                              <img src=${book.imgUrlFull} alt="${book.name}" title="${book.name}">
                            </a>
                            <h3 class="booksetItemBookName" title="${book.name}"> <a href="/book-${book.bookId}#">${book.name}</a></h3>
                        </div>
                      </li>
                `
				$('#booksetList-wrapper').append(li)
			})
            let fakeLength = listLimitLength - booksetListRes.length;
			$.each(Array(fakeLength).fill(), function() {
				let li = `<li class="fake-li-item"></li>`
				$('#booksetList-wrapper').append(li)
			})
		}
	}
}

function getData(url, data, email='') {
    const langData = window.langData
    return new Promise((resolve, reject) => {
        $.ajax({
            url,
            type: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'access_token': 'access_token',
                'email': email,
                'token': 'b3a63115-6ade-4658-88d7-7f5cab8c2e2f',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: JSON.stringify(data),
            success(res) {
                if (!res.success) { 
                    showMessage(langData?.errorParam || '参数错误')
                }
                resolve(res)
            },
            error(err) {
                reject(err)
            },
        })
    })
}

function showMessage(msg) {
    $('body').append(`<div id="msg-success">${msg}</div>`)
    $('#msg-success').css({ // 显示
      'position': 'fixed',
      'top': '105px',
      'left': '50%',
      'padding': '0 15px',
      'z-index': '9999',
      'transform': 'translateX(-50%)',
      'min-width': '162px',
      'height': '68px',
      'line-height': '68px',
      'background-color': '#fff',
      'box-shadow': '0px 0px 12px rgba(0, 0, 0, 0.2)',
      'border-radius': '15px',
      'font-size': '16px',
      'color': '#374250',
      'text-align': 'center',
    }).animate({
      'transform': 'translateX(-50%)',
    }, 1500, 'swing');

    $('#msg-success').animate({ // 隐藏
      'transform': 'translateX(-50%) translateY(-100%)',
    }, 1500, 'swing', function() {
      $(this).remove()
    });
}