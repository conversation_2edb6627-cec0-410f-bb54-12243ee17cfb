<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="orderDetails?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->


        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="sell.info"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.balance"/></a>
                </li>
                <li class="active"> <spring:message code="sell.info"/></li>
            </ul>
        </div>
        <!-- page heading end-->
        <sec:authentication var="user" property="principal" />
        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="sell.info"/>
                            <span class="tools pull-right">
                         </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <spring:message code="search.book.input" var="placeHolderBookInput"/>
                            <spring:message code="search.time.select" var="placeHolderTimeRange"/>
                            <spring:message code="search.by.range"/>：
                            <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}"/>
                            <script>
                                laydate.render({
                                    elem: '#rangeDate'
                                    ,format: 'yyyyMMdd'
                                    ,range: true
                                    ,lang:'<spring:message code="search.time.lang"/>'
                                });
                            </script>
                            <input type="hidden" name="searchType" value="0">
                            <form:input path="searchText" size="25" maxlength="40" cssClass="form-control" placeholder="${placeHolderBookInput}"/>
                            <form:select path="searchSelect" items="${publisherList}" itemLabel="name" itemValue="value"
                                         cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                         cssClass="form-control m-bot15"/>
                            <form:select path="searchOption"
                                         cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                         cssClass="form-control m-bot15">
                                <form:option value="6" ><spring:message code="search.by.time.desc"/></form:option>
                                <form:option value="5" ><spring:message code="search.by.book.name"/></form:option>
                                <%--<form:option value="1" >销量从高到低 ↓</form:option>
                                <form:option value="2" >销量从低到高 ↑</form:option>
                                <form:option value="3" >销售额从高到低 ↓</form:option>
                                <form:option value="4" >销售额从低到高 ↑</form:option>--%>
                            </form:select>
                            <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.select"/> </button>
                            <!--pagination start-->
                            <div class="">
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->


        <jsp:include page="../admin/footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#orderDetailsUIOrder").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
