<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <sec:authentication var="user" property="principal" />
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->
            <!--search end -->
        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="sell.balance.confirm"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.balance"/></a>
                </li>
                <li class="active"> <spring:message code="sell.balance.confirm"/></li>
            </ul>
        </div>
        <!-- page heading end-->
<form:form action="balanceToBank?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="sell.count"/>
                            <span class="tools pull-right">
                         </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">

                            <!--collapse start-->
                            <div class="panel-group " id="accordion">
                                <div class="panel">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseOne">
                                                <spring:message code="income.vendor"/>：HK$${balanceDetail.balanceBottom.incomeVendor}
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapseOne" class="panel-collapse collapse in">
                                        <div class="panel-body">
                                            <table class="table  table-hover general-table" >
                                                <thead>
                                                <tr>
                                                    <th><spring:message code="label.month"/> </th>
                                                    <th><spring:message code="order.sales"/> </th>
                                                    <th><spring:message code="income.sell"/></th>
                                                    <th><spring:message code="income.vendor"/> </th>
                                                </tr>
                                                </thead>
                                                <tbody>

                                                <c:forEach var="list" items="${balanceDetail.balanceList}">
                                                    <tr>
                                                        <td class="hidden-phone">${list.purchasedMonth}</td>
                                                        <td>
                                                                ${list.quantity}
                                                        </td>
                                                        <td>
                                                            HK$${list.incomeTotal}
                                                        </td>
                                                        <td>
                                                            HK$${list.incomeVendor}
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                                <%--<tr>
                                                    <td colspan="7" align="right">
                                                        <button class="btn btn-primary" type="submit" onclick="return cartValidate()"><spring:message code="account.settle"/> </button>
                                                    </td>
                                                </tr>--%>

                                                <tr>
                                                    <td colspan="4">
                                                        <br>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="hidden-phone">
                                                        <strong>小计：</strong></td>
                                                    <td>
                                                            ${balanceDetail.balanceBottom.quantity}
                                                    </td>
                                                    <td>
                                                        HK$${balanceDetail.balanceBottom.incomeTotal}
                                                    </td>
                                                    <td>
                                                        HK$${balanceDetail.balanceBottom.incomeVendor}
                                                    </td>
                                                </tr>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo">
                                                <spring:message code="fee.book.cost"/>：-HK$${balanceDetail.bookCostTotal}
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapseTwo" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <table class="table  table-hover general-table" >
                                                <thead>
                                                <tr>
                                                    <th><spring:message code="book.title"/> </th>
                                                    <th><spring:message code="fee.book.cost"/> </th>
                                                </tr>
                                                </thead>
                                                <tbody>

                                                <c:forEach var="list" items="${balanceDetail.bookCostList}">
                                                    <tr>
                                                        <td class="hidden-phone">${list.bookTitle}</td>
                                                        <td>
                                                            HK$${list.bookCost}
                                                        </td>
                                                    </tr>
                                                </c:forEach>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseThree">
                                                <spring:message code="fee.transfer"/>：-HK$${balanceDetail.transferFee}
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapseThree" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <spring:message code="fee.transfer"/>：-HK$${balanceDetail.transferFee}
                                        </div>
                                    </div>
                                </div>

                                <%--<div class="panel">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseFour">
                                                <spring:message code="fee.transfer"/>：HK$${balanceDetail.transferFee}
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapseFour" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <spring:message code="fee.transfer"/>：HK$${balanceDetail.transferFee}
                                        </div>
                                    </div>
                                </div>--%>

                            </div>
                            <!--collapse end-->

                            <div class="pull-left">
                                <div class="alert alert-success"><spring:message code="balance.fee"/>：HK$${balanceDetail.totalFee}</div>
                            </div>
                            <!--pagination start-->
                            <c:if test="${user.authorities[0].authority == 'ROLE_VENDOR'}">
                                <div class="pull-right">
                                    <a type="submit" class="btn btn-danger" onclick="javascript:history.go(-1)"><spring:message code="button.cancel"/> </a>
                                    <a type="submit" class="btn btn-primary" href="balanceBank"><spring:message code="button.confirm"/> </a>
                                </div>
                            </c:if>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->
</form:form>

        <jsp:include page="../admin/footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>
<script src="<c:url value='/statics/js/aaronBalance.js' />"></script>
<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#sellBalanceUI").addClass("active");
    })

    function cartValidate() {
        var checked = false;
        for (var i=0; i<checkMyBox.length; i++){
            if (checkMyBox[i].checked){
                checked = true;
            }
        }
        if (checked){
            return true;
        }
        alert("<spring:message code='error.book.select'/>");
        return false;
    }
</script>

</body>
</html>
