<%@ page import="com.aaron.spring.common.WebUtil" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%
    String baseServerPath = WebUtil.getBasePath();
    request.setAttribute("baseServerPath",baseServerPath);
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="books?${_csrf.parameterName}=${_csrf.token}" modelAttribute="book" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->
                <div class="form-group">
                    <spring:message code="search.book.input" var="placeHolderBookInput"/>
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="${placeHolderBookInput}"/>
                    <form:select path="searchOption"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15">
                        <form:option value="0" ><spring:message code="search.by.book.all"/></form:option>
                        <form:option value="1" ><spring:message code="search.by.book.released"/></form:option>
                        <form:option value="2" ><spring:message code="search.by.book.unreleased"/></form:option>
                    </form:select>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.button"/></button>
                </div>


            <!--search end-->

            <!--notification menu start -->

            <!--notification menu end -->

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="book.list"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.balance"/></a>
                </li>
                <li class="active"> <spring:message code="book.list"/></li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="book.list"/>
                            <span class="tools pull-right">
                            </span>
                        </header>
                        <div class="panel-body">
                            <table class="table  table-hover general-table">
                                <thead>
                                <tr>
                                    <th><spring:message code="book.title"/></th>
                                    <th><spring:message code="book.author"/></th>
                                    <th>URL</th>
                                    <th><spring:message code="book.status"/></th>
                                </tr>
                                </thead>
                                <tbody>

                                <c:forEach var="list" items="${list}">
                                <tr>
                                    <td>
                                            ${list.bookTitle}
                                    </td>
                                    <td>
                                        <c:set var="authors" value="${fn:split(list.author, '#')}" />
                                        <c:forEach var="author" items="${authors}">
                                             ${author}
                                        </c:forEach>
                                    </td>
                                    <td>
                                            ${baseServerPath}book-${list.bookId}#
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${list.shelfStatus==1}">
                                                <span class="label label-success label-mini"><spring:message code="search.by.book.released"/></span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="label label-danger label-mini"><spring:message code="search.by.book.unreleased"/></span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                                </c:forEach>

                                </tbody>
                            </table>
                            <c:if test="${empty list}">
                                <div class="">
                                    <spring:message code="data.empty"/>
                                </div>
                            </c:if>
                            <!--pagination start-->
                            <div class="">
                                ${pageLand}
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>

        </div>
        <!--body wrapper end-->


        <jsp:include page="../admin/footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#bookList").addClass("active");
    })

    function checkDel() {
        let msg = "您确定要删除本书吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
    function checkUp() {
        let msg = "您真的要上架本书吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
    function checkDown() {
        let msg = "您确定要下架本书吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
    function checkPresaleEmail(name) {
        let msg = "您确定要发《"+name+"》的预售邮件吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
