<%@ page import="java.util.Enumeration" %>
<%@ page import="org.springframework.security.web.csrf.DefaultCsrfToken" %>
<%@ page import="org.apache.commons.lang3.StringUtils" %>
<%@ page import="com.aaron.spring.common.WebUtil" %><%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="login.title"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="shop/header-css.jsp"/>
    <!-- Modernizr-->
    <script src="<c:url value='/js/modernizr.min.js' />"></script>
    <jsp:include page="shop/track-info.jsp"/>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="shop/header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <%
        String targetUrl = request.getParameter("targetUrl");
        if (StringUtils.isBlank(targetUrl)){
            targetUrl = request.getHeader("Referer");
            if (StringUtils.indexOf(targetUrl, "emailSuccess") != -1){
                targetUrl = "/index";
            }
        }
        if (StringUtils.isBlank(targetUrl)){
            targetUrl = "/index";
        }else{
			if (!targetUrl.contains(WebUtil.getBasePath())){
				targetUrl = "/index";
            }
        }
    %>
    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-2 padding-top-2x">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-10">


                    <h4 class="margin-bottom-1x text-center"><spring:message code="login.title"/> </h4>

                    <c:if test="${SPRING_SECURITY_LAST_EXCEPTION.message != null}">
                        <div class="col-12 padding-bottom-1x">
                            <h6 class="text-danger">
                                <c:choose>
                                    <c:when test="${SPRING_SECURITY_LAST_EXCEPTION.message == 'disabled'}">
                                        <spring:message code="account.disabled" arguments="<a href='/earbe?emailCode=${LAST_USERNAME}'/>;</a>" htmlEscape="false" argumentSeparator=";"/>
                                    </c:when>
                                    <c:when test="${SPRING_SECURITY_LAST_EXCEPTION.message == 'usernameNotFound'}">
                                        <spring:message code="error.email.notexist" />
                                    </c:when>
                                    <c:otherwise>
                                        <%--${SPRING_SECURITY_LAST_EXCEPTION.message}--%>
                                        <spring:message code="AbstractUserDetailsAuthenticationProvider.badCredentials"/>
                                    </c:otherwise>
                                </c:choose>
                            </h6>
                        </div>
                    </c:if>
                    <c:if test="${param.errorCode != null}">
                        <div class="col-12 padding-bottom-1x">
                            <h6 class="text-danger"><spring:message code='${param.errorCode}'/></h6>
                        </div>
                    </c:if>
                    <%--<c:if test="${param.error != null}">
                        <div class="col-12 padding-bottom-1x">
                            <h6 class="text-danger"><spring:message code="login.error"/> ${param.error}</h6>
                        </div>
                    </c:if>
                    <c:if test="${param.logout != null}">
                        <div class="form-group input-group">
                            <span class="label label-success label-mini"></span>
                        </div>
                    </c:if>--%>
                <form:form action="${loginUrl}?${_csrf.parameterName}=${_csrf.token}" modelAttribute="authUser" method="post" enctype="multipart/form-data" role="form" cssClass="login-box">
                    <div class="form-group input-group">
                        <input class="form-control" type="email" placeholder="<spring:message code='user.email'/>" id="username" name="email" required value="${authUser.email}"><span class="input-group-addon"><i class="icon-mail"></i></span>
                    </div>
                    <div class="form-group input-group">
                        <input class="form-control" type="password" placeholder="<spring:message code='passwd.label'/>" id="password" name="password" required value="${authUser.userPassword}"><span class="input-group-addon"><i class="icon-lock"></i></span>
                        <input type="hidden" name="${_csrf.parameterName}"  value="${_csrf.token}" />
                        <input type="hidden" name="targetUrl"  value="<%=targetUrl%>" />
                    </div>
                    <div class="d-flex flex-wrap justify-content-between padding-bottom-1x">
                        <label class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" name="remember-me" checked><span class="custom-control-indicator"></span><span class="custom-control-description"><spring:message code="login.stay"/> </span>
                        </label><a class="navi-link" href="pwdForget"><spring:message code="passwd.forget"/> ? </a>
                    </div>
                    <div class="text-center text-sm-right">
                        <a class="btn margin-bottom-none btn-outline-secondary" href="reg"><i class="icon-arrow-left"></i>&nbsp;<spring:message code="reg.title"/> </a>
                        <button class="btn btn-primary margin-bottom-none" type="submit"><spring:message code="login.now"/> </button>
                    </div>
                </form:form>
            </div>
        </div>
    </div>
    <%session.removeAttribute("SPRING_SECURITY_LAST_EXCEPTION");%>
    <!-- Site Footer start-->
    <jsp:include page="shop/footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="shop/service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<%--<script src="<c:url value='/js/vendor.min.js' />"></script>
<script src="<c:url value='/js/scripts.min.js' />"></script>
<script src="<c:url value='/statics/js/aaron-js.js' />"></script>--%>
<jsp:include page="shop/footer-js.jsp"/>
</body>
</html>