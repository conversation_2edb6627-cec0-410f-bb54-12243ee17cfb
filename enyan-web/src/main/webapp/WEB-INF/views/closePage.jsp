<%--
  User: Aaron
  Date: 2017/12/2
  Time: 下午10:44
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>关闭标签</title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Favicon Icons-->
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
</head>
<!-- Body-->
<body class="error-page">

<section>
    <div class="container ">

        <section class="error-wrapper text-center">
            <h2>操作成功</h2>
            <h3>
                <c:choose>
                    <c:when test="${not empty errorMsg}">
                        ${errorMsg}
                    </c:when>
                    <c:otherwise>
                        <c:choose>
                            <c:when test="${not empty exception.message}">
                                ${exception.message}
                            </c:when>
                            <c:otherwise>
                                <a href="#" onclick="javascript:close()"></a>关闭标签
                            </c:otherwise>
                        </c:choose>
                    </c:otherwise>
                </c:choose>
            </h3>
            <a class="back-btn" href="/"> 返回首页</a>
        </section>

    </div>
</section>
<script>
    function close() {
        //为了不出现提示框
        window.opener = null;
        //关闭窗口
        window.close();
    }
</script>
</body>
</html>