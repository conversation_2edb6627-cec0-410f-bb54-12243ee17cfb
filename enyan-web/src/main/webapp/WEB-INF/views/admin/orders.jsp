<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="orders?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->


                <div class="form-group">
                    <spring:message code="search.by.range"/>：
                    <spring:message code="search.time.select" var="placeHolderTimeRange"/>
                    <spring:message code="search.input" var="placeHolderInput"/>
                    <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}"/>
                    <script>
                        laydate.render({
                            elem: '#rangeDate'
                            ,format: 'yyyyMMdd'
                            ,range: true
                            ,lang:'<spring:message code="search.time.lang"/>'
                        });
                    </script>
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="${placeHolderInput}"/>
                    <form:select path="searchType"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15">
                        <form:option value="1" >用户名</form:option>
                        <form:option value="0" >订单号</form:option>
                    </form:select>
                    <form:select path="searchOption"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15">
                        <form:option value="" >全部</form:option>
                        <form:option value="0" >未付费</form:option>
                        <form:option value="1" >已付费</form:option>
                        <form:option value="2" >已取消</form:option>
                    </form:select>
                    <form:select path="searchSelect"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15">
                        <form:option value="" >全部</form:option>
                        <form:option value="0" >电子书单本</form:option>
                        <form:option value="1" >购买兑换码</form:option>
                        <form:option value="2" >兑换码兑换</form:option>
                        <form:option value="4" >电子书套装</form:option>
                    </form:select>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
                </div>


            <!--search end-->

            <!--notification menu start -->

            <!--notification menu end -->

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="order.list"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.balance"/></a>
                </li>
                <li class="active"> <spring:message code="order.list"/></li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="order.list"/>
                            <span class="tools pull-right">
                         </span>
                        </header>
                        <div class="panel-body">
                            <table class="table  table-hover general-table" >
                                <thead>
                                <tr>
                                    <th class="hidden-phone">订单号</th>
                                    <th>用户名</th>
                                    <th>金额</th>
                                    <th>订单时间</th>
                                    <th>订单状态</th>
                                    <th><spring:message code="income.from"/> </th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>

                                <c:forEach var="list" items="${list}">
                                <tr>
                                    <td class="hidden-phone">
                                        <a href="get-${list.orderId}" target="_blank">${list.orderNum}</a>
                                    </td>
                                    <td class="hidden-phone">${list.userEmail}</td>
                                    <td>
                                        HK$${list.orderTotal}
                                    </td>
                                    <td><fmt:formatDate value="${list.purchasedAt}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${list.isValid == 1}">
                                                <c:choose>
                                                    <c:when test="${list.isPaid == 1}">
                                                        <span class="label label-success label-mini">已付款</span>
                                                        <span class="label label-danger label-mini">${list.orderPayInfo.charge}</span>
                                                        <c:choose>
                                                            <c:when test="${list.orderType == 0}">
                                                                <span class="label label-danger label-mini"></span>
                                                            </c:when>
                                                            <c:when test="${list.orderType == 1}">
                                                                <span class="label label-danger label-mini">购买兑换码</span>
                                                            </c:when>
                                                            <c:when test="${list.orderType == 2}">
                                                                <span class="label label-danger label-mini"></span>
                                                            </c:when>
                                                            <c:when test="${list.orderType == 3}">
                                                                <span class="label label-danger label-mini"></span>
                                                            </c:when>
                                                            <c:when test="${list.orderType == 4}">
                                                                <span class="label label-danger label-mini">套装书</span>
                                                            </c:when>
                                                        </c:choose>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span class="label label-danger label-mini">未付款</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="label label-danger label-mini">已取消</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${list.orderFrom == 1}">
                                                App
                                            </c:when>
                                            <c:otherwise>
                                                web
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <a href="get-${list.orderId}" target="_blank">&nbsp;查看</a>&nbsp;
                                    </td>
                                </tr>
                                </c:forEach>

                                </tbody>
                            </table>
                            <c:if test="${empty list}">
                                <div class="">
                                    <spring:message code="data.empty"/>
                                </div>
                            </c:if>
                            <!--pagination start-->
                            <div class="">
                                ${pageLand}
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->


        <jsp:include page="footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#orderList").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
