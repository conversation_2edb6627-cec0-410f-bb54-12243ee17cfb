<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">
<sec:authentication var="user" property="principal" />
<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="list?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->
            <div class="form-group">
                <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="请输入"/>
                <form:select path="searchType"
                             cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                             cssClass="form-control m-bot15">
                    <form:option value="0" >全部</form:option>
                </form:select>
                <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
            </div>
            <!--search end-->

            <!--notification menu start -->

            <!--notification menu end -->

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="blog.list"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.web"/></a>
                </li>
                <li class="active"><spring:message code="blog.list"/></li>
            </ul>

        </div>
        <!-- page heading end-->
        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="blog.list"/>
                            <span class="tools pull-right">
                                <a href="addUI"><spring:message code="blog.add"/></a>
                            </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <table class="table  table-hover general-table" >
                                <thead>
                                <tr>
                                    <th>标题</th>
                                    <th>作者</th>
                                    <th>出版商</th>
                                    <th>类别</th>
                                    <th>推荐优先级</th>
                                    <th>阅读量</th>
                                    <th>点赞量</th>
                                    <th>隐藏</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>

                                <c:forEach var="list" items="${list}">
                                    <tr>
                                        <td>${list.blogTitle}</td>
                                        <td>${list.author}</td>
                                        <td><at:publisherName publisherId="${list.publisherId}" trimEmpty="true"/></td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${list.categoryId == 1}">
                                                    <span class="label label-danger label-mini"><spring:message code="reading.nav.1"/></span>
                                                </c:when>
                                                <c:when test="${list.categoryId == 2}">
                                                    <span class="label label-success label-mini"><spring:message code="reading.nav.2"/></span>
                                                </c:when>
                                                <c:when test="${list.categoryId == 3}">
                                                    <span class="label label-success label-mini"><spring:message code="reading.nav.3"/></span>
                                                </c:when>
                                                <c:when test="${list.categoryId == 4}">
                                                    <span class="label label-success label-mini"><spring:message code="reading.nav.4"/></span>
                                                </c:when>
                                                <c:when test="${list.categoryId == 5}">
                                                    <span class="label label-success label-mini"><spring:message code="reading.nav.5"/></span>
                                                </c:when>
                                                <c:when test="${list.categoryId == 6}">
                                                    <span class="label label-success label-mini"><spring:message code="reading.nav.6"/></span>
                                                </c:when>
                                                <c:when test="${list.categoryId == 7}">
                                                    <span class="label label-success label-mini"><spring:message code="reading.nav.7"/></span>
                                                </c:when>
                                                <c:when test="${list.categoryId == 8}">
                                                    <span class="label label-success label-mini"><spring:message code="reading.nav.8"/></span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="label label-danger label-mini"></span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>${list.recommendedOrder}</td>
                                        <td>${list.readCount}</td>
                                        <td>${list.likeCount}</td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${list.isDeleted == 0}">
                                                    <span class="label label-success label-mini">否</span>
                                                </c:when>
                                                <c:when test="${list.isDeleted == 1}">
                                                    <span class="label label-danger label-mini">是</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="label label-danger label-mini"></span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td><fmt:formatDate value="${list.createAt}" pattern="yyyy-MM-dd"/></td>
                                        <td>
                                            <a href="get-${list.blogId}" target="_blank">编辑</a>
                                        </td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                            <c:if test="${empty list}">
                                <div class="">
                                    <spring:message code="data.empty"/>
                                </div>
                            </c:if>
                            <!--pagination start-->
                            <div class="">
                                ${pageLand}
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->


        <jsp:include page="../admin/footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#blogList").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
