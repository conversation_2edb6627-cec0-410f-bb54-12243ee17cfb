<%@ page language="java" contentType="text/html; charset=utf-8"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<html> 
	<head>
		<title>产品列表</title>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">    
	<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
	<meta http-equiv="description" content="This is my page">
	<link href="<%=basePath %>css/admin.css" rel="stylesheet" type="text/css">
	<script type="text/javascript" src="<%=basePath %>js/jquery.js"></script>
	<script type="text/javascript" src="<%=basePath %>js/comm.js"></script>
	<script type="text/javascript" src="<%=basePath %>js/jsMsg.jsp"></script>
 <script type="text/javascript">	
function overList(tid)
{
	tid.style.backgroundColor="#E6F5FA";
}

function outList(tid)
{
	tid.style.backgroundColor="#ffffff";
}
function checkIsLiquidated()
{
	 if(!confirm('您确认已清算？'))   return   false;

} 	
</script>
  </head>  
  <body> 
  <div id="wrap">
  <form:form action="pageProducts.html" modelAttribute="productInfo">
  <table width="100%" border="0">
  <tr>
    <td>当前位置：网站管理 &gt;&gt; ${explan }</td>
  </tr>
</table>  
 <table cellspacing=0 cellpadding=0 width="100%" border=0>
        <tbody>
        <tr>
        	<td width=300>
        	
        	</td>
            <td  align=left>
              <div align="left">
                <form:input path="title" size="25"  maxlength="50"/>
                <%-- <jsp:useBean id="typeMap1" class="java.util.HashMap" scope="request"/>
				<c:set target="${typeMap1}" property="1" value="MM1"/> --%>
           		<form:select path="type" items="${productInfo.typeMap}" ></form:select>
                <input type="submit" value="搜索" onclick="disabled=true;this.form.submit();">
                 &nbsp;                
                </div>
           </td>
        <td align="left">        
         <a href="addProductUI.html" >添加产品</a>
	</td>
  </tr>
 </tbody>
</table>
      <Table width=100% border=0 cellpadding=2 cellspacing=1 bgcolor=#A4B6D7 align=center>
         <tr> 
       
         <th vertical-align="middle" width=15% class="headtitles" >产品名称
         <th vertical-align="middle" width=10% class="headtitles" >优先级
         <th vertical-align="middle" width=10% class="headtitles" >首页展示
         <th vertical-align="middle" width=10% class="headtitles" >更新时间
         <th vertical-align="middle" width=10% class="headtitles" >操作
         </tr>         
         <c:forEach var="list" items="${list}">         
         <tr bgcolor='#FFFFFF' onMouseOver="overList(this);" onMouseOut="outList(this)"> 
          <td  height="20" align='left' vertical-align='middle' >
         &nbsp;
              
         	<a href="get-${list.id}.html" >${list.title}</a>
         </td>
          <td height="20" align='left' vertical-align='middle' >
         &nbsp;${list.priority}
         </td> 
         <td height="20" align='left' vertical-align='middle' >
         &nbsp;
			<c:if test="${list.isIndex==1}">
				<font color="red"><b>是</b></font>
			</c:if>
			<c:if test="${list.isIndex==0}">
				否
			</c:if>
         </td> 
         <td height="20" align='left' vertical-align='middle' >
         &nbsp;${list.createtime}
         </td> 
         <td  height="20" align='center' vertical-align='middle' >
              
         	<a href="get-${list.id}.html" >&nbsp;编辑｜</a>&nbsp;
         	<a href="del-${list.id}.html" >｜删除 &nbsp;</a>
         </td>                   
         </tr> 
         </c:forEach>
      </table>      
      <table width="100%" border="0" align="center">
      <tr>              
       	<td><DIV class="pages_btns"><DIV class=pages>${pager.pageLand}</DIV></DIV></td>
      </tr>      
       </table>     
	</form:form>
</div>

<c:forEach var="entry" items="${myMap}">
  Key: <c:out value="${entry.key}"/>
  Value: <c:out value="${entry.value}"/>
</c:forEach>
  </body>
</html>

