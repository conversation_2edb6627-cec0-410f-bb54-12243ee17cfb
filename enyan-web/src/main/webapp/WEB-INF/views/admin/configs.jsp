<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <meta name="csrf-token" content="${_csrf.token}">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="configs?${_csrf.parameterName}=${_csrf.token}" modelAttribute="enyanConfig" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->


                <div class="form-group">
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control"/>
                    <form:select path="searchType" items="${enyanConfig.searchList}" itemLabel="name" itemValue="value"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15"/>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
                </div>


            <!--search end-->

            <!--notification menu start -->

            <!--notification menu end -->

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="config.list"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.web"/></a>
                </li>
                <li class="active"> <spring:message code="config.list"/></li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">
            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading custom-tab ">
                            <ul class="nav nav-tabs">
                                <li class="active">
                                    <a href="#tab1" data-toggle="tab">汇率</a>
                                </li>
                                <li class="">
                                    <a href="#tab2" data-toggle="tab">重新生成</a>
                                </li>
                                <li class="">
                                    <a href="#tab3" data-toggle="tab">广告设置</a>
                                </li>
                                <%--
                                <li class="">
                                    <a href="#tab4" data-toggle="tab">优惠码</a>
                                </li>--%>
                                <li class="">
                                    <a href="#tab5" data-toggle="tab">灵修信息</a>
                                </li>
                            </ul>
                        </header>
                        <div class="panel-body">
                            <div class="tab-content">
                                <div class="tab-pane active" id="tab1">
                                    <table class="table table-striped table-hover table-bordered" id="editable-sample">
                                        <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>名称</th>
                                            <th>值</th>
                                            <th>编辑</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="list" items="${moneyList}">
                                            <tr class="">
                                                <td>${list.configId}</td>
                                                <td>${list.configDescription}</td>
                                                <td>${list.configValue}</td>
                                                <td><a class="edit" href="javascript:;">编辑</a></td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="tab-pane" id="tab2">
                                    <table class="table table-striped table-hover table-bordered" id="editable-refresh">
                                        <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>名称</th>
                                            <th>值</th>
                                            <th>编辑</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="list" items="${refreshList}">
                                            <tr class="">
                                                <td>${list.configId}</td>
                                                <td>${list.configDescription}</td>
                                                <td>${list.configValue}</td>
                                                <td><a class="edit" href="javascript:;">编辑</a></td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="tab-pane" id="tab3">
                                    <table class="table table-striped table-hover table-bordered" id="editable-ad">
                                        <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>名称</th>
                                            <th>值</th>
                                            <th>编辑</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="list" items="${adList}">
                                            <tr class="">
                                                <td>${list.configId}</td>
                                                <td>${list.configDescription}</td>
                                                <td>${list.configValue}</td>
                                                <td><a class="edit" href="javascript:;">编辑</a></td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>

                                <%--
                                <div class="tab-pane" id="tab4">
                                    <table class="table table-striped table-hover table-bordered" id="editable-coupon">
                                        <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>名称</th>
                                            <th>值</th>
                                            <th>编辑</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="list" items="${couponList}">
                                            <tr class="">
                                                <td>${list.configId}</td>
                                                <td>${list.configDescription}</td>
                                                <td>${list.configValue}</td>
                                                <td><a class="edit" href="javascript:;">编辑</a></td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                                --%>
                                <div class="tab-pane" id="tab5">
                                    <table class="table table-striped table-hover table-bordered" id="editable-spirit">
                                        <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>名称</th>
                                            <th>值</th>
                                            <th>编辑</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="list" items="${spiritList}">
                                            <tr class="">
                                                <td>${list.configId}</td>
                                                <td>${list.configDescription}</td>
                                                <td>${list.configValue}</td>
                                                <td><a class="edit" href="javascript:;">编辑</a></td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </section>

                </div>
            </div>


        </div>
        <!--body wrapper end-->


        <jsp:include page="footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>
<script type="text/javascript" src="<c:url value='/statics/js/editable-enyan-config.js?v=201207' />"></script>
<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#configList").addClass("active");
    })


    jQuery(document).ready(function() {
        EditableTable.init();
    });

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
