<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/jquery-multi-select/css/multi-select.css' />" />

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-daterangepicker/daterangepicker-bs3.css' />" />
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-datepicker/css/datepicker-custom.css' />" />
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->

    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="save?${_csrf.parameterName}=${_csrf.token}" modelAttribute="record" method="post" enctype="multipart/form-data" role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <c:choose>
                        <c:when test="${record.dataId==null}">
                            <spring:message code="coupon.add"/>
                        </c:when>
                        <c:otherwise>
                            <spring:message code="coupon.edit"/>
                        </c:otherwise>
                    </c:choose>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active">
                        <c:choose>
                            <c:when test="${record.dataId==null}">
                                <spring:message code="coupon.add"/>
                            </c:when>
                            <c:otherwise>
                                <spring:message code="coupon.edit"/>
                            </c:otherwise>
                        </c:choose>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <c:choose>
                                    <c:when test="${record.dataId==null}">
                                        <spring:message code="coupon.add"/>
                                    </c:when>
                                    <c:otherwise>
                                        <spring:message code="coupon.edit"/>
                                    </c:otherwise>
                                </c:choose>
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="couponName">优惠码名称</label>
                                        <div class="col-sm-10">
                                            <form:input path="couponName" cssClass="form-control" id="couponName" maxlength="40"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="couponCode">优惠码</label>
                                        <div class="col-sm-10">
                                            <form:input path="couponCode" cssClass="form-control" id="couponCode" maxlength="40"/>
                                            （添加之后不可修改）
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="couponValue">优惠金额</label>
                                        <div class="col-sm-10">
                                            <form:input path="couponValue" cssClass="form-control" id="couponValue" maxlength="5" placeholder="整数数值"/>
                                            （添加之后不可修改）
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="minLimitValue">使用门槛</label>
                                        <div class="col-sm-10">
                                            <form:input path="minLimitValue" cssClass="form-control" id="minLimitValue" maxlength="5"/>
                                            （添加之后不可修改）
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="useMax">个人限用次数</label>
                                        <div class="col-sm-10">
                                            <form:input path="useMax" cssClass="form-control" id="useMax" maxlength="5"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="buyMax">总数</label>
                                        <div class="col-sm-10">
                                            <form:input path="buyMax" cssClass="form-control" id="buyMax" maxlength="5"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="couponStatus">状态</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="couponStatus" value="1" id="couponStatus"/>有效
                                            <form:radiobutton path="couponStatus" value="0"/>无效
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="couponType">使用范围</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="couponType" value="1" id="couponType"/>指定书籍
                                            <form:radiobutton path="couponType" value="0"/>全部书籍
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="couponStatus">起止日期</label>
                                        <div class="col-sm-10 input-group input-large">
                                            <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="请选择时间范围"/>
                                            <script>
                                                laydate.render({
                                                    elem: '#rangeDate'
                                                    ,format: 'yyyyMMdd'
                                                    ,range: true
                                                });
                                            </script>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" >关联书籍</label>
                                        <div class="col-sm-10 input-group input-large">
                                            <form:select path="bookIDs" items="${bookIDsList}" itemLabel="name" itemValue="value" cssClass="multi-select" multiple="true" id="my_multi_select3" />
                                            <form:hidden path="bookIDsOld"/>
                                        </div>
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <form:hidden path="dataId"/>
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">提  交</button>
                                    </div>
                                </form>

                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#couponList").addClass("active");
    })
</script>
<!--multi-select-->
<script type="text/javascript" src="<c:url value='/statics/js/jquery-multi-select/js/jquery.multi-select.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/jquery-multi-select/js/jquery.quicksearch.js' />"></script>
<script src="<c:url value='/statics/js/multi-select-init.js' />"></script>
</body>
</html>
