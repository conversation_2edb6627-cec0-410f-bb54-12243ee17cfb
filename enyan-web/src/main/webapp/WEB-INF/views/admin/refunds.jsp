<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">
<sec:authentication var="user" property="principal" />
<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="refunds?${_csrf.parameterName}=${_csrf.token}" modelAttribute="refund" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <div class="form-group">
                    <spring:message code="search.time.select" var="placeHolderTimeRange"/>
                    <spring:message code="search.input" var="placeHolderInput"/>
                    <spring:message code="search.label.order"/><spring:message code="search.by.range"/>：
                    <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}"/>
                    <script>
                        laydate.render({
                            elem: '#rangeDate'
                            ,format: 'yyyyMMdd'
                            ,range: true
                            ,lang:'<spring:message code="search.time.lang"/>'
                        });
                    </script>
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="${placeHolderInput}"/>
                    <form:select path="searchType" items="${refund.searchList}" itemLabel="name" itemValue="value"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15"/>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.select"/> </button>
                    <a class="btn btn-primary" href="/vendor/excelDownload-${refund.startDate}-${refund.endDate}-${refund.downloadType}?searchText=${refund.searchText}"><spring:message code="search.download"/> </a>
                </div>


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <spring:message code="refund.list"/>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active"> <spring:message code="refund.list"/></li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">
                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <spring:message code="refund.list"/>
                                <span class="tools pull-right">
                                    <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                        <a href="addRefundRedeemUI"><spring:message code="refund.add.redeem"/></a>
                                        <a href="addRefundUI"><spring:message code="refund.add"/></a>
                                    </c:if>
                         </span>
                            </header>
                            <div class="panel-body">
                                <table class="table  table-hover general-table">
                                    <thead>
                                    <tr>
                                        <th> <spring:message code="header.order.no"/></th>
                                        <th class="hidden-phone"><spring:message code="header.date.order"/></th>
                                        <th> <spring:message code="header.book.title"/></th>
                                        <th> <spring:message code="header.publisher"/></th>
                                        <th> <spring:message code="header.change.volume"/></th>
                                        <th> <spring:message code="header.change.sales"/></th>
                                        <th> <spring:message code="header.refund.reason"/></th>
                                        <th> <spring:message code="header.refund.date"/></th>
                                        <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                            <th>操作</th>
                                        </c:if>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    <c:forEach var="list" items="${list}">
                                        <tr>
                                            <td>
                                                ${list.orderNum}<a href="get-${list.refundId}"></a>
                                            </td>
                                            <td class="hidden-phone">
                                                <fmt:formatDate value="${list.purchasedAt}" pattern="yyyy-MM-dd"/>
                                            </td>
                                            <td>
                                                    ${list.bookTitle}
                                            </td>
                                            <td>
                                                    ${list.publisherName}
                                            </td>
                                            <td>
                                                    ${list.salesVolumeDecrease}
                                            </td>
                                            <td>
                                                    ${list.incomeTotalDecrease}
                                            </td>
                                            <td>
                                                    ${list.reasonContent}
                                            </td>
                                            <td class="hidden-phone">
                                                <fmt:formatDate value="${list.createTime}" pattern="yyyy-MM-dd hh:mm:ss"/>
                                            </td>
                                            <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                                <td>
                                                    <a href="del-${list.refundId}" onclick="javascript:return checkDel()">删除</a>
                                                </td>
                                            </c:if>
                                        </tr>
                                    </c:forEach>

                                    </tbody>
                                </table>
                                <c:if test="${empty list}">
                                    <div class="">
                                        <spring:message code="data.empty"/>
                                    </div>
                                </c:if>
                                <!--pagination start-->
                                <div class="">
                                        ${pageLand}
                                </div>
                                <!--pagination end-->
                            </div>
                        </section>

                    </div>
                </div>
            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#refundList").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
