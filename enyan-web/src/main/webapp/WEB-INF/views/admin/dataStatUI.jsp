<%@ page import="com.aaron.spring.common.Constant" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->

    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->


        </div>
        <!-- header section end-->
        <sec:authentication var="user" property="principal" />
        <!-- page heading start-->
        <div class="page-heading">
            <h3>
               下单/注册
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#">数据统计</a>
                </li>
                <li class="active">下单/注册</li>
                <span class="panel-heading pull-right ">
                                注册用户总数：${dto.userAllCount} <br>
                                激活用户总数：${dto.userActiveCount} <br>
                                下单用户总数（含免费）：${dto.userBuyCount}<br>
                    <a class="btn btn-primary" href="excelDownloadSubscribe">订阅email导出</a>
                            </span>
            </ul>

        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">

            <form:form action="list?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="search.time.select" var="placeHolderTimeRange"/>
                            <spring:message code="search.by.day"/>
                            <span class="tools pull-right">
                         </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <spring:message code="search.by.range"/>：
                            <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}" id="rangeDateDay"/>
                            <script>
                                laydate.render({
                                    elem: '#rangeDateDay'
                                    ,format: 'yyyyMMdd'
                                    ,range: true
                                });
                            </script>
                            <form:hidden path="searchType" value="0"/>

                            <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.select"/> </button>
                            <!--pagination start-->
                            <div class="">
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
            </form:form>
            <form:form action="list?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="search.by.month"/>
                            <span class="tools pull-right">
                         </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <spring:message code="search.by.range"/>：
                            <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}" id="rangeDateMonth"/>
                            <script>
                                laydate.render({
                                    elem: '#rangeDateMonth'
                                    ,type: 'month'
                                    ,format: 'yyyyMM'
                                    ,range: true
                                });
                            </script>
                            <form:hidden path="searchType" value="2"/>

                            <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.select"/> </button>
                            <!--pagination start-->
                            <div class="">
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
            </form:form>
            <form:form action="list?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="search.by.year"/>
                            <span class="tools pull-right">
                         </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <spring:message code="search.by.range"/>：
                            <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}" id="rangeDateYear"/>
                            <script>
                                laydate.render({
                                    elem: '#rangeDateYear'
                                    ,type: 'year'
                                    ,range: true
                                });
                            </script>
                            <form:hidden path="searchType" value="3"/>

                            <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.select"/> </button>
                            <!--pagination start-->
                            <div class="">
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
            </form:form>
        </div>
        <!--body wrapper end-->
        <jsp:include page="../admin/footer_show.jsp"></jsp:include>
    </div>
    <!-- main content end-->
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_stat").addClass("nav-active");
        $("#dataStatList").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
