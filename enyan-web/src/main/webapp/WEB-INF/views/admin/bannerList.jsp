<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="list?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->
                <div class="form-group">
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="请输入"/>
                    <form:select path="searchType"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15">
                        <form:option value="0" >全部</form:option>
                        <form:option value="1" >网站首页</form:option>
                        <form:option value="2" >读书会首页</form:option>
                    </form:select>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
                </div>
            <!--search end-->

            <!--notification menu start -->

            <!--notification menu end -->

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="banner.list"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.web"/></a>
                </li>
                <li class="active"><spring:message code="banner.list"/></li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">

            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="banner.list"/>
                            <span class="tools pull-right">
                                <a href="reset" target="_blank">重置Banner</a>
                                <a href="addUI">添加Banner</a>
                            </span>
                        </header>
                        <div class="panel-body">
                            <table class="table  table-hover general-table">
                                <thead>
                                <tr>
                                    <th> 描述</th>
                                    <th> 类型</th>
                                    <th>优先级</th>
                                    <th>截止日期</th>
                                    <th>图片</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>

                                <c:forEach var="list" items="${list}">
                                <tr vertical-align="middle" id="data${list.dataId}">
                                    <td vertical-align="center">
                                        <a href="get-${list.dataId}"></a>${list.dataName}
                                    </td>
                                    <td vertical-align="center">
                                        <c:if test="${list.dataIndexShow==1}">
                                            <span class="label label-success label-mini">网站首页</span>
                                        </c:if>
                                        <c:if test="${list.dataMiddleShow==1}">
                                            <span class="label label-danger label-mini">首页中部</span>
                                        </c:if>
                                        <c:if test="${list.dataReadShow==1}">
                                            <span class="label label-primary label-mini">读书会首页</span>
                                        </c:if>
                                    </td>
                                    <td vertical-align="center">
                                        <c:choose>
                                            <c:when test="${list.dataStatus == 1}">
                                                <span class="label label-success label-mini">启用</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="label label-danger label-mini">禁用</span>
                                            </c:otherwise>
                                        </c:choose>
                                            ${list.dataPriority}
                                    </td>
                                    <td>
                                        <fmt:formatDate value="${list.endAt}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                    </td>
                                    <td vertical-align="center">
                                        <a href="${list.dataToUrl}" target="_blank"><img src="${list.dataImgUrl}" width="300" height="150"></a>
                                    </td>

                                    <td vertical-align="center">
                                        <a href="get-${list.dataId}">&nbsp;编辑｜</a>&nbsp;
                                        <a onclick="javascript:return checkDel(${list.dataId})">｜ 删除 &nbsp;</a>
                                    </td>
                                </tr>
                                </c:forEach>

                                </tbody>
                            </table>
                            <c:if test="${empty list}">
                                <div class="">
                                    <spring:message code="data.empty"/>
                                </div>
                            </c:if>
                            <!--pagination start-->
                            <div class="">
                                ${pageLand}
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->


        <jsp:include page="footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#bannerList").addClass("active");
    })

    function checkDel(dataId) {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            solve(dataId)
            return true;
        }else{
            return false;
        }
    }

    function copyText(dom) {
        dom.select();
        //alert(dom.text());
        document.execCommand("Copy");
        alert("已复制至剪切板");
    }

    function solve(dataId){
        url="solve?${_csrf.parameterName}=${_csrf.token}";
        $.jpost(url, {
            "dataId":dataId
        }).then(res => {
            //console.log(res);
            if(res.success){
                //alert("sss")
                //$('#do'+dataId).html("")
                //$('#status'+dataId).html("<span class=\"label label-success label-mini\">已回复</span>")
                $('#data'+dataId).remove()
            }else{
                alert("发生错误！");
            }
        });
    }


</script>

</body>
</html>
