<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="list?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->
                <div class="form-group">
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="请输入"/>
                    <form:select path="searchType"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15">
                        <form:option value="1" >书籍ID</form:option>
                        <form:option value="0" >Email</form:option>
                    </form:select>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
                </div>
            <!--search end-->

            <!--notification menu start -->

            <!--notification menu end -->

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="comment.list"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.web"/></a>
                </li>
                <li class="active"><spring:message code="comment.list"/></li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">

            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="comment.list"/>
                            <span class="tools pull-right">

<%--                                <a href="addUI"><spring:message code="feedback.add"/></a>--%>
                         </span>
                        </header>
                        <div class="panel-body">
                            <table class="table  table-hover general-table">
                                <thead>
                                <tr>
                                    <th width="10">ID</th>
                                    <th width="200">书名</th>
                                    <th >标题/内容</th>
                                    <th width="100">Email</th>
                                    <th width="100">时间</th>
                                    <th width="10">星级</th>
                                    <th width="50">点赞数</th>
                                    <th width="50">子回复数</th>
                                    <th width="10">状态</th>
                                    <th width="50">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <c:forEach var="list" items="${list}">
                                <tr vertical-align="middle" id="data${list.dataId}">
                                    <td vertical-align="center">
                                        <a href="get-${list.dataId}"></a>
                                            ${list.dataId}
                                    </td>
                                    <td vertical-align="center">
                                        <span class="label label-danger label-mini">书籍ID：</span>${list.bookId}
                                        <br/>
                                        <a href="/book-${list.bookId}" target="_blank"><at:bookName bookId="${list.bookId}"/></a>
                                    </td>
                                    <td vertical-align="center">
                                        <span class="label label-danger label-mini">标题：</span>${list.title}
                                        <br/>
                                        <span class="label label-success label-mini">内容：</span>${list.content}
                                    </td>
                                    <td vertical-align="center">
                                        ${list.email}
                                    </td>
                                    <td vertical-align="center">
                                        <fmt:formatDate value="${list.createAt}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                    </td>
                                    <td vertical-align="center">
                                            ${list.star}
                                    </td>
                                    <td vertical-align="center">
                                            ${list.likeCount}
                                    </td>
                                    <td vertical-align="center">
                                        <c:if test="${list.parentId == 0}">
                                            ${list.commentCount}
                                        </c:if>
                                    </td>
                                    <td vertical-align="center" id="status${list.dataId}">
                                        <c:choose>
                                            <c:when test="${list.isDeleted==1}">
                                                <span class="label label-danger label-mini">已删除</span>
                                            </c:when>
                                            <c:when test="${list.isDeleted==0}">
                                                <span class="label label-success label-mini">正常</span>
                                            </c:when>
                                        </c:choose>
                                    </td>
                                    <td vertical-align="center" id="do${list.dataId}">
                                        <c:if test="${list.isDeleted==0}">
                                            <a onclick="javascript:return checkDel(${list.dataId})">删除</a>
                                        </c:if>
                                    </td>
                                </tr>
                                </c:forEach>

                                </tbody>
                            </table>
                            <c:if test="${empty list}">
                                <div class="">
                                    <spring:message code="data.empty"/>
                                </div>
                            </c:if>
                            <!--pagination start-->
                            <div class="">
                                ${pageLand}
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->
        <jsp:include page="footer_show.jsp"></jsp:include>
    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_operation").addClass("nav-active");
        $("#commentList").addClass("active");
    })

    function checkDel(dataId) {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            del(dataId)
            return true;
        }else{
            return false;
        }
    }
    function solve(dataId){
        url="solve?${_csrf.parameterName}=${_csrf.token}";
        $.jpost(url, {
            "dataId":dataId
        }).then(res => {
            //console.log(res);
            if(res.success){
                //alert("sss")
                $('#do'+dataId).html("")
                $('#status'+dataId).html("<span class=\"label label-danger label-mini\">已删除</span>")
                //$('#data'+dataId).remove()
            }else{
                alert("发生错误！");
            }
        });
    }
    function del(dataId){
        url="del?${_csrf.parameterName}=${_csrf.token}";
        $.jpost(url, {
            "dataId":dataId
        }).then(res => {
            //console.log(res);
            if(res.success){
                //alert("sss")
                $('#do'+dataId).html("")
                $('#status'+dataId).html("<span class=\"label label-danger label-mini\">已删除</span>")
                //$('#data'+dataId).remove()
            }else{
                alert("发生错误！");
            }
        });
    }
</script>

</body>
</html>
