<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="discounts?${_csrf.parameterName}=${_csrf.token}" modelAttribute="discount" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <div class="form-group">
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="请输入"/>
                    <form:select path="searchType" items="${discount.searchList}" itemLabel="name" itemValue="value"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15"/>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
                </div>


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <spring:message code="discount.list"/>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active"> <spring:message code="discount.list"/></li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <spring:message code="discount.list"/>
                                <span class="tools pull-right">
                            <a href="addDiscountUI"><spring:message code="discount.add"/></a>
                         </span>
                            </header>
                            <div class="panel-body">
                                <table class="table  table-hover general-table">
                                    <thead>
                                    <tr>
                                        <th> 折扣名称</th>
                                        <th class="hidden-phone">折扣类型</th>
                                        <th> 折扣详情</th>
                                        <th> 书籍数量</th>
                                        <th> 开始日期</th>
                                        <th> 结束日期</th>
                                        <th> 状态</th>
                                        <th> 折扣展示</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    <c:forEach var="list" items="${list}">
                                        <tr>
                                            <td>
                                                <a href="get-${list.discountId}">${list.discountTitle}</a>
                                            </td>
                                            <td class="hidden-phone">
                                                <c:choose>
                                                    <c:when test="${list.discountType==0}">
                                                        累计折扣
                                                    </c:when>
                                                    <c:when test="${list.discountType==1}">
                                                        满减折扣
                                                    </c:when>
                                                    <c:when test="${list.discountType==2}">
                                                        单个折扣
                                                    </c:when>
                                                </c:choose>
                                            </td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${list.discountType==0}">
                                                        累计${list.cumulatePackage}件，折扣${list.cumulateDiscount}%
                                                        <c:if test="${not empty list.cumulatePackageMuti}">
                                                            ；累计${list.cumulatePackageMuti}件，折扣${list.cumulateDiscountMuti}%
                                                        </c:if>
                                                    </c:when>
                                                    <c:when test="${list.discountType==1}">
                                                        满${list.fullBase}元，减${list.fullMinus}元
                                                    </c:when>
                                                    <c:when test="${list.discountType==2}">
                                                        单个折扣：${list.discountSingleValue}%
                                                    </c:when>
                                                </c:choose>
                                            </td>
                                            <td>
                                                    ${list.bookCount}
                                            </td>
                                            <td>
                                                <fmt:formatDate value="${list.startTime}" pattern="yyyy-MM-dd"/>
                                            </td>
                                            <td>
                                                <fmt:formatDate value="${list.endTime}" pattern="yyyy-MM-dd"/>
                                            </td>
                                            <td>
                                                <c:if test="${list.isValid==0}">
                                                    <span style="color: red;font-weight: bold">无效</span>
                                                </c:if>
                                                <c:if test="${list.isValid==1}">
                                                    <span style="color: #00EA00;font-weight: bold">有效</span>
                                                </c:if>
                                            </td>
                                            <td>
                                                <c:if test="${list.isShow==0}">
                                                    <span style="color: red;font-weight: bold">隐藏</span>
                                                </c:if>
                                                <c:if test="${list.isShow==1}">
                                                    <span style="color: #00EA00;font-weight: bold">展示</span>
                                                </c:if>
                                            </td>
                                            <td>
                                                <a href="set-${list.discountId}" target="_blank">&nbsp;关联书籍｜</a>&nbsp;
                                                <a href="get-${list.discountId}">设置｜</a>&nbsp;
                                                <a href="del-${list.discountId}" onclick="javascript:return checkDel()"></a>
                                            </td>
                                        </tr>
                                    </c:forEach>

                                    </tbody>
                                </table>
                                <c:if test="${empty list}">
                                    <div class="">
                                        <spring:message code="data.empty"/>
                                    </div>
                                </c:if>
                                <!--pagination start-->
                                <div class="">
                                        ${pageLand}
                                </div>
                                <!--pagination end-->
                            </div>
                        </section>

                    </div>
                </div>
            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#discountList").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
