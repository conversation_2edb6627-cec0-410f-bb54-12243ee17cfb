<%@ page language="java" contentType="text/html; charset=utf-8" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import="com.aaron.spring.common.*" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<html>
<head>
    <title>修改产品</title>
    <link href="<%=basePath %>css/admin.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="<%=basePath %>js/comm.js"></script>
    <script type="text/javascript" src="<%=basePath %>ckeditor/ckeditor.js"></script>
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
    <meta http-equiv="description" content="This is my page">

</head>

<body>
<Table width="90%" border="0" align="center" cellpadding="10" cellspacing="0" class="table1">
    <tr>
        <td colspan="3">
            <strong>
                修改产品
            </strong></td>
    </tr>

    <tr>
        <td colspan="3"
            <c:if test='${isSaveError }'>class="errormsg"</c:if> <c:if test='${isSaveSuccess }'>class="msg3"</c:if>>
            ${msg}
        </td>

    </tr>

    <form:form action="updateProduct.html" modelAttribute="productInfo" enctype="multipart/form-data">
        <form:hidden path="id"/>
        <tr class="td1">
            <td vertical-align=middle width=80 align=right><b>产品名称</b><span class="font2">*</span></td>
            <td width=800>
                <form:input path="title" cssClass="input2" size="100" maxlength="100"
                            onblur="changeStyle('titleMsg','msg1');" onfocus="changeStyle('titleMsg','msg2');"/>
                <div class="msg1" id="titleMsg">请填写全英文的产品名称</div>

            </td>
            <td>

            </td>
        </tr>
        <tr>
            <td vertical-align=middle width=80 align=right><b>设置优先级</b><span class="font2">*</span></td>
            <td width=200>
                <form:input path="priority" cssClass="input2" size="40" maxlength="4"
                            onblur="changeStyle('priorityMsg','msg1');" onfocus="changeStyle('priorityMsg','msg2');"/>
                <div class="msg1" id="priorityMsg">设置优先级</div>

            </td>
            <td>

            </td>
        </tr>

        <tr class="td1">
            <td vertical-align=middle width=80 align=right><b>首页展示</b><span class="font2">*</span></td>
            <td width=800>

                <form:radiobutton path="isIndex" value="0" label="否"/>
                <form:radiobutton path="isIndex" value="1" label="是"/>

                <div class="msg1" id="indexMsg"></div>

            </td>
            <td>

            </td>
        </tr>

        <tr>
            <td vertical-align=middle width=80 align=right><b>选择产品类型</b><span class="font2">*</span></td>
            <td width=200>
                <form:select path="type" items="${productInfo.typeMap}" cssClass="input2"
                             onblur="changeStyle('typeMsg','msg1');" onfocus="changeStyle('typeMsg','msg2');"/>
                <div class="msg1" id="typeMsg">选择产品类型</div>

            </td>
            <td>

            </td>
        </tr>
        <tr class="td1">
            <td vertical-align=middle align=right><b>上传图片</b></td>
            <td>
                <input type="file" name="upload" class="input2" size="40" id="upload">
                <div class="msg1" id="uploadMsg">请选择上传的图片</div>

            </td>
            <td>

            </td>
        </tr>
        <c:if test="${productInfo.havepic==1}">
            <tr class="td1">
                <td>
                </td>
                <td>
                    <img src="<%=basePath %>html/product_images/${productInfo.picfilename }"
                         width="<%=Constant.IMG_PAGE_PIC_WIDTH %>" height="<%=Constant.IMG_PAGE_PIC_WIDTH %>">
                </td>
                <td>
                </td>
            </tr>
        </c:if>

        <tr class="td1">
            <td vertical-align=middle width=80 align=right><b>重量</b><span class="font2">*</span></td>
            <td width=800>
                <form:input path="property3" cssClass="input2" size="100" maxlength="100"
                            onblur="changeStyle('property3Msg','msg1');" onfocus="changeStyle('property3Msg','msg2');"/>
                <div class="msg1" id="property3Msg">比如：7000KG</div>

            </td>
            <td>

            </td>
        </tr>

        <tr class="td1">
            <td vertical-align=middle width=80 align=right><b>描述1</b><span class="font2">*</span></td>
            <td width=800>
                <form:input path="property1" cssClass="input2" size="100" maxlength="100"
                            onblur="changeStyle('property1Msg','msg1');" onfocus="changeStyle('property1Msg','msg2');"/>
                <div class="msg1" id="property1Msg">请填写描述1</div>

            </td>
            <td>

            </td>
        </tr>

        <tr class="td1">
            <td vertical-align=middle width=80 align=right><b>描述2</b><span class="font2">*</span></td>
            <td width=800>
                <form:textarea path="property2" rows="10" cols="80" cssClass="input2"
                               onblur="changeStyle('property2Msg','msg1');"
                               onfocus="changeStyle('property2Msg','msg2');"/>
                <div class="msg1" id="property2Msg">请填写描述1</div>

            </td>
            <td>

            </td>
        </tr>

        <tr>
            <td vertical-align="middle" align="right"><strong>内容</strong></td>
            <td vertical-align="top">
                <form:textarea path="context" cssClass="ckeditor"/>
            </td>
            <td>

            </td>
        </tr>
        <tr class="td1">
            <td colspan="3" align=center>
                <input type="submit" value="修 改" onclick="disabled=true;this.form.submit();"/>

            </td>
        </tr>
    </form:form>
</table>

</body>
</html>
