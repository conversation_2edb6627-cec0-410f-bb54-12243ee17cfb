<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="shortcut icon" href="#" type="image/png">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <spring:message code="passwd.update"/>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.info"/></a>
                    </li>
                    <li class="active">
                        <spring:message code="passwd.update"/>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <spring:message code="passwd.update"/>
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form" action="updatePwdAction" method="post" class="form-horizontal adminex-form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="salt"><spring:message code="passwdUpdate.input1"/> </label>
                                        <div class="col-sm-10">
                                            <input class="form-control" type="password" name="salt" class="form-control" id="salt" maxlength="50"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="userPassword"><spring:message code="passwdUpdate.input2"/></label>
                                        <div class="col-sm-10">
                                            <input class="form-control" type="password" name="userPassword" class="form-control" id="userPassword" maxlength="50"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="userPasswordAgain"><spring:message code="passwdUpdate.input3"/></label>
                                        <div class="col-sm-10">
                                            <input class="form-control" type="password" name="userPasswordAgain" class="form-control" id="userPasswordAgain" maxlength="50"/>
                                        </div>
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <input type="hidden" name="${_csrf.parameterName}"  value="${_csrf.token}" />
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="button.submit"/></button>
                                    </div>
                                </form>

                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_info").addClass("nav-active");
        $("#passwd").addClass("active");
    })
</script>

</body>
</html>
