<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="passwd.reset"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="恩道电子书">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="shop/header-css.jsp"/>
    <!-- Modernizr-->
    <script src="<c:url value='/js/modernizr.min.js' />"></script>
    <jsp:include page="shop/track-info.jsp"/>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="shop/header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->

    <div class="container padding-bottom-3x mb-2 padding-top-2x">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-10">
                <form:form action="pwdForgetUpdateAction?${_csrf.parameterName}=${_csrf.token}" modelAttribute="authUser" method="post" enctype="multipart/form-data" role="form" cssClass="login-box">
                    <h4 class="margin-bottom-1x text-center"><spring:message code="passwd.reset"/> </h4>
                    <c:if test='${isSaveError }'>
                        <div class="col-12 padding-bottom-1x">
                            <h6 class="text-danger">${msg}</h6>
                        </div>
                    </c:if>


                    <div class="form-group input-group">
                        <input class="form-control" type="password" placeholder="<spring:message code='passwd.label'/>：<spring:message code='placehoder.passwd'/>" name="userPassword" required value="${authUser.userPassword}">
                        <span class="input-group-addon"><i class="icon-lock"></i></span>
                    </div>
                    <div class="form-group input-group">
                        <input class="form-control" type="password" placeholder="<spring:message code='passwd.confirm'/>" name="userPasswordAgain" required value="${authUser.userPasswordAgain}"><span class="input-group-addon"><i class="icon-lock"></i></span>
                    </div>
                    <div class="text-center text-sm-right">
                        <%--<a class="btn margin-bottom-none btn-outline-secondary" href="#"><i class="icon-arrow-left"></i>&nbsp;去登录</a>--%>
                        <input type="hidden" name="${_csrf.parameterName}"  value="${_csrf.token}" />
                        <input type="hidden" name="salt"  value="${emailCode}" />
                        <button class="btn btn-primary margin-bottom-none" type="submit"><spring:message code="passwd.reset"/> </button>
                        <!-- <a class="btn btn-outline-primary btn-sm" href="#"></a> -->
                    </div>
                </form:form>
            </div>
        </div>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="shop/footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<%--<script src="<c:url value='/js/vendor.min.js' />"></script>
<script src="<c:url value='/js/scripts.min.js' />"></script>
<script src="<c:url value='/statics/js/aaron-js.js' />"></script>--%>

</body>
</html>