
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<jsp:include page="indexWechat.jsp"/>
<%--
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <title>阅读器下载-<spring:message code="shop.title"/></title>
    <jsp:include page="track-info.jsp"/>
    <script type="text/javascript">

        // 获取终端的相关信息，根据终端辨别下载地址
        var Terminal = {
            // 辨别移动终端类型
            platform : function(){
                var u = navigator.userAgent, app = navigator.appVersion;
                return {
                    //IE内核
                    windows: u.indexOf('Windows') > -1,
                    //苹果、谷歌内核
                    webKit: u.indexOf('AppleWebKit') > -1,
                    //是否为移动终端
                    mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/),
                    //ios终端
                    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
                    // android终端
                    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,
                    // 是否为iPhone
                    iPhone: u.indexOf('iPhone') > -1 ,
                    // 是否iPad
                    iPad: u.indexOf('iPad') > -1,
                    //是否为mac系统
                    Mac: u.indexOf('Macintosh') > -1,
                    //是否web应该程序
                    webApp: u.indexOf('Safari') == -1,
                    //是否微信
                    weixin: u.indexOf('MicroMessenger') > -1,
                };
            }(),
        }

        // 根据不同的终端，跳转到不同的地址
        var theUrl = 'index-Reader';
        if(Terminal.platform.weixin){
            theUrl = '#';
        }else if(Terminal.platform.android){
            theUrl = 'https://inspiratas3.blob.core.windows.net/ebook/Inspirata_eBooks.apk';
        }else if(Terminal.platform.iPhone){
            theUrl = 'https://apps.apple.com/us/app/恩道电子书-inspirata-ebooks/id1463909109';
        }else if(Terminal.platform.iPad){
            theUrl = 'https://apps.apple.com/us/app/恩道电子书-inspirata-ebooks/id1463909109';
        }else if(Terminal.platform.windows){
            //theUrl = 'https://adedownload.adobe.com/pub/adobe/digitaleditions/ADE_4.5_Installer.exe';
        }else if(Terminal.platform.Mac){
            //theUrl = 'https://adedownload.adobe.com/pub/adobe/digitaleditions/ADE_4.5_Installer.dmg';
        }else {
            //theUrl = 'https://ebook.endao.co/dl/Adobe_Digital_Editions.apk';
        }
        location.href = theUrl;
    </script>
</head>
<body>

</body>
</html>--%>
