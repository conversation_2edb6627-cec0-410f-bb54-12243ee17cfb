<%--
  User: Aaron
  Date: 2017/12/9
  Time: 上午10:41
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code='label.cart'/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <!-- Modernizr-->
    <script src="<c:url value='/js/modernizr.min.js' />"></script>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1><spring:message code='label.cart'/></h1>
            </div>
            <div class="column">
                <ul class="breadcrumbs">
                    <li><a href="/index">首页</a>
                    </li>
                    <li class="separator">&nbsp;</li>
                    <li>购物车</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-1">
        <!-- 购物车-->
        <div class="table-responsive shopping-cart">
            <c:forEach var="discountList" items="${BUYER_CART.cartDiscountInfoList}" varStatus="status">
                <table class="table">
                    <thead>
                    <tr>
                        <th>${discountList.discountTitle}</th>
                        <th class="text-center">型号</th>
                        <th class="text-center">单价</th>
                        <th class="text-center">数量</th>
                        <th class="text-center">小计</th>
                        <th class="text-center">
                            <a class="btn btn-sm btn-outline-danger" href="clearCart">
                                <c:if test="${status.index == 0}">
                                    清空购物车
                                </c:if>
                            </a>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                <c:forEach var="cartLine" items="${discountList.productInfoList}">
                    <tr>
                        <td>
                            <div class="product-item"><a class="product-thumb" href="book-${cartLine.productInfo.code}"><img src="${cartLine.productInfo.productCover}" alt="Product"></a>
                                <div class="product-info">
                                    <br />
                                    <h4 class="product-title">
                                        <a href="book-${cartLine.productInfo.code}" target="_blank">
                                            <c:if test="${cartLine.productInfo.salesModel == 1}">
                                                <spring:message code="book.title.presale"/>
                                            </c:if>
                                                ${cartLine.productInfo.name}
                                        </a>
                                </h4><span><em>作 者:</em>${cartLine.productInfo.producer}</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center text-lg text-medium">eIP021</td>
                        <td class="text-center text-lg text-medium">
                            <c:choose>
                                <c:when test="${cartLine.productInfo.discountSingleIsValid}">
                                    <c:choose>
                                        <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                            <del>$ ${cartLine.productInfo.priceUsd}</del>$ ${cartLine.productInfo.priceUSDDiscount}
                                        </c:when>
                                        <c:otherwise>
                                            <del>¥${cartLine.productInfo.priceCny}</del>¥${cartLine.productInfo.priceCnyDiscount}
                                        </c:otherwise>
                                    </c:choose>
                                </c:when>
                                <c:otherwise>
                                    <c:choose>
                                        <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                            $ ${cartLine.productInfo.priceUsd}
                                        </c:when>
                                        <c:otherwise>
                                            ¥${cartLine.productInfo.priceCny}
                                        </c:otherwise>
                                    </c:choose>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td class="text-center text-lg text-medium">1</td>
                        <td class="text-center text-lg text-medium">
                            <c:choose>
                                <c:when test="${cartLine.productInfo.discountSingleIsValid}">
                                    <c:choose>
                                        <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                            <del>$ ${cartLine.productInfo.priceUsd}</del>$ ${cartLine.productInfo.priceUSDDiscount}
                                        </c:when>
                                        <c:otherwise>
                                            <del>¥${cartLine.productInfo.priceCny}</del>¥${cartLine.productInfo.priceCnyDiscount}
                                        </c:otherwise>
                                    </c:choose>
                                </c:when>
                                <c:otherwise>
                                    <c:choose>
                                        <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                            $ ${cartLine.productInfo.priceUsd}
                                        </c:when>
                                        <c:otherwise>
                                            ¥${cartLine.productInfo.priceCny}
                                        </c:otherwise>
                                    </c:choose>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td class="text-center"><a class="remove-from-cart" href="delCart-${cartLine.productInfo.code}" data-toggle="tooltip" title="删除" onclick="javascript:return checkDel()"><i class="icon-cross"></i></a></td>
                    </tr>
                </c:forEach>
                    </tbody>
                </table>
            </c:forEach>
        </div>
        <div class="shopping-cart-footer">
            <!-- <div class="column">
              <form class="coupon-form" method="post">
                <input class="form-control form-control-sm" type="text" placeholder="" required>
                <button class="btn btn-outline-primary btn-sm" type="submit"></button>
              </form>
            </div> -->
            <div class="column text-lg">总计： <span class="text-medium">
                <c:choose>
                    <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                        $  ${BUYER_CART.amountUsd}
                    </c:when>
                    <c:otherwise>
                        ¥  ${BUYER_CART.amountCny}
                    </c:otherwise>
                </c:choose>
            </span><br />应付金额： <span class="text-medium">
                <c:choose>
                    <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                        $  ${BUYER_CART.amountUsd}
                    </c:when>
                    <c:otherwise>
                        ¥  ${BUYER_CART.amountCny}
                    </c:otherwise>
                </c:choose>
            </span></div>
        </div>
        <div class="shopping-cart-footer">
            <div class="column"><a class="btn btn-outline-secondary" href="category-0-grid-0-0-0-0-0-0-0-0" data-toast-position="topRight"><i class="icon-arrow-left"></i>&nbsp;返回继续购物</a>
                <c:choose>
                    <c:when test="${BUYER_CART.amountCny >0}">
                        <a class="btn btn-primary" href="toCheckout">立即支付</a>
                    </c:when>
                    <c:otherwise>
                        <a class="btn btn-primary" href="#">立即支付</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </div>
    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<script src="<c:url value='/js/vendor.min.js' />"></script>
<script src="<c:url value='/js/scripts.min.js' />"></script>
<script src="<c:url value='/statics/js/aaronCartToOrder.js' />"></script>
<script>
    function checkDel() {
        var msg = "您真的确定要从购物车删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>
</body>
</html>
