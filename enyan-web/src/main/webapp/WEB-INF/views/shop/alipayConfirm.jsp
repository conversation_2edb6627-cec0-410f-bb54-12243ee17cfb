<%--
  User: Aaron
  Date: 2017/12/14
  Time: 上午8:58
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>
        <c:choose>
            <c:when test="${'HK' == payType}">
                <spring:message code="pay.alipay.hk"/>
            </c:when>
            <c:otherwise>
                <spring:message code="pay.alipay"/>
            </c:otherwise>
        </c:choose>
    </title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <!-- Modernizr-->
    <jsp:include page="track-info.jsp"/>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- 订单页面 -->
    <div class="container padding-bottom-3x mb-1" style="width: 1070px;">
        <div class="row mt-3">
            <div class="col-sm-12 col-lg-12">
                <!-- 订单信息 -->
                <ol class="list-unstyled" style="line-height:34px;">
                    <li><strong><spring:message code="order.num"/>: </strong><span>${orderMain.orderNum}</span></li>
                    <li><strong><spring:message code="pay.amount"/>: </strong><span style="color:#f60;font-size: 22px; font-weight:600;">${order.amountHkd}<span style="font-size: 12px;">&nbsp;HKD</span></span></li>
                    <li>
                        <strong>
                            <c:choose>
                                <c:when test="${'HK' == payType}">
                                    <spring:message code="pay.alipay.msg2"/>
                                </c:when>
                                <c:otherwise>
                                    <spring:message code="pay.alipay.msg1"/>
                                </c:otherwise>
                            </c:choose>
                        </strong>
                    </li>
                    <li style="margin-top: -4px;"><strong><spring:message code="pay.alipay.msg3"/></strong><a href="" class="btn btn-outline-primary btn-sm"><spring:message code='order.haspay'/></a></li>
                </ol>
            </div>
        </div>
        <!-- 支付二维码 -->
        <div class="table-responsive shopping-cart">
            <div class="qrcode-img-area">
                <iframe id="qr" src="${result}" width="100%" height="400" frameborder="0" scrolling="no"></iframe>
            </div>
        </div>
    </div>


    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
<script>
    //parent.location.reload();
</script>
</body>
</html>
