<%--
  User: Aaron
  Date: 2017/12/2
  Time: 下午10:44
--%>
<%@ page import="java.util.Locale" %>
<%@ page import="com.aaron.spring.common.Constant" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<%--
<div class="modal fade" id="modalPrepare" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body padding-top-2x text-center">
                <h3>网站升级维护中，敬请期待！</h3>
                <p>维护时间：2019-09-25 至 2019-09-30</p>
                <p>如有问题，欢迎致信 <a class="text-medium text-decoration-none" href="mailto:<EMAIL>"><EMAIL></a> 咨询</p>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalPrepare" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body padding-top-2x text-center">
                <h3>网站升级维护中，敬请期待！</h3>
                <p>已注册用户可正常阅读已购电子书</p>
                <p>如有问题，欢迎致信 <a class="text-medium text-decoration-none" href="mailto:<EMAIL>"><EMAIL></a> 咨询</p>
            </div>
        </div>
    </div>
</div>
--%>
<!--header start-->
<jsp:include page="header.jsp"/>

<%--<script>
    $(function () {
        $('#modalPrepare').modal({
            keyboard: false,
            backdrop: 'static'
        })
    })
</script>--%>

<!--header end-->

<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Content-->
    <!-- Main Slider-->
    <section class="hero-slider text-center">
        <div class="owl-carousel dots-inside" data-owl-carousel="{ &quot;nav&quot;: true, &quot;dots&quot;: true, &quot;loop&quot;: true, &quot;autoplay&quot;: true, &quot;autoplayTimeout&quot;: 8000 }">
            <c:forEach var="list" items="${config.indexBanners}">
                <a href="${list.toUrl}"><img src="${list.imgUrl}" alt="7月"></a>
            </c:forEach>
        <%--<a href="category-0-grid-0-0-0-0-0"><img src="<c:url value='/statics/images/banner/main-banner351.png'/>" alt="新书特惠"></a>--%>
            <%--<a href="category-0-grid-0-0-0-1-0"><img src="<c:url value='/statics/images/banner/main-banner361.png'/>" alt="预售书折扣"></a>--%>
        </div>
    </section>
    <!-- 通知 -->
    <div class="container padding-top-1x">
        <div class="row">
            <div class="col-lg-12">
                <div class="alert alert-danger alert-dismissible fade show text-center"><span class="alert-close" data-dismiss="alert"></span><a class="navi-link" style="color: black;" href="#"><i class="icon-bell"></i><span>&nbsp;&nbsp;恩道电子书未设立任何代理人/机构，谨防受骗，一切合作和团购事宜请发邮件至：<a class="text-decoration-none" href="mailto:<EMAIL>?subject=feedback"><EMAIL> </a></span></a></div>
            </div>
        </div>
    </div>
    <!-- Top Categories-->
    <section class="container padding-top-2x">
        <h3 class="text-center mb-30"><spring:message code='shop.indexEditor'/></h3>
        <div class="owl-carousel" data-owl-carousel="{ &quot;nav&quot;: true, &quot;dots&quot;: true, &quot;loop&quot;: true, &quot;autoplay&quot;: true, &quot;autoplayTimeout&quot;: 6000, &quot;margin&quot;: 30, &quot;responsive&quot;: {&quot;0&quot;:{&quot;items&quot;:1},&quot;576&quot;:{&quot;items&quot;:2},&quot;768&quot;:{&quot;items&quot;:3},&quot;991&quot;:{&quot;items&quot;:4},&quot;1200&quot;:{&quot;items&quot;:3}} }">

            <c:forEach var="list" items="${recommendedList}">

                <div class="card mb-30"><a class="card-img-tiles" href="book-${list.bookId}#">
                    <div class="inner">
                        <div class="main-img">
                            <div class="product-badge text-danger text-left">
                                <c:choose>
                                    <c:when test="${list.discountIsValid == 1}">
                                        <spring:message code="discount.icon"/><br>
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${list.discountSingleIsValid == 1}">
                                            <at:bookInfo infoType="discountSingle" enyanBook="${list}"/>
                                            <br>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>
                                <c:if test="${list.salesModel == 1}">
                                    <div class="text-right"><spring:message code="book.presale"/></div>
                                </c:if>
                            </div>
                            <img src="${list.bookCover}" alt="Category">
                        </div>
                    </div></a>
                    <div class="card-body text-center">
                        <h4 class="card-title"><a class="navi-link word-break" href="book-${list.bookId}#" title="${list.bookTitle}">${list.bookTitle}</a></h4>
                        <div >
                            <c:if test="${not empty list.author}">
                                <c:set var="authors" value="${fn:split(list.author, '#')}" />
                                <c:forEach var="author" items="${authors}">
                                    <a class="navi-link word-break" href="searchAuthor?searchText=${author}">${author}</a>
                                </c:forEach>
                            </c:if>
                        </div>
                        <c:if test="${list.showPublisher == 1}">
                            <div class="text-muted">
                                <a class="navi-link" href="category-0-grid-0-${list.publisherId}-0-0-0-0-0-0">
                                    <at:publisherName publisherId="${list.publisherId}"/>
                                </a>
                            </div>
                        </c:if>
                        <%--<at:bookPrice enyanBook="${list}" currency="${currency}"/>--%>
                        <at:bookPrice enyanBook="${list}" currency="${currency}" index="true"/>

                        <sec:authorize access="isAuthenticated() or isRememberMe()">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                        data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                        data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${list.bookId})"><i class="icon-heart"></i></button>
                                <c:if test="${list.price > 0  and list.bookType != 1}">
                                    <button class="btn btn-outline-primary btn-sm btn-bag" data-toast data-toast-type="danger" data-toast-position="topRight"
                                            data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                            data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="addToCart(${list.bookId})"><i class="icon-bag"></i>
                                    </button>
                                </c:if>
                            </div>
                        </sec:authorize>
                        <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                    <i class="icon-heart"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                    <i class="icon-bag"></i>
                                </button>
                            </div>
                        </sec:authorize>
                    </div>
            </div>
            </c:forEach>
        </div>
    </section>

    <!-- Top banner -->
    <section class="container padding-top-3x">
        <!-- Banner  -->
        <div class="owl-carousel" data-owl-carousel="{ &quot;dots&quot;: true, &quot;loop&quot;: true, &quot;autoplay&quot;: true, &quot;data-wrap&quot;: true, &quot;autoplayTimeout&quot;: 4000 }">
            <c:forEach var="list" items="${config.middleBanners}">
                <figure>
                    <a href="${list.toUrl}"><img src="${list.imgUrl}" alt="通知"></a>
                </figure>
            </c:forEach>
        </div>
    </section>

    <!-- 圣经注释 -->
    <c:forEach var="category" items="${indexCategoryList}">
    <section class="container padding-top-3x">
        <h3 class="text-center mb-30">
            <c:choose>
                <c:when test="${pageContext.response.locale == 'zh_CN'}">
                    ${category.categoryName}
                </c:when>
                <c:when test="${pageContext.response.locale == 'en_US'}">
                    ${category.categoryNameEn}
                </c:when>
                <c:otherwise>
                    ${category.categoryNameTc}
                </c:otherwise>
            </c:choose>

                    <a class="btn btn-sm text-muted" href="category-${category.categoryId}-grid-0-0-0-0-0-0-0-0">More >></a></h3>
        <div class="row">

            <c:forEach var="list" items="${category.page.records}" begin="0" end="3">

            <div class="col-md-6 col-sm-6">
                <!-- Product ON SALE -->
                <div class="product-card product-list"><a class="product-thumb" href="book-${list.bookId}#">
                    <div class="product-badge text-danger text-left">
                        <c:choose>
                            <c:when test="${list.discountIsValid == 1}">
                                <spring:message code="discount.icon"/><br>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${list.discountSingleIsValid == 1}">
                                    <at:bookInfo infoType="discountSingle" enyanBook="${list}"/><br>
                                </c:if>
                            </c:otherwise>
                        </c:choose>
                        <c:if test="${list.salesModel == 1}">
                            <div class="text-right"><spring:message code="book.presale"/></div>
                        </c:if>
                    </div><img src="${list.bookCover}" alt="Product"></a>
                    <div class="product-info">
                        <h3 class="product-title word-break"><a href="book-${list.bookId}#">${list.bookTitle}</a></h3>
                        <h4 class="product-price">
                            <at:bookPrice enyanBook="${list}" currency="${currency}" indexBottom="true"/>
                        </h4>
                        <p>
                            <span class="d-inline text-muted">
                                <c:if test="${not empty list.author}">
                                    <c:set var="authors" value="${fn:split(list.author, '#')}" />
                                    <c:forEach var="author" items="${authors}">
                                        <a class="navi-link word-break" href="searchAuthor?searchText=${author}">${author}</a>
                                    </c:forEach>
                                </c:if>
                            </span>
                            <span class="d-inline text-muted">
                                <c:if test="${list.showPublisher == 1}">
                                    <a class="navi-link" href="category-0-grid-0-${list.publisherId}-0-0-0-0-0-0">
                                        <at:publisherName publisherId="${list.publisherId}"/>
                                    </a>
                                </c:if>
                            </span>
                        </p>
                        <sec:authorize access="isAuthenticated() or isRememberMe()">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                        data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                        data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${list.bookId})"><i class="icon-heart"></i></button>
                                <c:if test="${list.price > 0  and list.bookType != 1}">
                                    <button class="btn btn-outline-primary btn-sm btn-bag" data-toast data-toast-type="danger" data-toast-position="topRight"
                                            data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                            data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="addToCart(${list.bookId})"><i class="icon-bag"></i>
                                    </button>
                                </c:if>
                            </div>
                        </sec:authorize>
                        <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                    <i class="icon-heart"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                    <i class="icon-bag"></i>
                                </button>
                            </div>
                        </sec:authorize>
                    </div>
                </div>
            </div>
            </c:forEach>
        </div>
    </section>
    </c:forEach>

    <!-- 合作出版方 -->
   <%-- <section class="padding-bottom-3x padding-top-3x">
        <div class="container">
            <h3 class="text-center mb-30 pb-2"><spring:message code='shop.index.publisher'/></h3>
            <div class="owl-carousel" data-owl-carousel="{ &quot;nav&quot;: false, &quot;dots&quot;: false, &quot;loop&quot;: true, &quot;margin&quot;: 100, &quot;autoplay&quot;: true, &quot;autoplayTimeout&quot;: 4000, &quot;responsive&quot;: {&quot;0&quot;:{&quot;items&quot;:2}, &quot;470&quot;:{&quot;items&quot;:3},&quot;630&quot;:{&quot;items&quot;:4},&quot;991&quot;:{&quot;items&quot;:5}} }">
                <a href="category-0-grid-0-7-0-0-0"><img src="<c:url value='/statics/images/publisher/G_glhx.png' />" alt="橄榄华宣"></a>
                <a href="category-0-grid-0-6-0-0-0"><img src="<c:url value='/statics/images/publisher/J_jdcc.png' />" alt="经典传承"></a>
                <a href="category-0-grid-0-3-0-0-0"><img src="<c:url value='/statics/images/publisher/X_xgsf.png' />" alt="贤理·璀雅出版社"></a>
                <a href="category-0-grid-0-8-0-0-0"><img src="<c:url value='/statics/images/publisher/X_xl.png' />" alt="香港三元福音倍进布道"></a>
                <a href="category-0-grid-0-4-0-0-0"><img src="<c:url value='/statics/images/publisher/Z_zz.png' />" alt="中国主日学协会"></a>
                <a href="category-0-grid-0-10-0-0-0"><img src="<c:url value='/statics/images/publisher/K_wfy.png' />" alt="跨文翻译"></a>
                <a href="category-0-grid-0-12-0-0-0"><img src="<c:url value='/statics/images/publisher/OMF.png' />" alt="OMF"></a>
                <a href="category-0-grid-0-13-0-0-0"><img src="<c:url value='/statics/images/publisher/X_xs.png' />" alt="橡树"></a>
                <a href="category-0-grid-0-9-0-0-0"><img src="<c:url value='/statics/images/publisher/PO.png' />" alt="PO"></a>
                <a href="category-0-grid-0-15-0-0-0"><img src="<c:url value='/statics/images/publisher/J_jnfy.png' />" alt="迦南翻译"></a>
                <a href="category-0-grid-0-16-0-0-0"><img src="<c:url value='/statics/images/publisher/Q_qgl.png' />" alt="青橄榄书殿"></a>
                <a href="category-0-grid-0-17-0-0-0"><img src="<c:url value='/statics/images/publisher/J_jndefxh.png' />" alt="加拿大恩福协会"></a>
            </div>
        </div>
    </section>--%>
    <!-- 合作伙伴 -->
    <!-- <section class="bg-faded padding-top-3x padding-bottom-3x">
      <div class="container">
        <h3 class="text-center mb-30 pb-2">合作伙伴</h3>

      </div>
    </section> -->

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
    <jsp:include page="ad-info.jsp"/>
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
</body>
</html>