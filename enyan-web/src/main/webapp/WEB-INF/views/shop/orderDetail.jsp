<%@ page import="com.aaron.spring.common.Constant" %><%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="order.detail"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="footer-js.jsp"/>
    <script src="<c:url value='/statics/js/moment.js' />"></script>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<!-- 弹出窗 -->
<div class="modal fade" id="myModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body padding-top-1x padding-bottom-1x">
                <h6><spring:message code="pay.ing"/> </h6>
            </div>
        </div>
    </div>
</div>

<!--header start-->
<jsp:include page="header.jsp"/>
<script src="<c:url value='/statics/js/jquery.countdown.js' />"></script>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-1 padding-top-2x">
        <div class="row mt-3">
            <div class="col-sm-12">
                <c:if test="${orderMain.isValid == 1 and orderMain.isPaid != 1}">
                        <h6><spring:message code="pay.left.21"/>&nbsp;<span class="text-danger" id="clock"></span>.&nbsp;<spring:message code="pay.left.22"/> </h6><br />
                        <script type="text/javascript">
                            <%--var offset = new Date().getTimezoneOffset();
                            var date = new Date('<fmt:formatDate value='${orderMain.expiredAt}' pattern='yyyy/MM/dd HH:mm:ss' timeZone="GMT"/>');
                            var now_utc =  Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
                                date.getUTCHours(), date.getUTCMinutes() - offset, date.getUTCSeconds());
                            var toDate = new Date(date.toUTCString().substr(0, 25))  ;
                            alert(now_utc);--%>
                            var newDate = new Date(${orderMain.expiredAt.time});
                            $('#clock').countdown(newDate)
                                .on('update.countdown', function(event) {
                                    $(this).text(event.strftime('%H : %M : %S'));
                                })
                                .on('finish.countdown', function(event) {
                                    window.location.href="";
                                });
                        </script>
                </c:if>

                <ol class="list-unstyled">
                    <li><span class="text-muted"><spring:message code="order.num"/>:</span> ${orderMain.orderNum}</li>
                    <li><span class="text-muted"><spring:message code="order.date"/>:</span>
                        <script>document.write(moment(${orderMain.purchasedAt.time}).format("YYYY-MM-DD HH:mm:ss"))</script>
                    </li>
                    <c:if test="${orderMain.isPaid == 1}">
                    <li><span class="text-muted"><spring:message code="pay.type"/>:</span>
                        <c:choose>
                            <c:when test="${orderMain.orderPayInfo.charge.payType == 2 ||orderMain.orderPayInfo.charge.payType == 21 || orderMain.orderPayInfo.charge.payType == 22}">
                                <spring:message code="credit.label"/>
                            </c:when>
                            <c:when test="${orderMain.orderPayInfo.charge.payType == 3}">
                                <spring:message code="label.free"/>
                            </c:when>
                            <c:when test="${orderMain.orderPayInfo.charge.payType == 4}">
                                <spring:message code="label.redeemCode"/>
                            </c:when>
                            <c:when test="${orderMain.orderPayInfo.charge.payType == 11}">
                                <spring:message code="alipay.label.hk"/>
                            </c:when>
                            <c:otherwise>
                                <spring:message code="alipay.label"/>
                            </c:otherwise>
                        </c:choose>
                    </li>
                    </c:if>
                </ol>
            </div>
        </div>
        <!-- 购物车-->
        <div class="table-responsive shopping-cart">
            <table class="table">
                <thead>
                <tr>
                    <th><spring:message code="book.title"/> </th>
                    <th class="text-center"><spring:message code="book.price"/> </th>
                    <th class="text-center"><spring:message code="book.amount"/> </th>
                    <th class="text-center"><spring:message code="book.total" /></th>
                </tr>
                </thead>
                <tbody>

                <c:forEach var="discountList" items="${order.cartDiscountInfoList}" varStatus="status">
                    <c:choose>
                        <c:when test="${discountList.discountId > 0}">
                            <c:if test="${not empty discountList.discountTitle}">
                                <tr>
                                    <td colspan="4">
                                        <span class="text-danger">
                                             <spring:message code="discount.n.has"/>${discountList.discountTitle}
                                        </span>
                                    </td>
                                </tr>
                            </c:if>
                        </c:when>
                        <c:otherwise>
                            <c:if test="${status.index == 1}">
                                <tr>
                                    <td colspan="4">
                                        <span class="text-danger">
                                            <spring:message code="discount.n.not"/>
                                        </span>
                                    </td>
                                </tr>
                            </c:if>
                        </c:otherwise>
                    </c:choose>

                    <c:forEach var="productInfo" items="${discountList.productInfoList}" varStatus="st">
                        <tr>
                            <td>
                                <c:choose>
                                    <c:when test="${orderMain.isPaid == 1 and orderMain.orderPayInfo.charge.payType == 4}">
                                        <div class="product-item">
                                            <img src="${productInfo.productCover}" alt="Product">
                                            <div class="product-info">
                                                <h4 class="product-title">
                                                    <c:if test="${productInfo.salesModel == 1}">
                                                        <spring:message code="book.title.presale"/>
                                                    </c:if>
                                                        ${productInfo.name}
                                                </h4>
                                                <span><em><spring:message code="book.author"/> :</em>
                                                    <c:if test="${not empty productInfo.producer}">
                                                        <c:set var="authors" value="${fn:split(productInfo.producer, '#')}" />
                                                        <c:forEach var="author" items="${authors}">
                                                            ${author}
                                                        </c:forEach>
                                                    </c:if>
                                                    </span>
                                            </div>
                                        </div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="product-item">
                                            <a class="product-thumb" href="/book-${productInfo.code}" target="_blank">
                                                <img src="${productInfo.productCover}" alt="Product"></a>
                                            <div class="product-info">
                                                <h4 class="product-title">
                                                    <a href="/book-${productInfo.code}" target="_blank">
                                                        <c:if test="${productInfo.salesModel == 1}">
                                                            <spring:message code="book.title.presale"/>
                                                        </c:if>
                                                            ${productInfo.name}
                                                    </a>
                                                </h4>
                                                <span><em><spring:message code="book.author"/> :</em>
                                                    <c:if test="${not empty productInfo.producer}">
                                                        <c:set var="authors" value="${fn:split(productInfo.producer, '#')}" />
                                                        <c:forEach var="author" items="${authors}">
                                                            <a class="navi-link" href="searchBook?searchText=${author}">${author}</a>
                                                        </c:forEach>
                                                    </c:if>
                                                    </span>
                                            </div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="text-center text-lg text-medium">
                                <c:choose>
                                    <c:when test="${productInfo.discountAnyIsValid}">
                                        <del>HK$${productInfo.priceHkd}</del>
                                        HK$${productInfo.priceHKDDiscount}
                                    </c:when>
                                    <c:otherwise>
                                        HK$${productInfo.priceHkd}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="text-center text-lg text-medium">${productInfo.quantity}</td>
                            <td class="text-center text-lg text-medium">
                                <c:choose>
                                    <c:when test="${productInfo.discountAnyIsValid}">
                                        HK$${productInfo.priceHKDDiscount*productInfo.quantity}
                                    </c:when>
                                    <c:otherwise>
                                        HK$${productInfo.priceHkd*productInfo.quantity}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                    </c:forEach>
                </c:forEach>
                </tbody>
            </table>
        </div>
        <c:choose>
            <c:when test="${orderMain.isPaid == 0}">
                <div class="shopping-cart-footer">
                    <!-- <div class="column">
                      <form class="coupon-form" method="post">
                        <input class="form-control form-control-sm" type="text" placeholder="" required>
                        <button class="btn btn-outline-primary btn-sm" type="submit"></button>
                      </form>
                    </div> -->
                    <div class="column text-lg">
                        <c:if test="${0 < order.amountHkdMiddle}">
                            <spring:message code="account.total"/>：<span class="text-medium"> HK$${order.amountHkdMiddle}</span><br/>
                        </c:if>
                        <c:if test="${order.amountDiscount > 0}">
                            <%--N件折（${order.discountTitle}） --%><spring:message code="account.discount"/>：
                            <span class="text-medium"> -HK$${order.amountDiscount}</span><br/>
                        </c:if>
                        <c:if test="${order.amountCoupon > 0}">
                            <spring:message code="coupon.value.label"/>：<span class="text-medium"> -HK$<fmt:formatNumber type="number" value="${order.amountCoupon}" pattern="0.00" maxFractionDigits="2"/></span><br/>
                        </c:if>
                        <spring:message code="account.toPay"/>： <span class="text-medium">HK$${order.amountHkd}</span><br/>
                        <at:bookPrice orderDetail="true" priceToDo="${order.amountHkd}"/>
                        <%
                            if (!Constant.IS_PRODUCT){
                        %>
                        <a href="testAliPay?orderNum=${orderMain.orderNum}">测试版支付宝支付</a>
                        <a href="testStripPay?orderNum=${orderMain.orderNum}">测试版信用卡支付</a>
                        <%
                            }
                        %>
                    </div>
                </div>
                <!-- 支付方式 start-->
                <div class="col-xl-8 col-lg-8">
                    <h4 class="mb-3"><spring:message code="label.pay.method"/></h4>
                    <hr>
                    <div class="accordion" id="accordion" role="tablist">
                        <div class="collapse show mt-1 mb-1" role="tabpanel">
                            <div class="card-body" style="padding-bottom: 0;">
                                <!-- checked 默认勾选 -->
                                <label class="custom-control custom-radio">
                                    <input class="custom-control-input" type="radio" checked name="payCheck" value="0" id="payCheck">
                                    <span class="custom-control-indicator"></span>
                                    <span class="custom-control-description text-color-dark"><spring:message code="pay.alipay"/>: &nbsp;<img class="d-inline-block align-middle" src="<c:url value='/statics/images/credit-alipay.png' />" style="width: 75px;" alt="<spring:message code='pay.alipay'/>"></span>
                                </label>
                            </div>
                            <div class="card-body" style="padding: 0.6rem 1.25rem;">
                                <!-- alipayHK -->
                                <label class="custom-control custom-radio">
                                    <input class="custom-control-input" type="radio" name="payCheck" value="3" id="payCheck">
                                    <span class="custom-control-indicator"></span>
                                    <span class="custom-control-description text-color-dark"><spring:message code="pay.alipay.hk"/>: &nbsp;<img class="d-inline-block align-middle" src="<c:url value='/statics/images/credit-alipayHK.svg' />" style="width: 110px;" alt="<spring:message code='pay.alipay.hk'/>"></span>
                                </label>
                            </div>
                                <div class="card-body" style="padding: 0 1.25rem 1.25rem;">
                                    <label class="custom-control custom-radio">
                                        <input class="custom-control-input" type="radio"  name="payCheck" value="1" id="payCheck" onclick="credit()">
                                        <span class="custom-control-indicator"></span>
                                        <span class="custom-control-description text-color-dark"><spring:message code="pay.credit"/>: &nbsp;<img class="d-inline-block align-middle" src="<c:url value='/statics/images/credit-cards.png' />" style="width: 90px;" alt="<spring:message code='pay.credit'/>"></span>
                                    </label>
                                    <fieldset class="card-info">
                                        <form class="interactive-credit-card row">
                                            <div class="form-group col-sm-12">
                                                <input class="form-control" type="text" name="payNumber"
                                                       placeholder="Card Number" required id="payNumber" value="<%--****************--%>">
                                            </div>
                                            <div class="form-group col-sm-8">
                                                <div class="row">
                                                    <label class="col-form-label" ><spring:message code="credit.to"/></label>
                                                    <div class="col-sm-4">
                                                        <select class="form-control input-lg" name="month" id="month" required>
                                                            <option value = "01">01</option>
                                                            <option value = "02">02</option>
                                                            <option value = "03">03</option>
                                                            <option value = "04">04</option>
                                                            <option value = "05">05</option>
                                                            <option value = "06">06</option>
                                                            <option value = "07">07</option>
                                                            <option value = "08">08</option>
                                                            <option value = "09">09</option>
                                                            <option value = "10">10</option>
                                                            <option value = "11">11</option>
                                                            <option value = "12">12</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-sm-4">
                                                        <select class="form-control input-lg" name="year" id="year" required>
                                                            <option value = "23">2023</option>
                                                            <option value = "24">2024</option>
                                                            <option value = "25">2025</option>
                                                            <option value = "26">2026</option>
                                                            <option value = "27">2027</option>
                                                            <option value = "28">2028</option>
                                                            <option value = "29">2029</option>
                                                            <option value = "30">2030</option>
                                                            <option value = "31">2031</option>
                                                            <option value = "32">2032</option>
                                                            <option value = "33">2033</option>
                                                            <option value = "34">2034</option>
                                                            <option value = "35">2035</option>
                                                            <option value = "36">2036</option>
                                                            <option value = "37">2037</option>
                                                            <option value = "38">2038</option>
                                                            <option value = "39">2039</option>
                                                            <option value = "40">2040</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group col-sm-4">
                                                <input class="form-control" type="text" name="payCvc" placeholder="CVC" required id="payCvc">
                                            </div>
                                        </form>
                                    </fieldset>
                                </div>
                            </div>
                        </div>
                    <hr>
                    </div>

                <!-- 支付方式 end-->
                    <div class="shopping-cart-footer" style="border-top:0px; padding-left:15px;">
                        <div class="column text-left">
                            <div class="row">
                                <div class="col-lg-12 col-md-8">
                                <p class="text-color-dark"><spring:message code="read.had.top"/></p>
                                <ol class="list-unstyled">
                                    <li><span >1. </span><spring:message code="read.had.top.1"/></li>
                                    <li><span >2. </span><spring:message code="read.had.top.2"/></li>
                                </ol>
                            </div>
                        </div>
                        <label class="custom-control custom-checkbox d-block">
                            <input id="agree" class="custom-control-input" type="checkbox" onchange="warning()">
                            <span class="custom-control-indicator"></span><span class="custom-control-description text-color-dark"><spring:message code="read.had"/>
                            <a class="text-medium text-decoration-none" href="/index-Conditions" target="_blank"><spring:message code="label.conditions"/></a> <spring:message code="label.and"/>
                            <a class="text-medium text-decoration-none" href="/index-Refund" target="_blank"><spring:message code="label.refund"/></a>
                            <p id="warning" class="text-medium text-danger mt-1"><spring:message code="error.pay.agree"/></p>
                        </label>
                        <button class="btn btn-primary" id="pay" onclick="pay()"  disabled><spring:message code="account.pay"/> </button>
                        <a class="btn btn-outline-secondary" href="myOrders" data-toast-position="topRight"><i class="icon-arrow-left"></i>&nbsp;<spring:message code="back.orders"/> </a>
                    </div>
                </div>
                <!-- 支付方式 end-->
            </c:when>
            <c:otherwise>
                <div class="shopping-cart-footer">
                    <!-- <div class="column">
                      <form class="coupon-form" method="post">
                        <input class="form-control form-control-sm" type="text" placeholder="" required>
                        <button class="btn btn-outline-primary btn-sm" type="submit"></button>
                      </form>
                    </div> -->
                    <div class="column text-lg">
                        <c:choose>
                            <c:when test="${orderMain.orderType == 2}"> <%--兑换码兑换--%>
                                <spring:message code="account.hasPay"/>： <span class="text-medium">HK$0</span>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${0 < order.amountHkdMiddle}">
                                    <spring:message code="account.total"/>：<span class="text-medium"> HK$${order.amountHkdMiddle}</span><br/>
                                </c:if>
                                <c:if test="${order.amountDiscount > 0}">
                                    <%--N件折（${order.discountTitle}） --%><spring:message code="account.discount"/>：
                                    <span class="text-medium"> -HK$${order.amountDiscount}</span><br/>
                                </c:if>
                                <c:if test="${order.amountCoupon > 0}">
                                    <spring:message code="coupon.value.label"/>：<span class="text-medium"> -HK$<fmt:formatNumber type="number" value="${order.amountCoupon}" pattern="0.00" maxFractionDigits="2"/></span><br/>
                                </c:if>
                                <spring:message code="account.hasPay"/>： <span class="text-medium">HK$${order.amountHkd}</span><br/>
                                <%--<at:bookPrice orderDetail="true" priceToDo="${order.amountHkd}"/>--%>
                            </c:otherwise>
                        </c:choose>

                    </div>
                </div>
            </c:otherwise>
        </c:choose>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>

<script>
    var Terminal = {
        // 辨别移动终端类型
        platform : function(){
            var u = navigator.userAgent, app = navigator.appVersion;
            return {
                //IE内核
                windows: u.indexOf('Windows') > -1,
                //苹果、谷歌内核
                webKit: u.indexOf('AppleWebKit') > -1,
                //是否为移动终端
                mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/),
                //ios终端
                ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
                // android终端
                android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,
                // 是否为iPhone
                iPhone: u.indexOf('iPhone') > -1 ,
                // 是否iPad
                iPad: u.indexOf('iPad') > -1,
                //是否为mac系统
                Mac: u.indexOf('Macintosh') > -1,
                //是否web应该程序
                webApp: u.indexOf('Safari') == -1,
                //是否微信
                weixin: u.indexOf('MicroMessenger') > -1,
            };
        }(),
    }
    function isEmpty(str){
        if($.trim(str)==''){
            return true;
        }else{
            return false;
        }
    };
    function validate() {
        var hasRead = document.getElementById('agree');
        if (!hasRead.checked){
            alert("<spring:message code='error.protocol'/>") ;
            return false;
        }
        return true;
    };
    function warning(){
        var checkAllBox = document.getElementById("agree");
        var displayWarning = document.getElementById("warning");
        var pay = document.getElementById("pay");
        if(checkAllBox.checked){
            displayWarning.innerHTML="";
            pay.disabled = false;
        }else{
            displayWarning.innerHTML='<spring:message code="error.pay.agree"/>';
            pay.disabled = true;
        }
    }
    function pay() {
        var hasRead = document.getElementById('agree');
        if (!hasRead.checked){
            alert("<spring:message code='error.protocol'/>") ;
            return;
        }
        var mac = "not";
        if (Terminal.platform.Mac){
            mac = "mac";
        }
        payCheck = document.querySelector('input[name="payCheck"]:checked').value;;
        if(payCheck == "1"){
            usdPay();
        }else if(payCheck == "3"){//HK
            window.location.href="checkout-${orderMain.orderId}?type=HK&mac="+mac;
        }else {
            //window.open('checkout-${orderMain.orderId}','_blank');//_self
            window.location.href="checkout-${orderMain.orderId}?mac="+mac;
        }
    }
    function usdPay(){
        payNumber = $("#payNumber").val();
        payExpire = $("#month").val()+"/"+$("#year").val();
        payCvc = $("#payCvc").val();
        var hasRead = document.getElementById('agree');
        if (!hasRead.checked){
            alert("<spring:message code='error.protocol'/>") ;
            return;
        }

        if(isEmpty(payNumber)){
            alert("<spring:message code='error.credit'/>");
            return;
        }
        if (payExpire.length == 0){
            alert("<spring:message code='error.date'/>");
            return;
        }
        if (payCvc.length == 0){
            alert("<spring:message code='error.cvc'/>");
            return;
        }
        $('#myModal').modal('show');
        url="/checkoutUsd-${orderMain.orderId}?${_csrf.parameterName}=${_csrf.token}";
        $.jpost(url, {
            "payNumber":payNumber,
            "payExpire":payExpire,
            "payCvc":payCvc
        }).then(res => {
            //console.log(res);
            $('#myModal').modal('hide');
        if(res.success){
            //alert("sss")

            alert(res.successMessage);
            window.location.href= res.result;
        }else{
            alert(res.errorMessages[0]);
        }
        //alert(res.result);
        //window.location.reload();
        //windows.href("");

    });
    }
    function credit(){
        $(".card-info").toggle(true)
    }
</script>
</body>
</html>