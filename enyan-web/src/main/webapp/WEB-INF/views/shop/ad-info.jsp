<%@ page import="com.aaron.spring.api.AdModel" %>
<%@ page import="com.aaron.spring.common.Constant" %>
<%@ page import="java.util.Date" %><%--
  User: Aaron
  Date: 2023/08/20
  Time: 下午4:23
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    AdModel adWeb = null;
    if (Constant.DEFAULT_REST_CONFIG.getAdWebList().size() > 0){
		adWeb = Constant.DEFAULT_REST_CONFIG.getAdWebList().get(0);
    }
	if (null == adWeb){
		return;
    }

    if (null != adWeb.getBeginDate() && null != adWeb.getEndDate()) {
        //Date current = new Date();
        Long current = System.currentTimeMillis();
        if (current < adWeb.getBeginDate()) {
            return;
        }
        if (current > adWeb.getEndDate()) {
            return;
        }
    }
%>
<!-- A link to ad -->
<!-- 弹窗 -->
<div class="pop" id="myPop" style="display: none;">
    <div class="pop-content">
        <button class="close-btn" onclick="closepop()">&times;</button>
        <a href="<%=adWeb.getToUrl()%>" target="_blank"><img src="<%=adWeb.getImgUrl()%>" alt="" style="width: 200px;"></a>
    </div>
</div>
