<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午4:06
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>
        <c:choose>
            <c:when test="${'HK' == payType}">
                <spring:message code="pay.alipay.hk"/>
            </c:when>
            <c:otherwise>
                <spring:message code="pay.alipay"/>
            </c:otherwise>
        </c:choose>
    </title>
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">
    <link charset="utf-8" rel="stylesheet" href="<c:url value='/css/front-old.css' />" media="all">
    <style>
        #header {
            height: 60px;
            background-color: #fff;
            border-bottom: 1px solid #d9d9d9;
            margin-top: 0px;
        }
        #header .header-title {
            height: 60px;
            float: left;
        }
        #header .logo {
            float: left;
            height: 31px;
            width: 95px;
            margin-top: 14px;
            text-indent: -9999px;
            background: none; !important
        }
        #header .logo-title {
            font-size: 16px;
            font-weight: normal;
            font-family: "Microsoft YaHei",微锟斤拷锟脚猴拷,"锟斤拷锟斤拷";
            border-left: 1px solid #676d70;
            color: #676d70;
            height: 20px;
            float: left;
            margin-top: 15px;
            margin-left: 10px;
            padding-top: 10px;
            padding-left: 10px;
        }
        .header-container {
            width: 950px;
            margin: 0 auto;
        }

        body,
        #footer{
            background-color: #eff0f1;
        }

        #footer #ServerNum {
            color: #eff0f1;
        }
        .login-switchable-container {
            background-color: #fff;
        }

        #order.order-bow .orderDetail-base,
        #order.order-bow .ui-detail {
            border-bottom: 3px solid #bbb;
            background: #eff0f1;
            color: #000;
        }

        .order-ext-trigger {
            position: absolute;
            right: 20px;
            bottom: 0;
            height: 22px;
            padding: 2px 8px 1px;
            font-weight: 700;
            border-top: 0;
            background: #b3b3b3;
            z-index: 100;
            color: #fff;
        }

        #partner {
            margin-top: 0;
            padding-top: 0;
            background-color: #eff0f1;
        }

        #order.order-bow .orderDetail-base, #order.order-bow .ui-detail {
            border-bottom: 3px solid #b3b3b3;
        }

        .payAmount-area {
            bottom: 36px;
        }

        .alipay-logo {
            display: block;
            position: relative;
            left: 0;
            top: 10px;
            float: left;
            height: 40px;
        }

        .header-switchLang {
            float: right;
            height: 32px;
            width: 93px;
            margin-top: 14px;
            border: 1px solid #A2A2A2;
            box-sizing: border-box;
        }

        .switch_unit {
            float: left;
            width: 45px;
            font-size: 16px;
            text-align: center;
            line-height: 30px;
            cursor: pointer;
        }

        .switch_divider {
            float: left;
            height: 100%;
            border-left: 1px solid #A2A2A2;
        }

        .choosed_lang {
            background: #D8D8D8;
        }
        .qrcode-area {
            margin: 0 auto;
            position: relative;
        }
        .qrcode-integration .qrcode-header {
            display: block;
            width: auto;
            margin: 0;
            padding: 0;
            margin-top: 75px;
            margin-bottom: 16px;
        }
        .qrcode-header-money {
            font-size: 26px;
            font-weight: 700;
            color: #f60;
        }
        .qrcode-integration .qrcode-img-area {
            width: 200px;
            height: 200px;
            text-align: center;
        }
        .qrcode-img-area.qrcode-img-crash {
            height: 220px;
        }
        .qrcode-reward-wrapper {
            text-align: center;
        }
        .qrcode-reward {
            display: inline-block;
            margin: 0;
            padding: 2px 5px;
            background-color: #0188cd;
            border-radius: 0;

            font-size: 12px;
            line-height: 16px;
            color: #fff;
        }
        .qrcode-reward-question {
            font-size: 12px;
            margin-left: 5px;
            margin-right: 0;
        }
        .qrcode-integration .qrcode-loading {
            top: 70px;
            left: 60px;
        }
        .qrcode-integration .qrcode-img {
            top: 70px;
            left: 70px;
        }
        .qrcode-integration .qrcode-img-wrapper {
            position: relative;
            width: 200px;
            height: auto;
            min-height: 260px;
            margin: 0 auto;
            padding: 6px;
            border: 1px solid #d3d3d3;
            -webkit-box-shadow: 1px 1px 1px #ccc;
            box-shadow: 1px 1px 1px #ccc;
        }

        .qrcode-img-area .qrcode-busy-icon {
            padding-top: 15px;
        }

        .qrcode-img-area .qrcode-busy-text {
            margin-top: 20px;
        }

        a.mi-button-lwhite .mi-button-text {
            padding: 8px 39px 4px 36px;
        }

        .qrcode-img-area .mi-button {
            margin-top: 40px;
        }

        .qrcode-img-explain {
            padding: 10px 0 6px;
        }

        .qrcode-img-explain img {
            margin-left: 30px;
            margin-top: 5px;
            height: 28px;
        }

        .qrcode-img-explain div {
            margin-left: 10px;
        }
        .qrcode-foot {
            text-align: center;
        }

        .qrcode-downloadApp,
        .qrcode-downloadApp:hover,
        .qrcode-downloadApp:active,
        .qrcode-explain a.qrcode-downloadApp:hover {
            font-size: 12px;
            color: #a6a6a6;
            text-decoration: underline;
        }
        .area-split {
            margin-top: 156px;
            width: 10px;
            height: 300px;
            background-repeat: no-repeat;
        }
        .qrguide-area {
            position: absolute;
            top: 2px;
            left: 260px;
            width: 204px;
            height: 183px;
            cursor: pointer;
        }
        .qrguide-area .qrguide-area-img {
            display: block;
            position: absolute;
            width: 204px;
            height: 183px;
            bottom: 0;
            left: 0;
            z-index: -1;
        }

        .qrguide-area .qrguide-area-img.active {
            z-index: 10;
        }

        .qrguide-area .qrguide-area-img.background {
            z-index: 9;
        }

        .qrcode-notice .qrcode-notice-title {
            padding: 10px 10px 11px 63px;
        }
        .ui-securitycore .ui-label, .mi-label {
            text-align: left;
            height: auto;
            line-height: 18px;
            padding: 0;
            display: block;
            padding-bottom: 8px;
            margin: 0;
            width: auto;
            float: none;
            font:14px/1.5 tahoma,arial,\5b8b\4f53;
        }

        .ui-securitycore .ui-form-item {
            position: relative;
            padding: 0 0 10px 0;
            width: 350px;

        }

        .ui-securitycore .ui-form-explain {
            height: 18px;
            font-family:tahoma,arial,\5b8b\4f53;
        }

        .ui-securitycore .edit-link {
            position: absolute;
            top: -3px;
            right: 0;
        }

        .ui-securitycore .ui-input {
            height: 28px;
            font-size: 14px;
        }

        .ui-securitycore .standardPwdContainer .ui-input {
            width: 340px;
        }

        .ui-securitycore .mobile-section.checkcode-section {
            margin-top: 10px;
        }

        .mobile-form .ui-securitycore .ui-form-item-mobile {
            display: none;
        }

        .mobile-form .ui-securitycore .ui-form-item-mobile .ui-label {

        }

        .mobile-form .ui-securitycore .ui-form-item-mobile .ui-form-text {
            display: none;
        }

        .mobile-form .ui-securitycore .ui-form-item-counter {
            padding-left: 0;
            padding-right: 0;
            padding-bottom: 20px;
            position: relative;
            height: 87px;
        }

        .mobile-form .ui-securitycore .ui-form-item-counter .ui-label {
            display: block;
            float: none;
            margin-left: 0;
            text-align: left;
            line-height: 18px !important;
            padding: 0 0 8px 0;
        }
        .mobile-form .ui-securitycore .ui-form-item-counter .ui-form-field {
            /*display: block;*/
            zoom: 1;
        }
        .mobile-form .ui-securitycore .ui-form-item-counter .ui-form-field:after {
            visibility: hidden;
            display: block;
            font-size: 0;
            content: " ";
            clear: both;
            height: 0;
        }
        .mobile-form .ui-securitycore .ui-form-item-counter .ui-checkcode-input {
            height: 24px;
            line-height: 24px;
            width: 148px;
            border: 1px solid #ccc;
            padding: 7px 10px;
            float: left;
            display: block;
            font-size: 14px;
        }
        .mobile-form .ui-securitycore .ui-form-item-counter .ui-checkcode-input:focus {
            color: #4d4d4d;
            border-color: #07f;
            outline: 1px solid #8cddff;
        }
        .mobile-form .ui-securitycore .ui-form-item-counter .eSend-btn {
            float: left;
            color: #08c;
        }

        #mobileSend {
            position: absolute;
            right: 0;
            top: 26px;
        }
        .mobile-form .ui-securitycore .ui-form-item-counter .ui-checkcode-messagecode-btn {
            float: left;
            width: 178px;
            height: 40px;
            _height: 38px;
            line-height: 38px;
            _line-height: 35px;
            color: #676d70;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 1px;
            background: #f3f3f3;
            margin-left: 2px;
            padding-left: 0;
            padding-right: 0;

        }
        .mobile-form .ui-securitycore .ui-form-item-counter .ui-checkcode-messagecode-disabled-btn {
            background: #cacccd;
            border: 1px solid #cacccd;
            color: #aeb1b3;
            font-weight: normal;
            cursor: default;
        }

        .mobile-form .ui-securitycore .ui-form-item-counter .reSend-btn {
            float: left;
            margin-top: 10px;
            color: #08c;
        }

        .ui-checkcode-messagecode-disabled-btn {

        }
        .mobile-form .ui-securitycore .ui-form-item-counter .ui-form-field {
            display: block;
        }
        .mobile-form .ui-securitycore .ui-form-item-counter .ui-form-field .fn-hide,
        .mobile-form .ui-securitycore .ui-form-item-counter .fn-hide .reSend-btn {
            display: none;
        }
        .alieditContainer object {
            width: 348px;
            height:38px;
        }
        #container .alieditContainer {
            width: 348px;
            height: 38px;
        }
        #container .alieditContainer a.aliedit-install {
            line-height: 38px;
        }
        #container .alieditContainer .ui-input {
            width:324px;
            padding:7px 10px;
            font-size:14px;
            height: 20px;
            line-height: 24px;
        }
        #container .alieditContainer .ui-input:focus {
            color:#4D4D4D;
            border-color:#07F;
            outline:1px solid #8CDDFF;
            *padding:7px 3px 4px;
            *border:2px solid #07F;
        }
        .teBox {
            height: auto;
        }
        #J_loginPwdMemberT {
            padding: 20px 0 60px 0;
        }
        #J_loginPwdMemberT #teLogin {
            height: auto;
        }
        #J_loginPwdMemberT .mi-form-item{
            padding: 0 0 10px 0;
        }
        #J_loginPwdMemberT .teBox-in {
            padding: 0;
            width: 350px;
            margin: 0 auto;
        }
        .t-contract-container {
            width: 76%;
        }
        .contract-container {
            width: 450px;
            margin: 0 auto;
            text-align: left;
            position: relative;
        }
        .contract-container .contract-container-label {
            width: 450px;
        }
        .mb-text {
            font-size: 14px;
            padding-top: 10px;
        }
        .ml5 {
            margin-left: 5px;
        }
        .user-login-account {
            font-size: 16px;
        }
        .mi-mobile-button {
            font-weight: bold;
        }
        .alipay-agreement-link {
            margin-left: 5px;
            color: #999;
        }
        .alipay-agreement {
            width: 600px;
            height: 270px;
            padding: 10px;
            text-align: center;
        }

        .alipay-agreement-content {
            height: 230px;
            width: 600px;
            margin-bottom: 5px;
        }
        #container .order-timeout-notice {
            margin-top: 30px;
            display: none;
        }
        .login-panel .fn-mb8{
            margin-bottom: 8px;
        }
        .login-panel .fn-mt8{
            margin-top: 8px;
        }
        .order-area {
            position: relative;
            z-index: 10;
        }
        .cashier-center-container {
            overflow: hidden;
            position: relative;
            z-index: 1;
            width: 950px;
            min-height: 580px;
            background-color: #fff;
            border-bottom: 3px solid #b3b3b3;
        }
        .cashiser-switch-wrapper {
            width: 1800px;
        }
        .cashier-center-view {
            position: relative;
            width: 803px;
        }
        .cashier-center-view.view-pc {
            display: block;
        }
        .cashier-center-view.view-pc .loginBox {
            padding: 60px 0 20px 238px;
            width: 350px;
            margin: 0;
        }
        .loginBox .login-title-area {
            margin: 0;
            margin-bottom: 30px;
        }
        .login-title .rt-text {
            font-size: 14px;
        }
        .teForm {
            padding: 0;
        }
        .mi-form-item {
            padding: 0 0 12px 0;
        }
        .submitContainer {
            margin-top: 6px;
        }
        .view-switch {
            width: 146px;
            height: 400px;
            padding-top: 126px;
            background-color: #e6e6e6;
            cursor: pointer;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            user-select: none;
        }
        .view-switch.qrcode-show {
            border-left: 1px solid #d9d9d9;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
        .view-switch.qrcode-hide {
            border-right: 1px solid #d9d9d9;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        .switch-tip {
            text-align: center;
        }
        .switch-tip-font {
            font-size: 16px;
            font-family: tahoma, arial, '\5FAE\8F6F\96C5\9ED1', '\5B8B\4F53';
        }
        .switch-tip-icon {
            position: relative;
            z-index: 10;
            display: block;
            margin-top: 4px;
            font-size: 78px;
            color: #a6a6a6;
            cursor: pointer;
        }
        .switch-tip-btn {
            display: block;
            width: 106px;
            height: 36px;
            margin: 6px auto 0;
            border: 1px solid #0fa4db;
            background-color: #00aeef;
            border-radius: 5px;
            font-size: 12px;
            font-weight: 400;
            line-height: 36px;
            text-align: center;
            color: #fff;
            text-decoration: none;
        }
        .switch-tip-btn:hover {
            color: #fff;
            text-decoration: none;
        }
        .view-switch.qrcode-hide .view-switch-content {
            height: 334px;
            padding-top: 126px;
        }
        .switch-pc-tip .switch-tip-icon {
            position: relative;
            z-index: 10;
            margin-top: 4px;
            font-size: 78px;
        }
        .switch-tip-icon-wrapper {
            position: relative;
        }
        .switch-tip-icon-wrapper:before {
            content: '';
            position: absolute;
            left: 47px;
            top: 24px;
            z-index: 0;
            width: 50px;
            height: 70px;
            background-color: #fff;
        }
        .switch-qrcode-tip .switch-tip-icon-wrapper:before {
            left: 38px;
            top: 25px;
            width: 70px;
            height: 47px;
        }
        .switch-tip-icon-img {
            position: absolute;
            left: 58px;
            top: 35px;
            z-index: 11;
        }
        .switch-qrcode-tip .switch-tip-icon-img {
            left: 48px;
            top: 39px;
        }
        .standardPwdContainer object {
            width: 348px;
            height:38px;
        }
        #container .standardPwdContainer {
            width: 348px;
            height: 38px;
        }
        #container .standardPwdContainer a.aliedit-install {
            line-height: 38px;
        }
        #container .standardPwdContainer .ui-input {
            width:324px;
            padding:7px 10px;
            font-size:14px;
            height: 20px;
            line-height: 24px;
        }
        #container .standardPwdContainer .ui-input:focus {
            color:#4D4D4D;
            border-color:#07F;
            outline:1px solid #8CDDFF;
            *padding:7px 3px 4px;
            *border:2px solid #07F;
        }.sub-th {
             width: 136px;
         }
        .hide-pc .cashier-center-view {
            width: 960px;
            height: 526px;
        }
        .hide-pc .qrguide-area {
            left: 583px;
        }
        .hide-pc .view-switch.qrcode-show {
            display: none;
        }
        .hide-pc .cashier-center-view.view-pc {
            display: none;
        }
    </style>
</head>


<div id="header">
    <div class="header-container fn-clear">
        <div class="header-title">
            <div class="alipay-logo">
                <c:choose>
                    <c:when test="${'HK' == payType}">
                        <img src="<c:url value='/statics/images/credit-alipayHK.svg' />" style="height: 100%" alt="alipay_logo">
                    </c:when>
                    <c:otherwise>
                        <img src="<c:url value='/statics/images/credit-alipay2.png' />" style="height: 100%" alt="alipay_logo">
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </div>
</div>
<div id="container">
    <div id="content" class="fn-clear">
        <div class="order-area">
            <div id="order" class="order order-bow">
                <div class="orderDetail-base">
                    <div class="order-extand-explain fn-clear">
                            <span class="fn-left explain-trigger-area order-type-navigator" style="cursor: auto">
                                <span><spring:message code="pay.alipay.msg1"/></span>
                            </span>
                    </div>
                    <div class="commodity-message-row">
                            <span class="first long-content">
                                <spring:message code="order.num"/>：<span>${orderMain.orderNum}</span>
                            </span>
                    </div>
                    <div style="padding-top: 0px;">
                        <span style="font-weight: normal;font-size: 12px;"><spring:message code="pay.store"/>：Inspirata Publishing (Hong Kong) Limited</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="cashier-center-container">
            <div class="cashiser-switch-wrapper fn-clear">
                <div class="cashier-center-view view-qrcode fn-left">
                    <div class="qrcode-integration qrcode-area">
                        <div class="qrcode-header">
                            <div class="ft-center"><spring:message code="pay.alipay.scan1"/></div>
                            <div class="ft-center qrcode-header-money">${order.amountHkd} <span style="font-size: 12px;">HKD</span></div>
                            <p class="ft-center" style="padding-top: 4px;">
                                <c:choose>
                                    <c:when test="${'HK' == payType}">
                                    </c:when>
                                    <c:otherwise>
                                        <spring:message code="pay.alipay.msg2"/>
                                    </c:otherwise>
                                </c:choose>
                            </p>
                        </div>
                        <div class="qrcode-img-wrapper">
                            <div class="qrcode-img-area">
                                <iframe id="qr" src="${result}" width="200" height="200" frameborder="0" scrolling="no"></iframe>
                                <div class="qrcode-img-explain fn-clear">
                                    <img class="fn-left" src="<c:url value='/statics/images/alipay_scan2.png' />" alt="扫一扫">
                                    <div style="width: 110px" class="fn-left">
                                        <c:choose>
                                            <c:when test="${'HK' == payType}">
                                                <spring:message code="pay.alipay.scan3"/>
                                            </c:when>
                                            <c:otherwise>
                                                <spring:message code="pay.alipay.scan2"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="qrguide-area">
                            <img src="<c:url value='/statics/images/alipay_scan.png' />" class="qrguide-area-img active" style="display: block;">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </body>
</html>
