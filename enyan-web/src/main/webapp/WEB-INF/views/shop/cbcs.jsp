<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="shop.cbcs.title"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
    <style>.offcanvas-wrapper{min-height:auto}.h2{margin-top:0;margin-bottom:0.5rem !important;font-size:2.4rem;font-weight:700 !important;color:#000000 !important;word-spacing:0.1rem;letter-spacing:-0.01rem}@media (max-width:767px){.text-h{display:none}.title-sm{font-size:0.90rem !important}}.tab-content{padding:0px;border:0px;border-bottom-left-radius:0px;border-bottom-right-radius:0px}.nav-tabs{border-bottom-color:#ffffff}.nav-tabs .nav-link{font-size:16px}.nav-tabs .nav-item.show .nav-link,.nav-tabs .nav-link.active{color:#000000;border-color:transparent}.nav-tabs .nav-item.show .nav-link,.nav-tabs .nav-link.active:after{content:"";display:block;background:#cc4646;border-radius:2rem;position:absolute;width:50px;height:4px;margin-top:0;transition:all 0.2s ease-in-out}.nav-tabs .nav-link{padding:2px 0px;margin:2px 20px;border:0px solid transparent}</style>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">

    <c:choose>
        <c:when test="${pageContext.response.locale == 'zh_CN'}">
            <jsp:include page="cbcs-sc.jsp"/>
        </c:when>
        <c:when test="${pageContext.response.locale == 'en_US'}">
            <jsp:include page="cbcs-en.jsp"/>
        </c:when>
        <c:otherwise>
            <jsp:include page="cbcs-tc.jsp"/>
        </c:otherwise>
    </c:choose>

    <!-- 书籍 -->
    <section class="padding-top-2x">
        <div class="container">
            <h3 class="text-center mb-30"><spring:message code="shop.cbcs.ot"/></h3>
            <ul class="nav nav-tabs justify-content-center" role="tablist">
                <li class="nav-item"><a class="nav-link active" href="#osc" data-toggle="tab" role="tab"><spring:message code="shop.cbcs.sc"/></a></li>
                <li class="nav-item"><a class="nav-link" href="#otc" data-toggle="tab" role="tab"><spring:message code="shop.cbcs.tc"/></a></li>
            </ul>
            <div class="tab-content mt-30">
                <div class="tab-pane fade show active" id="osc" role="tabpanel">
                    <div class="row">
                        <!-- 旧约简体 -->
                        <c:forEach var="list" items="${bookList.otSc}">
                            <div class="col-lg-3 col-md-6 col-sm-6 mb-30">
                                <div class="product-card">
                                    <a class="product-thumb" href="book-${list.bookId}#" title="${list.bookTitle}"><img src="${list.bookCover}" alt="Product"></a>
                                    <div class="product-badge text-danger text-left">
                                        <c:choose>
                                            <c:when test="${list.discountIsValid == 1}">
                                                <spring:message code="discount.icon"/><br>
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${list.discountSingleIsValid == 1}">
                                                    <at:bookInfo infoType="discountSingle" enyanBook="${list}"/><br>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>

                                        <c:if test="${list.salesModel == 1}">
                                            <div class="text-right"><spring:message code="book.presale"/></div>
                                        </c:if>
                                    </div>
                                    <h4 class="product-title word-break"><a href="book-${list.bookId}#" title="${list.bookTitle}">${list.bookTitle}</a></h4>
                                    <h4 class="product-price"><at:bookPrice enyanBook="${list}" currency="${currency}" category="true"/></h4>

                                    <sec:authorize access="isAuthenticated() or isRememberMe()">
                                        <div class="product-buttons">
                                            <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                    data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                                    data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${list.bookId})"><i class="icon-heart"></i></button>
                                            <c:if test="${list.bookType != 1}">
                                                <button class="btn btn-outline-primary btn-sm btn-bag" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                        data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                                        data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="addToCart(${list.bookId})"><i class="icon-bag"></i>
                                                </button>
                                            </c:if>
                                        </div>
                                    </sec:authorize>
                                    <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                                        <div class="product-buttons">
                                            <button class="btn btn-outline-secondary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                                <i class="icon-heart"></i>
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                                <i class="icon-bag"></i>
                                            </button>
                                        </div>
                                    </sec:authorize>

                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </div>
                <div class="tab-pane fade" id="otc" role="tabpanel">
                    <div class="row">
                        <!-- 旧约繁体 -->
                        <c:forEach var="list" items="${bookList.otTc}">
                            <div class="col-lg-3 col-md-6 col-sm-6 mb-30">
                                <div class="product-card">
                                    <a class="product-thumb" href="book-${list.bookId}#" title="${list.bookTitle}"><img src="${list.bookCover}" alt="Product"></a>
                                    <div class="product-badge text-danger text-left">
                                        <c:choose>
                                            <c:when test="${list.discountIsValid == 1}">
                                                <spring:message code="discount.icon"/><br>
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${list.discountSingleIsValid == 1}">
                                                    <at:bookInfo infoType="discountSingle" enyanBook="${list}"/><br>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>

                                        <c:if test="${list.salesModel == 1}">
                                            <div class="text-right"><spring:message code="book.presale"/></div>
                                        </c:if>
                                    </div>
                                    <h4 class="product-title word-break"><a href="book-${list.bookId}#" title="${list.bookTitle}">${list.bookTitle}</a></h4>
                                    <h4 class="product-price"><at:bookPrice enyanBook="${list}" currency="${currency}" category="true"/></h4>

                                    <sec:authorize access="isAuthenticated() or isRememberMe()">
                                        <div class="product-buttons">
                                            <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                    data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                                    data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${list.bookId})"><i class="icon-heart"></i></button>
                                            <c:if test="${list.bookType != 1}">
                                                <button class="btn btn-outline-primary btn-sm btn-bag" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                        data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                                        data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="addToCart(${list.bookId})"><i class="icon-bag"></i>
                                                </button>
                                            </c:if>
                                        </div>
                                    </sec:authorize>
                                    <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                                        <div class="product-buttons">
                                            <button class="btn btn-outline-secondary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                                <i class="icon-heart"></i>
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                                <i class="icon-bag"></i>
                                            </button>
                                        </div>
                                    </sec:authorize>

                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="padding-top-2x padding-bottom-2x">
        <div class="container">
            <h3 class="text-center mb-30"><spring:message code="shop.cbcs.nt"/></h3>
            <ul class="nav nav-tabs justify-content-center" role="tablist">
                <li class="nav-item"><a class="nav-link active" href="#nsc" data-toggle="tab" role="tab"><spring:message code="shop.cbcs.sc"/></a></li>
                <li class="nav-item"><a class="nav-link" href="#ntc" data-toggle="tab" role="tab"><spring:message code="shop.cbcs.tc"/></a></li>
            </ul>
            <div class="tab-content mt-30">
                <div class="tab-pane fade show active" id="nsc" role="tabpanel">
                    <div class="row">
                        <!-- 新约简体 -->
                        <c:forEach var="list" items="${bookList.ntSc}">
                            <div class="col-lg-3 col-md-6 col-sm-6 mb-30">
                                <div class="product-card">
                                    <a class="product-thumb" href="book-${list.bookId}#" title="${list.bookTitle}"><img src="${list.bookCover}" alt="Product"></a>
                                    <div class="product-badge text-danger text-left">
                                        <c:choose>
                                            <c:when test="${list.discountIsValid == 1}">
                                                <spring:message code="discount.icon"/><br>
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${list.discountSingleIsValid == 1}">
                                                    <at:bookInfo infoType="discountSingle" enyanBook="${list}"/><br>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>

                                        <c:if test="${list.salesModel == 1}">
                                            <div class="text-right"><spring:message code="book.presale"/></div>
                                        </c:if>
                                    </div>
                                    <h4 class="product-title word-break"><a href="book-${list.bookId}#" title="${list.bookTitle}">${list.bookTitle}</a></h4>
                                    <h4 class="product-price"><at:bookPrice enyanBook="${list}" currency="${currency}" category="true"/></h4>

                                    <sec:authorize access="isAuthenticated() or isRememberMe()">
                                        <div class="product-buttons">
                                            <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                    data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                                    data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${list.bookId})"><i class="icon-heart"></i></button>
                                            <c:if test="${list.bookType != 1}">
                                                <button class="btn btn-outline-primary btn-sm btn-bag" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                        data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                                        data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="addToCart(${list.bookId})"><i class="icon-bag"></i>
                                                </button>
                                            </c:if>
                                        </div>
                                    </sec:authorize>
                                    <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                                        <div class="product-buttons">
                                            <button class="btn btn-outline-secondary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                                <i class="icon-heart"></i>
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                                <i class="icon-bag"></i>
                                            </button>
                                        </div>
                                    </sec:authorize>

                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </div>
                <div class="tab-pane fade" id="ntc" role="tabpanel">
                    <div class="row">
                        <!-- 新约繁体 -->
                        <c:forEach var="list" items="${bookList.ntTc}">
                            <div class="col-lg-3 col-md-6 col-sm-6 mb-30">
                                <div class="product-card">
                                    <a class="product-thumb" href="book-${list.bookId}#" title="${list.bookTitle}"><img src="${list.bookCover}" alt="Product"></a>
                                    <div class="product-badge text-danger text-left">
                                        <c:choose>
                                            <c:when test="${list.discountIsValid == 1}">
                                                <spring:message code="discount.icon"/><br>
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${list.discountSingleIsValid == 1}">
                                                    <at:bookInfo infoType="discountSingle" enyanBook="${list}"/><br>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>

                                        <c:if test="${list.salesModel == 1}">
                                            <div class="text-right"><spring:message code="book.presale"/></div>
                                        </c:if>
                                    </div>
                                    <h4 class="product-title word-break"><a href="book-${list.bookId}#" title="${list.bookTitle}">${list.bookTitle}</a></h4>
                                    <h4 class="product-price"><at:bookPrice enyanBook="${list}" currency="${currency}" category="true"/></h4>

                                    <sec:authorize access="isAuthenticated() or isRememberMe()">
                                        <div class="product-buttons">
                                            <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                    data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                                    data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${list.bookId})"><i class="icon-heart"></i></button>
                                            <c:if test="${list.bookType != 1}">
                                                <button class="btn btn-outline-primary btn-sm btn-bag" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                        data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                                        data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="addToCart(${list.bookId})"><i class="icon-bag"></i>
                                                </button>
                                            </c:if>
                                        </div>
                                    </sec:authorize>
                                    <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                                        <div class="product-buttons">
                                            <button class="btn btn-outline-secondary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                                <i class="icon-heart"></i>
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                                <i class="icon-bag"></i>
                                            </button>
                                        </div>
                                    </sec:authorize>

                                </div>
                            </div>
                        </c:forEach>

                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>

</body>
</html>