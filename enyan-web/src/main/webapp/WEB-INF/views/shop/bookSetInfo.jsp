<%--
  User: Aaron
  Date: 2017/12/4
  Time: 下午6:56
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="s" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>
        ${obj.setName}-<spring:message code="shop.title"/>
    </title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
    <style>
        .istext-overflow {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
            height: 80px;
        }
    </style>
</head>
<!-- Body-->
<body>

<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->

<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <!-- 开始页面 -->
    <div class="offcanvas-wrapper">
        <c:if test="${not empty obj.bannerUrl}">
            <section class="container">
                <div class="owl-carousel" data-owl-carousel="{ &quot;dots&quot;: true, &quot;loop&quot;: true, &quot;autoplay&quot;: true, &quot;data-wrap&quot;: true, &quot;autoplayTimeout&quot;: 4000 }">
                    <figure><img src="${obj.bannerUrl}" alt="Image"></figure>
                </div>
            </section>
        </c:if>
        <!-- 书单 -->
        <section class="container padding-top-2x padding-bottom-2x">
            <h4 class="text-center"><span>${obj.setName}</span></h4>
            <c:if test="${not empty obj.setAbstract}">
                <div class="row">
                    <div class="col-2"></div>
                    <div class="col-xl-auto col-xl-8">
                        <p class="text-overflow pt-3 pl-5 pr-5" style="line-height: 2;">${obj.setAbstract}</p>
                    </div>
                </div>
            </c:if>
            <!-- 书单列表 -->

        </section>
        <div class="container padding-bottom-3x mb-1">
            <div class="row">
                <!-- Products-->
                <c:forEach var="list" items="${bookList}">
                    <div class="col-xl-6 col-lg-6">
                        <div class="product-card product-list"><a class="product-thumb" href="book-${list.bookId}#">
                            <!-- <div class="product-badge text-danger">6折</div> -->
                            <img src="${list.bookCover}" alt="Product"></a>
                            <div class="product-info">
                                <h3 class="product-title"><a href="book-${list.bookId}#">${list.bookTitle}</a></h3>
                                <at:bookPrice enyanBook="${list}" currency="${currency}" bookSet="true"/>
                                <div class="pt-1">
                                    <div class="d-inline text-muted">
                                        <c:if test="${not empty list.author}">
                                            <c:set var="authors" value="${fn:split(list.author, '#')}" />
                                            <c:forEach var="author" items="${authors}">
                                                <a class="navi-link" href="searchBook?searchText=${author}">${author}</a>
                                            </c:forEach>
                                        </c:if>
                                    </div>
                                </div>
                                <c:if test="${list.showPublisher == 1}">
                                    <div>
                                        <div class="d-inline text-muted">
                                            <a class="navi-link" href="category-0-grid-0-${list.publisherId}-${isFreeType}-${isPresaleType}-${isDiscountType}-${isSpecialOffer}-0-0">
                                                <at:publisherName publisherId="${list.publisherId}"/>
                                            </a>
                                        </div>
                                    </div>
                                </c:if>
                                <p class="hidden-xs-down istext-overflow text-muted pt-3">${list.recommendedCaption}</p>
                                <sec:authorize access="isAuthenticated() or isRememberMe()">
                                    <div class="product-buttons">
                                        <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                                data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${list.bookId})"><i class="icon-heart"></i></button>
                                        <c:if test="${list.bookType != 1}">
                                            <button class="btn btn-outline-primary btn-sm btn-bag" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                    data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                                    data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="addToCart(${list.bookId})"><i class="icon-bag"></i>
                                            </button>
                                        </c:if>
                                    </div>
                                </sec:authorize>
                                <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                                    <div class="product-buttons">
                                        <button class="btn btn-outline-secondary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                            <i class="icon-heart"></i>
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                            <i class="icon-bag"></i>
                                        </button>
                                    </div>
                                </sec:authorize>
                            </div>
                        </div>
                    </div>
                </c:forEach>

            </div>
            <div class="pt-2">
                <!-- 页码 -->
                <nav class="pagination">
                    ${pageLand}
                </nav>
            </div>
        </div>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
<script>
    function changeSort(sel)
    {
        var url = "/blogs?order="+sel.value;
        //alert(url);
        location.href = url;//location.href实现客户端页面的跳转
    }
    function like(){
        var blogLikes = $("#blogLikes").text();
        url="/blogLike?${_csrf.parameterName}=${_csrf.token}";
        $("#blogLikes").text(Number(blogLikes) + 1);
        $('#blogLikeBtn').addClass('btn-outline-primary')
        $('#blogLikeBtn').removeAttr("onclick");

        $.jpost(url, {
            "blogTitle":${obj.blogId}
        }).then(res => {
            //console.log(res);
            //$('#myModal').modal('hide');
            if(res.success){
                //alert("sss")
                // blogLikes.innerHTML=blogLikes + 1;

                //alert(res.successMessage);

            }else{
                alert(res.errorMessages[0]);
            }
            //alert(res.result);
            //window.location.reload();
            //windows.href("");

        });
    }
</script>
</body>
</html>