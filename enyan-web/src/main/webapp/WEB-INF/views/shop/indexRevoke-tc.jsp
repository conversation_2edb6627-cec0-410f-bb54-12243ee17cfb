<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<!-- Page Title-->
<%--
<div class="page-title">
    <div class="container">
        <div class="column">
            <h1>賬戶註銷</h1>
        </div>
        <div class="column">
            <ul class="breadcrumbs">
                <li><a href="/">首頁</a>
                </li>
                <li class="separator">&nbsp;</li>
                <li><a href="/myCenter">個人中心</a>
                </li>
                <li class="separator">&nbsp;</li>
                <li>賬戶註銷</li>
            </ul>
        </div>
    </div>
</div>--%>
<!-- Page Content-->
<div class="container padding-top-3x padding-bottom-3x mb-2">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <form:form action="revokeAction?${_csrf.parameterName}=${_csrf.token}" modelAttribute="authUser" method="post" enctype="multipart/form-data" role="form" cssClass="close-box">
                <h4 class="margin-bottom-2x text-center">重要提示</h4>
                <p class="margin-bottom-1x">註銷恩道電子書帳號是不可恢復的操作，在您申請註銷前，請充分閱讀、理解並同意下列事項：</p>
                <ol class="list-unstyled">
                    <li><span class="text-primary text-medium">1. 所有恩道平台均無法繼續使用本賬號</span>
                        <p>註銷賬號後，您將無法在所有恩道平台（網站/客戶端）登錄、使用本帳號。已經登錄本賬號的設備將自動登出賬號。</p>
                    </li>
                    <li><span class="text-primary text-medium">2. 所有已購電子書和贈書兌換碼將視為自動放棄</span>
                        <p>請您務必在註銷前處理未送出的贈書兌換碼。</p>
                    </li>
                    <li><span class="text-primary text-medium">3. 帳號相關信息將被清空且無法恢復</span>
                        <p>包括但不限於本賬號的個人資料、閱讀數據（讀書/靈修進度、划線筆記、書簽等）、購書記錄、贈書記錄、收藏數據等。建議您在最終確定註銷前自行備份本帳號相關的所有重要信息。</p>
                    </li>
                    <li><span class="text-primary text-medium">4. 帳號註銷後無法找回</span>
                        <p>如果您使用相同的電子郵箱再次註冊，會以新的用戶身份進行登錄，依舊無法找回之前的帳號信息。</p>
                    </li>
                </ol>
                <div class="d-flex flex-wrap justify-content-between">
                    <label class="custom-control custom-checkbox">
                        <input class="custom-control-input" type="checkbox" id="agree" onchange="warning()"><span class="custom-control-indicator"></span><span class="custom-control-description" >我已閱讀並接受以上事項<br><p class="text-primary mt-1" id="warning">您需要接受以上條款，方能進行註銷</p></span>
                    </label>
                </div>
                <c:if test='${isSaveError }'>
                    <div class="form-group input-group">
                        <h6 class="text-danger">${msg}</h6>
                    </div>
                </c:if>
                <div class="form-group input-group">
                    <input class="form-control" type="email" placeholder="郵箱地址" required name="email"><span class="input-group-addon"><i class="icon-mail"></i></span>
                </div>
                <div class="form-group input-group">
                    <input class="form-control" type="password" placeholder="密碼" required name="userPassword"><span class="input-group-addon"><i class="icon-lock"></i></span>
                </div>
                <div class="text-center">
                    <button class="btn btn-primary margin-bottom-none" type="submit" id="pay" disabled onclick="javascript:return checkRevoke()">申請註銷</button>
                </div>
            </form:form>
        </div>
    </div>
</div>
<script>
    function warning(){
        var checkAllBox = document.getElementById("agree");
        var displayWarning = document.getElementById("warning");
        var pay = document.getElementById("pay");
        if(checkAllBox.checked){
            displayWarning.innerHTML="";
            pay.disabled = false;
        }else{
            displayWarning.innerHTML='您需要接受以上條款，方能進行註銷';
            pay.disabled = true;
        }
    }
    function checkRevoke() {
        let msg = "點擊確認註銷，您的帳號將被立即註銷，無法撤銷，請謹慎操作。";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>