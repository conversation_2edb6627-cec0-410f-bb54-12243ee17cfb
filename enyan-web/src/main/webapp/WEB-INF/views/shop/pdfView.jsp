<%--
  User: Aaron
  Date: 2018/3/28
  Time: 上午9:13
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<html>
<head>
    <title>View-<spring:message code="shop.title"/></title>
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <script src="<c:url value='/statics/js/pdf/pdfobject.min.js' />"></script>
    <style>
        /*
        PDFObject appends the classname "pdfobject-container" to the target element.
        This enables you to style the element differently depending on whether the embed was successful.
        In this example, a successful embed will result in a large box.
        A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
        */

        .pdfobject-container {
            width: 100%;
            max-width: 600px;
            height: 600px;
            margin: 2em 0;
        }

        .pdfobject { border: solid 1px #666; }
        #results { padding: 1rem; }
        .hidden { display: none; }
        .success { color: #4F8A10; background-color: #DFF2BF; }
        .fail { color: #D8000C; background-color: #FFBABA; }
    </style>
    <jsp:include page="track-info.jsp"/>
</head>
<body>

<script>
    PDFObject.embed("<at:bookImage imageName='${pdf}.pdf' scope='sample'/>");
</script>
<%--
<object data="pdfFiles/interfaces.pdf" type="application/pdf">
    <embed src="<c:url value='/book_image/sample/${pdf}.pdf' />" width="100%" height="100%" alt="pdf" pluginspage="http://www.adobe.com/products/acrobat/readstep2.html">
</object>--%>
<%--
<div id="pdf" class=" pdfobject-container" style="position: relative; overflow: auto;">
    <div style="overflow: hidden; position: absolute; top: 0; right: 0; bottom: 0; left: 0;">
        <iframe src="<c:url value='/statics/js/pdf/other/viewer.html' />?file=<c:url value='/book_image/sample/${pdf}.pdf' />#navpanes=0&amp;toolbar=0&amp;statusbar=0&amp;view=FitV&amp;pagemode=thumbs&amp;page=2" style="border: none; width: 100%; height: 100%;" frameborder="0"></iframe>
    </div>
</div>

<script>
    var options = {
        pdfOpenParams: {
            navpanes: 0,
            toolbar: 0,
            statusbar: 0,
            view: "FitV",
            pagemode: "thumbs",
            page: 2
        },
        forcePDFJS: true,
        PDFJS_URL: "<c:url value='/statics/js/pdf/other/viewer.html' />"
    };

    var myPDF = PDFObject.embed("<c:url value='/book_image/sample/${pdf}.pdf' />", "#pdf", options);

    //var el = document.querySelector("#results");
    //el.setAttribute("class", (myPDF) ? "success" : "fail");
    //el.innerHTML = (myPDF) ? "PDFObject was successful!" : "Uh-oh, the embed didn't work.";
</script>
--%>



</body>
</html>
