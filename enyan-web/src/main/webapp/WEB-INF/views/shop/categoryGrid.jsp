<%--
  User: <PERSON>
  Date: 2017/12/4
  Time: 下午6:56
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="s" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>
        <c:choose>
            <c:when test="${not empty isSearch}">
                <spring:message code="search.label"/> ${explan}
            </c:when>
            <c:otherwise>
                <c:if test="${not empty isInCn}">
                    <c:choose>
                        <c:when test="${isInCn == 0}">
                        </c:when>
                        <c:when test="${isInCn == 1}">
                            <spring:message code="shop.nav.book.sc"/>-
                        </c:when>
                        <c:when test="${isInCn == 3}">
                            <spring:message code="shop.nav.book.eng"/>-
                        </c:when>
                        <c:otherwise>
                            <spring:message code="shop.nav.book.tc"/>-
                        </c:otherwise>
                    </c:choose>
                </c:if>
                <c:choose>
                    <c:when test="${pageContext.response.locale == 'zh_CN'}">
                        ${explan.name}
                    </c:when>
                    <c:when test="${pageContext.response.locale == 'en_US'}">
                        ${explan.third}
                    </c:when>
                    <c:otherwise>
                        ${explan.other}
                    </c:otherwise>
                </c:choose>
            </c:otherwise>
        </c:choose>
        -<spring:message code="shop.title"/>
    </title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>

<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->

<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-1 padding-top-2x">
        <div class="row">
            <!-- Products-->
            <div class="col-xl-10 col-lg-10 order-lg-2">
                <!-- Shop Toolbar-->
                <div class="shop-toolbar padding-bottom-1x mb-2">
                    <div class="column">
                        <div class="shop-sorting text-muted">
                            <label for="sortingSelect" class="navbar-back">
                                <spring:message code="order.label"/> :</label>
                            <select class="form-control text-muted" id="sortingSelect" onchange="changeSort(this);">
                                <c:choose>
                                    <c:when test="${1==isFreeType}">
                                        <option value="0" <c:if test="${param.order==0}">selected</c:if>><spring:message code="order.new"/> </option>
                                        <option value="3" <c:if test="${param.order==3}">selected</c:if>><spring:message code="order.sales.desc"/> </option>
                                        <option value="5" <c:if test="${param.order==5}">selected</c:if>><spring:message code="order.default"/> </option>
                                        <%--<option value="4" <c:if test="${param.order==4}">selected</c:if>><spring:message code="order.sales.asc"/> </option>--%>
                                    </c:when>
                                    <c:otherwise>
                                        <option value="5" <c:if test="${param.order==5}">selected</c:if>><spring:message code="order.default"/> </option>
                                        <option value="0" <c:if test="${param.order==0}">selected</c:if>><spring:message code="order.new"/> </option>
                                        <option value="3" <c:if test="${param.order==3}">selected</c:if>><spring:message code="order.sales.desc"/> </option>
                                        <option value="1" <c:if test="${param.order==1}">selected</c:if>><spring:message code="order.price.desc"/> </option>
                                        <option value="2" <c:if test="${param.order==2}">selected</c:if>><spring:message code="order.price.asc"/> </option>
                                        <%--<option value="4" <c:if test="${param.order==4}">selected</c:if>><spring:message code="order.sales.asc"/> </option>--%>
                                    </c:otherwise>
                                </c:choose>

                                <%--<option value="3" <c:if test="${param.order==3}">selected</c:if>><spring:message code="order.price"/> </option>--%>
                            </select>
                            <span class="text-muted navbar-back"><spring:message code="print"/> :&nbsp;</span><span class="navbar-back">${pageDescription} </span>
                        </div>
                    </div>
                    <div class="column navbar-back">
                        <div class="shop-view">
                            <c:if test="${empty isSearch}">
                                <a class="grid-view active" href="category-${categoryId}-grid-${isInCn}-${publisherId}-${isFreeType}-${isPresaleType}-${isDiscountType}-${isSpecialOffer}-0-0"><span></span><span></span><span></span></a>
                                <a class="list-view" href="category-${categoryId}-list-${isInCn}-${publisherId}-${isFreeType}-${isPresaleType}-${isDiscountType}-${isSpecialOffer}-0-0"><span></span><span></span><span></span></a>
                            </c:if>
                        </div>
                    </div>
                </div>

                <c:if test="${not empty isSearch}">
                    <div class="padding-bottom-1x">
                        <c:choose>
                            <c:when test="${empty bookList}">
                                <p class="text-muted"><spring:message code="search.null"/></p>
                                <%--<h6 class="text-danger">由于您的搜索关键词区分简繁体，此搜索结果可能不完整。搜索简体书请使用简体中文，搜索繁体书请使用繁体中文。</h6>
                                <h6 class="text-medium">抱歉，沒有找到相關記錄，您可以換個關鍵詞試試！</h6>--%>
                            </c:when>
                        </c:choose>
                    </div>
                </c:if>


                <!-- Products Grid-->
                <div class="isotope-grid cols-3 mb-2">
                    <div class="gutter-sizer"></div>
                    <div class="grid-sizer"></div>
                    <!-- Product ON SALE-->

                    <c:forEach var="list" items="${bookList}">
                    <div class="grid-item">
                        <div class="product-card">
                            <a class="product-thumb" href="book-${list.bookId}#">
                                <img src="${list.bookCover}" alt="Product">
                            </a>

                            <div class="product-badge text-danger text-left">
                                <c:choose>
                                    <c:when test="${list.discountIsValid == 1}">
                                        <spring:message code="discount.icon"/><br>
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${list.discountSingleIsValid == 1}">
                                            <at:bookInfo infoType="discountSingle" enyanBook="${list}"/><br>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>

                                <c:if test="${list.salesModel == 1}">
                                    <div class="text-right"><spring:message code="book.presale"/></div>
                                </c:if>
                            </div>
                            <h4 class="product-title"><a href="book-${list.bookId}#">${list.bookTitle}</a></h4>
                            <h4 class="product-price">
                                <at:bookPrice enyanBook="${list}" currency="${currency}" category="true"/>
                            </h4>
                            <sec:authorize access="isAuthenticated() or isRememberMe()">
                                <div class="product-buttons">
                                    <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                            data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                            data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${list.bookId})"><i class="icon-heart"></i></button>
                                    <c:if test="${list.bookType != 1}">
                                        <button class="btn btn-outline-primary btn-sm btn-bag" data-toast data-toast-type="danger" data-toast-position="topRight"
                                                data-toast-title="${list.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                                data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="addToCart(${list.bookId})"><i class="icon-bag"></i>
                                        </button>
                                    </c:if>
                                </div>
                            </sec:authorize>
                            <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                                <div class="product-buttons">
                                    <button class="btn btn-outline-secondary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                        <i class="icon-heart"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                                        <i class="icon-bag"></i>
                                    </button>
                                </div>
                            </sec:authorize>
                        </div>
                    </div>
                    </c:forEach>


                </div>
                <!-- 页码 -->
                <nav class="pagination">
                    ${pageLand}
                </nav>
            </div>

            <!-- Sidebar   begin-->
            <div class="col-xl-2 col-lg-2 order-lg-1">
                <aside class="sidebar">
                    <div class="padding-top-2x hidden-lg-up"></div>
                    <!-- 侧边目录分类 -->
                    <section class="widget widget-categories">
                        <h3 class="widget-title"><spring:message code="book.category"/> </h3>
                        <ul>
                            <c:choose>
                                <c:when test="${pageContext.response.locale == 'zh_CN'}">
                                    <c:forEach var="category" items="${categoryList}">
                                        <li class="has-children <c:if test='${category.value == categoryId}'>expanded</c:if>"><a href="category-${category.value}-grid-${isInCn}-${publisherId}-${isFreeType}-${isPresaleType}-${isDiscountType}-${isSpecialOffer}-0-0">${category.name}</a><span></span></li>
                                    </c:forEach>
                                </c:when>
                                <c:when test="${pageContext.response.locale == 'en_US'}">
                                    <c:forEach var="category" items="${categoryList}">
                                        <li class="has-children <c:if test='${category.value == categoryId}'>expanded</c:if>"><a href="category-${category.value}-grid-${isInCn}-${publisherId}-${isFreeType}-${isPresaleType}-${isDiscountType}-${isSpecialOffer}-0-0">${category.third}</a><span></span></li>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <c:forEach var="category" items="${categoryList}">
                                        <li class="has-children <c:if test='${category.value == categoryId}'>expanded</c:if>"><a href="category-${category.value}-grid-${isInCn}-${publisherId}-${isFreeType}-${isPresaleType}-${isDiscountType}-${isSpecialOffer}-0-0">${category.other}</a><span></span></li>
                                    </c:forEach>
                                </c:otherwise>
                            </c:choose>
                        </ul>
                    </section>
                </aside>
            </div>
            <!-- Sidebar   end-->

        </div>
    </div>
    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
    <jsp:include page="ad-info.jsp"/>
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
<script>
    function changeSort(sel) {
        var url = "/category-${categoryId}-grid-${isInCn}-${publisherId}-${isFreeType}-${isPresaleType}-${isDiscountType}-${isSpecialOffer}-0-0?order="+sel.value;
        //alert(url);
        <c:if test="${not empty isSearch}">
            url = "searchAuthor?order="+sel.value+"&searchText=${book.searchText}";
        </c:if>
        location.href = url;//location.href实现客户端页面的跳转
    }
</script>
</body>
</html>