<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<li class="menu-list" id="menu_web"><a href="#"><i class="fa fa-th-list"></i> <span><spring:message code="menu.web"/></span></a>
    <ul class="sub-menu-list">
        <li id="bookList"><a href="<c:url value='/book/books' />"><spring:message code="book.list"/></a></li>
        <li id="spiritList"><a href="<c:url value='/spirit/spirits' />">灵修书籍</a></li>
        <li id="publicationsList"><a href="<c:url value="/lcp/publications" />"><spring:message code="publication.list"/></a></li>
        <li id="categoryList"><a href="<c:url value="/category/categories" />" ><spring:message code="category.list"/></a></li>
        <li id="publisherList"><a href="<c:url value="/publisher/publishers" />"><spring:message code="publisher.list"/></a></li>
        <li id="discountList"><a href="<c:url value="/discount/discounts" />"><spring:message code="discount.list"/></a></li>
        <li id="configList"><a href="<c:url value="/config/configs" />"><spring:message code="config.list"/></a></li>
        <li id="imgList"><a href="<c:url value="/adminImg/imgs" />"><spring:message code="img.list"/></a></li>
        <%--<li id="imgList1"><a href="<c:url value="/adminImg/imgs?imageType=1" />">PDF列表</a></li>--%>
        <li id="redeemCodeList"><a href="<c:url value="/adminRedeemCode/codes" />"><spring:message code="redeemCode.list"/></a></li>
        <li id="couponList"><a href="<c:url value="/coupon/list" />"><spring:message code="coupon.list"/></a></li>
        <li id="blogList"><a href="<c:url value="/blog/list" />">Blog列表</a></li>
        <li id="readingList"><a href="<c:url value="/reading/list" />"><spring:message code="reading.list"/></a></li>
        <li id="bannerList"><a href="<c:url value="/banner/list" />"><spring:message code="banner.list"/></a></li>
        <li id="dailyWordsList"><a href="<c:url value="/dailyWords/list" />"><spring:message code="daily.words.list"/></a></li>
        <li id="bookSetList"><a href="<c:url value="/bookSetAdmin/list" />"><spring:message code="bookSet.list"/></a></li>
        <li id="bookListList"><a href="<c:url value="/bookListAdmin/list" />"><spring:message code="bookList.list"/></a></li>
    </ul>
</li>


<li class="menu-list" id="menu_operation"><a href="#"><i class="fa fa-th-list"></i> <span>运维管理</span></a>
    <ul class="sub-menu-list">
        <li id="userDevicesList"><a href="<c:url value='/admin/userDevices' />">设备管理</a></li>
        <li id="feedbackAdminList"><a href="<c:url value='/feedbackAdmin/list' />">反馈管理</a></li>
        <li id="commentList"><a href="<c:url value='/commentAdmin/list' />">评论管理</a></li>
        <li id="highlightsList"><a href="<c:url value='/highlightsAdmin/list' />">用户划线</a></li>
    </ul>
</li>

<li class="menu-list" id="menu_info"><a href="#"><i class="fa fa-user"></i> <span><spring:message code="menu.info"/></span></a>
    <ul class="sub-menu-list">
        <li id="passwd"><a href="<c:url value="/user/updatePwd" />"><spring:message code="passwd.update"/></a></li>

    </ul>
</li>
<li class="menu-list" id="menu_lang"><a href="#"><i class="fa fa-user"></i> <span>语言/language</span></a>
    <ul class="sub-menu-list">
        <c:choose>
            <c:when test="${pageContext.response.locale == 'zh_CN'}">
                <%--                    <li><i class="fa fa-stack-exchange"></i> <span>简体 </span></li>--%>
                <li><a href="<c:url value="/admin" />?locale=zh_HK"><i class="fa fa-stack-exchange"></i> <span>切換繁體 </span></a></li>
                <li><a href="<c:url value="/admin" />?locale=en_US"><i class="fa fa-stack-exchange"></i> <span>English </span></a></li>
            </c:when>
            <c:when test="${pageContext.response.locale == 'zh_HK'}">
                <li><a href="<c:url value="/admin" />?locale=zh_CN"><i class="fa fa-stack-exchange"></i> <span>切换简体 </span></a></li>
                <%--                    <li><i class="fa fa-stack-exchange"></i> <span>繁體 </span></li>--%>
                <li><a href="<c:url value="/admin" />?locale=en_US"><i class="fa fa-stack-exchange"></i> <span>English </span></a></li>
            </c:when>
            <c:otherwise>
                <li><a href="<c:url value="/admin" />?locale=zh_CN"><i class="fa fa-stack-exchange"></i> <span>切换简体 </span></a></li>
                <li><a href="<c:url value="/admin" />?locale=zh_HK"><i class="fa fa-stack-exchange"></i> <span>切換繁體 </span></a></li>
                <%--                    <li><i class="fa fa-stack-exchange"></i> <span>English </span></li>--%>
            </c:otherwise>
        </c:choose>
    </ul>
</li>

