<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!-- Placed js at the end of the document so the pages load faster -->
<script src="<%=basePath%>statics/js/jquery-1.10.2.min.js"></script>
<script src="<%=basePath%>statics/js/jquery-ui-1.9.2.custom.min.js"></script>
<script src="<%=basePath%>statics/js/jquery-migrate-1.2.1.min.js"></script>
<script src="<%=basePath%>statics/js/bootstrap.min.js"></script>
<script src="<%=basePath%>statics/js/modernizr.min.js"></script>
<script src="<%=basePath%>statics/js/jquery.nicescroll.js"></script>

<!--gritter script-->
<script type="text/javascript" src="<%=basePath%>statics/js/gritter/js/jquery.gritter.js"></script>
<script src="<%=basePath%>statics/js/gritter/js/gritter-init.js" type="text/javascript"></script>

<!--common scripts for all pages-->
<script src="<%=basePath%>statics/js/scripts.js"></script>

<%--
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>


<c:if test="${result!=null}">

    <!--modal start-->
    <section class="panel">
        <header class="panel-heading">
            Modal Dialogs
            <span class="tools pull-right">
                                        <a class="fa fa-chevron-down" href="javascript:;"></a>

                                        <a class="fa fa-times" href="javascript:;"></a>
                                    </span>
        </header>
        <div class="panel-body">
            <a href="#successAlert" data-toggle="modal" class="btn btn-success">
                Dialog
            </a>
            <a href="#warnAlert" data-toggle="modal" class="btn btn-warning">
                Confirm
            </a>
            <a href="#errorAlert" data-toggle="modal" class="btn btn-danger" id="errorAlertHref">
                Alert !
            </a>
            <!-- Modal -->
            <div aria-hidden="true" aria-labelledby="myModalLabel" role="dialog" tabindex="-1" id="successAlert" class="modal fade">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button aria-hidden="true" data-dismiss="modal" class="close" type="button">×</button>
                            <h4 class="modal-title">Modal Tittle</h4>
                        </div>
                        <div class="modal-body">

                            Body goes here...

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-success">Save changes</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- modal -->
            <!-- Modal -->
            <div aria-hidden="true" aria-labelledby="myModalLabel" role="dialog" tabindex="-1" id="warnAlert" class="modal fade">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button aria-hidden="true" data-dismiss="modal" class="close" type="button">×</button>
                            <h4 class="modal-title">Modal Tittle</h4>
                        </div>
                        <div class="modal-body">

                                ${result.successMessage}
                                ${result.errorMessages[0]}

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-warning"> Confirm</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- modal -->
            <!-- Modal -->
            <div aria-hidden="true" aria-labelledby="myModalLabel" role="dialog" tabindex="-1" id="errorAlert" class="modal fade">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button aria-hidden="true" data-dismiss="modal" class="close" type="button">×</button>
                            <h4 class="modal-title">Modal Tittle</h4>
                        </div>
                        <div class="modal-body">

                                ${result.successMessage}
                                ${result.errorMessages[0]}

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-danger"> Ok</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- modal -->

        </div>
    </section>
    <script>
        $(document).ready(function () {  ${'#errorAlertHref'}[0].click(); });
        $(document).ready(function () {  ${"#errorAlertHref"}.trigger("click"); });
    </script>
    <c:if test="${result.success}">
        <script>
            ${"#successAlert"}.trigger("click");
            alert("success");
        </script>
    </c:if>
    <c:if test="${!result.success}">
        <script>
            ${"#errorAlert"}.trigger("click");
            alert("error");
        </script>
    </c:if>
    <!--modal end-->
</c:if>
--%>