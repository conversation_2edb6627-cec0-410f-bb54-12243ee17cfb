<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>TTi Academics(Peer Assessment System)</title>

    <!-- Bootstrap core CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css" integrity="sha384-WskhaSGFgHYWDcbwN70/dfYBj47jz9qbsMId/iRN3ewGhXQFZCSftd1LZCfmhktB" crossorigin="anonymous">

    <!-- Custom styles for this template -->
    <style>
    /* Space out content a bit */
        body {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
        }

        /* Everything but the jumbotron gets side spacing for mobile first views */
        .header,
        .marketing,
        .footer {
        padding-right: 1rem;
        padding-left: 1rem;
        }

        /* Custom page header */
        .header {
        padding-bottom: 1rem;
        border-bottom: .05rem solid #e5e5e5;
        }
        /* Make the masthead heading the same height as the navigation */
        .header h3 {
        margin-top: 0;
        margin-bottom: 0;
        line-height: 3rem;
        }

        /* Custom page footer */
        .footer {
        padding-top: 1.5rem;
        color: #777;
        border-top: .05rem solid #e5e5e5;
        }

        /* Customize container */
        @media (min-width: 48em) {
        .container {
            max-width: 110rem;
        }
        }
        .container-narrow > hr {
        margin: 2rem 0;
        }

        /* Main marketing message and sign up button */
        .jumbotron {
        text-align: center;
        border-bottom: .05rem solid #e5e5e5;
        }
        .jumbotron .btn {
        padding: .75rem 1.5rem;
        font-size: 1.5rem;
        }

        /* Supporting marketing content */
        .marketing {
        margin: 3rem 0;
        }
        .marketing p + h4 {
        margin-top: 1.5rem;
        }

        /* Responsive: Portrait tablets and up */
        @media screen and (min-width: 48em) {
        /* Remove the padding we set earlier */
        .header,
        .marketing,
        .footer {
            padding-right: 0;
            padding-left: 0;
        }
        /* Space out the masthead */
        .header {
            margin-bottom: 2rem;
        }
        /* Remove the bottom border on the jumbotron for visual effect */
        .jumbotron {
            border-bottom: 0;
        }
        }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header clearfix">
        <h3 class="text-muted">签到系统</h3>
      </div>
      <div class="row marketing">
        <table class='table table-hover table-bordered small sticky-header'>
          <thead>
          <tr bgcolor="#FFFFFF">
            <th scope="col" width="40px"><a href="students" >学员列表</a></th>
            <th scope="col" width="40px"><a href="enrolls" >签到列表</a></th>
            <th scope="col" width="40px"><a href="import" ></a></th>
            <th scope="col" width="40px"><a href="print" target="_blank">打印</a></th>
          </tr>
          </thead>
        </table>
      </div>
      <form class="form-horizontal" method="POST" action="">
        <!-- 学生ID: <input type="text" name="enroll_date"> -->
      <div class="row marketing">
          <table class='table table-hover table-bordered small sticky-header'>
            <thead>
                <tr bgcolor="#FFFFFF">
                  <!--<th scope="col" width="40px">报到</th>-->
                  <!--<th scope="col" width="40px">学生ID</th>-->
                  <!--<th scope="col" width="40px">姓(Last)</th>
                  <th scope="col" width="40px">名(First)</th>-->
                  <th scope="col" width="40px">学员名</th>
                  <th scope="col" width="40px">电子邮件</th>
                  <th scope="col" width="40px">密码</th>
                  <th scope="col" width="40px">报到码</th>
                  <th scope="col" width="40px">班级</th>
                  <th scope="col" width="40px">宿舍</th>
                  <th scope="col" width="40px">组名</th>
                  <th scope="col" width="40px">是否组长</th>
                  <th scope="col" width="40px">讨论房间</th>
                </tr>
            </thead>
            <tbody>
              <!--<tr>
                <td><input type="text" name="lastName"></td>
                <td><input type="text" name="firstName"></td>
                <td><input type="text" name="email"></td>
                <td><input type="text" name="passwd"></td>
                <td><input type="text" name="enroll_code"></td>
                <td><input type="text" name="student_class"></td>
                <td><input type="text" name="housing"></td>
                <td><input type="text" name="team"></td>
                <td><select name="leader">
                  <option value="1">组长</option>
                  <option value="2">副组长</option>
                  <option value="3">组员</option>
                  <option value="4">班长</option>
                </select></td>
                <td><input type="text" name="discussion"></td>
              </tr>
              <tr>
                <p class="float-right"><button type="submit" class="btn btn-primary">加入学生信息</button></p>
              </tr>-->
                <tr th:each="student : ${list}">
                  <!--<td><input type="checkbox" name="{{ student.student_id }}"></td>-->
                  <!--<td>{{ student.student_id }}</td>
                  <td>{{ student.lastName }}</td>
                  <td>{{ student.firstName }}</td>-->
                  <td th:text="${student.lastName+student.firstName}">姓名</td>
                  <td th:text="${student.email}">email</a></td>
                  <td th:text="${student.password}">密码</td>
                  <td th:text="${student.enrollCode}">报到码</td>
                  <td th:text="${student.stuClass}">班级</td>
                  <td th:text="${student.stuHousing}">宿舍</td>
                  <td th:text="${student.stuTeamName}">组名</td>
                  <td th:switch="${student.teamRole}">
                      <p th:case="0">组员</p>
                      <p th:case="1" style="color:teal;">副组长</p>
                      <p th:case="2" style="color:green;">组长</p>
                      <p th:case="3" style="color:#ff00e5;">班长</p>
                      <p th:case="4" style="color:red;">老师</p>
                      <p th:case="5" style="color:red;">同工</p>
                      <p th:case="*">---</p>
                  </td>
                  <td th:text="${student.discussionRoom}">讨论房间</td>
                </tr>
            </tbody>
          </table>
        </form>
      </div>
    </div>
  </div>
    <script>
        (function () {
        'use strict'
        if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
            var msViewportStyle = document.createElement('style')
            msViewportStyle.appendChild(
            document.createTextNode(
                '@-ms-viewport{width:auto!important}'
            )
            )
            document.head.appendChild(msViewportStyle)
        }
        }())
    </script>
    <script src="https://cdn.staticfile.org/jquery/2.1.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/floatthead/2.1.3/jquery.floatThead.js"></script>
    <script type="application/javascript">
      $(document).ready(function() {
        $('.sticky-header').floatThead();
      });
      $('a[data-toggle="tab"]').on('shown.bs.tab', function(){
        $('.sticky-header:visible').floatThead('reflow');
      });
    </script>
  </body>
</html>
