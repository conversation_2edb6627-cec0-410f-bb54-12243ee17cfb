(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c49cbe6c"],{3980:function(t,a,s){},4730:function(t,a,s){"use strict";s.r(a);var e=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"container padding-bottom-3x mb-1 AliPay-container",staticStyle:{width:"1070px"}},[s("div",{staticClass:"row mt-3"},[s("div",{staticClass:"col-sm-12 col-lg-12"},[s("ol",{staticClass:"list-unstyled",staticStyle:{"line-height":"34px"}},[s("li",[s("strong",[t._v(t._s(t.$t("message.orderNum"))+": ")]),s("span",[t._v(t._s(t.payData.orderNum))])]),t._v(" "),s("li",[s("strong",[t._v(t._s(t.$t("message.pay.orderFee"))+": ")]),s("span",{staticStyle:{color:"#f60","font-size":"22px","font-weight":"600"}},[t._v(t._s(t.payData.costHk)),s("span",{staticStyle:{"font-size":"12px"}},[t._v(" HKD")])])]),t._v(" "),s("li",[s("strong",[t._v(t._s(t.$t("message.pay.aliPayIntro")))])]),t._v(" "),s("li",{staticStyle:{"margin-top":"-4px"}},[s("strong",[t._v(t._s(t.$t("message.pay.noRedirectUrlAlert"))+"：")]),t._v(" "),s("a",{staticClass:"btn btn-outline-primary btn-sm",attrs:{href:"javascript:;"},on:{click:t.ConfirmHasPay}},[t._v(t._s(t.$t("message.pay.hasPaySuccess")))])])])])]),t._v(" "),s("div",{staticClass:"table-responsive shopping-cart"},[s("div",{staticClass:"qrcode-img-area"},[s("iframe",{attrs:{id:"qr",src:t.aliPayQRcode,width:"100%",height:"400",frameborder:"0",scrolling:"no"}})])])])},i=[],o=s("7ba0"),n=o["a"],r=(s("48e1"),s("2877")),c=Object(r["a"])(n,e,i,!1,null,"8b192090",null);a["default"]=c.exports},"48e1":function(t,a,s){"use strict";s("3980")},"7ba0":function(t,a,s){"use strict";(function(t){var e=s("3de4"),i=s("ed08");a["a"]={components:{},data:function(){return{payData:{},aliPayQRcode:""}},watch:{"$store.state.app.lang":{handler:function(){Object(i["a"])(this.$t("message.metaTitle.aliPay"))}}},created:function(){Object(i["a"])(this.$t("message.metaTitle.aliPay")),t("footer").css("display","block"),t(".scroll-to-top-btn").css("display","block");var a=sessionStorage.getItem("aliPayData");a?this.payData=JSON.parse(a):(this.payData=this.$route.params.item,sessionStorage.setItem("aliPayData",JSON.stringify(this.$route.params.item))),this.getQRcode()},beforeDestroy:function(){sessionStorage.removeItem("aliPayData")},methods:{getQRcode:function(){var t=this;Object(e["k"])({orderNum:this.payData.orderNum,alipayType:this.payData.alipayType,toRentMonths:this.payData.toRentMonths}).then((function(a){a.success&&(t.aliPayQRcode=a.result.payUrl)}))},ConfirmHasPay:function(){var t=this;Object(e["d"])({orderNum:this.payData.orderNum}).then((function(a){a.success&&t.$router.push({name:"paySuccess",params:{type:t.payData.type}})}))}}}}).call(this,s("1157"))}}]);