(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0b45f6b6"],{2085:function(t,e,a){"use strict";a("4762")},4762:function(t,e,a){},"48a4":function(t,e,a){"use strict";a("7104")},"4f63":function(t,e,a){"use strict";var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"countDown"},[t._v("\n  "+t._s(t._f("zeroFormat")(t.payTimeHours))+" : "+t._s(t._f("zeroFormat")(t.payTimeMinutes))+" : "+t._s(t._f("zeroFormat")(t.payTimeSeconds))+"\n")])},i=[],n=(a("c5f6"),a("c1df")),o=a.n(n),r={props:{time:{type:[String,Number],default:0},index:{type:[String,Number],required:!0},type:{type:String,default:""}},data:function(){return{payTimeHours:"",payTimeMinutes:"",payTimeSeconds:"",timeRange:72e5}},watch:{timeRange:{handler:function(t){t<=0&&(t=0,"rentList"===this.type?this.$nextTick((function(){this.$emit("stopCountDown")})):"rentAdd"===this.type&&this.$router.push({path:"/rent/list"})),this.payTimeHours=o.a.duration(t).hours(),this.payTimeMinutes=o.a.duration(t).minutes(),this.payTimeSeconds=o.a.duration(t).seconds()},immediate:!0}},created:function(){this.timeRange-this.time>=0?this.timeRange-this.time>72e5?this.timeRange=72e5:this.timeRange-=this.time:this.timeRange-this.time<0&&(this.timeRange=0)},mounted:function(){this.countDown()},methods:{countDown:function(){var t=this,e=setInterval((function(){t.timeRange-=1e3,t.timeRange<=0&&clearInterval(e)}),1e3)}}},m=r,c=(a("2085"),a("2877")),u=Object(c["a"])(m,s,i,!1,null,"644df45c",null);e["a"]=u.exports},7104:function(t,e,a){},e906:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container padding-bottom-3x mb-1 padding-top-2x rentPay-container"},[a("h6",[t._v(t._s(t.$t("message.pay.countdown1"))),a("count-down",{attrs:{time:t.remainTime(t.payData.createAt),type:"rentAdd",index:t.payData.orderNum}}),t._v(t._s(t.$t("message.pay.countdown2")))],1),t._v(" "),a("ol",{staticClass:"list-unstyled"},[a("li",{staticClass:"li-item"},[a("span",{staticClass:"text-muted"},[t._v(t._s(t.$t("message.orderNum"))+": ")]),t._v(t._s(t.payData.orderNum))]),t._v(" "),a("li",{staticClass:"li-item"},[a("span",{staticClass:"text-muted"},[t._v(t._s(t.$t("message.orderTime"))+": ")]),t._v(t._s(t._f("dateFormat")(t.payData.createAt)))])]),t._v(" "),a("hr"),t._v(" "),a("p",{staticClass:"title mb-10"},[t._v(t._s(t.$t("message.rentToBuy"))+"·"+t._s(t.$t("message.fangjiaoshi"))+t._s(t._f("rentTypeName")(t.payData.rentType))+t._s(t.$t("message.fullPackage"))+"("+t._s(t._f("rentLangName")(t.payData.rentLang))+")·"+t._s(t._f("autoName")(t.payData.isAuto)))]),t._v(" "),a("p",{staticClass:"autoInfo"},[t._v(t._s(t.$t("message.pay.rangeTime1"))+"：1"+t._s(t.$t("message.pay.rangeTime2")))]),t._v(" "),0===t.payData.isAuto?a("p",{staticClass:"autoInfo mb-10"},[t._v(t._s(t.$t("message.expiredTime"))+"："+t._s(t._f("dateFormat")(t.expiredTime,"YYYY-MM-DD")))]):t._e(),t._v(" "),0===t.payData.isAuto?a("p",{staticClass:"autoInfo endaoRed"},[t._v(t._s(t.$t("message.pay.beforeExpiredToGoOn")))]):t._e(),t._v(" "),1===t.payData.isAuto?a("p",{staticClass:"autoInfo mb-10"},[t._v(t._s(t.$t("message.autoDeductTime"))+"："+t._s(t.autoPayTime))]):t._e(),t._v(" "),1===t.payData.isAuto?a("p",{staticClass:"autoInfo endaoRed"},[t._v(t._s(t.$t("message.pay.autoRentAlert1"))+"HK$"+t._s(t.payData.rentPrice)+"/"+t._s(t.$t("message.pay.autoRentAlert2")))]):t._e(),t._v(" "),a("hr"),t._v(" "),a("div",{staticClass:"cost"},[a("p",[t._v(t._s(t.$t("message.shouldPay"))+"：HK$ "+t._s(t._f("twoDecimal")(t.payData.rentPrice)))]),t._v(" "),a("p",[t._v("("+t._s(t.$store.state.app.currencySign)),a("span",{ref:"addPayCurrency"},[t._v(t._s(t._f("twoDecimal")(t.payData.rentPriceCurrency)))]),t._v(")")])]),t._v(" "),a("pay-type",{attrs:{"is-auto":t.payData.isAuto,"order-num":t.payData.orderNum,"rent-months":1,"cost-hk":t.payData.rentPrice,type:"add","back-name":t.$t("message.pay.backToRentList")}})],1)},i=[],n=a("eae0"),o=n["a"],r=(a("48a4"),a("2877")),m=Object(r["a"])(o,s,i,!1,null,"49d30fcc",null);e["default"]=m.exports},eae0:function(t,e,a){"use strict";(function(t){var s=a("c1df"),i=a.n(s),n=a("4f63"),o=a("cff8"),r=a("ed08");e["a"]={components:{countDown:n["a"],PayType:o["a"]},data:function(){return{payData:{},payTimeHours:"",payTimeMinutes:"",payTimeSeconds:"",expiredTime:"",autoPayTime:""}},computed:{remainTime:function(){return function(t){return i()().diff(t)}}},watch:{"$store.state.app.exchangeRate":{handler:function(t){this.$refs.addPayCurrency.innerHTML=Math.round(this.payData.rentPrice*t*100)/100}},"$store.state.app.lang":{handler:function(){Object(r["a"])(this.$t("message.metaTitle.addPay"))}}},created:function(){Object(r["a"])(this.$t("message.metaTitle.addPay")),t("footer").css("display","block"),t(".scroll-to-top-btn").css("display","block");var e=sessionStorage.getItem("addPayData");e?this.payData=JSON.parse(e):(this.payData=this.$route.params.item,sessionStorage.setItem("addPayData",JSON.stringify(this.$route.params.item))),this.expiredTime=i()(this.payData.createAt).add(1,"months").format("YYYY-MM-DD"),this.autoPayTime=i()(this.expiredTime).subtract(1,"days").format("YYYY-MM-DD")},mounted:function(){},beforeDestroy:function(){sessionStorage.removeItem("addPayData")}}}).call(this,a("1157"))}}]);