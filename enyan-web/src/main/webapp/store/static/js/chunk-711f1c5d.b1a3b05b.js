(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-711f1c5d"],{"1bfc":function(t,e,o){},"47c4":function(t,e,o){"use strict";(function(t){o("28a5");var s=o("5ac9"),a=o("5c96"),n=o("ed08");e["a"]={data:function(){return{bookSetInfo:{setName:"",bannerUrl:"",setAbstract:"",discountValue:0,isDiscountValid:0,price:0,priceDiscount:0},bookSetBooks:[],bookVersions:[],setId:0,totalPage:0,activePage:1,hasLogin:!1,showConfirm:!1,btnDisabled:!1,loading:!1,activeLocale:""}},watch:{"$store.state.app.currency":{immediate:!1,handler:function(t){this.renderBookSetInfo(),this.renderBookSetBooks()}},"$store.state.app.lang":{handler:function(t){this.activeLocale=t,Object(n["a"])(this.bookSetInfo.setName)}}},created:function(){t("footer").css("display","block"),t(".scroll-to-top-btn").css("display","block");var e=window.location.href;this.setId=parseInt(e.split("#")[0].split("bookset-")[1]),this.$cookies.get("Cookie_Lang")&&(this.activeLocale=this.$cookies.get("Cookie_Lang")),this.renderBookSetInfo(),this.renderBookSetBooks()},mounted:function(){},methods:{renderBookSetInfo:function(){var t=this;this.loading=!0,Object(s["j"])({setId:this.setId,needLoginInfo:!0}).then((function(e){t.loading=!1,e.success&&(t.bookSetInfo=e.result,t.bookVersions=e.result.bookVersions||[],Object(n["a"])(t.bookSetInfo.setName),"1"===e.result.user.isLogin&&(t.hasLogin=!0),Object(n["b"])(e.result.user))})).catch((function(e){t.loading=!1,console.log(e)}))},renderBookSetBooks:function(){var t=this;Object(s["i"])({setId:this.setId,page:this.activePage}).then((function(e){e.success&&(t.bookSetBooks=e.result,t.totalPage=e.totalPage)}))},buyAllSetBooks:function(){this.hasLogin?this.$router.push({name:"allBooksInSetOrder",params:{item:{setId:this.setId,isDiscountValid:this.bookSetInfo.isDiscountValid}}}):window.location.href="/login"},changePage:function(t){t!==this.activePage&&(this.activePage=t,this.renderBookSetBooks())},toShowAddCart:function(){this.hasLogin?this.showConfirm=!0:window.location.href="/login"},addAllToCart:function(){var t=this;this.btnDisabled=!0,Object(s["d"])({setId:this.setId}).then((function(e){t.btnDisabled=!1,e.success&&(t.showConfirm=!1,Object(n["d"])("",t.$t("message.bookSet.addAllCartSuccess")))}))},favThisBook:function(t,e){var o=this;Object(s["l"])({bookId:t}).then((function(t){t.success?Object(n["d"])(e,o.$t("message.bookSet.addFavSuccess")):t.success||a["Message"].error({message:o.$t("message.bookSet.operationFail"),customClass:"error-msg",duration:3e3})}))},cartThisBook:function(t,e){var o=this;Object(s["k"])({list:[t]}).then((function(t){t.success?Object(n["d"])(e,o.$t("message.bookSet.addCartSuccess")):t.success||a["Message"].error({message:o.$t("message.bookSet.operationFail"),customClass:"error-msg",duration:3e3})}))},goToLogin:function(){window.location.href="/login"}}}}).call(this,o("1157"))},"5ac9":function(t,e,o){"use strict";o.d(e,"j",(function(){return n})),o.d(e,"i",(function(){return i})),o.d(e,"b",(function(){return c})),o.d(e,"f",(function(){return r})),o.d(e,"d",(function(){return l})),o.d(e,"h",(function(){return u})),o.d(e,"g",(function(){return d})),o.d(e,"a",(function(){return b})),o.d(e,"e",(function(){return h})),o.d(e,"c",(function(){return f})),o.d(e,"l",(function(){return v})),o.d(e,"k",(function(){return k}));var s=o("b775"),a=o("4360");function n(t){return Object(s["a"])({url:"/bookSet/bookSetById",method:"POST",data:t,headers:{currency:a["a"].state.app.currency}})}function i(t){return Object(s["a"])({url:"/bookSet/bookListBySet",method:"POST",data:t,headers:{currency:a["a"].state.app.currency}})}function c(t){return Object(s["a"])({url:"/bookSet/buyBookSetToBuy",method:"POST",data:t})}function r(t){return Object(s["a"])({url:"/bookSet/buyBookSet",method:"POST",data:t})}function l(t){return Object(s["a"])({url:"/shop/cartAddWithSetId",method:"POST",data:t})}function u(t){return Object(s["a"])({url:"/bookSet/bookMenuById",method:"POST",data:t,headers:{currency:a["a"].state.app.currency}})}function d(t){return Object(s["a"])({url:"/bookSet/bookListByMenu",method:"POST",data:t})}function b(t){return Object(s["a"])({url:"/bookSet/buyBookMenuToBuy",method:"POST",data:t})}function h(t){return Object(s["a"])({url:"/bookSet/buyBookMenu",method:"POST",data:t})}function f(t){return Object(s["a"])({url:"/shop/cartAddWithListId",method:"POST",data:t})}function v(t){return Object(s["a"])({url:"/shop/favoriteAdd",method:"POST",data:t})}function k(t){return Object(s["a"])({url:"/shop/cartAdd",method:"POST",data:t})}},acb1:function(t,e,o){"use strict";o.r(e);var s=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"container bookSet-container topOffsetAdjust",attrs:{"element-loading-text":t.$t("message.common.loading")}},[t.bookSetInfo.bannerUrl?o("div",{staticClass:"banner"},[o("img",{staticClass:"bannerSet",attrs:{src:t.bookSetInfo.bannerUrl,alt:t.bookSetInfo.setName}})]):t._e(),t._v(" "),o("h4",{staticClass:"text-center isSetDiscount",class:{notSetDiscount:!t.bookSetInfo.isDiscountValid,noBanner:!t.bookSetInfo.bannerUrl}},[o("span",{staticClass:"setName"},[t._v(t._s(t.bookSetInfo.setName))])]),t._v(" "),t.bookSetInfo.isDiscountValid?o("h6",{staticClass:"text-center setPriceDiscount"},[o("del",{staticClass:"del-price"},[t._v("HK$"+t._s(t.bookSetInfo.price))]),t._v(" "),o("span",{staticClass:"set-price1 endaoRed"},[t._v("HK$"+t._s(t.bookSetInfo.priceDiscount))]),t._v(" "),o("span",{staticClass:"set-price2 endaoRed"},[t._v("("+t._s(t.$store.state.app.currencySign)+t._s(t.bookSetInfo.priceCurrency)+")")])]):t._e(),t._v(" "),t.bookVersions.length?o("h6",{staticClass:"text-center otherVersions"},[o("span",[t._v(t._s(t.$t("message.bookSet.otherVersions"))+"：")]),t._v(" "),t._l(t.bookVersions,(function(e){return o("a",{key:e.value,staticClass:"product-thumb endaoRed",attrs:{href:"/store/bookset-"+e.value+(t.activeLocale?"?locale="+t.activeLocale:"")}},[t._v("\n      "+t._s(e.name)+"\n    ")])}))],2):t._e(),t._v(" "),t.bookSetInfo.setName?o("div",{staticClass:"row flex_x_center toBuyBtn"},[t.bookSetInfo.isDiscountValid?t._e():o("el-button",{staticClass:"allToCart",attrs:{round:""},on:{click:t.toShowAddCart}},[t._v(t._s(t.$t("message.bookSet.addCart")))]),t._v(" "),t.bookSetInfo.canAllBuy?o("el-button",{class:{buyAllBooks:!0,notSetDiscount:!t.bookSetInfo.isDiscountValid},attrs:{type:"danger",round:""},on:{click:t.buyAllSetBooks}},[t._v(t._s(t.$t("message.bookSet.buyAllSetBooks")))]):t._e()],1):t._e(),t._v(" "),o("div",{staticClass:"row"},[o("div",{staticClass:"col-2"},[t._v(t._s(""))]),t._v(" "),o("div",{staticClass:"col-xl-auto col-xl-8"},[o("p",{staticClass:"text-overflow pt-3 pl-5 pr-5 endao-html",style:{marginBottom:t.bookSetInfo.setAbstract?"56px":"18px",lineHeight:2,fontSize:"14px",color:"#101010"},domProps:{innerHTML:t._s(t.bookSetInfo.setAbstract)}})])]),t._v(" "),o("div",{staticClass:"row"},t._l(t.bookSetBooks,(function(e){return o("div",{key:e.bookId,staticClass:"col-xl-6 col-lg-6"},[o("div",{staticClass:"product-card product-list"},[o("a",{staticClass:"product-thumb",attrs:{href:"/book-"+e.bookId+(t.activeLocale?"?locale="+t.activeLocale:"")}},[o("img",{attrs:{src:e.imgUrlFull,alt:"Product"}})]),t._v(" "),o("div",{staticClass:"product-info"},[o("h3",{staticClass:"product-title endao-text-overflow-2",staticStyle:{height:"44px","line-height":"22px"}},[o("a",{attrs:{href:"/book-"+e.bookId+(t.activeLocale?"?locale="+t.activeLocale:"")}},[t._v(t._s(e.name))])]),t._v(" "),o("h4",{staticClass:"product-price text-danger"},[o("del",[t._v("HK$"+t._s(e.price))]),o("span",[t._v("HK$"+t._s(e.priceDiscount))]),o("span",[t._v("("+t._s(t.$store.state.app.currencySign)+t._s(e.priceCurrency)+")")])]),t._v(" "),o("div",{staticClass:"pt-1"},[o("div",{staticClass:"d-inline text-muted author-publisher-overflow"},[o("a",{staticClass:"navi-link",attrs:{href:"/searchBook?searchText="+e.author+(t.activeLocale?"&locale="+t.activeLocale:"")}},[t._v(t._s(e.author.replace(/#/g," ")))])])]),t._v(" "),o("p",{staticClass:"hidden-xs-down istext-overflow text-muted pt-3 endao-text-overflow-3",staticStyle:{height:"80px","font-size":"14px"}},[t._v(t._s(e.recommendedCaption))]),t._v(" "),t.hasLogin?o("div",{staticClass:"product-buttons"},[o("button",{staticClass:"btn btn-outline-secondary btn-sm btn-round",attrs:{"data-toast":"","data-toggle":"tooltip",title:t.$t("message.bookSet.favorite")},on:{click:function(o){return t.favThisBook(e.bookId,e.name)}}},[o("i",{staticClass:"icon-heart"})]),t._v(" "),o("button",{staticClass:"btn btn-outline-primary btn-sm btn-bag",attrs:{"data-toast":"","data-toggle":"tooltip",title:t.$t("message.bookSet.cart")},on:{click:function(o){return t.cartThisBook(e.bookId,e.name)}}},[o("i",{staticClass:"icon-bag"})])]):t._e(),t._v(" "),t.hasLogin?t._e():o("div",{staticClass:"product-buttons"},[o("button",{staticClass:"btn btn-outline-secondary btn-sm btn-round",attrs:{"data-toggle":"tooltip",title:t.$t("message.bookSet.favorite")},on:{click:t.goToLogin}},[o("i",{staticClass:"icon-heart"})]),t._v(" "),o("button",{staticClass:"btn btn-outline-primary btn-sm btn-round",attrs:{"data-toggle":"tooltip",title:t.$t("message.bookSet.cart")},on:{click:t.goToLogin}},[o("i",{staticClass:"icon-bag"})])])])])])})),0),t._v(" "),o("div",{staticClass:"row"},[t.bookSetBooks.length?o("nav",{staticClass:"pagination"},[o("div",{directives:[{name:"show",rawName:"v-show",value:1!==t.activePage,expression:"activePage !== 1"}],staticClass:"column text-left hidden-xs-down"},[o("a",{staticClass:"btn btn-outline-secondary btn-sm",attrs:{href:"#"},on:{click:function(e){return t.changePage(t.activePage-1)}}},[o("i",{staticClass:"icon-arrow-left"}),t._v(" "+t._s(t.$t("message.prevPage")))])]),t._v(" "),o("div",{staticClass:"column"},[o("ul",{staticClass:"pages"},t._l(t.totalPage,(function(e,s){return o("li",{key:s,class:{active:t.activePage===e},on:{click:function(o){return t.changePage(e)}}},[o("a",{attrs:{href:"#"}},[t._v(t._s(e))])])})),0)]),t._v(" "),o("div",{directives:[{name:"show",rawName:"v-show",value:t.activePage!==t.totalPage,expression:"activePage !== totalPage"}],staticClass:"column text-right hidden-xs-down"},[o("a",{staticClass:"btn btn-outline-secondary btn-sm",attrs:{href:"#"},on:{click:function(e){return t.changePage(t.activePage+1)}}},[t._v(t._s(t.$t("message.nextPage"))+" "),o("i",{staticClass:"icon-arrow-right"})])])]):t._e()]),t._v(" "),o("el-dialog",{attrs:{"show-close":!1,"modal-append-to-body":!1,visible:t.showConfirm,width:"460px",top:"35vh"},on:{"update:visible":function(e){t.showConfirm=e}}},[o("div",{staticStyle:{height:"56px"}},[o("p",[o("span",[t._v(t._s(t.$t("message.bookSet.addConfirm")))])])]),t._v(" "),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{round:""},on:{click:function(e){t.showConfirm=!1}}},[t._v(t._s(t.$t("message.bookSet.cancel")))]),t._v(" "),o("el-button",{attrs:{type:"danger",round:"",disabled:t.btnDisabled},on:{click:t.addAllToCart}},[t._v(t._s(t.$t("message.bookSet.add")))])],1)])],1)},a=[],n=o("47c4"),i=n["a"],c=(o("d47b"),o("2877")),r=Object(c["a"])(i,s,a,!1,null,"239c9eac",null);e["default"]=r.exports},d47b:function(t,e,o){"use strict";o("1bfc")}}]);