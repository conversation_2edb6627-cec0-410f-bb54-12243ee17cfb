(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-408781a7"],{"0439":function(t,s,o){"use strict";o("b22d")},"1d23":function(t,s,o){"use strict";(function(t){o("ac6a"),o("28a5");var e=o("5ac9");s["a"]={data:function(){return{submitOrderDisabled:!1,payData:{totalHK:0,discountHK:0,willPayTotalHK:0},setId:0,discountInfo:{countNum:1,discountNum:0},isDiscountBookList:[],notDiscountBookList:[],hasBuyList:[],isDiscountValid:0}},computed:{totalCurrency:function(){return Math.round(this.payData.willPayTotalHK*this.$store.state.app.exchangeRate*100)/100},formatAuthor:function(){return function(t){return t.split("#")}}},created:function(){t("footer").css("display","block"),t(".scroll-to-top-btn").css("display","block"),this.setId=this.$route.params.item.setId,this.isDiscountValid=this.$route.params.item.isDiscountValid,this.getAllSetBooks()},mounted:function(){},methods:{getAllSetBooks:function(){var t=this;Object(e["b"])({setId:this.setId}).then((function(s){if(s.success){t.hasBuyList=s.result.buyList,t.notDiscountBookList=s.result.notBuyList.filter((function(t){return 0===t.discountIsValid})),t.isDiscountBookList=s.result.notBuyList.filter((function(t){return 1===t.discountIsValid}));var o=t.isDiscountBookList.length;if(o){var e=t.isDiscountBookList[0];t.discountInfo.countNum=e.cumulatePackage,t.discountInfo.discountNum=e.cumulateDiscount/10,o>=e.cumulatePackageMuti?(t.discountInfo.countNum=e.cumulatePackageMuti,t.discountInfo.discountNum=e.cumulateDiscountMuti/10,t.isDiscountBookList.map((function(s){return s.priceDiscount=s.price*t.discountInfo.discountNum/10,s}))):o>=e.cumulatePackage?(t.discountInfo.countNum=e.cumulatePackage,t.discountInfo.discountNum=e.cumulateDiscount/10,t.isDiscountBookList.map((function(s){return s.priceDiscount=s.price*t.discountInfo.discountNum/10,s}))):1===o&&1===e.discountSingleIsValid&&(t.discountInfo.countNum=1,t.discountInfo.discountNum=e.discountPercent/10,t.isDiscountBookList.map((function(s){return s.priceDiscount=s.price*t.discountInfo.discountNum/10,s})))}var a=0,i=0,c=0;t.notDiscountBookList.forEach((function(t){a+=t.price,i+=t.price-t.priceDiscount,c+=t.priceDiscount}));var n=0,u=0,r=0;t.isDiscountBookList.forEach((function(t){n+=t.price,u+=t.price-t.priceDiscount,r+=t.priceDiscount})),t.payData.totalHK=a+n,t.payData.discountHK=i+u,t.payData.willPayTotalHK=c+r}}))},submitOrder:function(){var t=this;this.submitOrderDisabled=!0,Object(e["f"])({setId:this.setId}).then((function(s){t.submitOrderDisabled=!1,s.success&&(window.location.href="/orderDetail-".concat(s.result.orderId))}))},goBack:function(){this.$router.go(-1)}}}}).call(this,o("1157"))},"315c":function(t,s,o){"use strict";o.r(s);var e=function(){var t=this,s=t.$createElement,o=t._self._c||s;return o("div",{staticClass:"container padding-bottom-3x mb-1 padding-top-2x setBooks-container"},[o("div",{staticClass:"table-responsive shopping-cart"},[o("table",{staticClass:"table"},[o("thead",[o("tr",[o("th",{staticStyle:{width:"55.7%"}},[t._v(t._s(t.$t("message.common.bookName"))+" ")]),t._v(" "),o("th",{staticClass:"text-center",staticStyle:{width:"24.5%"}},[t._v(t._s(t.$t("message.common.price"))+" ")]),t._v(" "),o("th",{staticClass:"text-center",staticStyle:{width:"6.9%"}},[t._v(t._s(t.$t("message.common.number"))+" ")]),t._v(" "),o("th",{staticClass:"text-center",staticStyle:{width:"12.9%"}},[t._v(t._s(t.$t("message.common.subTotal")))])])]),t._v(" "),t.isDiscountBookList.length?o("tbody",[t.isDiscountBookList.length>=t.discountInfo.countNum?o("tr",[o("td",{attrs:{colspan:"4"}},[o("span",{staticClass:"text-danger discountAlert"},[t._v(t._s(t.$t("message.common.isDiscountTitle"))+t._s(t.discountInfo.countNum)+"件"+t._s(t.discountInfo.discountNum)+"折")])])]):t._e(),t._v(" "),t._l(t.isDiscountBookList,(function(s){return o("tr",{key:s.bookId},[o("td",[o("div",{staticClass:"product-item"},[o("a",{staticClass:"product-thumb",attrs:{href:"javascript:;"}},[o("img",{staticClass:"bookCover",attrs:{src:""+s.imgUrl,alt:"Product"}})]),t._v(" "),o("div",{staticClass:"product-info"},[o("h4",{staticClass:"product-title",staticStyle:{display:"flex","align-items":"baseline",color:"#7a7a7a"}},[o("a",{attrs:{href:"/book-"+s.bookId}},[t._v(t._s(s.name))])]),t._v(" "),o("span",[o("em",{class:{author:!0}},[t._v(t._s(t.$t("message.common.author"))+" :")]),t._v(" "),t._l(t.formatAuthor(s.author),(function(s,e){return o("a",{key:e,class:{"navi-link":!0},attrs:{href:"/searchBook?searchText="+s}},[t._v(t._s(s))])}))],2)])])]),t._v(" "),o("td",{staticClass:"text-center text-lg text-medium tdItem"},[o("span",[s.price!=s.priceDiscount?o("del",[t._v(" HK$"+t._s(t._f("twoDecimal")(s.price)))]):t._e(),t._v(" HK$"+t._s(t._f("twoDecimal")(s.priceDiscount))+" ")])]),t._v(" "),t._m(0,!0),t._v(" "),o("td",{staticClass:"text-center text-lg text-medium tdItem"},[o("span",[t._v(" HK$"+t._s(t._f("twoDecimal")(s.priceDiscount))+" ")])])])}))],2):t._e(),t._v(" "),t.notDiscountBookList.length?o("tbody",[t.isDiscountBookList.length<t.discountInfo.countNum||t.isDiscountValid?t._e():o("tr",[o("td",{attrs:{colspan:"4"}},[o("span",{staticClass:"text-danger discountAlert"},[t._v(t._s(t.$t("message.common.notDiscountTitle")))])])]),t._v(" "),t._l(t.notDiscountBookList,(function(s){return o("tr",{key:s.bookId},[o("td",[o("div",{staticClass:"product-item"},[o("a",{staticClass:"product-thumb",attrs:{href:"javascript:;"}},[o("img",{staticClass:"bookCover",attrs:{src:""+s.imgUrl,alt:"Product"}})]),t._v(" "),o("div",{staticClass:"product-info"},[o("h4",{staticClass:"product-title",staticStyle:{display:"flex","align-items":"baseline",color:"#7a7a7a"}},[o("a",{attrs:{href:"/book-"+s.bookId}},[t._v(t._s(s.name))])]),t._v(" "),o("span",[o("em",{class:{author:!0}},[t._v(t._s(t.$t("message.common.author"))+" :")]),t._v(" "),t._l(t.formatAuthor(s.author),(function(s,e){return o("a",{key:e,class:{"navi-link":!0},attrs:{href:"/searchBook?searchText="+s}},[t._v(t._s(s))])}))],2)])])]),t._v(" "),o("td",{staticClass:"text-center text-lg text-medium tdItem"},[o("span",[s.price!=s.priceDiscount?o("del",[t._v(" HK$"+t._s(t._f("twoDecimal")(s.price)))]):t._e(),t._v(" HK$"+t._s(t._f("twoDecimal")(s.priceDiscount))+" ")])]),t._v(" "),t._m(1,!0),t._v(" "),o("td",{staticClass:"text-center text-lg text-medium tdItem"},[o("span",[t._v(" HK$"+t._s(t._f("twoDecimal")(s.priceDiscount))+" ")])])])}))],2):t._e(),t._v(" "),t.hasBuyList.length?o("tbody",[o("tr",[o("td",{attrs:{colspan:"4"}},[o("span",{staticClass:"text-danger discountAlert"},[t._v(t._s(t.$t("message.common.purchasedGoods")))])])]),t._v(" "),t._l(t.hasBuyList,(function(s){return o("tr",{key:s.bookId},[o("td",[o("div",{staticClass:"product-item"},[o("a",{staticClass:"product-thumb",attrs:{href:"javascript:;"}},[o("img",{staticClass:"bookCover",attrs:{src:""+s.imgUrl,alt:"Product"}})]),t._v(" "),o("div",{staticClass:"product-info"},[o("h4",{staticClass:"product-title",staticStyle:{display:"flex","align-items":"baseline",color:"#7a7a7a"}},[o("span",{staticStyle:{"font-size":"14px"}},[t._v(t._s(t.$t("message.common.purchased"))+"  ")]),t._v(" "),o("a",{class:{isBuy:!0},attrs:{href:"/book-"+s.bookId}},[t._v(t._s(s.name))])]),t._v(" "),o("span",[o("em",{class:{author:!0,isBuy:!0}},[t._v(t._s(t.$t("message.common.author"))+" :")]),t._v(" "),t._l(t.formatAuthor(s.author),(function(s,e){return o("a",{key:e,class:{"navi-link":!0,isBuy:!0},attrs:{href:"/searchBook?searchText="+s}},[t._v(t._s(s))])}))],2)])])])])}))],2):t._e()])]),t._v(" "),o("div",{staticClass:"shopping-cart-footer"},[o("div",{staticClass:"col-xl-11"},[o("span",{staticClass:"text-medium text-danger",attrs:{id:"warning"}},[t._v(t._s(t.$t("message.common.intro")))])]),t._v(" "),o("div",{staticClass:"column text-lg-right"},[o("h4",{staticClass:"product-price"},[t._v("\n        "+t._s(t.$t("message.common.total"))+" :   "),o("span",{staticClass:"text-medium"},[t._v("HK$"+t._s(t._f("twoDecimal")(t.payData.totalHK)))])]),t._v(" "),o("h4",{staticClass:"product-price"},[t._v("\n        "+t._s(t.$t("message.common.discountAmount"))+" :  "),o("span",{staticClass:"text-medium",staticStyle:{"margin-left":"-3px",display:"inline-block"}},[t._v("-HK$"+t._s(t._f("twoDecimal")(t.payData.discountHK)))])]),t._v(" "),o("h4",{staticClass:"product-price"},[t._v("\n        "+t._s(t.$t("message.common.payAble"))+" :   "),o("span",{staticClass:"text-medium"},[t._v("HK$"+t._s(t._f("twoDecimal")(t.payData.willPayTotalHK)))])]),t._v(" "),o("h4",{staticClass:"product-price"},[o("span",{staticClass:"text-medium"},[t._v("("+t._s(t.$store.state.app.currencySign)+t._s(t.totalCurrency)+")")])])])]),t._v(" "),o("div",{staticClass:"shopping-cart-footer"},[o("div",{staticClass:"column"},[o("el-button",{staticStyle:{"text-transform":"uppercase"},attrs:{round:""},on:{click:t.goBack}},[o("i",{staticClass:"el-icon-back",staticStyle:{"font-size":"12px"}}),t._v(" "+t._s(t.$t("message.common.goBack")))]),t._v(" "),o("el-button",{staticStyle:{"text-transform":"uppercase"},attrs:{type:"danger",round:"",disabled:t.submitOrderDisabled},on:{click:t.submitOrder}},[t._v(t._s(t.$t("message.common.submitOrder")))])],1)])])},a=[function(){var t=this,s=t.$createElement,o=t._self._c||s;return o("td",{staticClass:"text-center text-lg text-medium tdItem"},[o("span",[t._v("1")])])},function(){var t=this,s=t.$createElement,o=t._self._c||s;return o("td",{staticClass:"text-center text-lg text-medium tdItem"},[o("span",[t._v("1")])])}],i=o("1d23"),c=i["a"],n=(o("0439"),o("2877")),u=Object(n["a"])(c,e,a,!1,null,"7c4505ea",null);s["default"]=u.exports},"5ac9":function(t,s,o){"use strict";o.d(s,"j",(function(){return i})),o.d(s,"i",(function(){return c})),o.d(s,"b",(function(){return n})),o.d(s,"f",(function(){return u})),o.d(s,"d",(function(){return r})),o.d(s,"h",(function(){return l})),o.d(s,"g",(function(){return d})),o.d(s,"a",(function(){return m})),o.d(s,"e",(function(){return _})),o.d(s,"c",(function(){return p})),o.d(s,"l",(function(){return f})),o.d(s,"k",(function(){return h}));var e=o("b775"),a=o("4360");function i(t){return Object(e["a"])({url:"/bookSet/bookSetById",method:"POST",data:t,headers:{currency:a["a"].state.app.currency}})}function c(t){return Object(e["a"])({url:"/bookSet/bookListBySet",method:"POST",data:t,headers:{currency:a["a"].state.app.currency}})}function n(t){return Object(e["a"])({url:"/bookSet/buyBookSetToBuy",method:"POST",data:t})}function u(t){return Object(e["a"])({url:"/bookSet/buyBookSet",method:"POST",data:t})}function r(t){return Object(e["a"])({url:"/shop/cartAddWithSetId",method:"POST",data:t})}function l(t){return Object(e["a"])({url:"/bookSet/bookMenuById",method:"POST",data:t,headers:{currency:a["a"].state.app.currency}})}function d(t){return Object(e["a"])({url:"/bookSet/bookListByMenu",method:"POST",data:t})}function m(t){return Object(e["a"])({url:"/bookSet/buyBookMenuToBuy",method:"POST",data:t})}function _(t){return Object(e["a"])({url:"/bookSet/buyBookMenu",method:"POST",data:t})}function p(t){return Object(e["a"])({url:"/shop/cartAddWithListId",method:"POST",data:t})}function f(t){return Object(e["a"])({url:"/shop/favoriteAdd",method:"POST",data:t})}function h(t){return Object(e["a"])({url:"/shop/cartAdd",method:"POST",data:t})}},b22d:function(t,s,o){}}]);