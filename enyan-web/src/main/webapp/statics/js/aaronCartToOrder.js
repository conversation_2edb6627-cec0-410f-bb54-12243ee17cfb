function addCart(code) {

    var input = $('#' + code);
    input.val(parseInt(input.val()) + 1);
    $('#' + code).trigger('change');
    if (parseInt(input.val()) != 1) {
        $('#min' + code).attr('disabled', false);
    }
}

function minCart(code) {

    var input = $('#' + code);
    if (parseInt(input.val()) == 1) {
        $('#min' + code).attr('disabled', true);
    } else {
        input.val(parseInt(input.val()) - 1);
        $('#' + code).trigger('change');
    }
}

function sendAndUpdate(code) {

    if ($('#' + code).val() >= 1) {
        $.get('singleupdate/' + code + '/' + $('#' + code).val(), function (msg) {
            if (msg == 'success') {
                window.location.reload();
                //$("#mainContent").;//要刷新的div
            }
        });
    } else {

        alert("商品数量必须大于等于一");
        $('#' + code).val(1);
        $('#' + code).trigger('change');
    }
}

function delet(code) {
    window.location.reload();
    $.get('deleteproduct/' + code, function (msg) {
        if (msg == 'success')
            window.location.reload();
    })
}

function addToCart(code) {
    var url = "addCart-"+code;
    $.get(url, function (res) {
        //console.log(res);
        if(!res.success){
            if(res.errorMessages[0] == "请登录"){
                window.location.href = "/login";
            }else {
                alert(res.errorMessages[0]);
            }
        }else {
            $("#buyerCartQuantity").text(res.data.quantity)
        }
    });
}
function addToWish(code) {
    var url = "wish-add-"+code;
    $.get(url, function (res) {
        //console.log(res);
        //alert(res.success);
        if(!res.success){
            if(res.errorMessages[0] == "请登录"){
                window.location.href = "/login";
            }else {
                alert(res.errorMessages[0]);
            }
        }else {
            //$("#buyerCartQuantity").text(res.data.quantity)
            //alert("success");
        }
    });
}

