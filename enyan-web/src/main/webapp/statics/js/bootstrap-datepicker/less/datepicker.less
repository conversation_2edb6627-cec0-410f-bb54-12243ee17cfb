/*!
 * Datepicker for Bootstrap
 *
 * Copyright 2012 <PERSON>
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
 
.datepicker {
	top: 0;
	left: 0;
	padding: 4px;
	margin-top: 1px;
	.border-radius(4px);
	&:before {
		content: '';
		display: inline-block;
		border-left:   7px solid transparent;
		border-right:  7px solid transparent;
		border-bottom: 7px solid #ccc;
		border-bottom-color: rgba(0,0,0,.2);
		position: absolute;
		top: -7px;
		left: 6px;
	}
	&:after {
		content: '';
		display: inline-block;
		border-left:   6px solid transparent;
		border-right:  6px solid transparent;
		border-bottom: 6px solid @white;
		position: absolute;
		top: -6px;
		left: 7px;
	}
	>div {
		display: none;
	}
	table{
		width: 100%;
		margin: 0;
	}
	td,
	th{
		text-align: center;
		width: 20px;
		height: 20px;
		.border-radius(4px);
	}
	td {
		&.day:hover {
			background: @grayLighter;
			cursor: pointer;
		}
		&.old,
		&.new {
			color: @grayLight;
		}
		&.active,
		&.active:hover {
			.buttonBackground(@primaryButtonBackground, spin(@primaryButtonBackground, 20));
			color: #fff;
			text-shadow: 0 -1px 0 rgba(0,0,0,.25);
		}
		span {
			display: block;
			width: 47px;
			height: 54px;
			line-height: 54px;
			float: left;
			margin: 2px;
			cursor: pointer;
			.border-radius(4px);
			&:hover {
				background: @grayLighter;
			}
			&.active {
				.buttonBackground(@primaryButtonBackground, spin(@primaryButtonBackground, 20));
				color: #fff;
				text-shadow: 0 -1px 0 rgba(0,0,0,.25);
			}
			&.old {
				color: @grayLight;
			}
		}
	}
	
	th {
		&.switch {
			width: 145px;
		}
		&.next,
		&.prev {
			font-size: @baseFontSize * 1.5;
		}
	}
	
	thead tr:first-child th {
		cursor: pointer;
		&:hover{
			background: @grayLighter;
		}
	}
	/*.dow {
		border-top: 1px solid #ddd !important;
	}*/
}
.input-append,
.input-prepend {
	&.date {
		.add-on i {
			display: block;
			cursor: pointer;
			width: 16px;
			height: 16px;
		}
	}
}