$.extend({
    jpost: function(url, body) {
        //console.log(body);
        return $.ajax({
            type: 'POST',
            url: url,
            data: JSON.stringify(body),
            headers:
                {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
            contentType: "application/json",
            dataType: 'json'
        });
    },
    jget: function(url, body) {
        return $.ajax({
            type: 'GET',
            url: url,
            data: JSON.stringify(body),
            contentType: "application/json",
            dataType: 'json'
        });
    }
});

$('#btn-send-coupon').on('click',function(e){
    var url = "sendCouponCode";
    var code = $('#couponCode').val();
    $.jpost(url, {
        code:code
        //_token:csrf_token
    }).then(res => {
        //console.log(res);
        //alert(res.successMessage);
        if(!res.success){
            if(res.errorMessages[0] == "请登录"){
                window.location.href = "/login";
            }else {
                $('#couponError').text(res.errorMessages[0]);
            }
        }else {
            //$('.sendto',currentRow).text(email);
            //$('#modal-sendcode').modal('hide');

            $('#couponCode').attr("disabled","disabled");
            $('#couponCode').val(code);
            $('#couponError').text(res.data);
            $('#btn-send-coupon').attr("disabled","disabled");
        }
    });
});

jQuery.fn.extend( {
    selected: function( itemId , itemValue ) {
        window.location.href = "/book-"+ itemId + "#";
    }
});

$( function() {
    var cache = {};
    //var termTemplate = "<span class='ui-autocomplete-term'>%s</span>";
    $( "#shopSearch" ).autocomplete({
        source: function( request, response ) {
            var term = request.term;
            if ( term in cache ) {
                response( cache[ term ] );
                return;
            }
            $.ajax( {
                type: 'POST',
                url: "/api/v4/info/search",
                dataType: "json",
                contentType: "application/json",
                data: JSON.stringify({
                    term: term
                }),
                success: function( data ) {
                    //console.log(request.term);
                    //console.log(data);
                    cache[ term ] = data.result;
                    response( data.result );
                }
            } );
        },
        minLength: 2,
        select: function( event, ui ) {
            //selected( "Selected: " + ui.item.value + " aka " + ui.item.id );
            $.fn.selected( ui.item.id , ui.item.value );
            //$.selected()
        }
    } );
} );