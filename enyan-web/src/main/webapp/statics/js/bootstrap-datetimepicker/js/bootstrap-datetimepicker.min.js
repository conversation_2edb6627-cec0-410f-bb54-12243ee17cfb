!function(t){function e(){return new Date(Date.UTC.apply(Date,arguments))}var i=function(e,i){var a=this;this.element=t(e),this.language=i.language||this.element.data("date-language")||"en",this.language=this.language in n?this.language:"en",this.isRTL=n[this.language].rtl||"rtl"==t("body").css("direction"),this.formatType=i.formatType||this.element.data("format-type")||"standard",this.format=o.parseFormat(i.format||this.element.data("date-format")||n[this.language].format||o.getDefaultFormat(this.formatType,"input"),this.formatType),this.isInline=!1,this.isVisible=!1,this.isInput=this.element.is("input"),this.component=this.element.is(".date")?this.element.find(".date-set").parent():!1,this.componentReset=this.element.is(".date")?this.element.find(".date-reset").parent():!1,this.hasInput=this.component&&this.element.find("input").length,this.component&&0===this.component.length&&(this.component=!1),this.linkField=i.linkField||this.element.data("link-field")||!1,this.linkFormat=o.parseFormat(i.linkFormat||this.element.data("link-format")||o.getDefaultFormat(this.formatType,"link"),this.formatType),this.minuteStep=i.minuteStep||this.element.data("minute-step")||5,this.pickerPosition=i.pickerPosition||this.element.data("picker-position")||"bottom-right",this.showMeridian=i.showMeridian||this.element.data("show-meridian")||!1,this.initialDate=i.initialDate||new Date,this._attachEvents(),this.formatViewType="datetime","formatViewType"in i?this.formatViewType=i.formatViewType:"formatViewType"in this.element.data()&&(this.formatViewType=this.element.data("formatViewType")),this.minView=0,"minView"in i?this.minView=i.minView:"minView"in this.element.data()&&(this.minView=this.element.data("min-view")),this.minView=o.convertViewMode(this.minView),this.maxView=o.modes.length-1,"maxView"in i?this.maxView=i.maxView:"maxView"in this.element.data()&&(this.maxView=this.element.data("max-view")),this.maxView=o.convertViewMode(this.maxView),this.wheelViewModeNavigation=!1,"wheelViewModeNavigation"in i?this.wheelViewModeNavigation=i.wheelViewModeNavigation:"wheelViewModeNavigation"in this.element.data()&&(this.wheelViewModeNavigation=this.element.data("view-mode-wheel-navigation")),this.wheelViewModeNavigationInverseDirection=!1,"wheelViewModeNavigationInverseDirection"in i?this.wheelViewModeNavigationInverseDirection=i.wheelViewModeNavigationInverseDirection:"wheelViewModeNavigationInverseDirection"in this.element.data()&&(this.wheelViewModeNavigationInverseDirection=this.element.data("view-mode-wheel-navigation-inverse-dir")),this.wheelViewModeNavigationDelay=100,"wheelViewModeNavigationDelay"in i?this.wheelViewModeNavigationDelay=i.wheelViewModeNavigationDelay:"wheelViewModeNavigationDelay"in this.element.data()&&(this.wheelViewModeNavigationDelay=this.element.data("view-mode-wheel-navigation-delay")),this.startViewMode=2,"startView"in i?this.startViewMode=i.startView:"startView"in this.element.data()&&(this.startViewMode=this.element.data("start-view")),this.startViewMode=o.convertViewMode(this.startViewMode),this.viewMode=this.startViewMode,this.viewSelect=this.minView,"viewSelect"in i?this.viewSelect=i.viewSelect:"viewSelect"in this.element.data()&&(this.viewSelect=this.element.data("view-select")),this.viewSelect=o.convertViewMode(this.viewSelect),this.forceParse=!0,"forceParse"in i?this.forceParse=i.forceParse:"dateForceParse"in this.element.data()&&(this.forceParse=this.element.data("date-force-parse")),this.picker=t(o.template).appendTo(this.isInline?this.element:"body").on({click:t.proxy(this.click,this),mousedown:t.proxy(this.mousedown,this)}),this.wheelViewModeNavigation&&(t.fn.mousewheel?this.picker.on({mousewheel:t.proxy(this.mousewheel,this)}):console.log("Mouse Wheel event is not supported. Please include the jQuery Mouse Wheel plugin before enabling this option")),this.isInline?this.picker.addClass("datetimepicker-inline"):this.picker.addClass("datetimepicker-dropdown-"+this.pickerPosition+" dropdown-menu"),this.isRTL&&(this.picker.addClass("datetimepicker-rtl"),this.picker.find(".prev i, .next i").toggleClass("icon-arrow-left icon-arrow-right")),t(document).on("mousedown",function(e){0===t(e.target).closest(".datetimepicker").length&&a.hide()}),this.autoclose=!1,"autoclose"in i?this.autoclose=i.autoclose:"dateAutoclose"in this.element.data()&&(this.autoclose=this.element.data("date-autoclose")),this.keyboardNavigation=!0,"keyboardNavigation"in i?this.keyboardNavigation=i.keyboardNavigation:"dateKeyboardNavigation"in this.element.data()&&(this.keyboardNavigation=this.element.data("date-keyboard-navigation")),this.todayBtn=i.todayBtn||this.element.data("date-today-btn")||!1,this.todayHighlight=i.todayHighlight||this.element.data("date-today-highlight")||!1,this.weekStart=(i.weekStart||this.element.data("date-weekstart")||n[this.language].weekStart||0)%7,this.weekEnd=(this.weekStart+6)%7,this.startDate=-1/0,this.endDate=1/0,this.daysOfWeekDisabled=[],this.setStartDate(i.startDate||this.element.data("date-startdate")),this.setEndDate(i.endDate||this.element.data("date-enddate")),this.setDaysOfWeekDisabled(i.daysOfWeekDisabled||this.element.data("date-days-of-week-disabled")),this.fillDow(),this.fillMonths(),this.update(),this.showMode(),this.isInline&&this.show()};i.prototype={constructor:i,_events:[],_attachEvents:function(){this._detachEvents(),this.isInput?this._events=[[this.element,{focus:t.proxy(this.show,this),keyup:t.proxy(this.update,this),keydown:t.proxy(this.keydown,this)}]]:this.component&&this.hasInput?(this._events=[[this.element.find("input"),{focus:t.proxy(this.show,this),keyup:t.proxy(this.update,this),keydown:t.proxy(this.keydown,this)}],[this.component,{click:t.proxy(this.show,this)}]],this.componentReset&&this._events.push([this.componentReset,{click:t.proxy(this.reset,this)}])):this.element.is("div")?this.isInline=!0:this._events=[[this.element,{click:t.proxy(this.show,this)}]];for(var e,i,n=0;n<this._events.length;n++)e=this._events[n][0],i=this._events[n][1],e.on(i)},_detachEvents:function(){for(var t,e,i=0;i<this._events.length;i++)t=this._events[i][0],e=this._events[i][1],t.off(e);this._events=[]},show:function(e){this.picker.show(),this.height=this.component?this.component.outerHeight():this.element.outerHeight(),this.forceParse&&this.update(),this.place(),t(window).on("resize",t.proxy(this.place,this)),e&&(e.stopPropagation(),e.preventDefault()),this.isVisible=!0,this.element.trigger({type:"show",date:this.date})},hide:function(){this.isVisible&&(this.isInline||(this.picker.hide(),t(window).off("resize",this.place),this.viewMode=this.startViewMode,this.showMode(),this.isInput||t(document).off("mousedown",this.hide),this.forceParse&&(this.isInput&&this.element.val()||this.hasInput&&this.element.find("input").val())&&this.setValue(),this.isVisible=!1,this.element.trigger({type:"hide",date:this.date})))},remove:function(){this._detachEvents(),this.picker.remove(),delete this.picker,delete this.element.data().datetimepicker},getDate:function(){var t=this.getUTCDate();return new Date(t.getTime()+6e4*t.getTimezoneOffset())},getUTCDate:function(){return this.date},setDate:function(t){this.setUTCDate(new Date(t.getTime()-6e4*t.getTimezoneOffset()))},setUTCDate:function(t){t>=this.startDate&&t<=this.endDate?(this.date=t,this.setValue(),this.viewDate=this.date,this.fill()):this.element.trigger({type:"outOfRange",date:t,startDate:this.startDate,endDate:this.endDate})},setFormat:function(t){this.format=o.parseFormat(t,this.formatType);var e;this.isInput?e=this.element:this.component&&(e=this.element.find("input")),e&&e.val()&&this.setValue()},setValue:function(){var e=this.getFormattedDate();this.isInput?this.element.val(e):(this.component&&this.element.find("input").val(e),this.element.data("date",e)),this.linkField&&t("#"+this.linkField).val(this.getFormattedDate(this.linkFormat))},getFormattedDate:function(t){return void 0==t&&(t=this.format),o.formatDate(this.date,t,this.language,this.formatType)},setStartDate:function(t){this.startDate=t||-1/0,this.startDate!==-1/0&&(this.startDate=o.parseDate(this.startDate,this.format,this.language,this.formatType)),this.update(),this.updateNavArrows()},setEndDate:function(t){this.endDate=t||1/0,1/0!==this.endDate&&(this.endDate=o.parseDate(this.endDate,this.format,this.language,this.formatType)),this.update(),this.updateNavArrows()},setDaysOfWeekDisabled:function(e){this.daysOfWeekDisabled=e||[],t.isArray(this.daysOfWeekDisabled)||(this.daysOfWeekDisabled=this.daysOfWeekDisabled.split(/,\s*/)),this.daysOfWeekDisabled=t.map(this.daysOfWeekDisabled,function(t){return parseInt(t,10)}),this.update(),this.updateNavArrows()},place:function(){if(!this.isInline){var e,i,n,o=parseInt(this.element.parents().filter(function(){return"auto"!=t(this).css("z-index")}).first().css("z-index"))+10;this.component?(e=this.component.offset(),n=e.left,("bottom-left"==this.pickerPosition||"top-left"==this.pickerPosition)&&(n+=this.component.outerWidth()-this.picker.outerWidth())):(e=this.element.offset(),n=e.left),i="top-left"==this.pickerPosition||"top-right"==this.pickerPosition?e.top-this.picker.outerHeight():e.top+this.height,this.picker.css({top:i,left:n,zIndex:o})}},update:function(){var t,e=!1;arguments&&arguments.length&&("string"==typeof arguments[0]||arguments[0]instanceof Date)?(t=arguments[0],e=!0):t=this.element.data("date")||(this.isInput?this.element.val():this.element.find("input").val())||this.initialDate,t||(t=new Date,e=!1),this.date=o.parseDate(t,this.format,this.language,this.formatType),e&&this.setValue(),this.viewDate=this.date<this.startDate?new Date(this.startDate):this.date>this.endDate?new Date(this.endDate):new Date(this.date),this.fill()},fillDow:function(){for(var t=this.weekStart,e="<tr>";t<this.weekStart+7;)e+='<th class="dow">'+n[this.language].daysMin[t++%7]+"</th>";e+="</tr>",this.picker.find(".datetimepicker-days thead").append(e)},fillMonths:function(){for(var t="",e=0;12>e;)t+='<span class="month">'+n[this.language].monthsShort[e++]+"</span>";this.picker.find(".datetimepicker-months td").html(t)},fill:function(){if(null!=this.date&&null!=this.viewDate){var i=new Date(this.viewDate),a=i.getUTCFullYear(),r=i.getUTCMonth(),s=i.getUTCDate(),c=i.getUTCHours(),l=i.getUTCMinutes(),h=this.startDate!==-1/0?this.startDate.getUTCFullYear():-1/0,u=this.startDate!==-1/0?this.startDate.getUTCMonth():-1/0,d=1/0!==this.endDate?this.endDate.getUTCFullYear():1/0,f=1/0!==this.endDate?this.endDate.getUTCMonth():1/0,g=new e(this.date.getUTCFullYear(),this.date.getUTCMonth(),this.date.getUTCDate()).valueOf(),p=new Date;if(this.picker.find(".datetimepicker-days thead th:eq(1)").text(n[this.language].months[r]+" "+a),"time"==this.formatViewType){var _=c%12?c%12:12,b=(10>_?"0":"")+_,m=(10>l?"0":"")+l,v=n[this.language].meridiem[12>c?0:1];this.picker.find(".datetimepicker-hours thead th:eq(1)").text(b+":"+m+" "+v.toUpperCase()),this.picker.find(".datetimepicker-minutes thead th:eq(1)").text(b+":"+m+" "+v.toUpperCase())}else this.picker.find(".datetimepicker-hours thead th:eq(1)").text(s+" "+n[this.language].months[r]+" "+a),this.picker.find(".datetimepicker-minutes thead th:eq(1)").text(s+" "+n[this.language].months[r]+" "+a);this.picker.find("tfoot th.today").text(n[this.language].today).toggle(this.todayBtn!==!1),this.updateNavArrows(),this.fillMonths();var w=e(a,r-1,28,0,0,0,0),y=o.getDaysInMonth(w.getUTCFullYear(),w.getUTCMonth());w.setUTCDate(y),w.setUTCDate(y-(w.getUTCDay()-this.weekStart+7)%7);var x=new Date(w);x.setUTCDate(x.getUTCDate()+42),x=x.valueOf();for(var k,C=[];w.valueOf()<x;)w.getUTCDay()==this.weekStart&&C.push("<tr>"),k="",w.getUTCFullYear()<a||w.getUTCFullYear()==a&&w.getUTCMonth()<r?k+=" old":(w.getUTCFullYear()>a||w.getUTCFullYear()==a&&w.getUTCMonth()>r)&&(k+=" new"),this.todayHighlight&&w.getUTCFullYear()==p.getFullYear()&&w.getUTCMonth()==p.getMonth()&&w.getUTCDate()==p.getDate()&&(k+=" today"),w.valueOf()==g&&(k+=" active"),(w.valueOf()+864e5<=this.startDate||w.valueOf()>this.endDate||-1!==t.inArray(w.getUTCDay(),this.daysOfWeekDisabled))&&(k+=" disabled"),C.push('<td class="day'+k+'">'+w.getUTCDate()+"</td>"),w.getUTCDay()==this.weekEnd&&C.push("</tr>"),w.setUTCDate(w.getUTCDate()+1);this.picker.find(".datetimepicker-days tbody").empty().append(C.join("")),C=[];for(var A="",T="",D="",B=0;24>B;B++){var E=e(a,r,s,B);k="",E.valueOf()+36e5<=this.startDate||E.valueOf()>this.endDate?k+=" disabled":c==B&&(k+=" active"),this.showMeridian&&2==n[this.language].meridiem.length?(T=12>B?n[this.language].meridiem[0]:n[this.language].meridiem[1],T!=D&&(""!=D&&C.push("</fieldset>"),C.push('<fieldset class="hour"><legend>'+T.toUpperCase()+"</legend>")),D=T,A=B%12?B%12:12,C.push('<span class="hour'+k+" hour_"+(12>B?"am":"pm")+'">'+A+"</span>"),23==B&&C.push("</fieldset>")):(A=B+":00",C.push('<span class="hour'+k+'">'+A+"</span>"))}this.picker.find(".datetimepicker-hours td").html(C.join("")),C=[],A="",T="",D="";for(var B=0;60>B;B+=this.minuteStep){var E=e(a,r,s,c,B,0);k="",E.valueOf()<this.startDate||E.valueOf()>this.endDate?k+=" disabled":Math.floor(l/this.minuteStep)==Math.floor(B/this.minuteStep)&&(k+=" active"),this.showMeridian&&2==n[this.language].meridiem.length?(T=12>c?n[this.language].meridiem[0]:n[this.language].meridiem[1],T!=D&&(""!=D&&C.push("</fieldset>"),C.push('<fieldset class="minute"><legend>'+T.toUpperCase()+"</legend>")),D=T,A=c%12?c%12:12,C.push('<span class="minute'+k+'">'+A+":"+(10>B?"0"+B:B)+"</span>"),59==B&&C.push("</fieldset>")):(A=B+":00",C.push('<span class="minute'+k+'">'+c+":"+(10>B?"0"+B:B)+"</span>"))}this.picker.find(".datetimepicker-minutes td").html(C.join(""));var S=this.date.getUTCFullYear(),j=this.picker.find(".datetimepicker-months").find("th:eq(1)").text(a).end().find("span").removeClass("active");S==a&&j.eq(this.date.getUTCMonth()).addClass("active"),(h>a||a>d)&&j.addClass("disabled"),a==h&&j.slice(0,u).addClass("disabled"),a==d&&j.slice(f+1).addClass("disabled"),C="",a=10*parseInt(a/10,10);var F=this.picker.find(".datetimepicker-years").find("th:eq(1)").text(a+"-"+(a+9)).end().find("td");a-=1;for(var B=-1;11>B;B++)C+='<span class="year'+(-1==B||10==B?" old":"")+(S==a?" active":"")+(h>a||a>d?" disabled":"")+'">'+a+"</span>",a+=1;F.html(C),this.place()}},updateNavArrows:function(){var t=new Date(this.viewDate),e=t.getUTCFullYear(),i=t.getUTCMonth(),n=t.getUTCDate(),o=t.getUTCHours();switch(this.viewMode){case 0:this.startDate!==-1/0&&e<=this.startDate.getUTCFullYear()&&i<=this.startDate.getUTCMonth()&&n<=this.startDate.getUTCDate()&&o<=this.startDate.getUTCHours()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),1/0!==this.endDate&&e>=this.endDate.getUTCFullYear()&&i>=this.endDate.getUTCMonth()&&n>=this.endDate.getUTCDate()&&o>=this.endDate.getUTCHours()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"});break;case 1:this.startDate!==-1/0&&e<=this.startDate.getUTCFullYear()&&i<=this.startDate.getUTCMonth()&&n<=this.startDate.getUTCDate()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),1/0!==this.endDate&&e>=this.endDate.getUTCFullYear()&&i>=this.endDate.getUTCMonth()&&n>=this.endDate.getUTCDate()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"});break;case 2:this.startDate!==-1/0&&e<=this.startDate.getUTCFullYear()&&i<=this.startDate.getUTCMonth()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),1/0!==this.endDate&&e>=this.endDate.getUTCFullYear()&&i>=this.endDate.getUTCMonth()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"});break;case 3:case 4:this.startDate!==-1/0&&e<=this.startDate.getUTCFullYear()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),1/0!==this.endDate&&e>=this.endDate.getUTCFullYear()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"})}},mousewheel:function(e){if(e.preventDefault(),e.stopPropagation(),!this.wheelPause){this.wheelPause=!0;var i=e.originalEvent,n=i.wheelDelta,o=n>0?1:0===n?0:-1;this.wheelViewModeNavigationInverseDirection&&(o=-o),this.showMode(o),setTimeout(t.proxy(function(){this.wheelPause=!1},this),this.wheelViewModeNavigationDelay)}},click:function(i){i.stopPropagation(),i.preventDefault();var n=t(i.target).closest("span, td, th, legend");if(1==n.length){if(n.is(".disabled"))return this.element.trigger({type:"outOfRange",date:this.viewDate,startDate:this.startDate,endDate:this.endDate}),void 0;switch(n[0].nodeName.toLowerCase()){case"th":switch(n[0].className){case"switch":this.showMode(1);break;case"prev":case"next":var a=o.modes[this.viewMode].navStep*("prev"==n[0].className?-1:1);switch(this.viewMode){case 0:this.viewDate=this.moveHour(this.viewDate,a);break;case 1:this.viewDate=this.moveDate(this.viewDate,a);break;case 2:this.viewDate=this.moveMonth(this.viewDate,a);break;case 3:case 4:this.viewDate=this.moveYear(this.viewDate,a)}this.fill();break;case"today":var r=new Date;r=e(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),0),this.viewMode=this.startViewMode,this.showMode(0),this._setDate(r),this.fill(),this.autoclose&&this.hide()}break;case"span":if(!n.is(".disabled")){var s=this.viewDate.getUTCFullYear(),c=this.viewDate.getUTCMonth(),l=this.viewDate.getUTCDate(),h=this.viewDate.getUTCHours(),u=this.viewDate.getUTCMinutes(),d=this.viewDate.getUTCSeconds();if(n.is(".month")?(this.viewDate.setUTCDate(1),c=n.parent().find("span").index(n),l=this.viewDate.getUTCDate(),this.viewDate.setUTCMonth(c),this.element.trigger({type:"changeMonth",date:this.viewDate}),this.viewSelect>=3&&this._setDate(e(s,c,l,h,u,d,0))):n.is(".year")?(this.viewDate.setUTCDate(1),s=parseInt(n.text(),10)||0,this.viewDate.setUTCFullYear(s),this.element.trigger({type:"changeYear",date:this.viewDate}),this.viewSelect>=4&&this._setDate(e(s,c,l,h,u,d,0))):n.is(".hour")?(h=parseInt(n.text(),10)||0,(n.hasClass("hour_am")||n.hasClass("hour_pm"))&&(12==h&&n.hasClass("hour_am")?h=0:12!=h&&n.hasClass("hour_pm")&&(h+=12)),this.viewDate.setUTCHours(h),this.element.trigger({type:"changeHour",date:this.viewDate}),this.viewSelect>=1&&this._setDate(e(s,c,l,h,u,d,0))):n.is(".minute")&&(u=parseInt(n.text().substr(n.text().indexOf(":")+1),10)||0,this.viewDate.setUTCMinutes(u),this.element.trigger({type:"changeMinute",date:this.viewDate}),this.viewSelect>=0&&this._setDate(e(s,c,l,h,u,d,0))),0!=this.viewMode){var f=this.viewMode;this.showMode(-1),this.fill(),f==this.viewMode&&this.autoclose&&this.hide()}else this.fill(),this.autoclose&&this.hide()}break;case"td":if(n.is(".day")&&!n.is(".disabled")){var l=parseInt(n.text(),10)||1,s=this.viewDate.getUTCFullYear(),c=this.viewDate.getUTCMonth(),h=this.viewDate.getUTCHours(),u=this.viewDate.getUTCMinutes(),d=this.viewDate.getUTCSeconds();n.is(".old")?0===c?(c=11,s-=1):c-=1:n.is(".new")&&(11==c?(c=0,s+=1):c+=1),this.viewDate.setUTCFullYear(s),this.viewDate.setUTCMonth(c),this.viewDate.setUTCDate(l),this.element.trigger({type:"changeDay",date:this.viewDate}),this.viewSelect>=2&&this._setDate(e(s,c,l,h,u,d,0))}var f=this.viewMode;this.showMode(-1),this.fill(),f==this.viewMode&&this.autoclose&&this.hide()}}},_setDate:function(t,e){e&&"date"!=e||(this.date=t),e&&"view"!=e||(this.viewDate=t),this.fill(),this.setValue();var i;this.isInput?i=this.element:this.component&&(i=this.element.find("input")),i&&(i.change(),this.autoclose&&(!e||"date"==e)),this.element.trigger({type:"changeDate",date:this.date})},moveMinute:function(t,e){if(!e)return t;var i=new Date(t.valueOf());return i.setUTCMinutes(i.getUTCMinutes()+e*this.minuteStep),i},moveHour:function(t,e){if(!e)return t;var i=new Date(t.valueOf());return i.setUTCHours(i.getUTCHours()+e),i},moveDate:function(t,e){if(!e)return t;var i=new Date(t.valueOf());return i.setUTCDate(i.getUTCDate()+e),i},moveMonth:function(t,e){if(!e)return t;var i,n,o=new Date(t.valueOf()),a=o.getUTCDate(),r=o.getUTCMonth(),s=Math.abs(e);if(e=e>0?1:-1,1==s)n=-1==e?function(){return o.getUTCMonth()==r}:function(){return o.getUTCMonth()!=i},i=r+e,o.setUTCMonth(i),(0>i||i>11)&&(i=(i+12)%12);else{for(var c=0;s>c;c++)o=this.moveMonth(o,e);i=o.getUTCMonth(),o.setUTCDate(a),n=function(){return i!=o.getUTCMonth()}}for(;n();)o.setUTCDate(--a),o.setUTCMonth(i);return o},moveYear:function(t,e){return this.moveMonth(t,12*e)},dateWithinRange:function(t){return t>=this.startDate&&t<=this.endDate},keydown:function(t){if(this.picker.is(":not(:visible)"))return 27==t.keyCode&&this.show(),void 0;var e,i,n,o=!1;switch(t.keyCode){case 27:this.hide(),t.preventDefault();break;case 37:case 39:if(!this.keyboardNavigation)break;e=37==t.keyCode?-1:1,viewMode=this.viewMode,t.ctrlKey?viewMode+=2:t.shiftKey&&(viewMode+=1),4==viewMode?(i=this.moveYear(this.date,e),n=this.moveYear(this.viewDate,e)):3==viewMode?(i=this.moveMonth(this.date,e),n=this.moveMonth(this.viewDate,e)):2==viewMode?(i=this.moveDate(this.date,e),n=this.moveDate(this.viewDate,e)):1==viewMode?(i=this.moveHour(this.date,e),n=this.moveHour(this.viewDate,e)):0==viewMode&&(i=this.moveMinute(this.date,e),n=this.moveMinute(this.viewDate,e)),this.dateWithinRange(i)&&(this.date=i,this.viewDate=n,this.setValue(),this.update(),t.preventDefault(),o=!0);break;case 38:case 40:if(!this.keyboardNavigation)break;e=38==t.keyCode?-1:1,viewMode=this.viewMode,t.ctrlKey?viewMode+=2:t.shiftKey&&(viewMode+=1),4==viewMode?(i=this.moveYear(this.date,e),n=this.moveYear(this.viewDate,e)):3==viewMode?(i=this.moveMonth(this.date,e),n=this.moveMonth(this.viewDate,e)):2==viewMode?(i=this.moveDate(this.date,7*e),n=this.moveDate(this.viewDate,7*e)):1==viewMode?this.showMeridian?(i=this.moveHour(this.date,6*e),n=this.moveHour(this.viewDate,6*e)):(i=this.moveHour(this.date,4*e),n=this.moveHour(this.viewDate,4*e)):0==viewMode&&(i=this.moveMinute(this.date,4*e),n=this.moveMinute(this.viewDate,4*e)),this.dateWithinRange(i)&&(this.date=i,this.viewDate=n,this.setValue(),this.update(),t.preventDefault(),o=!0);break;case 13:if(0!=this.viewMode){var a=this.viewMode;this.showMode(-1),this.fill(),a==this.viewMode&&this.autoclose&&this.hide()}else this.fill(),this.autoclose&&this.hide();t.preventDefault();break;case 9:this.hide()}if(o){var r;this.isInput?r=this.element:this.component&&(r=this.element.find("input")),r&&r.change(),this.element.trigger({type:"changeDate",date:this.date})}},showMode:function(t){if(t){var e=Math.max(0,Math.min(o.modes.length-1,this.viewMode+t));e>=this.minView&&e<=this.maxView&&(this.element.trigger({type:"changeMode",date:this.viewDate,oldViewMode:this.viewMode,newViewMode:e}),this.viewMode=e)}this.picker.find(">div").hide().filter(".datetimepicker-"+o.modes[this.viewMode].clsName).css("display","block"),this.updateNavArrows()},reset:function(){this._setDate(null,"date")}},t.fn.datetimepicker=function(e){var n=Array.apply(null,arguments);return n.shift(),this.each(function(){var o=t(this),a=o.data("datetimepicker"),r="object"==typeof e&&e;a||o.data("datetimepicker",a=new i(this,t.extend({},t.fn.datetimepicker.defaults,r))),"string"==typeof e&&"function"==typeof a[e]&&a[e].apply(a,n)})},t.fn.datetimepicker.defaults={},t.fn.datetimepicker.Constructor=i;var n=t.fn.datetimepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa","Su"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],meridiem:["am","pm"],suffix:["st","nd","rd","th"],today:"Today"}},o={modes:[{clsName:"minutes",navFnc:"Hours",navStep:1},{clsName:"hours",navFnc:"Date",navStep:1},{clsName:"days",navFnc:"Month",navStep:1},{clsName:"months",navFnc:"FullYear",navStep:1},{clsName:"years",navFnc:"FullYear",navStep:10}],isLeapYear:function(t){return 0===t%4&&0!==t%100||0===t%400},getDaysInMonth:function(t,e){return[31,o.isLeapYear(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]},getDefaultFormat:function(t,e){if("standard"==t)return"input"==e?"yyyy-mm-dd hh:ii":"yyyy-mm-dd hh:ii:ss";if("php"==t)return"input"==e?"Y-m-d H:i":"Y-m-d H:i:s";throw new Error("Invalid format type.")},validParts:function(t){if("standard"==t)return/hh?|HH?|p|P|ii?|ss?|dd?|DD?|mm?|MM?|yy(?:yy)?/g;if("php"==t)return/[dDjlNwzFmMnStyYaABgGhHis]/g;throw new Error("Invalid format type.")},nonpunctuation:/[^ -\/:-@\[-`{-~\t\n\rTZ]+/g,parseFormat:function(t,e){var i=t.replace(this.validParts(e),"\0").split("\0"),n=t.match(this.validParts(e));if(!i||!i.length||!n||0==n.length)throw new Error("Invalid date format.");return{separators:i,parts:n}},parseDate:function(o,a,r,s){if(o instanceof Date){var c=new Date(o.valueOf()-6e4*o.getTimezoneOffset());return c.setMilliseconds(0),c}if(/^\d{4}\-\d{1,2}\-\d{1,2}$/.test(o)&&(a=this.parseFormat("yyyy-mm-dd",s)),/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}$/.test(o)&&(a=this.parseFormat("yyyy-mm-dd hh:ii",s)),/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}\:\d{1,2}[Z]{0,1}$/.test(o)&&(a=this.parseFormat("yyyy-mm-dd hh:ii:ss",s)),/^[-+]\d+[dmwy]([\s,]+[-+]\d+[dmwy])*$/.test(o)){var l,h,u=/([-+]\d+)([dmwy])/,d=o.match(/([-+]\d+)([dmwy])/g);o=new Date;for(var f=0;f<d.length;f++)switch(l=u.exec(d[f]),h=parseInt(l[1]),l[2]){case"d":o.setUTCDate(o.getUTCDate()+h);break;case"m":o=i.prototype.moveMonth.call(i.prototype,o,h);break;case"w":o.setUTCDate(o.getUTCDate()+7*h);break;case"y":o=i.prototype.moveYear.call(i.prototype,o,h)}return e(o.getUTCFullYear(),o.getUTCMonth(),o.getUTCDate(),o.getUTCHours(),o.getUTCMinutes(),o.getUTCSeconds(),0)}var g,p,l,d=o&&o.match(this.nonpunctuation)||[],o=new Date(0,0,0,0,0,0,0),_={},b=["hh","h","ii","i","ss","s","yyyy","yy","M","MM","m","mm","D","DD","d","dd","H","HH","p","P"],m={hh:function(t,e){return t.setUTCHours(e)},h:function(t,e){return t.setUTCHours(e)},HH:function(t,e){return t.setUTCHours(12==e?0:e)},H:function(t,e){return t.setUTCHours(12==e?0:e)},ii:function(t,e){return t.setUTCMinutes(e)},i:function(t,e){return t.setUTCMinutes(e)},ss:function(t,e){return t.setUTCSeconds(e)},s:function(t,e){return t.setUTCSeconds(e)},yyyy:function(t,e){return t.setUTCFullYear(e)},yy:function(t,e){return t.setUTCFullYear(2e3+e)},m:function(t,e){for(e-=1;0>e;)e+=12;for(e%=12,t.setUTCMonth(e);t.getUTCMonth()!=e;)t.setUTCDate(t.getUTCDate()-1);return t},d:function(t,e){return t.setUTCDate(e)},p:function(t,e){return t.setUTCHours(1==e?t.getUTCHours()+12:t.getUTCHours())}};if(m.M=m.MM=m.mm=m.m,m.dd=m.d,m.P=m.p,o=e(o.getFullYear(),o.getMonth(),o.getDate(),o.getHours(),o.getMinutes(),o.getSeconds()),d.length==a.parts.length){for(var f=0,v=a.parts.length;v>f;f++){if(g=parseInt(d[f],10),l=a.parts[f],isNaN(g))switch(l){case"MM":p=t(n[r].months).filter(function(){var t=this.slice(0,d[f].length),e=d[f].slice(0,t.length);return t==e}),g=t.inArray(p[0],n[r].months)+1;break;case"M":p=t(n[r].monthsShort).filter(function(){var t=this.slice(0,d[f].length),e=d[f].slice(0,t.length);return t==e}),g=t.inArray(p[0],n[r].monthsShort)+1;break;case"p":case"P":g=t.inArray(d[f].toLowerCase(),n[r].meridiem)}_[l]=g}for(var w,f=0;f<b.length;f++)w=b[f],w in _&&!isNaN(_[w])&&m[w](o,_[w])}return o},formatDate:function(e,i,a,r){if(null==e)return"";var s;if("standard"==r)s={yy:e.getUTCFullYear().toString().substring(2),yyyy:e.getUTCFullYear(),m:e.getUTCMonth()+1,M:n[a].monthsShort[e.getUTCMonth()],MM:n[a].months[e.getUTCMonth()],d:e.getUTCDate(),D:n[a].daysShort[e.getUTCDay()],DD:n[a].days[e.getUTCDay()],p:2==n[a].meridiem.length?n[a].meridiem[e.getUTCHours()<12?0:1]:"",h:e.getUTCHours(),i:e.getUTCMinutes(),s:e.getUTCSeconds()},s.H=0==s.h%12?12:s.h%12,s.HH=(s.H<10?"0":"")+s.H,s.P=s.p.toUpperCase(),s.hh=(s.h<10?"0":"")+s.h,s.ii=(s.i<10?"0":"")+s.i,s.ss=(s.s<10?"0":"")+s.s,s.dd=(s.d<10?"0":"")+s.d,s.mm=(s.m<10?"0":"")+s.m;else{if("php"!=r)throw new Error("Invalid format type.");s={y:e.getUTCFullYear().toString().substring(2),Y:e.getUTCFullYear(),F:n[a].months[e.getUTCMonth()],M:n[a].monthsShort[e.getUTCMonth()],n:e.getUTCMonth()+1,t:o.getDaysInMonth(e.getUTCFullYear(),e.getUTCMonth()),j:e.getUTCDate(),l:n[a].days[e.getUTCDay()],D:n[a].daysShort[e.getUTCDay()],w:e.getUTCDay(),N:0==e.getUTCDay()?7:e.getUTCDay(),S:e.getUTCDate()%10<=n[a].suffix.length?n[a].suffix[e.getUTCDate()%10-1]:"",a:2==n[a].meridiem.length?n[a].meridiem[e.getUTCHours()<12?0:1]:"",g:0==e.getUTCHours()%12?12:e.getUTCHours()%12,G:e.getUTCHours(),i:e.getUTCMinutes(),s:e.getUTCSeconds()},s.m=(s.n<10?"0":"")+s.n,s.d=(s.j<10?"0":"")+s.j,s.A=s.a.toString().toUpperCase(),s.h=(s.g<10?"0":"")+s.g,s.H=(s.G<10?"0":"")+s.G,s.i=(s.i<10?"0":"")+s.i,s.s=(s.s<10?"0":"")+s.s}for(var e=[],c=t.extend([],i.separators),l=0,h=i.parts.length;h>l;l++)c.length&&e.push(c.shift()),e.push(s[i.parts[l]]);return e.join("")},convertViewMode:function(t){switch(t){case 4:case"decade":t=4;break;case 3:case"year":t=3;break;case 2:case"month":t=2;break;case 1:case"day":t=1;break;case 0:case"hour":t=0}return t},headTemplate:'<thead><tr><th class="prev"><i class="icon-angle-left"/></th><th colspan="5" class="switch"></th><th class="next"><i class="icon-angle-right"/></th></tr></thead>',contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr></tfoot>'};o.template='<div class="datetimepicker"><div class="datetimepicker-minutes"><table class=" table-condensed">'+o.headTemplate+o.contTemplate+o.footTemplate+"</table>"+"</div>"+'<div class="datetimepicker-hours">'+'<table class=" table-condensed">'+o.headTemplate+o.contTemplate+o.footTemplate+"</table>"+"</div>"+'<div class="datetimepicker-days">'+'<table class=" table-condensed">'+o.headTemplate+"<tbody></tbody>"+o.footTemplate+"</table>"+"</div>"+'<div class="datetimepicker-months">'+'<table class="table-condensed">'+o.headTemplate+o.contTemplate+o.footTemplate+"</table>"+"</div>"+'<div class="datetimepicker-years">'+'<table class="table-condensed">'+o.headTemplate+o.contTemplate+o.footTemplate+"</table>"+"</div>"+"</div>",t.fn.datetimepicker.DPGlobal=o,t.fn.datetimepicker.noConflict=function(){return t.fn.datetimepicker=old,this},t(document).on("focus.datetimepicker.data-api click.datetimepicker.data-api",'[data-provide="datetimepicker"]',function(e){var i=t(this);i.data("datetimepicker")||(e.preventDefault(),i.datetimepicker("show"))}),t(function(){t('[data-provide="datetimepicker-inline"]').datetimepicker()})}(window.jQuery);