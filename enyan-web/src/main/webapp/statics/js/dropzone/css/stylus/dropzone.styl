@import "nib"
@import "basic"

@keyframes loading
  from
    background-position: 0 -400px
  to
    background-position: -7px -400px

.dropzone
  border 1px solid rgba(0, 0, 0, 0.03)
  min-height 360px
  border-radius 3px
  background rgba(0, 0, 0, 0.03)
  padding 23px

  .default.message
    opacity 1
    transition opacity 0.3s ease-in-out
    
    image "../images/spritemap.png" 428px 406px
    background-repeat no-repeat
    background-position 0 0

    position absolute
    width 428px
    height 123px
    margin-left -(@width / 2)
    margin-top -(@height / 2)
    top 50%
    left 50%
    span
      display none

  &.square
    .default.message
      background-position 0 -123px
      width 268px
      margin-left -(@width / 2)
      height 174px
      margin-top -(@height / 2)

  &.drag-hover
    .message
      opacity 0.15

  &.started
    .message
      display block
      opacity 0 // Rather fade out nicely


.dropzone
.dropzone-previews

  .preview
    box-shadow 1px 1px 4px rgba(0, 0, 0, 0.16)
    font-size 14px


    .details
      img
        width 100px
        height 100px

      // Not implemented yet. This is the CSS definition of the file
      // content as text.
      // .content
      //   font-size 3px
      //   white-space pre
      //   position absolute
      //   top 5px
      //   left 12px
      //   right 19px
      //   bottom 5px
      //   overflow hidden
      //   line-height 100%
      //   cursor default
      //   word-wrap break-word

    &.image-preview
      &:hover
        .details
          img
            display block
            opacity 0.1

    &.success
      .success-mark
        opacity 1
    &.error
      .error-mark
        opacity 1
      .progress .upload
        background #EE1E2D

    .error-mark
    .success-mark
      display block
      opacity 0 // Fade in / out
      transition opacity 0.4s ease-in-out
      image "../images/spritemap.png" 428px 406px
      background-repeat no-repeat

      span
        display none
    .error-mark
      background-position -268px -123px
    .success-mark
      background-position -268px -163px



    .progress
      .upload
        animation loading 0.4s linear infinite
        transition width 0.3s ease-in-out
        border-radius 2px
        position absolute
        top 0
        left 0
        width 0%
        height 100%
        
        image "../images/spritemap.png" 428px 406px
        background-repeat repeat-x
        background-position 0px -400px


    &.success
      .progress
        display block
        opacity 0
        transition opacity 0.4s ease-in-out


    // Disabled for now until I find a better way to cope with long filenames
    // .filename
    //   span
    //     overflow ellipsis

    .error-message
      display block
      opacity 0 // Rather fade in / out
      transition opacity 0.3s ease-in-out

    &:hover.error
      .error-message
        opacity 1



