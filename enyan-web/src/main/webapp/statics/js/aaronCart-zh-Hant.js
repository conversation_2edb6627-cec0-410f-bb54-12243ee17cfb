/**
 *
 */
window.onload = function() {
    checkTable();
    //console.log("checkAll4.js");
};

//var table = document.getElementById("shit");
var checkAllBox = document.getElementById("checkAllBox");
var checkAllBoxBottom = document.getElementById("checkAllBoxBottom");
var sumLabel = document.getElementById("priceSum");
var sumLabel2 = document.getElementById("priceSum2");
var sumCurrencyLabel = document.getElementById("priceCurrencySum");
var goodsSelectLabel = document.getElementById("goodsSelect");

var checkMyBox = document.getElementsByName("cartProductId");
var checkABox = [];
var checkState = [];
var priceBox = [];
var priceCurrencyBox = [];
var sub=0;
var sum=0;
var sumTotal = 0;
var sumShould = 0;
var sumCurrency=0;
var productIds = []
var hasDiscount = false;

var priceMapString = $('#priceMap').val();
//console.log(priceMapString);
var priceMap = $.parseJSON(priceMapString);
// console.log("這是一個json對象：",priceMap);

function checkTable(){
    checkAllBox.checked = true;
    for(var i=0; i<checkMyBox.length; i++){
        priceBox[i] = document.getElementById("price"+i);
        priceCurrencyBox[i] = document.getElementById("priceCurrency"+i);
    }
    changeAllCheckState();

};

function changeAllCheckState(){
    checkAllBoxBottom.checked=checkAllBox.checked;
    for (var i=0; i<checkMyBox.length; i++){
        checkMyBox[i].checked=checkAllBox.checked;
    }
    changeSUB();
}

function changeAllCheckBottomState(){
    checkAllBox.checked=checkAllBoxBottom.checked;
    for (var i=0; i<checkMyBox.length; i++){
        checkMyBox[i].checked=checkAllBoxBottom.checked;
    }
    changeSUB();
}

function changeCheckState(){
    sub=0;
    var allState=true;

    for (var i=0; i<checkMyBox.length; i++){
        if (!checkMyBox[i].checked){
            allState = false;
        }
    }

    if(allState){
        checkAllBox.checked=true;
        checkAllBoxBottom.checked=true;
    }else{
        checkAllBox.checked=false;
        checkAllBoxBottom.checked=false;
    }
    changeSUB();
};

function changeSUB(){
    productIds = []
    sum= new Number(0);
    sumCurrency= new Number(0);
    for(var i=0; i<checkMyBox.length; i++){
        if(checkMyBox[i].checked){
            sum=sum+Number(priceBox[i].innerHTML);
            sumCurrency=sumCurrency+Number(priceCurrencyBox[i].innerHTML);
            productIds.push(checkMyBox[i].value);
        }
    }
    sumCurrencyLabel.innerHTML=sumCurrency.toFixed(2);

    sumTotal = new Number(0);
    sumShould = new Number(0);
    hasDiscount = false;
    $('#discountText').text("");
    priceMap.lineList.forEach(function(discount) {
        var sumFix= new Number(0);
        var sumDiscount= new Number(0);
        var productCount = 0;
        discount.productList.forEach(function(product) {
            if (productIds.indexOf(product.code.toString()) > -1){
                sumDiscount = sumDiscount + product.priceDiscount;
                sumFix = sumFix + product.price;
                if (product.priceDiscount > 0){//价格大于0
                    productCount++;
                }
                if (product.priceDiscount != product.price){
                    hasDiscount = true;
                }
            }
        });
        sumTotal = sumTotal + sumFix;
        if (discount.valid == true){
            // console.log("discount true");
            if (productCount>=discount.packageMuti) {
                hasDiscount = true;
                sumDiscount = (sumFix * discount.discountMuti/100);
                var discountText = getDiscountText(discount.packageMuti,discount.discountMuti);
                //$('#discountText').text("（"+discountText+"）");
                document.getElementById("discountTitle"+discount.discountId).innerHTML = "以下商品已享"+discountText;
            }else if (productCount>=discount.package) {
                hasDiscount = true;
                sumDiscount = (sumFix * discount.discount/100);
                var discountText = getDiscountText(discount.package,discount.discount);
                //$('#discountText').text("（"+discountText+"）");

                var discountTextNew = getDiscountText(discount.packageMuti,discount.discountMuti);
                document.getElementById("discountTitle"+discount.discountId).innerHTML = "以下商品已享"+discountText
                    + "，再買"+(discount.packageMuti-productCount)+"件可享"+discountTextNew
                    +"，<a href='category-0-grid-0-0-0-0-1-0-0-0'>去湊單></a>";
            }else if(productCount > 0){
                var discountTextNew = getDiscountText(discount.package,discount.discount);
                document.getElementById("discountTitle"+discount.discountId).innerHTML = "以下商品可享N件折優惠"
                    + "，再買"+(discount.package-productCount)+"件可享"+discountTextNew
                    +"，<a href='category-0-grid-0-0-0-0-1-0-0-0'>去湊單></a>";
            }else{
                document.getElementById("discountTitle"+discount.discountId).innerHTML = "以下商品可享N件折優惠";
            }
        }

        sumShould = sumShould + sumDiscount;
    });

    if (hasDiscount){
        document.getElementById("discountShow").style.display="inline-block";
    }else{
        document.getElementById("discountShow").style.display="none";
    }
    sumLabel.innerHTML=sumTotal.toFixed(2);
    sumLabel2.innerHTML=sumShould.toFixed(2);
    document.getElementById("priceDiscount").innerHTML= (sumTotal - sumShould).toFixed(2);
    sumCurrency= new Number(0);
    sumCurrency = sumShould * (new  Number($('#currency').val())) / (new  Number($('#currencyHK').val()))
    sumCurrencyLabel.innerHTML=sumCurrency.toFixed(2);
    goodsSelectLabel.innerHTML=productIds.length;
}
function getDiscountText(package,discount) {
    // console.log("getDiscountText:",package,discount);
    if (discount < 10){
        return package + "件0."+discount+"折";
    }
    if (discount %10 == 0){
        return package + "件"+discount/10+"折";
    }
    if (discount<100){
        return package + "件"+discount+"折";
    }
    return "";
}