(function(fa){function sc(){function n(e,c){e.attr("transform",function(e){return"translate("+(c(e)+U)+", 0)"})}function s(e,c){e.attr("transform",function(e){return"translate(0,"+c(e)+")"})}function fa(e){var c=e[0];e=e[e.length-1];return c<e?[c,e]:[e,c]}function Ca(e){for(var c=[],n=Math.ceil(e[0]);n<e[1];n++)c.push(n);0<c.length&&0<c[0]&&c.unshift(c[0]-(c[1]-c[0]));return c}function m(x){x.each(function(){var x=e.select(this),m=Ca(H.domain()),M=x.selectAll(".tick.major").data(m,String),C=M.enter().insert("g",
"path").attr("class","tick major").style("opacity",1E-6),ra=e.transition(M.exit()).style("opacity",1E-6).remove(),sa=e.transition(M).style("opacity",1),D,V=H.rangeExtent?H.rangeExtent():fa(H.range()),x=x.selectAll(".domain").data([0]);x.enter().append("path").attr("class","domain");var x=e.transition(x),N=H.copy(),fb=this.__chart__||N;this.__chart__=N;C.append("line");C.append("text");var ta=C.select("line"),Da=sa.select("line"),ha=M.select("text"),Ea=C.select("text"),Fa=sa.select("text");U=(N(1)-
N(0))/2;M=c?0:U;switch(w){case "bottom":D=n;ta.attr("y2",E);Ea.attr("y",Math.max(E,0)+I);Da.attr("x1",M).attr("x2",M).attr("y2",E);Fa.attr("x",0).attr("y",Math.max(E,0)+I);ha.attr("dy",".71em").style("text-anchor","middle");ha.text(function(e){return m.length<P||0===e%Math.ceil(m.length/P)?e<O.length?O[e]:e:""});x.attr("d","M"+V[0]+","+W+"V0H"+V[1]+"V"+W);break;case "left":D=s,ta.attr("x2",-E),Ea.attr("x",-(Math.max(E,0)+I)),Da.attr("x2",-E).attr("y2",0),Fa.attr("x",-(Math.max(E,0)+I)).attr("y",U),
ha.attr("dy",".32em").style("text-anchor","end"),ha.text(function(e){return m.length<P||0===e%Math.ceil(m.length/P)?e<O.length?O[e]:e:""}),x.attr("d","M"+-W+","+V[0]+"H0V"+V[1]+"H"+-W)}if(H.ticks)C.call(D,fb),sa.call(D,N),ra.call(D,N);else{var ia=N.rangeBand()/2,ra=function(e){return N(e)+ia};C.call(D,ra);sa.call(D,ra)}})}var H=e.scale.linear(),w="bottom",E=6,W=6,I=3,c=!1,P=10,U=0,O=[];m.scale=function(e){if(!arguments.length)return H;H=e;return m};m.orient=function(e){if(!arguments.length)return w;
w=e in{top:1,right:1,bottom:1,left:1}?e+"":"bottom";return m};m.categories=function(e){if(!arguments.length)return O;O=e;return m};m.tickCentered=function(e){if(!arguments.length)return c;c=e;return m};m.tickTextNum=function(e){if(!arguments.length)return P;P=e;return m};m.tickOffset=function(){return U};m.ticks=function(){};return m}function s(e){return"undefined"===typeof e}function n(e){return"undefined"!==typeof e}var gb=fa.c3={},e=fa.d3;gb.generate=function(T){var Bb,Cb,Ca,m,H,w,E,W,I;function c(a,
b){for(var d=T,h=0;h<a.length;h++){if(!(a[h]in d))return b;d=d[a[h]]}return d}function P(){hb=null===Eb?+e.select(ua).style("width").replace("px",""):Eb;var a=null===Fb?+e.select(ua).style("height").replace("px",""):Fb;va=0<a?a:320;Gb=20+Hb+ja;Ib=!Jb||f||ib?0:50;Va=jb?0:40;Kb=va-Hb-ja;Lb=20+ja;Mb=va-ja;w=0;E=Ib;W=Gb;I=Va;Ca=Kb;m=Lb;H=Va;Bb=Mb;Cb=Va;q=hb-I-E;t=va-w-W;ka=va-Ca-m}function U(){Nb=f?10:0;Ob=f?t:q;kb=f?0:t;lb=f?q:1;l=O(Nb,Ob,n(l)?l.domain():void 0,function(){return J.tickOffset()});z=x(kb,
lb,n(z)?z.domain():void 0);Q=x(kb,lb,n(Q)?Q.domain():void 0);X=O(0,q,n(R)?R:void 0,function(a){return 0===a%1?Ga.tickOffset():0});Wa=x(ka,10);Xa=x(ka,10);J=M(l,tc);la=e.svg.axis().scale(z).orient(uc);Ya=e.svg.axis().scale(Q).orient(vc);Ga=M(X,wc)}function O(a,b,d,h){var p=(A?e.time.scale():e.scale.linear()).range([a,b]);p.orgDomain=function(){return p.domain()};n(d)&&p.domain(d);s(h)&&(h=function(){return 0});if(Y){var wa=p,c,p=function(a){return wa(a)+h(a)};for(c in wa)p[c]=wa[c];p.orgDomain=function(){return wa.domain()};
p.domain=function(a){if(!arguments.length)return a=wa.domain(),[a[0],a[1]+1];wa.domain(a);return p}}return p}function x(a,b){return e.scale.linear().range([a,b])}function ga(a){return"y2"===C(a)?Q:z}function Ua(a){return"y2"===C(a)?Xa:Wa}function M(a,b){var d=(Y?sc():e.svg.axis()).scale(a).orient(b);A&&d.tickFormat(Pb?function(a){return e.time.format(Pb)(a)}:xc);Y?d.categories(Ha).tickCentered(yc):d.tickOffset=function(){return 0};return d}function C(a){return a in Qb?Qb[a]:"y"}function ra(a){return e.min(a,
function(a){return e.min(a.values,function(a){return a.value})})}function sa(a){var b={},d,h;a.forEach(function(a){b[a.id]=[];a.values.forEach(function(d){b[a.id].push(d.value)})});for(d=0;d<v.length;d++)for(h=1;h<v[d].length;h++)Ia(v[d][h])&&(s(b[v[d][h]])||b[v[d][h]].forEach(function(a,e){C(v[d][h])===C(v[d][0])&&(b[v[d][0]][e]+=1*a)}));return e.max(Object.keys(b).map(function(a){return e.max(b[a])}))}function D(a,b){var d=Za(function(a){return C(a.id)===b}),h="y2"===b?zc:Ac,p="y2"===b?Bc:Cc,h=
null!==h?h:ra(d),p=null!==p?p:sa(d),e=0.1*Math.abs(p-h),c=e,f=e,g="y2"===b?Dc:Ec;null!==g&&(h=Math.max(Math.abs(h),Math.abs(p)),p=h-g,h=g-h);"y"===b&&null!==Ja&&(c=n(Ja.top)?Ja.top:e,f=n(Ja.bottom)?Ja.bottom:e);"y2"===b&&null!==Ka&&(c=n(Ka.top)?Ka.top:e,f=n(Ka.bottom)?Ka.bottom:e);return[Fc(d,"bar")?0:h-f,p+c]}function V(a){var b;a?(a=l.domain(),b=F.extent()):(a=R,b=l.domain());return(a[1]-a[0])/(b[1]-b[0])}function N(a){for(var b=0;b<a.length;b++)if(!(a[b]in $a))return!1;return!0}function fb(a){for(var b=
[],d=0;d<a.length;d++)a[d]in $a&&b.push(Db($a[a[d]]));return b}function ta(a){return"start"in a?l(A?ma(a.start):a.start):0}function Da(a){var b=ta(a);a=("end"in a?l(A?ma(a.end):a.end):q)-b;return 0>a?0:a}function ha(a){var b=La[a.id];a.name=n(b)?b:a.id;return a}function Ea(a){var b=a[0],d={},h=[],e,c;for(e=1;e<a.length;e++){d={};for(c=0;c<a[e].length;c++)d[b[c]]=a[e][c];h.push(d)}return h}function Fa(a){var b=[],d,h,e;for(d=0;d<a.length;d++)for(e=a[d][0],h=1;h<a[d].length;h++)s(b[h-1])&&(b[h-1]={}),
b[h-1][e]=a[d][h];return b}function ia(a){var b=e.keys(a[0]).filter(function(a){return a!==na}),d=0,h;if(A&&!na)return fa.alert('data.x must be specified when axis.x.type == "timeseries"'),[];a.forEach(function(a){if(A){if(!(na in a))throw Error("'"+na+"' must be included in data");h=ma(a[na]);if(null===h)throw Error("Failed to parse timeseries date in data");a.x=h}else a.x=Rb?a[na]:d++;null===ab&&(ab=new Date(a.x));mb=new Date(a.x)});b=b.map(function(b){var d=Gc(b);return{id:d,id_org:b,values:a.map(function(a){return{x:a.x,
value:null!==a[b]?+a[b]:null,id:d}})}});b.forEach(function(a){$a[a.id_org]=Db(a)});return b}function Db(a){return{id:a.id,id_org:a.id_org,values:a.values.map(function(a){return{x:a.x,value:a.value,id:a.id}})}}function xa(){return e.max(k.data.targets,function(a){return a.values.length})}function nb(a){a=s(a)?k.data.targets:a;return a.map(function(a){return a.id})}function ob(a){var b=nb(),d;for(d=0;d<b.length;d++)if(b[d]===a)return!0;return!1}function Za(a){return n(a)?k.data.targets.filter(a):k.data.targets}
function pb(a){return"-shapes -shapes-"+a.id}function Sb(a){return pb(a)+" -line -line-"+a.id}function gb(a){return pb(a)+" -circles -circles-"+a.id}function Tb(a){return pb(a)+" -bars -bars-"+a.id}function Hc(a,b){return"-shape -shape-"+b+" -circle -circle-"+b}function Ub(a,b){return"-shape -shape-"+b+" -bar -bar-"+b}function Ic(a,b){return"region region-"+b+" "+("classes"in a?[].concat(a.classes).join(" "):"")}function bb(a){return l(a.x)}function Ma(a){return l(A?ma(a.value):a.value)}function Vb(a){return z(a.value)}
function Z(a){return l(a.x)}function $(a){return ga(a.id)(a.value)}function Jc(){var a={},b=0,d,h;Za(Ia).forEach(function(e){for(d=0;d<v.length;d++)if(!(0>v[d].indexOf(e.id)))for(h=0;h<v[d].length;h++)if(v[d][h]in a){a[e.id]=a[v[d][h]];break}s(a[e.id])&&(a[e.id]=b++)});a.__max__=b-1;return a}function Wb(a,b,d,e){var p=e?X:l;return function(e){var h=e.id in d?d[e.id]:0;return p(e.x)-a*(b/2-h)}}function Xb(a,b,d,e){var p=Object.keys(b);return function(c,f){var g=0,k=e?Ua(c.id):ga(c.id);Za(Ia).forEach(function(d){d.id!==
c.id&&b[d.id]===b[c.id]&&p.indexOf(d.id)<p.indexOf(c.id)&&(g+=a(d.values[f]))});return d?g:k(c.value)-g}}function Yb(a,b){var d=null===a?function(a){return a}:function(b){return a-b};return function(a){var e=b?Ua(a.id):ga(a.id);return d(e(a.value))}}function qb(a,b){var d=s(a)?nb():a;"string"===typeof d&&(d=[d]);for(var e=0;e<d.length;e++)aa[d[e]]=b}function Fc(a,b){var d=!1;a.forEach(function(a){aa[a.id]===b&&(d=!0);a.id in aa||"line"!==b||(d=!0)});return d}function rb(a){a="string"===typeof a?a:
a.id;return!(a in aa)||"line"===aa[a]||"spline"===aa[a]}function Ia(a){return"bar"===aa["string"===typeof a?a:a.id]}function Kc(a){return rb(a)?a.values:[]}function Zb(a){return Ia(a)?a.values:[]}function $b(a,b){var d=e.mouse(a),h=e.select(a),c=1*h.attr("cx"),h=1*h.attr("cy");return Math.sqrt(Math.pow(c-d[0],2)+Math.pow(h-d[1],2))<b}function ac(a){var b=e.mouse(a),d=e.select(a);a=1*d.attr("x");var h=1*d.attr("y"),d=1*d.attr("width");return a-10<b[0]&&b[0]<a+d+10&&h-10<b[1]}function Lc(a,b){var d;
for(d=0;d<b.length;d++)if(b[d].start<a&&a<=b[d].end)return!0;return!1}function bc(a,b,d){Mc(a,b);g.select(".selected-circles-"+b.id).selectAll(".selected-circle-"+d).data([b]).enter().append("circle").attr("class",function(){return"selected-circle selected-circle-"+d}).attr("cx",f?$:Z).attr("cy",f?Z:$).attr("stroke",function(){return S(b.id)}).attr("r",1.4*Na).transition().duration(100).attr("r",Na)}function sb(a,b,d){Nc(a,b);g.select(".selected-circles-"+b.id).selectAll(".selected-circle-"+d).transition().duration(100).attr("r",
0).remove()}function cc(a,b,d,e){a?bc(b,d,e):sb(b,d,e)}function Oc(){}function dc(){}function ec(a,b,d,e){}function fc(a){return a.filter(function(a){return null!==a.value})}function Pc(a,b,d,e){var c,g,r="M",k,l,u,gc,q,m=[];if(n(e))for(c=0;c<e.length;c++)m[c]={},s(e[c].start)?m[c].start=a[0].x:A&&(m[c].start=ma(e[c].start)),s(e[c].end)?m[c].end=a[a.length-1].x:A&&(m[c].end=ma(e[c].end));gc=f?function(a){return d(a.value)}:function(a){return b(a.x)};q=f?function(a){return b(a.x)}:function(a){return d(a.value)};
e=A?function(a,e,c,h){var f=a.x.getTime();a=e.x-a.x;e=new Date(f+a*(c+h));return"M"+b(new Date(f+a*c))+" "+d(l(c))+" "+b(e)+" "+d(l(c+h))}:function(a,e,c,h){return"M"+b(k(c))+" "+d(l(c))+" "+b(k(c+h))+" "+d(l(c+h))};for(c=0;c<a.length;c++)if(s(m)||!Lc(a[c].x,m))r+=" "+gc(a[c])+" "+q(a[c]);else{k=O(a[c-1].x,a[c].x);l=x(a[c-1].value,a[c].value);g=b(a[c].x)-b(a[c-1].x);u=d(a[c].value)-d(a[c-1].value);g=Math.sqrt(Math.pow(g,2)+Math.pow(u,2));u=2/g;var t=2*u;for(g=u;1>=g;g+=t)r+=e(a[c-1],a[c],g,u)}return r}
function tb(a){var b=k.data.targets=ia(a),d,c;hc=e.select(ua);if(hc.empty())fa.alert('No bind element found. Check the selector specified by "bindto" and existance of that element. Default "bindto" is "#chart".');else{P();U();l.domain(e.extent(a.map(function(a){return a.x})));z.domain(D(b,"y"));Q.domain(D(b,"y2"));X.domain(l.domain());Wa.domain(z.domain());Xa.domain(Q.domain());J.ticks(10>a.length?a.length:10);la.ticks(Qc).outerTickSize(0).tickFormat(Rc);Ya.ticks(Sc).outerTickSize(0).tickFormat(Tc);
R=l.domain();F.x(X);oa&&G.x(l);Oa=e.select(ua).append("svg").attr("width",q+I+E).attr("height",t+w+W).on("mouseenter",Uc).on("mouseleave",Vc);cb=Oa.append("defs");cb.append("clipPath").attr("id",ub).append("rect").attr("y",w).attr("width",q).attr("height",t-w);cb.append("clipPath").attr("id","xaxis-clip").append("rect").attr("x",-1).attr("y",-1).attr("width",q+2).attr("height",40);cb.append("clipPath").attr("id","yaxis-clip").append("rect").attr("x",-I+1).attr("y",w-1).attr("width",I).attr("height",
t-w+2);g=Oa.append("g").attr("transform",K.main);y=ya?Oa.append("g").attr("transform",K.context):null;za=Aa?Oa.append("g").attr("transform",K.legend):null;ba=e.select(ua).style("position","relative").append("div").style("position","absolute").style("width","30%").style("z-index","10").style("visibility","hidden");g.append("g").attr("class","x axis").attr("clip-path",f?"":"url(#xaxis-clip)").attr("transform",K.x).call(f?la:J);g.append("g").attr("class","y axis").attr("clip-path",f?"url(#yaxis-clip)":
"").call(f?J:la).append("text").attr("transform","rotate(-90)").attr("dy","1.4em").attr("dx","-.8em").style("text-anchor","end").text(Wc);Jb&&g.append("g").attr("class","y2 axis").attr("transform",K.y2).call(Ya);d=g.append("g").attr("clip-path",Pa).attr("class","grid");ic&&d.append("g").attr("class","xgrids");vb&&(c=d.append("g").attr("class","xgrid-lines").selectAll(".xgrid-line").data(vb).enter().append("g").attr("class","xgrid-line"),c.append("line").attr("class",function(a){return""+a["class"]}),
c.append("text").attr("class",function(a){return""+a["class"]}).attr("text-anchor","end").attr("transform",f?"":"rotate(-90)").attr("dx",f?0:-w).attr("dy",-6).text(function(a){return a.text}));Xc&&d.append("g").attr("class","xgrid-focus").append("line").attr("class","xgrid-focus").attr("x1",f?0:-10).attr("x2",f?q:-10).attr("y1",f?-10:w).attr("y2",f?-10:t);jc&&d.append("g").attr("class","ygrids");wb&&d.append("g").attr("class","ygrid-lines").selectAll("ygrid-line").data(wb).enter().append("line").attr("class",
function(a){return"ygrid-line "+a["class"]});g.append("g").attr("clip-path",Pa).attr("class","regions");g.append("g").attr("clip-path",Pa).attr("class","chart");g.select(".chart").append("g").attr("class","event-rects").style("fill-opacity",0).style("cursor",oa?"ew-resize":null).selectAll(".event-rects").data(a).enter().append("rect").attr("class",function(a,b){return"event-rect event-rect-"+b}).style("cursor",ca&&pa?"pointer":null).on("mouseover",function(a,b){if(!db){var d=k.data.targets.map(function(a){return ha(a.values[b])}),
c,h;if(0<Object.keys(La).length){h=[];for(var n in La)for(c=0;c<d.length;c++)if(d[c].id===n){h.push(d[c]);d.shift(c);break}d=h.concat(d)}kc&&g.selectAll(".-circle-"+b).classed(da,!0).attr("r",lc);g.selectAll(".-bar-"+b).classed(da,!0);g.selectAll("line.xgrid-focus").style("visibility","visible").data([d[0]]).attr(f?"y1":"x1",bb).attr(f?"y2":"x2",bb);ba.style("top",e.mouse(this)[1]+30+"px").style("left",(f?e.mouse(this)[0]:l(d[0].x))+60+"px");ba.html(mc(d));ba.style("visibility","visible")}}).on("mouseout",
function(a,b){g.select("line.xgrid-focus").style("visibility","hidden");ba.style("visibility","hidden");g.selectAll(".-circle-"+b).filter(function(){return e.select(this).classed(da)}).classed(da,!1).attr("r",xb);g.selectAll(".-bar-"+b).classed(da,!1)}).on("mousemove",function(a,b){ca&&!db&&(pa||g.selectAll(".-shape-"+b).filter(function(a){return qa(a)}).each(function(){var a=e.select(this).classed(da,!0);"circle"===this.nodeName&&a.attr("r",lc);e.select(".event-rect-"+b).style("cursor",null)}).filter(function(){var a=
e.select(this);if("circle"===this.nodeName)return $b(this,Na);if("rect"===this.nodeName)return ac(this,a.attr("x"),a.attr("y"))}).each(function(){var a=e.select(this);a.classed(da)||(a.classed(da,!0),"circle"===this.nodeName&&a.attr("r",Na));e.select(".event-rect-"+b).style("cursor","pointer")}))}).on("click",function(a,b){yb?yb=!1:g.selectAll(".-shape-"+b).each(function(a){var d=e.select(this),c=d.classed(ea),h=!1,f;"circle"===this.nodeName?(h=$b(this,1.5*Na),f=cc):"rect"===this.nodeName&&(h=ac(this),
f=ec);if(pa||h)ca&&qa(a)&&(d.classed(ea,!c),f(!c,d,a,b)),Yc(a,d)})}).call(e.behavior.drag().origin(Object).on("drag",function(){if(ca&&(!oa||G.altDomain)){var a=zb[0],b=zb[1],d=e.mouse(this),c=d[0],d=d[1],h=Math.min(a,c),f=Math.max(a,c),k=pa?w:Math.min(b,d),l=pa?t:Math.max(b,d);g.select(".dragarea").attr("x",h).attr("y",k).attr("width",f-h).attr("height",l-k);g.selectAll(".-shapes").selectAll(".-shape").filter(function(a){return qa(a)}).each(function(a,b){var d=e.select(this),c=d.classed(ea),g=d.classed(Ab),
p,r,n,m;p=!1;"circle"===this.nodeName?(p=1*d.attr("cx"),r=1*d.attr("cy"),m=cc,p=h<p&&p<f&&k<r&&r<l):"rect"===this.nodeName&&(p=1*d.attr("x"),r=1*d.attr("y"),n=1*d.attr("width"),m=ec,p=h<p+n&&p<f&&r<l);p^g&&(d.classed(Ab,!g),d.classed(ea,!c),m(!c,d,a,b))})}}).on("dragstart",function(){ca&&(zb=e.mouse(this),g.select(".chart").append("rect").attr("class","dragarea").style("opacity",0.1),db=!0)}).on("dragend",function(){ca&&(g.select(".dragarea").transition().duration(100).style("opacity",0).remove(),
g.selectAll(".-shape").classed(Ab,!1),db=!1)})).call(G).on("dblclick.zoom",null);g.select(".chart").append("g").attr("class","chart-bars");g.select(".chart").append("g").attr("class","chart-lines");if(oa)g.insert("rect",Zc?null:"g.grid").attr("class","zoom-rect").attr("width",q).attr("height",t).style("opacity",0).style("cursor","ew-resize").call(G).on("dblclick.zoom",null);null!==Qa&&F.extent("function"!==typeof Qa?Qa:A?Qa(ab,mb):Qa(0,xa()-1));ya&&(y.append("g").attr("clip-path",Pa).attr("class",
"chart"),y.select(".chart").append("g").attr("class","chart-bars"),y.select(".chart").append("g").attr("class","chart-lines"),y.append("g").attr("clip-path",Pa).attr("class","x brush").call(F).selectAll("rect").attr("height",ka),y.append("g").attr("class","x axis").attr("transform",K.subx).call(Ga));Aa&&eb(b);nc(b);B({withTransition:!1,withUpdateXDomain:!0});if($c){if(A&&"string"===typeof Ba){Ba=ma(Ba);for(a=0;a<b[0].values.length&&0!==b[0].values[a].x-Ba;a++);Ba=a}ba.html(mc(b.map(function(a){return ha(a.values[Ba])})));
ba.style("top",oc.top).style("left",oc.left).style("visibility","visible")}}}function B(a){var b,d,c=Jc(),p=c.__max__+1,m,r,s,v,u;a=n(a)?a:{};b=n(a.withY)?a.withY:!0;v=n(a.withSubchart)?a.withSubchart:!0;u=n(a.withTransition)?a.withTransition:!0;a=n(a.withUpdateXDomain)?a.withUpdateXDomain:!1;u=u?250:0;a&&(l.domain(F.empty()?R:F.extent()),oa&&G.x(l).updateScaleExtent());z.domain(D(k.data.targets,"y"));Q.domain(D(k.data.targets,"y2"));g.select(".x.axis").transition().duration(f?u:0).call(f?la:J);g.select(".y.axis").transition().duration(f?
0:u).call(f?J:la);g.select(".y2.axis").transition().call(Ya);Wa.domain(z.domain());Xa.domain(Q.domain());ba.style("visibility","hidden");g.select("line.xgrid-focus").style("visibility","hidden").attr("y2",t);if(ic){if("year"===ad)for(a=[],r=ab.getFullYear(),m=mb.getFullYear();r<=m;r++)a.push(new Date(r+"-01-01 00:00:00"));else a=l.ticks(10);a=g.select(".xgrids").selectAll(".xgrid").data(a);a.enter().append("line").attr("class","xgrid");a.exit().remove();g.selectAll(".xgrid").attr("x1",function(a){return l(a)-
J.tickOffset()}).attr("x2",function(a){return l(a)-J.tickOffset()}).attr("y1",w).attr("y2",t)}vb&&(a=g.selectAll(".xgrid-lines"),a.selectAll("line").attr("x1",f?0:Ma).attr("x2",f?q:Ma).attr("y1",f?Ma:w).attr("y2",f?Ma:t),a.selectAll("text").attr("x",f?q:0).attr("y",Ma));b&&jc&&(a=g.select(".ygrids").selectAll(".ygrid").data(z.ticks(10)),a.enter().append("line").attr("class","ygrid"),a.attr("x1",f?z:0).attr("x2",f?z:q).attr("y1",f?0:z).attr("y2",f?t:z).attr("opacity",0).transition().attr("opacity",
1),a.exit().remove());b&&wb&&g.select(".ygrid-lines").selectAll(".ygrid-line").attr("y1",Vb).attr("y2",Vb);m=Y?1.2*J.tickOffset()/p:0.6*((f?t:q)*V(!1)/(xa()-1));r=Yb(f?null:t);b=Wb(m,p,c);a=Xb(r,c,f);d=g.selectAll(".-bars").selectAll(".-bar").data(Zb);d.transition().duration(u).attr("x",f?a:b).attr("y",f?b:a).attr("width",f?r:m).attr("height",f?m:r);d.enter().append("rect").attr("class",Ub).attr("x",f?a:b).attr("y",f?b:a).attr("width",f?r:m).attr("height",f?m:r).style("opacity",0).transition().duration(u).style("opacity",
1);d.exit().transition().duration(u).style("opacity",0).remove();g.selectAll(".-line").transition().duration(u).attr("d",bd);b=g.selectAll(".-circles").selectAll(".-circle").data(Kc);b.transition().duration(u).style("opacity",function(a){return null===a.value?0:1}).attr("cx",f?$:Z).attr("cy",f?Z:$);b.enter().append("circle").style("opacity",function(a){return null===a.value?0:1}).attr("class",Hc).attr("cx",f?$:Z).attr("cy",f?Z:$).attr("r",xb);b.exit().remove();ya&&(null!==e.event&&"zoom"===e.event.type&&
F.extent(l.orgDomain()).update(),v&&(y.select(".x.axis").transition().duration(f?u:0).call(f?la:Ga),F.empty()||F.extent(l.orgDomain()).update(),m=Y?1.2*Ga.tickOffset()/p:0.6*((f?t:q)*V(!0)/(xa()-1)),r=Yb(ka,!0),b=Wb(m,p,c,!0),a=Xb(r,c,!1,!0),c=y.selectAll(".-bars").selectAll(".-bar").data(Zb),c.transition().duration(u).attr("x",b).attr("y",a).attr("width",m).attr("height",r),c.enter().append("rect").attr("class",Ub).attr("x",b).attr("y",a).attr("width",m).attr("height",r).style("opacity",0).transition().style("opacity",
1),c.exit().transition().style("opacity",0).remove(),y.selectAll(".-line").transition().duration(u).attr("d",cd)));g.selectAll(".selected-circles").filter(function(a){return Ia(a)}).selectAll("circle").remove();g.selectAll(".selected-circle").transition().duration(u).attr("cx",f?$:Z).attr("cy",f?Z:$);Rb?(s=function(a,b){var d=0<b?k.data.targets[0].values[b-1].x:void 0,c=b<xa()-1?k.data.targets[0].values[b+1].x:void 0;return(l(c?c:a.x+50)-l(d?d:a.x-50))/2},c=function(a,b){var d=0<b?k.data.targets[0].values[b-
1].x:void 0;return(l(a.x)+l(d?d:a.x-50))/2}):(s=(f?t:q)*V()/(xa()-1),c=function(a){return l(a.x)-s/2});g.selectAll(".event-rect").attr("x",f?0:c).attr("y",f?c:0).attr("width",f?q:s).attr("height",f?s:t);c=g.select(".regions").selectAll("rect.region").data(L);c.enter().append("rect");c.attr("class",Ic).attr("x",f?0:ta).attr("y",f?ta:w).attr("width",f?q:Da).attr("height",f?Da:t).style("fill-opacity",function(a){return n(a.opacity)?a.opacity:0.1});c.exit().transition().duration(u).style("fill-opacity",
0).remove()}function dd(){"mousemove"===e.event.sourceEvent.type&&G.altDomain?(l.domain(G.altDomain),G.x(l).updateScaleExtent()):(Y&&l.orgDomain()[0]===R[0]&&l.domain([R[0]-1E-10,l.orgDomain()[1]]),B({withTransition:!1,withY:!1,withSubchart:!1}),"mousemove"===e.event.sourceEvent.type&&(yb=!0))}function nc(a){var b;g.select(".chart-bars").selectAll(".chart-bar").data(a).enter().append("g").attr("class",function(a){return"chart-bar target target-"+a.id}).style("pointer-events","none").style("opacity",
0).append("g").attr("class",Tb).style("fill",function(a){return S(a.id)}).style("stroke",function(a){return S(a.id)}).style("stroke-width",0).style("cursor",function(a){return qa(a)?"pointer":null});b=g.select(".chart-lines").selectAll(".chart-line").data(a).enter().append("g").attr("class",function(a){return"chart-line target target-"+a.id}).style("pointer-events","none").style("opacity",0);b.append("path").attr("class",Sb).style("stroke",function(a){return S(a.id)});b.append("g").attr("class",function(a){return"selected-circles selected-circles-"+
a.id});b.append("g").attr("class",gb).style("fill",function(a){return S(a.id)}).style("cursor",function(a){return qa(a)?"pointer":null});a.forEach(function(a){g.selectAll(".selected-circles-"+a.id).selectAll(".selected-circle").each(function(b){b.value=a.values[b.x].value})});ya&&(b=y.select(".chart-bars").selectAll(".chart-bar").data(a),b=b.enter().append("g").attr("class",function(a){return"chart-bar target target-"+a.id}).style("opacity",0),b.append("g").attr("class",Tb).style("fill",function(a){return S(a.id)}),
b=y.select(".chart-lines").selectAll(".chart-line").data(a),b=b.enter().append("g").attr("class",function(a){return"chart-line target target-"+a.id}).style("opacity",0),b.append("path").attr("class",Sb).style("stroke",function(a){return S(a.id)}));Aa&&eb(a);e.selectAll(".target").transition().style("opacity",1)}function Ra(a,b){k.data.targets.forEach(function(b){for(var c=0;c<a.length;c++)if(b.id===a[c].id){b.values=a[c].values;a.splice(c,1);break}});k.data.targets=k.data.targets.concat(a);nc(k.data.targets);
B();b()}function eb(a,b){var d=nb(a),c,f=q/2-Sa*Object.keys(a).length/2,g;b=s(b)?{}:b;g=n(b.withTransition)?b.withTransition:!0;c=za.selectAll(".legend-item").data(d).enter().append("g").attr("class",function(a){return"legend-item legend-item-"+a}).style("cursor","pointer").on("click",function(a){ed(a)}).on("mouseover",function(a){e.selectAll(".legend-item").filter(function(b){return b!==a}).transition().duration(100).style("opacity",0.3);k.focus(a)}).on("mouseout",function(){e.selectAll(".legend-item").transition().duration(100).style("opacity",
1);k.revert()});c.append("rect").attr("class","legend-item-event").style("fill-opacity",0).attr("x",-200).attr("y",function(){return ja/2-16}).attr("width",Sa).attr("height",24);c.append("rect").attr("class","legend-item-tile").style("fill",function(a){return S(a)}).attr("x",-200).attr("y",function(){return ja/2-9}).attr("width",10).attr("height",10);c.append("text").text(function(a){return n(La[a])?La[a]:a}).attr("x",-200).attr("y",function(){return ja/2});za.selectAll("rect.legend-item-event").data(d).transition().duration(g?
250:0).attr("x",function(a,b){return f+Sa*b});za.selectAll("rect.legend-item-tile").data(d).transition().duration(g?250:0).attr("x",function(a,b){return f+Sa*b});za.selectAll("text").data(d).transition().duration(g?250:0).attr("x",function(a,b){return f+Sa*b+14})}function Ta(a){return n(a)?".target-"+a:".target"}var k={data:{}},$a={},da="_expanded_",ea="_selected_",Ab="_included_",ua=c(["bindto"],"#chart"),Eb=c(["size","width"],null),Fb=c(["size","height"],null),oa=c(["zoom","enabled"],!1),pc=c(["zoom",
"extent"],null),Zc=c(["zoom","privileged"],!1),Uc=c(["onenter"],function(){}),Vc=c(["onleave"],function(){});if(!("data"in T))throw Error("data is required in config");var na=c(["data","x"],void 0),fd=c(["data","x_format"],"%Y-%m-%d"),Gc=c(["data","id_converter"],function(a){return a}),La=c(["data","names"],{}),v=c(["data","groups"],[]),Qb=c(["data","axes"],{}),aa=c(["data","types"],{}),qc=c(["data","regions"],{}),gd=c(["data","colors"],{}),ca=c(["data","selection","enabled"],!1),pa=c(["data","selection",
"grouped"],!1),qa=c(["data","selection","isselectable"],function(){return!0}),ya=c(["subchart","show"],!1),Hb=ya?c(["subchart","size","height"],60):0,hd=c(["color","pattern"],null),Aa=c(["legend","show"],!0),Sa=c(["legend","item","width"],80),ed=c(["legend","item","onclick"],function(){}),rc=c(["axis","x","type"],"indexed"),Ha=c(["axis","x","categories"],[]),yc=c(["axis","x","tick","centered"],!1),Pb=c(["axis","x","tick","format"],null),Qa=c(["axis","x","default"],null),Cc=c(["axis","y","max"],null),
Ac=c(["axis","y","min"],null),Ec=c(["axis","y","center"],null),Wc=c(["axis","y","text"],null),jb=c(["axis","y","inner"],!1),Rc=c(["axis","y","format"],function(a){return a}),Ja=c(["axis","y","padding"],null),Qc=c(["axis","y","ticks"],10),Jb=c(["axis","y2","show"],!1),Bc=c(["axis","y2","max"],null),zc=c(["axis","y2","min"],null),Dc=c(["axis","y2","center"],null),ib=c(["axis","y2","inner"],!1),Tc=c(["axis","y2","format"],function(a){return a}),Ka=c(["axis","y2","padding"],null),Sc=c(["axis","y2","ticks"],
10),f=c(["axis","rotated"],!1),ic=c(["grid","x","show"],!1),ad=c(["grid","x","type"],"tick"),vb=c(["grid","x","lines"],null),jc=c(["grid","y","show"],!1),wb=c(["grid","y","lines"],null),xb=c(["point","show"],!0)?c(["point","r"],2.5):0,Xc=c(["point","focus","line","enabled"],!0),kc=c(["point","focus","expand","enabled"],!0),lc=c(["point","focus","expand","r"],kc?4:xb),Na=c(["point","focus","select","r"],8),Yc=c(["point","onclick"],function(){}),Mc=c(["point","onselected"],function(){}),Nc=c(["point",
"onunselected"],function(){}),L=c(["regions"],[]),mc=c(["tooltip","contents"],function(a){var b="<table class='-tooltip'><tr><th colspan='2'>"+(A?a[0].x.getFullYear()+"."+(a[0].x.getMonth()+1)+"."+a[0].x.getDate():Y?a[0].x<Ha.length?Ha[a[0].x]:a[0].x:a[0].x)+"</th></tr>",d,c,e;for(d=0;d<a.length;d++)n(a[d])?(c=n(a[d].value)?(Math.round(100*a[d].value)/100).toFixed(2):"-",e=a[d].name):e=c="-",b+="<tr class='-tooltip-name-"+a[d].id+"'><td class='name'><span style='background-color:"+S(a[d].id)+"'></span>"+
e+"</td><td class='value'>"+c+"</td></tr>";return b+"</table>"}),$c=c(["tooltip","init","show"],!1),Ba=c(["tooltip","init","x"],0),oc=c(["tooltip","init","position"],{top:"0px",left:"50px"}),ub=ua.replace("#","")+"-clip",Pa="url(#"+ub+")",A="timeseries"===rc,Y="categorized"===rc,Rb=!A&&na,zb=null,db=!1,yb=!1,ja=Aa?40:0,ma=e.time.format(fd).parse,S=function(a,b){var d=[],c=null!==b?b:"#1f77b4 #ff7f0e #2ca02c #d62728 #9467bd #8c564b #e377c2 #7f7f7f #bcbd22 #17becf".split(" ");return function(b){if(b in
a)return a[b];-1===d.indexOf(b)&&d.push(b);return c[d.indexOf(b)%c.length]}}(gd,hd),xc=function(){var a=[[e.time.format("%Y/%-m/%-d"),function(){return!0}],[e.time.format("%-m/%-d"),function(a){return a.getMonth()}],[e.time.format("%-m/%-d"),function(a){return 1!==a.getDate()}],[e.time.format("%-m/%-d"),function(a){return a.getDay()&&1!==a.getDate()}],[e.time.format("%I %p"),function(a){return a.getHours()}],[e.time.format("%I:%M"),function(a){return a.getMinutes()}],[e.time.format(":%S"),function(a){return a.getSeconds()}],
[e.time.format(".%L"),function(a){return a.getMilliseconds()}]];return function(b){for(var d=a.length-1,c=a[d];!c[1](b);)c=a[--d];return c[0](b)}}(),Gb,Lb,Ib,Va,Kb,Mb,q,t,ka,hb,va,Nb,Ob,kb,lb,l,z,Q,X,Wa,Xa,J,la,Ya,Ga,tc=f?"left":"bottom",uc=f?jb?"top":"bottom":jb?"right":"left",vc=f?ib?"bottom":"top":ib?"left":"right",wc="bottom",K={main:function(){return"translate("+I+","+w+")"},context:function(){return"translate("+H+","+Ca+")"},legend:function(){return"translate("+Cb+","+Bb+")"},y2:function(){return"translate("+
(f?0:q)+","+(f?10:0)+")"},x:function(){return"translate(0,"+t+")"},subx:function(){return"translate(0,"+ka+")"}},bd=function(){var a=e.svg.line().x(f?function(a){return ga(a.id)(a.value)}:bb).y(f?bb:function(a){return ga(a.id)(a.value)});return function(b){var d=fc(b.values),c;if(rb(b))return"spline"===aa["string"===typeof b?b:b.id]?a.interpolate("cardinal"):a.interpolate("linear"),0<Object.keys(qc).length?Pc(d,l,ga(b.id),qc[b.id]):a(d);c=l(d[0].x);b=ga(b.id)(d[0].value);return f?"M "+b+" "+c:"M "+
c+" "+b}}(),cd=function(){var a=e.svg.line().x(function(a){return X(a.x)}).y(function(a){return Ua(a.id)(a.value)});return function(b){var c=fc(b.values);return rb(b)?a(c):"M "+X(c[0].x)+" "+Ua(b.id)(c[0].value)}}(),F=e.svg.brush().on("brush",function(){B({withTransition:!1,withY:!1,withSubchart:!1,withUpdateXDomain:!0})}),G=e.behavior.zoom().on("zoomstart",function(){G.altDomain=e.event.sourceEvent.altKey?l.orgDomain():null}).on("zoom",oa?dd:null);F.update=function(){y&&y.select(".x.brush").call(this);
return this};G.orgScaleExtent=function(){var a=pc?pc:[1,10];return[a[0],Math.max(xa()/a[1],a[1])]};G.updateScaleExtent=function(){var a=l.orgDomain(),a=(a[1]-a[0])/(R[1]-R[0]),b=this.orgScaleExtent();this.scaleExtent([b[0]*a,b[1]*a]);return this};var Oa,cb,g,y,za,ba,hc,ab=null,mb=null,R;k.focus=function(a){k.defocus();e.selectAll(Ta(a)).filter(function(a){return ob(a.id)}).classed("focused",!0).transition().duration(100).style("opacity",1)};k.defocus=function(a){e.selectAll(Ta(a)).filter(function(a){return ob(a.id)}).classed("focused",
!1).transition().duration(100).style("opacity",0.3)};k.revert=function(a){e.selectAll(Ta(a)).filter(function(a){return ob(a.id)}).classed("focused",!1).transition().duration(100).style("opacity",1)};k.show=function(a){e.selectAll(Ta(a)).transition().style("opacity",1)};k.hide=function(a){e.selectAll(Ta(a)).transition().style("opacity",0)};k.unzoom=function(){F.clear().update();B({withUpdateXDomain:!0})};k.load=function(a){s(a.done)&&(a.done=function(){});"categories"in a&&Y&&(Ha=a.categories,J.categories(Ha));
if("cacheIds"in a&&N(a.cacheIds))Ra(fb(a.cacheIds),a.done);else if("data"in a)Ra(ia(a.data),a.done);else if("url"in a)e.csv(a.url,function(b,c){Ra(ia(c),a.done)});else if("rows"in a)Ra(ia(Ea(a.rows)),a.done);else if("columns"in a)Ra(ia(Fa(a.columns)),a.done);else throw Error("url or rows or columns is required.");};k.unload=function(a){k.data.targets=k.data.targets.filter(function(b){return b.id!==a});e.selectAll(".target-"+a).transition().style("opacity",0).remove();Aa&&(e.selectAll(".legend-item-"+
a).remove(),eb(k.data.targets));0<k.data.targets.length&&B()};k.selected=function(a){a=n(a)?"-"+a:"";return e.merge(g.selectAll(".-shapes"+a).selectAll(".-shape").filter(function(){return e.select(this).classed(ea)}).map(function(a){return a.map(function(a){return a.__data__})}))};k.select=function(a,b,c){ca&&g.selectAll(".-shapes").selectAll(".-shape").each(function(f,g){var k="circle"===this.nodeName?bc:Oc,l="circle"===this.nodeName?sb:dc;0<=b.indexOf(g)?qa(f)&&(pa||s(a)||0<=a.indexOf(f.id))&&k(e.select(this).classed(ea,
!0),f,g):n(c)&&c&&l(e.select(this).classed(ea,!1),f,g)})};k.unselect=function(a,b){ca&&g.selectAll(".-shapes").selectAll(".-shape").each(function(c,f){var g="circle"===this.nodeName?sb:dc;(s(b)||0<=b.indexOf(f))&&qa(c)&&(pa||s(a)||0<=a.indexOf(c.id))&&g(e.select(this).classed(ea,!1),c,f)})};k.toLine=function(a){qb(a,"line");B()};k.toSpline=function(a){qb(a,"spline");B()};k.toBar=function(a){qb(a,"bar");B()};k.groups=function(a){if(s(a))return v;v=a;B();return v};k.regions=function(a){if(s(a))return L;
L=a;B();return L};k.regions.add=function(a){if(s(a))return L;L=L.concat(a);B();return L};k.regions.remove=function(a,b){var c=[].concat(a);b=n(b)?b:{};c.forEach(function(a){var c=e.selectAll("."+a);n(b.duration)&&(c=c.transition().duration(b.duration).style("fill-opacity",0));c.remove();L=L.filter(function(b){return 0>b.classes.indexOf(a)})});return L};k.data.get=function(a){a=k.data.getAsTarget(a);return n(a)?a.values.map(function(a){return a.value}):void 0};k.data.getAsTarget=function(a){var b=
Za(function(b){return b.id===a});return 0<b.length?b[0]:void 0};if("url"in T.data)e.csv(T.data.url,function(a,b){tb(b)});else if("rows"in T.data)tb(Ea(T.data.rows));else if("columns"in T.data)tb(Fa(T.data.columns));else throw Error("url or rows or columns is required.");fa.onresize=function(){P();U();F.x(X);oa&&G.x(l);e.select("svg").attr("width",hb).attr("height",va);e.select("#"+ub).select("rect").attr("width",q).attr("height",t);e.select("#xaxis-clip").select("rect").attr("width",q+2);e.select(".zoom-rect").attr("width",
q).attr("height",t);g.select(".x.axis").attr("transform",K.x);g.select(".y2.axis").attr("transform",K.y2);ya&&(y.select(".x.brush").selectAll("rect").attr("height",ka),y.attr("transform",K.context),y.select(".x.axis").attr("transform",K.subx));Aa&&(za.attr("transform",K.legend),eb(k.data.targets,{withTransition:!1}));B({withTransition:!1,withUpdateXDomain:!0})};return k}})(window);
