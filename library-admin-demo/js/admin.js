// 图书馆管理系统 JavaScript

$(document).ready(function() {
    // 初始化菜单
    initMenu();
    
    // 默认显示概览页面
    showDashboard();
});

// 初始化菜单
function initMenu() {
    $('.menu-list > a').click(function(e) {
        e.preventDefault();
        
        // 切换子菜单显示/隐藏
        const $parent = $(this).parent();
        const $submenu = $parent.find('.sub-menu-list');
        
        if ($submenu.length > 0) {
            $parent.toggleClass('nav-active');
            $submenu.slideToggle(200);
        }
    });
    
    // 子菜单项点击
    $('.sub-menu-list a').click(function(e) {
        e.preventDefault();
        
        // 移除其他活动状态
        $('.sub-menu-list li').removeClass('active');
        
        // 添加当前活动状态
        $(this).parent().addClass('active');
    });
}

// 更新页面标题和面包屑
function updatePageHeader(title, breadcrumb) {
    $('#page-title').text(title);
    $('#breadcrumb-current').text(breadcrumb);
}

// 显示概览页面
function showDashboard() {
    updatePageHeader('图书馆管理系统', '概览');
    
    const content = `
        <div id="dashboard-content">
            <div class="row">
                <div class="col-md-3">
                    <div class="info-box bg-primary">
                        <div class="info-box-content">
                            <span class="info-box-text">图书馆总数</span>
                            <span class="info-box-number">5</span>
                        </div>
                        <div class="info-box-icon">
                            <i class="fa fa-building"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box bg-success">
                        <div class="info-box-content">
                            <span class="info-box-text">图书总数</span>
                            <span class="info-box-number">1,234</span>
                        </div>
                        <div class="info-box-icon">
                            <i class="fa fa-book"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box bg-warning">
                        <div class="info-box-content">
                            <span class="info-box-text">用户总数</span>
                            <span class="info-box-number">567</span>
                        </div>
                        <div class="info-box-icon">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box bg-danger">
                        <div class="info-box-content">
                            <span class="info-box-text">借阅中</span>
                            <span class="info-box-number">89</span>
                        </div>
                        <div class="info-box-icon">
                            <i class="fa fa-exchange-alt"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="panel">
                        <div class="panel-heading">
                            <h4>最近活动</h4>
                        </div>
                        <div class="panel-body">
                            <div class="activity-list">
                                <div class="activity-item">
                                    <i class="fa fa-plus text-success"></i>
                                    <span>新增图书馆"TTI神学院图书馆"</span>
                                    <small class="text-muted">2小时前</small>
                                </div>
                                <div class="activity-item">
                                    <i class="fa fa-book text-primary"></i>
                                    <span>批量导入图书 50 本</span>
                                    <small class="text-muted">4小时前</small>
                                </div>
                                <div class="activity-item">
                                    <i class="fa fa-user text-info"></i>
                                    <span>新用户注册：李四</span>
                                    <small class="text-muted">6小时前</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}

// 显示图书馆列表
function showLibraryList() {
    updatePageHeader('图书馆列表', '图书馆管理');
    
    const content = `
        <div class="panel">
            <div class="panel-heading">
                <h4>图书馆列表</h4>
                <div class="pull-right">
                    <button class="btn btn-primary btn-sm" onclick="showLibraryAdd()">
                        <i class="fa fa-plus"></i> 添加图书馆
                    </button>
                </div>
                <div style="clear: both;"></div>
            </div>
            <div class="panel-body">
                <div class="search-form">
                    <input type="text" class="form-control" placeholder="搜索图书馆名称..." style="width: 200px;">
                    <button class="btn btn-primary">搜索</button>
                </div>
                
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>图书馆名称</th>
                            <th>描述</th>
                            <th>状态</th>
                            <th>最大借阅数</th>
                            <th>默认借阅天数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>TTI神学院图书馆</td>
                            <td>TTI神学院专用图书馆</td>
                            <td><span class="label label-success">启用</span></td>
                            <td>10</td>
                            <td>14天</td>
                            <td>2025-01-01</td>
                            <td>
                                <button class="btn btn-primary btn-sm" onclick="editLibrary(1)">编辑</button>
                                <button class="btn btn-warning btn-sm" onclick="manageBooks(1)">管理图书</button>
                                <button class="btn btn-info btn-sm" onclick="manageUsers(1)">管理用户</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>社区图书馆</td>
                            <td>面向社区居民的公共图书馆</td>
                            <td><span class="label label-success">启用</span></td>
                            <td>7</td>
                            <td>14天</td>
                            <td>2025-01-02</td>
                            <td>
                                <button class="btn btn-primary btn-sm" onclick="editLibrary(2)">编辑</button>
                                <button class="btn btn-warning btn-sm" onclick="manageBooks(2)">管理图书</button>
                                <button class="btn btn-info btn-sm" onclick="manageUsers(2)">管理用户</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="text-center mt-4">
                    <nav>
                        <ul class="pagination">
                            <li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">下一页</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}

// 显示添加图书馆页面
function showLibraryAdd() {
    updatePageHeader('添加图书馆', '图书馆管理');
    
    const content = `
        <div class="panel">
            <div class="panel-heading">
                <h4>添加图书馆</h4>
            </div>
            <div class="panel-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">图书馆名称 *</label>
                                <input type="text" class="form-control" placeholder="请输入图书馆名称">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">唯一标识 *</label>
                                <input type="text" class="form-control" placeholder="用于LCP账号前缀">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">图书馆描述</label>
                        <textarea class="form-control" rows="3" placeholder="请输入图书馆描述"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">最大借阅数量</label>
                                <input type="number" class="form-control" value="10" min="1" max="50">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">默认借阅天数</label>
                                <input type="number" class="form-control" value="14" min="1" max="365">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">备注信息</label>
                        <textarea class="form-control" rows="2" placeholder="管理员备注"></textarea>
                    </div>
                    
                    <div class="text-center">
                        <button type="button" class="btn btn-success" onclick="saveLibrary()">保存</button>
                        <button type="button" class="btn btn-secondary" onclick="showLibraryList()">取消</button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}

// 保存图书馆
function saveLibrary() {
    alert('图书馆保存成功！');
    showLibraryList();
}

// 编辑图书馆
function editLibrary(id) {
    alert('编辑图书馆 ID: ' + id);
    // 这里可以加载编辑表单
}

// 管理图书
function manageBooks(libraryId) {
    showBookList(libraryId);
}

// 管理用户
function manageUsers(libraryId) {
    showUserList(libraryId);
}

// 显示图书列表
function showBookList(libraryId) {
    updatePageHeader('图书管理', '图书管理');
    
    const content = `
        <div class="panel">
            <div class="panel-heading">
                <h4>图书列表 ${libraryId ? '(图书馆ID: ' + libraryId + ')' : ''}</h4>
                <div class="pull-right">
                    <button class="btn btn-success btn-sm" onclick="showBookImport()">
                        <i class="fa fa-upload"></i> 批量导入
                    </button>
                    <button class="btn btn-primary btn-sm">
                        <i class="fa fa-plus"></i> 添加图书
                    </button>
                </div>
                <div style="clear: both;"></div>
            </div>
            <div class="panel-body">
                <div class="search-form">
                    <input type="text" class="form-control" placeholder="搜索书名、作者..." style="width: 200px;">
                    <select class="form-control" style="width: 120px;">
                        <option value="">全部状态</option>
                        <option value="1">上架</option>
                        <option value="0">下架</option>
                    </select>
                    <button class="btn btn-primary">搜索</button>
                </div>
                
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>书籍ID</th>
                            <th>书名</th>
                            <th>作者</th>
                            <th>总藏书量</th>
                            <th>可用数量</th>
                            <th>状态</th>
                            <th>借阅天数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1001</td>
                            <td>圣经</td>
                            <td>多人</td>
                            <td>10</td>
                            <td>5</td>
                            <td><span class="label label-success">上架</span></td>
                            <td>14天</td>
                            <td>
                                <button class="btn btn-primary btn-sm">编辑</button>
                                <button class="btn btn-warning btn-sm">下架</button>
                                <button class="btn btn-info btn-sm">查看借阅</button>
                            </td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>基督教要义</td>
                            <td>加尔文</td>
                            <td>5</td>
                            <td>3</td>
                            <td><span class="label label-success">上架</span></td>
                            <td>21天</td>
                            <td>
                                <button class="btn btn-primary btn-sm">编辑</button>
                                <button class="btn btn-warning btn-sm">下架</button>
                                <button class="btn btn-info btn-sm">查看借阅</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}

// 显示批量导入页面
function showBookImport() {
    updatePageHeader('批量导入图书', '图书管理');
    
    const content = `
        <div class="panel">
            <div class="panel-heading">
                <h4>批量导入图书</h4>
            </div>
            <div class="panel-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    请上传包含书名、编号、数量的Excel文件。支持格式：.xlsx, .xls
                </div>
                
                <form>
                    <div class="mb-3">
                        <label class="form-label">选择图书馆</label>
                        <select class="form-control">
                            <option value="">请选择图书馆</option>
                            <option value="1">TTI神学院图书馆</option>
                            <option value="2">社区图书馆</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">上传文件</label>
                        <input type="file" class="form-control" accept=".xlsx,.xls">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">导入说明</label>
                        <ul>
                            <li>Excel文件应包含：书名、编号、数量三列</li>
                            <li>编号不能重复，用于系统识别</li>
                            <li>数量必须为正整数</li>
                            <li>导入后默认全部上架</li>
                        </ul>
                    </div>
                    
                    <div class="text-center">
                        <button type="button" class="btn btn-success">开始导入</button>
                        <button type="button" class="btn btn-secondary" onclick="showBookList()">返回</button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}

// 显示用户列表
function showUserList(libraryId) {
    updatePageHeader('用户管理', '用户管理');
    
    const content = `
        <div class="panel">
            <div class="panel-heading">
                <h4>用户列表 ${libraryId ? '(图书馆ID: ' + libraryId + ')' : ''}</h4>
                <div class="pull-right">
                    <button class="btn btn-primary btn-sm" onclick="showUserInvite()">
                        <i class="fa fa-envelope"></i> 邀请用户
                    </button>
                </div>
                <div style="clear: both;"></div>
            </div>
            <div class="panel-body">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>邮箱</th>
                            <th>状态</th>
                            <th>加入时间</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1001</td>
                            <td><EMAIL></td>
                            <td><span class="label label-success">已确认</span></td>
                            <td>2025-01-01</td>
                            <td>神学院学生</td>
                            <td>
                                <button class="btn btn-primary btn-sm">编辑</button>
                                <button class="btn btn-danger btn-sm">移除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td><EMAIL></td>
                            <td><span class="label label-warning">待确认</span></td>
                            <td>2025-01-02</td>
                            <td>访问学者</td>
                            <td>
                                <button class="btn btn-primary btn-sm">编辑</button>
                                <button class="btn btn-info btn-sm">重发邀请</button>
                                <button class="btn btn-danger btn-sm">移除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}

// 显示邀请用户页面
function showUserInvite() {
    updatePageHeader('邀请用户', '用户管理');
    
    const content = `
        <div class="panel">
            <div class="panel-heading">
                <h4>邀请用户加入图书馆</h4>
            </div>
            <div class="panel-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">选择图书馆</label>
                        <select class="form-control">
                            <option value="">请选择图书馆</option>
                            <option value="1">TTI神学院图书馆</option>
                            <option value="2">社区图书馆</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">用户邮箱</label>
                        <input type="email" class="form-control" placeholder="请输入用户邮箱">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">用户备注</label>
                        <textarea class="form-control" rows="3" placeholder="请输入用户备注信息"></textarea>
                    </div>
                    
                    <div class="text-center">
                        <button type="button" class="btn btn-success">发送邀请</button>
                        <button type="button" class="btn btn-secondary" onclick="showUserList()">取消</button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}

// 显示借阅记录
function showBorrowList() {
    updatePageHeader('借阅记录', '借阅管理');
    
    const content = `
        <div class="panel">
            <div class="panel-heading">
                <h4>借阅记录</h4>
            </div>
            <div class="panel-body">
                <div class="search-form">
                    <select class="form-control" style="width: 150px;">
                        <option value="">选择图书馆</option>
                        <option value="1">TTI神学院图书馆</option>
                        <option value="2">社区图书馆</option>
                    </select>
                    <select class="form-control" style="width: 120px;">
                        <option value="">全部状态</option>
                        <option value="0">借阅中</option>
                        <option value="1">已归还</option>
                        <option value="2">已逾期</option>
                    </select>
                    <input type="text" class="form-control" placeholder="搜索用户邮箱..." style="width: 200px;">
                    <button class="btn btn-primary">搜索</button>
                </div>
                
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>借阅ID</th>
                            <th>用户邮箱</th>
                            <th>书名</th>
                            <th>借阅时间</th>
                            <th>应归还时间</th>
                            <th>状态</th>
                            <th>续借次数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>10001</td>
                            <td><EMAIL></td>
                            <td>圣经</td>
                            <td>2025-01-01</td>
                            <td>2025-01-15</td>
                            <td><span class="label label-warning">借阅中</span></td>
                            <td>0</td>
                            <td>
                                <button class="btn btn-success btn-sm">手动归还</button>
                                <button class="btn btn-info btn-sm">续借</button>
                            </td>
                        </tr>
                        <tr>
                            <td>10002</td>
                            <td><EMAIL></td>
                            <td>基督教要义</td>
                            <td>2024-12-20</td>
                            <td>2025-01-03</td>
                            <td><span class="label label-danger">已逾期</span></td>
                            <td>1</td>
                            <td>
                                <button class="btn btn-success btn-sm">手动归还</button>
                                <button class="btn btn-warning btn-sm">发送提醒</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}

// 显示预约记录
function showReservationList() {
    updatePageHeader('预约记录', '借阅管理');
    
    const content = `
        <div class="panel">
            <div class="panel-heading">
                <h4>预约记录</h4>
            </div>
            <div class="panel-body">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>预约ID</th>
                            <th>用户邮箱</th>
                            <th>书名</th>
                            <th>预约时间</th>
                            <th>过期时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>20001</td>
                            <td><EMAIL></td>
                            <td>圣经</td>
                            <td>2025-01-05</td>
                            <td>2025-01-12</td>
                            <td><span class="label label-info">等待中</span></td>
                            <td>
                                <button class="btn btn-danger btn-sm">取消预约</button>
                            </td>
                        </tr>
                        <tr>
                            <td>20002</td>
                            <td><EMAIL></td>
                            <td>基督教要义</td>
                            <td>2025-01-06</td>
                            <td>2025-01-13</td>
                            <td><span class="label label-success">可借阅</span></td>
                            <td>
                                <button class="btn btn-primary btn-sm">通知借阅</button>
                                <button class="btn btn-danger btn-sm">取消预约</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    $('#main-wrapper').html(content);
}
