# 图书馆后台管理系统 Demo

这是一个基于 HTML/CSS/JavaScript 的图书馆后台管理系统前端演示，参考了播客管理系统的设计风格和架构模式。

## 功能特性

### 1. 图书馆管理
- 图书馆列表展示
- 添加/编辑图书馆信息
- 图书馆状态管理
- 配置借阅规则（最大借阅数、默认借阅天数）

### 2. 图书管理
- 图书列表展示
- 批量导入图书功能
- 图书上架/下架管理
- 库存数量管理

### 3. 用户管理
- 用户列表展示
- 邀请用户加入图书馆
- 用户状态管理（待确认/已确认）
- 用户备注信息管理

### 4. 借阅管理
- 借阅记录查看
- 手动归还/续借操作
- 逾期提醒功能
- 预约记录管理

### 5. 系统概览
- 统计数据展示（图书馆数量、图书总数、用户总数、借阅中数量）
- 最近活动记录
- 数据可视化展示

## 技术栈

- **前端框架**: 纯 HTML5 + CSS3 + JavaScript (jQuery)
- **UI框架**: Bootstrap 5.1.3
- **图标库**: Font Awesome 6.0.0
- **响应式设计**: 支持桌面端和移动端

## 设计特点

### 1. 参考现有架构
- 基于播客管理系统的设计模式
- 保持与现有系统一致的视觉风格
- 采用相同的布局结构和交互模式

### 2. 符合设计文档
- 严格按照 `Docs/图书馆后台设计方案work.md` 的功能需求
- 实现了完整的图书馆管理流程
- 支持多图书馆管理模式

### 3. 用户体验优化
- 直观的导航菜单
- 清晰的数据展示
- 友好的操作反馈
- 响应式布局适配

## 文件结构

```
library-admin-demo/
├── index.html          # 主页面
├── css/
│   └── admin.css       # 样式文件
├── js/
│   └── admin.js        # 交互逻辑
└── README.md           # 说明文档
```

## 使用方法

1. 直接在浏览器中打开 `index.html` 文件
2. 通过左侧菜单导航到不同功能模块
3. 体验各种管理操作的界面和交互

## 主要页面

### 1. 首页概览
- 显示系统统计数据
- 展示最近活动记录
- 提供快速导航入口

### 2. 图书馆管理
- **图书馆列表**: 展示所有图书馆信息，支持搜索和分页
- **添加图书馆**: 创建新的图书馆，配置基本信息和借阅规则

### 3. 图书管理
- **图书列表**: 展示图书信息，支持按状态筛选
- **批量导入**: 通过Excel文件批量导入图书数据

### 4. 用户管理
- **用户列表**: 展示图书馆用户信息和状态
- **邀请用户**: 通过邮箱邀请新用户加入图书馆

### 5. 借阅管理
- **借阅记录**: 查看和管理所有借阅记录
- **预约记录**: 管理图书预约队列

## 数据模型

基于设计文档中的数据库结构：

- `lib_library`: 图书馆信息
- `lib_book`: 图书馆藏书
- `lib_user`: 图书馆用户
- `lib_borrow`: 借阅记录
- `lib_reservation`: 预约记录

## 特色功能

### 1. 多图书馆支持
- 支持管理多个图书馆
- 每个图书馆独立的用户和图书管理
- 灵活的权限控制

### 2. 智能借阅管理
- 自动计算借阅到期时间
- 支持续借和预约功能
- 逾期提醒和处理

### 3. 批量操作
- 批量导入图书数据
- 批量邀请用户
- 批量状态更新

### 4. 实时统计
- 实时更新统计数据
- 活动记录追踪
- 数据可视化展示

## 扩展性

这个demo为后续开发提供了良好的基础：

1. **后端集成**: 可以轻松集成到现有的Spring MVC架构中
2. **数据绑定**: 前端结构已为数据绑定做好准备
3. **功能扩展**: 模块化设计便于添加新功能
4. **样式定制**: CSS结构清晰，便于主题定制

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. 这是一个纯前端演示，所有数据都是模拟数据
2. 实际使用时需要集成后端API接口
3. 建议在现代浏览器中查看以获得最佳体验
4. 响应式设计在移动设备上也有良好表现

## 后续开发建议

1. **后端集成**: 按照现有的Controller/Service/Mapper架构实现后端功能
2. **数据验证**: 添加前端表单验证和后端数据校验
3. **权限控制**: 实现基于角色的访问控制
4. **性能优化**: 添加数据缓存和分页优化
5. **用户体验**: 增加加载动画和操作反馈

这个demo完整展示了图书馆后台管理系统的核心功能，为实际开发提供了清晰的参考和基础。
