/* 图书馆管理系统样式 */
body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
}

/* 左侧菜单样式 */
.left-side {
    position: fixed;
    left: 0;
    top: 0;
    width: 250px;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 1000;
    overflow-y: auto;
}

.sticky-left-side {
    position: fixed;
}

.logo {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.logo img {
    max-width: 100%;
    height: auto;
}

.left-side-inner {
    padding: 20px 0;
}

.custom-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.custom-nav > li {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.custom-nav > li > a {
    display: block;
    padding: 15px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.custom-nav > li > a:hover,
.custom-nav > li.nav-active > a {
    background-color: rgba(255,255,255,0.1);
    color: #fff;
}

.custom-nav > li > a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* 子菜单样式 */
.sub-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: rgba(0,0,0,0.1);
    display: none;
}

.menu-list.nav-active .sub-menu-list {
    display: block;
}

.sub-menu-list li a {
    display: block;
    padding: 10px 20px 10px 50px;
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.sub-menu-list li a:hover,
.sub-menu-list li.active a {
    background-color: rgba(255,255,255,0.1);
    color: #fff;
}

/* 主内容区域 */
.main-content {
    margin-left: 250px;
    min-height: 100vh;
    background-color: #f5f5f5;
}

/* 头部样式 */
.header-section {
    background: #fff;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toggle-btn {
    font-size: 18px;
    color: #666;
    cursor: pointer;
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    color: #666;
    font-size: 14px;
}

/* 页面标题 */
.page-heading {
    background: #fff;
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.page-heading h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 24px;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
}

.breadcrumb li {
    margin-right: 10px;
}

.breadcrumb li:after {
    content: " / ";
    margin-left: 10px;
    color: #ccc;
}

.breadcrumb li:last-child:after {
    content: "";
}

.breadcrumb a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumb .active {
    color: #666;
}

/* 内容包装器 */
.wrapper {
    padding: 20px;
}

/* 信息卡片 */
.info-box {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.info-box.bg-primary { background: linear-gradient(135deg, #007bff, #0056b3); color: #fff; }
.info-box.bg-success { background: linear-gradient(135deg, #28a745, #1e7e34); color: #fff; }
.info-box.bg-warning { background: linear-gradient(135deg, #ffc107, #e0a800); color: #fff; }
.info-box.bg-danger { background: linear-gradient(135deg, #dc3545, #c82333); color: #fff; }

.info-box-content {
    position: relative;
    z-index: 2;
}

.info-box-text {
    display: block;
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 5px;
}

.info-box-number {
    display: block;
    font-size: 32px;
    font-weight: bold;
}

.info-box-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 48px;
    opacity: 0.3;
}

/* 面板样式 */
.panel {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.panel-heading {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.panel-heading h4 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.panel-body {
    padding: 20px;
}

/* 表格样式 */
.table {
    width: 100%;
    margin-bottom: 0;
}

.table th,
.table td {
    padding: 12px;
    vertical-align: middle;
    border-bottom: 1px solid #e0e0e0;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-success {
    background-color: #28a745;
    color: #fff;
}

.btn-success:hover {
    background-color: #1e7e34;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
    color: #fff;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

/* 标签样式 */
.label {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    color: #fff;
}

.label-success { background-color: #28a745; }
.label-warning { background-color: #ffc107; color: #212529; }
.label-danger { background-color: #dc3545; }
.label-info { background-color: #17a2b8; }

/* 活动列表 */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

.activity-item span {
    flex: 1;
    margin-right: 10px;
}

.activity-item small {
    font-size: 12px;
}

/* 页脚 */
.footer {
    background: #fff;
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    margin-top: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .left-side {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .left-side.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
}

/* 工具提示 */
.text-muted {
    color: #6c757d !important;
}

.text-center {
    text-align: center;
}

.mt-4 {
    margin-top: 1.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.pull-right {
    float: right;
}

/* 搜索表单 */
.search-form {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
}

.form-control {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}
